# It's necessary to set this because some environments don't link sh -> bash.
SHELL := /usr/bin/env bash
BASEDIR = $(shell pwd)
GOCMD=GO111MODULE=on GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go

all: gotool build

build: cmdbserver

# 清除target目录
clean:
	rm -rf target/

gotool:
	${GOCMD} fmt cmd/cmdbserver/cmdbserver.go
	${GOCMD} vet cmd/cmdbserver/cmdbserver.go

# 编译monitor
monitor:
	${GOCMD} build -o target/monitor cmd/monitor/monitor.go

# 编译cmdbserver
cmdbserver:
	${GOCMD} build -o target/cmdbserver cmd/cmdbserver/cmdbserver.go

# 编译dnsserver
dnsserver:
	${GOCMD} build -o target/dnsserver cmd/dnsserver/apiserver/dnsserver.go

dnsagent:
	${GOCMD} build -o target/dnsagent cmd/dnsserver/agent/agent.go

asynqserver:
	${GOCMD} build -o target/asynqserver internal/tools/asynq_server/*.go


help:
	@echo "make - compile the source code"
	@echo "make clean - remove binary file and vim swp files"
	@echo "make gotool - run go tool 'fmt' and 'vet'"

.PHONY: clean gotool help cmdbserver accountserver build
