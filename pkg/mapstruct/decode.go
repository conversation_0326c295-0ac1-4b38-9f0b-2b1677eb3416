package mapstruct

import (
	"encoding/json"

	"github.com/mitchellh/mapstructure"
)

// Decode2Struct conv map to struct
// 适用场景：仅支持原生转 map to struct
func Decode2Struct(m map[string]interface{}, st interface{}) error {
	if err := mapstructure.Decode(m, &st); err != nil {
		return err
	}
	return nil
}

// Struct2Map TODO
func Struct2Map(v interface{}) (map[string]interface{}, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}
	data := make(map[string]interface{})
	if err := json.Unmarshal(bytes, &data); err != nil {
		return nil, err
	}
	return data, nil
}
