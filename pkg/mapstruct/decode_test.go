package mapstruct

import (
	"testing"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
)

func TestMapToStruct(t *testing.T) {
	m := map[string]interface{}{
		"type_name":  "select",
		"name":       "设备品牌",
		"code":       "ROUTER_brand8",
		"model_code": "ROUTER",
		"attr_group": "ROUTER_basic",
		"describe":   "",
		"attrs": map[string]interface{}{
			"user_hint": "记录设备的品牌",
			"rule": map[string]interface{}{
				"rule_re": "",
			},
		},
	}

	type modelAttrRule struct {
		RuleName string `json:"rule_name" bson:"rule_name" mapstructure:"rule_name"`
		RuleRe   string `json:"rule_re" bson:"rule_re" mapstructure:"rule_re"`
	}

	// SelectChainData 枚举继承数据的结构
	type selectChainData struct {
		DataTree []struct {
			Children []struct {
				ID   string `json:"id" bson:"id" mapstructure:"id"`
				Name string `json:"name" bson:"name" mapstructure:"name"`
			} `json:"children" bson:"children" mapstructure:"children"`
			ID   string `json:"id" bson:"id" mapstructure:"id"`
			Name string `json:"name" bson:"name" mapstructure:"name"`
		} `json:"data_tree" bson:"data_tree" mapstructure:"data_tree"`
		ParentCode string `json:"parent_code" bson:"parent_code" mapstructure:"parent_code"`
	}

	type selectAttribute struct {
		v1.BaseAttribute `mapstructure:",squash" bson:",inline"`
		Attrs            struct {
			modelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
			UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
			Inherit       bool   `json:"inherit" bson:"inherit" mapstructure:"inherit"`
			Depth         int    `json:"depth" bson:"depth" mapstructure:"depth"`
			Options       []struct {
				Name string `json:"name" bson:"name" mapstructure:"name,required"`
				ID   string `json:"id" bson:"id" mapstructure:"id,required"`
			} `json:"opts" bson:"opts" mapstructure:"opts,required"`
			ChainData selectChainData `json:"chain_data" bson:"chain_data" mapstructure:"chain_data,omitempty"`
		} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
	}
	s := &selectAttribute{}
	err := Decode2Struct(m, s)
	if err != nil {
		t.Log(err.Error())
	} else {
		t.Logf("%#v", s)
	}
}
