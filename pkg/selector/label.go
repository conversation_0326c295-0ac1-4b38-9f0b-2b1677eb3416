package selector

import (
	"regexp"
)

var (
	// LabelKeyRule 定义标签key的正则表达式
	// 标签的键必须以字母开头，并且只能包含字母、数字、破折号（-）、下划线（_）和句点（.），并以字母或数字结尾
	LabelKeyRule = regexp.MustCompile(`^[a-zA-Z]([a-z0-9A-Z\-_.]*[a-z0-9A-Z])?$`)
	// LabelValueRule 定义标签value的正则表达式
	// 标签的值必须以字母或数字开头，并且只能包含字母、数字、破折号（-）、下划线（_）和句点（.），并以字母或数字结尾
	LabelValueRule = regexp.MustCompile(`^[a-z0-9A-Z]([a-z0-9A-Z\-_.]*[a-z0-9A-Z])?$`)
)

// Label 定义标签，标签是一个键值对，比如说env=prod，env=qa，env=dev
type Label struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// Labels 定义标签数组
type Labels []Label

// Len 实现sort.Interface接口，返回标签数组的长度
func (ls Labels) Len() int {
	return len(ls)
}

// Less 目前排序规则先按照名称进行排序
func (ls Labels) Less(i, j int) bool {
	return ls[i].Name < ls[j].Name
}

// Swap 实现sort.Interface接口，交换标签数组中两个标签的位置
func (ls Labels) Swap(i, j int) {
	ls[i], ls[j] = ls[j], ls[i]
}

// Copy 复制标签数组,返回一个新的标签数组
func (ls Labels) Copy() Labels {
	newLabels := make(Labels, len(ls))
	copy(newLabels, ls)
	return newLabels
}

// Get 根据标签名称获取标签值
func (ls Labels) Get(name string) []string {
	var values []string

	for _, l := range ls {
		if l.Name == name {
			values = append(values, l.Value)
		}
	}
	return values
}

func (ls Labels) Has(name string) bool {
	for _, l := range ls {
		if l.Name == name {
			return true
		}
	}
	return false
}

// Range calls f on each label.
func (ls Labels) Range(f func(l Label)) {
	for _, l := range ls {
		f(l)
	}
}
