package selector

import (
	"errors"
	"fmt"
	"regexp"

	"ks-knoc-server/pkg/array"
)

var (
	// LabelNGKeyRule TODO
	LabelNGKeyRule = regexp.MustCompile(`^[a-zA-Z]([a-z0-9A-Z\-_.]*[a-z0-9A-Z])?$`)
	// LabelNGValueRule TODO
	LabelNGValueRule = regexp.MustCompile(`^[a-z0-9A-Z]([a-z0-9A-Z\-_.]*[a-z0-9A-Z])?$`)
)

// Operator 操作符
type Operator string

const (
	// DoesNotExist 不存在
	DoesNotExist Operator = "!"
	// Equals 等于
	Equals Operator = "="
	// In TODO
	In Operator = "in"
	// NotEquals 不等于
	NotEquals Operator = "!="
	// NotIn TODO
	NotIn Operator = "notin"
	// Exists 是否存在
	Exists Operator = "exists"
)

// AvailableOperators 可用的Operator
var AvailableOperators = []Operator{
	DoesNotExist,
	Equals,
	In,
	NotEquals,
	NotIn,
	Exists,
}

// Selector 选择器
type Selector struct {
	Key      string   `json:"key" field:"key" bson:"key"`
	Operator Operator `json:"operator" field:"operator" bson:"operator"`
	Values   []string `json:"values" field:"values" bson:"values"`
}

// Validate TODO
func (s *Selector) Validate() (string, error) {
	if array.InArray(s.Operator, AvailableOperators) == false {
		return "operator", fmt.Errorf("operator %s not available, available operators: %+v", s.Operator, AvailableOperators)
	}

	if (s.Operator == In || s.Operator == NotIn) && len(s.Values) == 0 {
		return "values", errors.New("values shouldn't be empty")
	}

	if (s.Operator == Exists || s.Operator == DoesNotExist) && len(s.Values) > 0 {
		return "values", errors.New("values shouldn't be empty")
	}

	if (s.Operator == Equals || s.Operator == NotEquals) && len(s.Values) != 1 {
		return "values", errors.New("values field length for equal operation should exactly one")
	}

	if LabelNGKeyRule.MatchString(s.Key) == false {
		return "key", fmt.Errorf("key %s invalid", s.Key)
	}
	return "", nil
}
