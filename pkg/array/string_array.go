package array

// StringArray 定义一个字符串的切片类型
type StringArray []string

// InArray 判断target是否在s中
func (s StringArray) InArray(target string) bool {
	for _, v := range s {
		if v == target {
			return true
		}
	}
	return false
}

// SubSlice 判断target是不是s的子切片，
func (s StringArray) SubSlice(target StringArray) bool {
	if len(target) == 0 || len(target) > len(s) {
		return false
	}
	eleMap := make(map[string]bool)
	for _, v := range s {
		eleMap[v] = true
	}
	for _, ele := range target {
		if _, ok := eleMap[ele]; !ok {
			return false
		}
	}
	return true
}

// ArrayEqual 判断target是否和s相等，值和顺序都要一样
func (s StringArray) ArrayEqual(target StringArray) bool {
	if len(s) != len(target) {
		return false
	}
	for i := range target {
		if s[i] != target[i] {
			return false
		}
	}
	return true
}

// ArrayValueEqual 只关注值是否相等，不关注顺序
func (s StringArray) ArrayValueEqual(target StringArray) bool {
	// 如果长度不相等，直接返回false
	if len(s) != len(target) {
		return false
	}
	for i := range target {
		if !s.InArray(target[i]) {
			return false
		}
	}
	return true
}

// ArrayDiff 返回s中存在，但是在arr中不存在的数据，即 s - arr
func (s StringArray) ArrayDiff(arr StringArray) StringArray {
	diff := make(StringArray, 0)
	m := make(map[string]bool)

	for _, v := range s {
		m[v] = true
	}

	for _, v := range arr {
		if _, ok := m[v]; !ok {
			diff = append(diff, v)
		}
	}

	return diff
}

// ArrayIntersect 返回arr和s的交集
func (s StringArray) ArrayIntersect(arr StringArray) StringArray {
	var intersect StringArray
	m := make(map[string]bool)

	for _, v := range s {
		m[v] = true
	}

	for _, v := range arr {
		if _, ok := m[v]; ok {
			intersect = append(intersect, v)
		}
	}

	return intersect
}

// ArrayUnique 返回s中的唯一元素，即去重
func (s StringArray) ArrayUnique() StringArray {
	m := make(map[string]bool)
	var unique StringArray

	for _, v := range s {
		if _, ok := m[v]; !ok {
			m[v] = true
			unique = append(unique, v)
		}
	}

	return unique
}

// ArrayMerge 合并s和arr，但是默认不会去重
func (s StringArray) ArrayMerge(arr StringArray, deduplicate bool) StringArray {
	if deduplicate {
		merged := append(s, arr...)
		return merged.ArrayUnique()
	}
	return append(s, arr...)
}

// ArraySearch 在s中查找target，返回对应的索引位置
func (s StringArray) ArraySearch(target string) int {
	for i, v := range s {
		if v == target {
			return i
		}
	}
	return -1
}

// ArrayChunk 将s分割成多个size大小的切片
func (s StringArray) ArrayChunk(size int) []StringArray {
	var chunk []StringArray
	for i := 0; i < len(s); i += size {
		end := i + size
		if end > len(s) {
			end = len(s)
		}
		chunk = append(chunk, s[i:end])
	}
	return chunk
}

// ArrayPad 用value填充s到size大小
func (s StringArray) ArrayPad(size int, value string) StringArray {
	for len(s) < size {
		s = append(s, value)
	}
	return s
}

// ArrayReverse 反转s
func (s StringArray) ArrayReverse() StringArray {
	for i := 0; i < len(s)/2; i++ {
		s[i], s[len(s)-1-i] = s[len(s)-1-i], s[i]
	}
	return s
}

// ArraySlice 返回s的子切片
func (s StringArray) ArraySlice(start, end int) StringArray {
	if start < 0 {
		start = len(s) + start
	}
	if end < 0 {
		end = len(s) + end
	}
	if start > end {
		return nil
	}
	if end > len(s) {
		end = len(s)
	}
	return s[start:end]
}

// ArraySplice 删除s中的一段子切片，并用replacement替换
func (s StringArray) ArraySplice(start, length int, replacement StringArray) StringArray {
	if start < 0 {
		start = len(s) + start
	}
	if length < 0 {
		length = len(s) + length
	}
	if start > len(s) {
		start = len(s)
	}
	if length > len(s) {
		length = len(s)
	}
	return append(s[:start], append(replacement, s[start+length:]...)...)
}

// ArrayCountValues 返回s中每个元素出现的次数
func (s StringArray) ArrayCountValues() map[string]int {
	m := make(map[string]int)
	for _, v := range s {
		m[v]++
	}
	return m
}

func (s StringArray) DeepCopy() StringArray {
	return append(StringArray{}, s...)
}

func (s StringArray) Delete(index int) StringArray {
	return append(s[:index], s[index+1:]...)
}

func (s StringArray) Insert(index int, value string) StringArray {
	return append(s[:index], append(StringArray{value}, s[index:]...)...)
}

func (s StringArray) Pop() (StringArray, string) {
	return s[:len(s)-1], s[len(s)-1]
}

func (s StringArray) Push(value string) StringArray {
	return append(s, value)
}

func (s StringArray) Shift() (StringArray, string) {
	return s[1:], s[0]
}

func (s StringArray) Unshift(value string) StringArray {
	return append(StringArray{value}, s...)
}

func (s StringArray) Walk(f func(index int, value string) string) StringArray {
	for i, v := range s {
		s[i] = f(i, v)
	}
	return s
}

// DeleteValue 删除s中的value
func (s StringArray) DeleteValue(value string) StringArray {
	for i, v := range s {
		if v == value {
			return append(s[:i], s[i+1:]...)
		}
	}
	return s
}
