package array

import "reflect"

// InArray TODO
func InArray(obj any, target any) bool {
	// 如果target是nil，直接返回false
	if target == nil {
		return false
	}

	targetValue := reflect.ValueOf(target)
	// 查看target的类型
	switch reflect.TypeOf(target).Kind() {
	// 如果是切片或者数组类型
	case reflect.Slice, reflect.Array:
		for i := 0; i < targetValue.Len(); i++ {
			// 比较两个interface{}类型的值是否相等，需要interface的动态类型和动态值都相等
			if targetValue.Index(i).Interface() == obj {
				return true
			}
		}
	case reflect.Map:
		if targetValue.MapIndex(reflect.ValueOf(obj)).IsValid() {
			return true
		}
	default:
		// 如果不是这数组，切片，或者map的话，直接返回false
		return false
	}
	return false
}

// ArrayDiff 返回arr1中存在，而arr2中不存在的元素
func ArrayDiff(arr1, arr2 []any) []any {
	var diff []any
	m := make(map[any]bool)

	for _, v := range arr2 {
		m[v] = true
	}

	for _, v := range arr1 {
		if _, ok := m[v]; !ok {
			diff = append(diff, v)
		}
	}

	return diff
}
