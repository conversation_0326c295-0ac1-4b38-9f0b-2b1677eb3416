package regex_pattern

import (
	"regexp"
)

var (
	ipRegex = regexp.MustCompile(`^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)
)

func CheckIP(ip string) bool {
	return ipRegex.MatchString(ip)
}

// RemoveExtraSpaces removes extra spaces from a string
func RemoveExtraSpaces(text string) string {
	regex := regexp.MustCompile(`\s+`)
	return regex.ReplaceAllString(text, " ")
}
