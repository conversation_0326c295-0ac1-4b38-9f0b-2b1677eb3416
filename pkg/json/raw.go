package json

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type J<PERSON><PERSON> json.RawMessage

// UnmarshalJSON 实现json.Unmarshaler接口
func (j *JSON) UnmarshalJSON(data []byte) error {
	if j == nil {
		return fmt.<PERSON><PERSON><PERSON>("json: UnmarshalJSON on nil pointer")
	}
	*j = append((*j)[0:0], data...)
	return nil
}

// MarshalJSON 实现json.Marshaler接口
func (j JSON) MarshalJSON() ([]byte, error) {
	if j == nil {
		return []byte("null"), nil
	}
	return j, nil
}

// Scan scan value into Jsonb, implements sql.Scanner interfaces
func (j *JSON) Scan(value any) error {
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("failed to unmarshal JSONB value: %v", value)
	}

	result := json.RawMessage{}
	err := json.Unmarshal(bytes, &result)
	*j = JSO<PERSON>(result)
	return err
}

func (j *JSON) Value() (driver.Value, error) {
	if len(*j) == 0 {
		return nil, nil
	}
	return json.RawMessage(*j), nil
}

// ParseStringSliceToJson 将字符串切片转换为JSON
func ParseStringSliceToJson(s []string) (JSON, error) {
	b, err := json.Marshal(s)
	if err != nil {
		return JSON{}, err
	}
	j := new(JSON)
	err = j.Scan(b)
	if err != nil {
		return JSON{}, err
	}
	return *j, nil
}

// ParseInt64SliceToJson 将int64切片转换为JSON
func ParseInt64SliceToJson(i []int64) (JSON, error) {
	b, err := json.Marshal(i)
	if err != nil {
		return JSON{}, err
	}
	j := new(JSON)
	err = j.Scan(b)
	if err != nil {
		return JSON{}, err
	}
	return *j, nil
}
