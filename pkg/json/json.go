package json

import (
    "strings"

    jsoniter "github.com/json-iterator/go"
)

var (
    iteratorJson = jsoniter.Config{
        EscapeHTML:             true,
        SortMapKeys:            true,
        ValidateJsonRawMessage: true,
        UseNumber:              true,
    }.Froze()
)

// MarshalToString TODO
func MarshalToString(v any) (string, error) {
	return iteratorJson.MarshalToString(v)
}

// Marshal TODO
func Marshal(v any) ([]byte, error) {
    return iteratorJson.Marshal(v)
}

// MarshalIndent TODO
func MarshalIndent(v any, prefix, indent string) ([]byte, error) {
	return iteratorJson.MarshalIndent(v, prefix, indent)
}

// UnmarshalFromString TODO
func UnmarshalFromString(str string, v any) error {
	return iteratorJson.UnmarshalFromString(str, v)
}

// Unmarshal TODO
func Unmarshal(data []byte, v any) error {
	return iteratorJson.Unmarshal(data, v)
}

// UnmarshalArray TODO
func UnmarshalArray(items []string, result any) error {
	strArrJSON := "[" + strings.Join(items, ",") + "]"
	return iteratorJson.Unmarshal([]byte(strArrJSON), result)
}
