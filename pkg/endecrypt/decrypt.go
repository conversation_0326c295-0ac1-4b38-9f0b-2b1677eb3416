package endecrypt

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"fmt"
	"io/ioutil"

	"github.com/dgrijalva/jwt-go"

	"ks-knoc-server/config"
)

func AESDecrypt(encrypt, secret string) ([]byte, error) {
	token, err := base64.StdEncoding.DecodeString(encrypt)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher([]byte(secret))
	if err != nil {
		return nil, err
	}

	iv := []byte(secret)[:16]

	blockMode := cipher.NewCBCDecrypter(block, iv)
	result := make([]byte, len(token))
	blockMode.CryptBlocks(result, token)

	unPadding := int(result[len(result)-1])
	result = result[:(len(result) - unPadding)][16:]

	return result, nil
}

func RSADecrypt(sn string) ([]byte, error) {
	enc, err := hex.DecodeString(sn)
	if err != nil {
		return nil, err
	}
	//enc := []byte(sn)

	privateKey, err := ioutil.ReadFile(config.PrivateFile)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(privateKey)
	if block == nil {
		return nil, errors.New("private key error")
	}

	pri, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return rsa.DecryptPKCS1v15(rand.Reader, pri, enc)
}

// JWTPares 解析JWT
func JWTPares(tokenString string, key interface{}) (string, bool) {
	token, _ := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return key, nil
	})
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims["user"].(string), true
		//return claims, true
	} else {
		return "", false
	}
}
