package main

import (
	"fmt"
	"os"

	"github.com/olekukonko/tablewriter"
)

type data struct {
	name   string
	parent string
}

func (d *data) SetParent(name string) {
	if d.parent == "" {
		d.parent = name
	} else {
		fmt.Println("Parent already exist")
	}
}

func (d *data) UnsetParent() {
	d.parent = ""
}

type dataList []*data

func (d *dataList) Len() int {
	return len(*d)
}

func (d *dataList) Search(name string) *data {
	for _, item := range *d {
		if item.name == name {
			return item
		}
	}
	return nil
}

func (d *dataList) Add(v data) error {
	if (*d).Search(v.name) != nil {
		return fmt.Errorf("data %s already exist", v.name)
	}
	*d = append(*d, &v)
	return nil
}

func (d *dataList) Show() {
	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"名称", "父级"})
	for _, v := range *d {
		table.Append([]string{v.name, v.parent})
	}
	table.Render()
}

func newDList() *dataList {
	return &dataList{}
}

var dlist = newDList()

type CT struct {
	Ancestor   string `json:"ancestor" bson:"ancestor" mapstructure:"ancestor"`
	Descendant string `json:"descendant" bson:"descendant" mapstructure:"descendant"`
	Depth      int    `json:"depth" bson:"depth" mapstructure:"depth"`
}

func (c CT) IsNil() bool {
	return c.Ancestor == "" && c.Descendant == ""
}

type CTList []CT

func NewCTList() *CTList {
	c := make(CTList, 0)
	return &c
}

func (c *CTList) Descendants(ancestor string) []CT {
	ct := make([]CT, 0)
	for _, v := range *c {
		if v.Ancestor == ancestor {
			ct = append(ct, v)
		}
	}
	return ct
}

func (c *CTList) Ancestors(name string) []*data {
	dl := make([]*data, 0)
	for _, v := range *c {
		if v.Descendant == name {
			dl = append(dl, dlist.Search(v.Ancestor))
		}
	}
	return dl
}

func (c *CTList) PathExist(p CT) bool {
	for _, v := range *c {
		if v.Ancestor == p.Ancestor && v.Descendant == p.Descendant {
			return true
		}
	}
	return false
}

func (c *CTList) Len() int {
	return len(*c)
}

func (c *CTList) Add(ct CT) {
	*c = append(*c, ct)
}

func (c *CTList) Delete(ct CT) []CT {
	for i, v := range *c {
		if v.Ancestor == ct.Ancestor && v.Descendant == ct.Descendant {
			*c = append((*c)[:i], (*c)[i+1:]...)
			break
		}
	}
	return *c
}

func (c *CTList) SearchByDescendant(descendant []string) []CT {
	ctl := make([]CT, 0)
	for _, v := range *c {
		for _, d := range descendant {
			if v.Descendant == d {
				ctl = append(ctl, v)
			}
		}
	}
	return ctl
}

func (c *CTList) SearchCT(ancestor, descendant string) CT {
	for _, v := range *c {
		if v.Ancestor == ancestor && v.Descendant == descendant {
			return v
		}
	}
	return CT{}
}

func (c *CTList) GetMaxDepth(name string) int {
	depth := 0
	sons := c.Descendants(name)
	if len(sons) <= 0 {
		return 0
	}

	for _, v := range sons {
		if v.Depth > depth {
			depth = v.Depth
		}
	}

	return depth
}

func GetCT(ctl *CTList, d *data, c *CT, descendants CTList) *CTList {
	newCT := CT{
		Ancestor:   d.parent,
		Descendant: d.name,
	}

	if !ctl.PathExist(newCT) {
		if c != nil {
			newCT.Depth = c.Depth + 1
		} else {
			newCT.Depth += 1
		}
		ctl.Add(newCT)
	}

	if descendants.Len() > 0 {
		for _, v := range descendants {
			subCt := CT{
				Ancestor:   d.parent,
				Descendant: v.Descendant,
			}
			subCt.Depth = v.Depth + 1

			if !ctl.PathExist(subCt) {
				ctl.Add(subCt)
			}
		}
	}

	parent := dlist.Search(d.parent)
	if parent.parent == "" {
		return ctl
	}

	return GetCT(ctl, parent, &newCT, ctl.Descendants(d.parent))
}

func (c *CTList) Show() {
	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"祖先", "后代", "深度"})
	for _, v := range *c {
		table.Append([]string{v.Ancestor, v.Descendant, fmt.Sprintf("%d", v.Depth)})
	}
	table.Render()
	fmt.Println()
}

func main() {
	// 初始化数据
	var datas = []data{
		{name: "server", parent: ""},
		{name: "rack", parent: ""},
		{name: "idc", parent: ""},
		{name: "office", parent: ""},
		{name: "area", parent: ""},
		{name: "vm", parent: ""},
	}
	for _, v := range datas {
		var err error
		if err = dlist.Add(v); err != nil {
			panic(err)
		}
	}

	ctList := NewCTList()

	fmt.Println("== 关联物理机到机柜 ==")
	dlist.Search("server").SetParent("rack")
	dlist.Show()
	descendants := ctList.Descendants("server")
	pathList := GetCT(ctList, dlist.Search("server"), nil, descendants)
	pathList.Show()

	fmt.Println("== 关联机柜到机房 ==")
	dlist.Search("rack").SetParent("idc")
	dlist.Show()
	descendants = ctList.Descendants("rack")
	pathList = GetCT(ctList, &data{"rack", "idc"}, nil, descendants)
	pathList.Show()

	fmt.Println("== 关联职场到区域 ==")
	dlist.Search("office").SetParent("area")
	dlist.Show()
	descendants = ctList.Descendants("office")
	pathList = GetCT(ctList, &data{"office", "area"}, nil, descendants)
	pathList.Show()

	fmt.Println("== 关联机房到职场 ==")
	dlist.Search("idc").SetParent("office")
	dlist.Show()
	descendants = ctList.Descendants("idc")
	pathList = GetCT(ctList, &data{"idc", "office"}, nil, descendants)
	pathList.Show()

	fmt.Println("== 关联虚拟机到物理机 ==")
	dlist.Search("vm").SetParent("server")
	dlist.Show()
	descendants = ctList.Descendants("vm")
	pathList = GetCT(ctList, &data{"vm", "server"}, nil, descendants)
	pathList.Show()

	// 模拟删除的操作
	fmt.Println("== 删除机房到职场的关联 ==")
	dlist.Search("idc").UnsetParent()
	dlist.Show()
	sons := make([]string, 0)
	for _, des := range ctList.Descendants("idc") {
		sons = append(sons, des.Descendant)
	}
	sons = append(sons, "idc")

	mySons := ctList.SearchByDescendant(sons)
	myAncestors := ctList.Ancestors("idc")

	for _, v := range mySons {
		for _, a := range myAncestors {
			ctList.Delete(ctList.SearchCT(a.name, v.Descendant))
		}
	}

	ctList.Show()
}
