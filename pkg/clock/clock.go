package clock

import (
	"time"
)

// GetFormattedTime 获取格式化的时间
func GetFormattedTime(timestamp int64) string {
	// 将Unix时间戳转换为时间对象
	t := time.Unix(timestamp, 0)

	// 格式化时间为带毫秒的字符串
	return t.Format("2006-01-02 15:04:05.000")
}

func GetFormattedDate(timestamp int64) string {
	// 将Unix时间戳转换为时间对象
	t := time.Unix(timestamp, 0)

	// 格式化时间为带毫秒的字符串
	return t.Format("2006-01-02")
}

func GetFormattedTimeInSeconds(timestamp int64) string {
	// 将Unix时间戳转换为时间对象
	t := time.Unix(timestamp, 0)

	// 格式化时间为带毫秒的字符串
	return t.Format("2006-01-02 15:04:05")
}

// Now 获取当前时间的秒时间戳
func Now() int64 {
	return time.Now().Unix()
}

// NowInMilli 获取当前时间的毫秒时间戳
func NowInMilli() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// NowInNano 获取当前时间的纳秒时间戳
func NowInNano() int64 {
	return time.Now().UnixNano()
}
