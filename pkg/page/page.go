package page

import (
	"strconv"

	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
)

// CheckPagination 检查分页参数
func CheckPagination(c *gin.Context) (pageNumber, pageSizeNumber int64, err error) {
	// 首先从用户提交的，用户有可能传递了分页参数，当然也有可能没传递
	page := c.Query("page")
	pageSize := c.Query("pageSize")
	// 初始化默认的分页相关的参数，用户不传递的话，默认就是第一页，一页10个
	pageNumber = 1
	pageSizeNumber = 10

	// 判断用户是否传入分页相关的参数，如果说用户传入的对应的页码和分页大小，那么就以用户的为准
	if page != "" {
		pageNumber, err = strconv.ParseInt(page, 10, 64)
		if err != nil {
			core.SendResponse(c, err, nil)
			return
		}
	}
	if pageSize != "" {
		pageSizeNumber, err = strconv.ParseInt(pageSize, 10, 64)
		if err != nil {
			core.SendResponse(c, err, nil)
			return
		}
	}
	if pageNumber < 1 || pageSizeNumber < 1 {
		return 0, 0, errno.ErrInvalidPagination.Add("页码和分页大小必须为≥1的整数")
	}
	return
}
