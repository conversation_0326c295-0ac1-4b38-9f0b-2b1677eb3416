package mapdata

func (m MapData) Keys() []string {
	keys := make([]string, 0)
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}

// Intersection 取交集
func (m MapData) Intersection(target MapData) []string {
	intersection := make([]string, 0)

	if len(m) == 0 || len(target) == 0 {
		return intersection
	}

	for aKey := range target {
		if _, exist := m[aKey]; exist {
			intersection = append(intersection, aKey)
		}
	}
	return intersection
}

// Union 取并集
func (m MapData) Union(target MapData) []string {
	union := make([]string, 0)

	for aKey := range m {
		union = append(union, aKey)
	}

	for bKey := range target {
		if _, exist := m[bKey]; !exist {
			union = append(union, bKey)
		}
	}

	return union
}

// Difference 取差集，在自己这里存在，在别人那里不存在的，即a - b
func (m MapData) Difference(target MapData) []string {
	difference := make([]string, 0)

	for aKey := range m {
		if _, exist := target[aKey]; !exist {
			difference = append(difference, aKey)
		}
	}

	return difference
}
