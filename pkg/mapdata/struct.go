package mapdata

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"go.uber.org/zap"
)

func setMapToReflectValue(structField reflect.StructField, returnVal, inputVal reflect.Value) (retVal reflect.Value, err error) {
	if !returnVal.CanSet() {
		return returnVal, fmt.Errorf("can not set to value %v", returnVal)
	}
	retVal = *(&returnVal)
	t := retVal.Type()
	if retVal.IsNil() {
		retVal.Set(reflect.MakeMap(t))
	}

	defer func() {
		if r := recover(); r != nil {
			fmt.Println("not support data type. field name: ", structField.Name, ", err:", r)
			switch x := r.(type) {
			case string:
				err = errors.New(x)
			case error:
				err = x
			default:
				err = fmt.Errorf("%#v", r)
			}
		}
	}()

	mapKeys := inputVal.MapKeys()
	for _, key := range mapKeys {
		if !inputVal.MapIndex(key).CanInterface() {
			return retVal, fmt.Errorf("not support data type. field name: %v", structField.Name)
		}
		value := inputVal.MapIndex(key).Interface()
		switch rawVal := value.(type) {
		case float64:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		case float32:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		case int64:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		case int32:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		case int:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		case string:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		case []any:
			retVal.SetMapIndex(key, reflect.ValueOf(rawVal))
		default:
			return retVal, fmt.Errorf("not support data type. field name: %v, type: %#v", structField.Name, value)
		}
	}

	return returnVal, err
}

// setStructByMapData set struct by map data
func setStructByMapData(targetType reflect.Type, targetValue reflect.Value, values MapData, tagName string) error {

	targetType = getTypeElem(targetType)
	targetValue = getValueElem(targetValue)

	numField := targetType.NumField()
	for i := 0; i < numField; i++ {
		structField := targetType.Field(i)
		tag, ok := structField.Tag.Lookup(tagName)
		if !ok {
			continue
		}

		if len(tag) == 0 || strings.Contains(tag, "ignoretostruct") {
			continue
		}

		tags := strings.Split(tag, ",")

		tagVal, ok := values[tags[0]]
		if !ok {
			continue
		}

		if nil == tagVal {
			continue
		}

		fieldValue := targetValue.FieldByName(structField.Name)
		if !fieldValue.CanSet() {
			return fmt.Errorf("%s can't be set", structField.Name)
		}

		switch structField.Type.Kind() {
		default:
			return fmt.Errorf("unsupport the type %s %v", structField.Name, structField.Type.Kind())

		case reflect.Map:
			if _, err := setMapToReflectValue(structField, fieldValue, reflect.ValueOf(tagVal)); nil != err {
				return err
			}

		case reflect.Interface:
			tmpVal := reflect.ValueOf(tagVal)
			switch tmpVal.Kind() {
			case reflect.Ptr:
				fieldValue.Set(tmpVal.Elem())
			default:
				fieldValue.Set(tmpVal)
			}

		case reflect.Struct:
			valMapStr, err := NewFromInterface(tagVal)
			if nil != err {
				return err
			}
			targetResult := reflect.New(structField.Type)
			if err := setStructByMapData(structField.Type, targetResult, valMapStr, tagName); nil != err {
				return err
			}
			fieldValue.Set(targetResult.Elem())

		case reflect.Ptr:

			targetResult := reflect.New(structField.Type.Elem())
			switch t := tagVal.(type) {
			default:
				valMapStr, err := NewFromInterface(tagVal)
				if nil != err {
					return err
				}
				if err := setStructByMapData(structField.Type, targetResult, valMapStr, tagName); nil != err {
					return err
				}
				fieldValue.Set(targetResult)
			case bool:
				if structField.Type.Elem().Kind() == reflect.Bool {
					targetResult = getValueElem(targetResult)
					targetResult.SetBool(t)
					fieldValue.Set(targetResult.Addr())
				}
			case string:
				targetResult = getValueElem(targetResult)
				targetResult.SetString(t)
				fieldValue.Set(targetResult.Addr())
			}

		case reflect.Bool:
			fieldValue.SetBool(toBool(tagVal))
		case reflect.Int, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Int8:
			fieldValue.SetInt(int64(toInt(tagVal)))
		case reflect.Uint, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint8:
			fieldValue.SetUint(uint64(toUint(tagVal)))
		case reflect.Float32, reflect.Float64:
			fieldValue.SetFloat(toFloat(tagVal))
		case reflect.String:
			switch t := tagVal.(type) {
			case string:
				fieldValue.SetString(t)
			}

		}

	}

	return nil
}

func isEmptyValue(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Array, reflect.Map, reflect.Slice, reflect.String:
		return v.Len() == 0
	case reflect.Bool:
		return !v.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return v.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0
	case reflect.Interface, reflect.Ptr:
		return v.IsNil()
	}
	return false
}

func setMapStrByStruct(targetType reflect.Type, targetValue reflect.Value, values MapData, tagName string) error {

	switch targetType.Kind() {
	case reflect.Ptr:
		targetType = targetType.Elem()
		targetValue = targetValue.Elem()

		if targetType.Kind() == reflect.Ptr {
			return setMapStrByStruct(targetType, targetValue, values, tagName)
		}

	}

	numField := targetType.NumField()
	for i := 0; i < numField; i++ {
		structField := targetType.Field(i)
		tag, ok := structField.Tag.Lookup(tagName)
		if !ok && !structField.Anonymous {
			continue
		}

		if (len(tag) == 0 || strings.Contains(tag, "ignoretomap")) && !structField.Anonymous {
			continue
		}
		tags := strings.Split(tag, ",")
		if len(tag) == 0 {
			tags = []string{structField.Name}
		}

		fieldValue := targetValue.FieldByName(structField.Name)
		if fieldValue.IsValid() && !fieldValue.CanInterface() {
			continue
		}

		if isEmptyValue(fieldValue) && strings.Contains(tag, "omitempty") {
			continue
		}

		switch structField.Type.Kind() {
		case reflect.String,
			reflect.Float32, reflect.Float64,
			reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
			reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64,
			reflect.Complex64, reflect.Complex128,
			reflect.Array,
			reflect.Interface,
			reflect.Map,
			reflect.Slice,
			reflect.Bool:
			values.Set(tags[0], fieldValue.Interface())
		case reflect.Struct:
			innerMapStr := SetValueToMapStrByTagsWithTagName(fieldValue.Interface(), tagName)
			values.Set(tags[0], innerMapStr)

		case reflect.Ptr:

			innerValue := dealPointer(fieldValue, tags[0], tagName)
			values.Set(tags[0], innerValue)
		default:
			zap.L().Info(fmt.Sprintf("[mapstr] invalid kind: %v for field %v", structField.Type.Kind(), tags[0]))
		}

	}
	return nil
}
