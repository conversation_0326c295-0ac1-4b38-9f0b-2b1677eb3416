package mapdata

import (
	"encoding/json"
	"fmt"
	"reflect"
)

// New 创建一个新的MapData实例
func New() MapData {
	return MapData{}
}

// SetValueToStructByTagsWithTagName 通过tagName设置结构体字段的值
// target: 目标结构体
// values: MapData类型的数据
// tagName: 结构体字段的tag名称
func SetValueToStructByTagsWithTagName(target interface{}, values MapData, tagName string) error {
	targetType := reflect.TypeOf(target)
	targetValue := reflect.ValueOf(target)

	return setStructByMapData(targetType, targetValue, values, tagName)
}

// NewFromInterface create a mapstr instance from the interface
// Support Input Type: []byte, string, base-type map, struct.
// If the input value type is []byte or string, then the value must be a valid json.
// Like: map[string]int will be converted into MapStr
// Like: struct { TestStr string TestInt int } will be converted into  MapStr{"TestStr":"", "TestInt":0}
func NewFromInterface(data interface{}) (MapData, error) {

	switch tmp := data.(type) {
	default:
		return convertInterfaceIntoMapStrByReflection(data, "")
	case nil:
		return MapData{}, nil
	case MapData:
		return tmp, nil
	case []byte:
		result := New()
		if len(tmp) == 0 {
			return result, nil
		}
		err := json.Unmarshal(tmp, &result)
		return result, err
	case string:
		result := New()
		if len(tmp) == 0  {
			return result, nil
		}
		err := json.Unmarshal([]byte(tmp), &result)
		return result, err
	case *map[string]any:
		return MapData(*tmp), nil
	case map[string]string:
		result := New()
		for key, val := range tmp {
			result.Set(key, val)
		}
		return result, nil
	case map[string]interface{}:
		return MapData(tmp), nil
	}
}

func convertInterfaceIntoMapStrByReflection(target any, tagName string) (MapData, error) {

	value := reflect.ValueOf(target)
	switch value.Kind() {
	case reflect.Map:
		return dealMap(value, tagName)
	case reflect.Struct:
		return dealStruct(value.Type(), value, tagName)
	}

	return nil, fmt.Errorf("no support the kind(%s)", value.Kind())
}

// SetValueToMapStrByTagsWithTagName convert a struct to MapStr by tags
func SetValueToMapStrByTagsWithTagName(source interface{}, tagName string) MapData {
	values := MapData{}

	if nil == source {
		return values
	}

	targetType := getTypeElem(reflect.TypeOf(source))
	targetValue := getValueElem(reflect.ValueOf(source))

	setMapStrByStruct(targetType, targetValue, values, tagName)

	return values
}
