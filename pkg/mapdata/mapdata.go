package mapdata

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/mohae/deepcopy"
)

// MapData 通用map类型数据的定义
type MapData map[string]any

func NewMapData() MapData {
	return make(MapData)
}

// Clone 通过深拷贝创建一个新的MapData类型的数据
func (m MapData) Clone() MapData {
	cpyInst := deepcopy.Copy(m)
	return cpyInst.(MapData)
}

// IsNil 判断value是否为nil，包括map[string]interface{}{nil}, *Struct{nil}
func IsNil(value any) bool {
	rflValue := reflect.ValueOf(value)
	if rflValue.IsValid() {
		return rflValue.IsNil()
	}
	return true
}

// Merge 将second合并到自身的map中，如果key相同则新值替换旧值。
func (m MapData) Merge(second MapData) {
	for key, val := range second {
		// 如果key中包含.，则将.前面的作为key，如果该key对应的值为nil，则删除该key
		if strings.Contains(key, ".") {
			// 获取.前面的key，假设说key为a.b.c，那么root就是a
			root := key[:strings.Index(key, ".")]
			// 如果root在自身的map中存在，并且root对应的值为nil，则删除该key
			if _, ok := m[root]; ok && IsNil(m[root]) {
				delete(m, root)
			}
		}
		// 将second中的key和val合并到自身的map中
		m[key] = val
	}
}

// ToJSON 转换MapData数据为json byte数组
func (m MapData) ToJSON() ([]byte, error) {
	js, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return js, nil
}

// MarshalJSONInto convert to the input value
func (m MapData) MarshalJSONInto(target interface{}) error {
	// 将MapData转换为json byte数组
	data, err := m.ToJSON()
	if nil != err {
		return fmt.Errorf("marshal %#v failed: %v", target, err)
	}
	// 将json byte数组转换为target
	d := json.NewDecoder(bytes.NewReader(data))
	// 将数字转换为json.Number类型
	d.UseNumber()
	err = d.Decode(target)
	if err != nil {
		return fmt.Errorf("unmarshal %s failed: %v", data, err)
	}
	return nil
}

// Get 根据key返回值
func (m MapData) Get(key string) (val interface{}, exists bool) {
	val, exists = m[key]
	return val, exists
}

// Delete 删除对应的key
func (m MapData) Delete(key string) {
	delete(m, key)
}

// Set 为对应的key设置一个新的值
func (m MapData) Set(key string, value interface{}) {
	m[key] = value
}

// Reset 重置MapData，将所有的key删除
func (m MapData) Reset() {
	for key := range m {
		delete(m, key)
	}
}

// MapData 查看对应的key是否为MapData类型，如果是则返回MapData类型的值（map本身可以多层嵌套）
func (m MapData) MapData(key string) (MapData, error) {
	// 判断key对应的值是否为MapData类型
	switch t := m[key].(type) {
	default:
		return nil, fmt.Errorf("the value of the key(%s) is not a map[string]interface{} type", key)
	case nil:
		// 如果key对应的值为nil，且存在这个key值的话，则返回一个空的MapData
		if _, ok := m[key]; ok {
			return MapData{}, nil
		}
		return nil, errors.New("the key is invalid")
	case MapData:
		// 如果key对应的值为MapData类型，则直接返回
		return t, nil
	case map[string]interface{}:
		// 如果key对应的值为map[string]interface{}类型，则将其转换为MapData类型
		return t, nil
	}
}

// ToMapInterface 将MapData转换为map[string]interface{}
func (m MapData) ToMapInterface() map[string]interface{} {
	return m
}

// ForEach 遍历MapData，然后调用callItem方法去处理每一个key和val，如果callItem返回error，则停止遍历
func (m MapData) ForEach(callItem func(key string, val interface{}) error) error {
	// 遍历MapData中的每一个key和val
	for key, val := range m {
		if err := callItem(key, val); err != nil {
			return err
		}
	}
	return nil
}

// Remove 通过key删除对应MapData中的item，并且返回删除的这个key的值
func (m MapData) Remove(key string) interface{} {
	if val, ok := m[key]; ok {
		delete(m, key)
		return val
	}
	return nil
}

// Exists 检测key是否存在
func (m MapData) Exists(key string) bool {
	_, ok := m[key]
	return ok
}

// IsEmpty 检测MapData是否为空，判断方式和map的判断方式一样，判断长度就可以了。
func (m MapData) IsEmpty() bool {
	return len(m) == 0
}

// Bool 根据key值返回bool类型的值
func (m MapData) Bool(key string) (bool, error) {
	switch t := m[key].(type) {
	case nil:
		return false, fmt.Errorf("the key (%s) is invalid", key)
	default:
		return false, fmt.Errorf("the key (%s) is invalid", key)
	case bool:
		return t, nil
	}
}

// Int64 根据key值返回int64类型的值
func (m MapData) Int64(key string) (int64, error) {
	switch t := m[key].(type) {
	default:
		return 0, errors.New("invalid num")
	case nil:
		return 0, errors.New("invalid key(" + key + "), not found value")
	case int:
		return int64(t), nil
	case int16:
		return int64(t), nil
	case int32:
		return int64(t), nil
	case int64:
		return t, nil
	case float32:
		return int64(t), nil
	case float64:
		return int64(t), nil
	case uint:
		return int64(t), nil
	case uint16:
		return int64(t), nil
	case uint32:
		return int64(t), nil
	case uint64:
		return int64(t), nil
	case json.Number:
		num, err := t.Int64()
		return int64(num), err
	case string:
		return strconv.ParseInt(t, 10, 64)
	}
}

// Float 根据key值返回float64类型的值
func (m MapData) Float(key string) (float64, error) {
	switch t := m[key].(type) {
	default:
		return 0, errors.New("invalid num")
	case nil:
		return 0, errors.New("invalid key, not found value")
	case int:
		return float64(t), nil
	case int16:
		return float64(t), nil
	case int32:
		return float64(t), nil
	case int64:
		return float64(t), nil
	case float32:
		return float64(t), nil
	case float64:
		return t, nil
	case json.Number:
		return t.Float64()
	}
}

// String 根据key值返回string类型的值
func (m MapData) String(key string) (string, error) {
	switch t := m[key].(type) {
	case nil:
		return "", nil
	case float32:
		return strconv.FormatFloat(float64(t), 'f', -1, 32), nil
	case float64:
		return strconv.FormatFloat(float64(t), 'f', -1, 64), nil
	case map[string]interface{}, []interface{}:
		rest, err := json.Marshal(t)
		if nil != err {
			return "", err
		}
		return string(rest), nil
	case json.Number:
		return t.String(), nil
	case string:
		return t, nil
	default:
		return fmt.Sprintf("%v", t), nil
	}
}

// Time 根据key值返回time.Time类型的值
func (m MapData) Time(key string) (*time.Time, error) {
	switch t := m[key].(type) {
	default:
		return nil, errors.New("invalid time value")
	case nil:
		return nil, errors.New("invalid key")
	case time.Time:
		return &t, nil
	case *time.Time:
		return t, nil
	case string:
		if tm, tmErr := time.Parse(time.RFC1123, t); nil == tmErr {
			return &tm, nil
		}

		if tm, tmErr := time.Parse(time.RFC1123Z, t); nil == tmErr {
			return &tm, nil
		}

		if tm, tmErr := time.Parse(time.RFC3339, t); nil == tmErr {
			return &tm, nil
		}

		if tm, tmErr := time.Parse(time.RFC3339Nano, t); nil == tmErr {
			return &tm, nil
		}

		if tm, tmErr := time.Parse(time.RFC822, t); nil == tmErr {
			return &tm, nil
		}

		if tm, tmErr := time.Parse(time.RFC822Z, t); nil == tmErr {
			return &tm, nil
		}

		if tm, tmErr := time.Parse(time.RFC850, t); nil == tmErr {
			return &tm, nil
		}

		return nil, errors.New("can not parse the datetime")
	}
}

// MapStrArray 获取MapData类型的切片
func (m MapData) MapStrArray(key string) ([]MapData, error) {
	// 根据用户传入的key进行类型的判断
	switch t := m[key].(type) {
	// 默认的情况
	default:
		val := reflect.ValueOf(m[key])
		switch val.Kind() {
		default:
			return nil, fmt.Errorf("the value of the key(%s) is not a valid type,%s", key, val.Kind().String())
		// 如果用户传入的key对应的值是一个切片，则进行类型转换，成功了就返回[]MapData，失败了返回nil和错误信息
		case reflect.Slice:
			tmpVal, ok := val.Interface().([]MapData)
			if ok {
				return tmpVal, nil
			}
			return nil, fmt.Errorf("the value of the key(%s) is not a valid type,%s", key, val.Kind().String())
		}
	// 如果用户传入的key不存在，则返回错误
	case nil:
		return nil, fmt.Errorf("the key(%s) is invalid", key)
	// 如果用户传入的key对应的值是一个MapData类型的切片，则直接返回
	case []MapData:
		return t, nil
	// 如果用户传入的key对应的值是一个map[string]interface{}类型的切片，则进行类型转换
	case []map[string]interface{}:
		items := make([]MapData, 0)
		for _, item := range t {
			items = append(items, item)
		}
		return items, nil
	// 如果用户传入的key对应的值是一个[]interface{}类型的切片，则进行类型转换
	// 如果其中有一个元素不是map[string]interface{}或者MapData类型，则返回错误
	case []interface{}:
		items := make([]MapData, 0)
		for _, item := range t {
			switch childType := item.(type) {
			case map[string]interface{}:
				items = append(items, childType)
			case MapData:
				items = append(items, childType)
			case nil:
				continue
			default:
				return nil, fmt.Errorf("the value of the key(%s) is not a valid type, type: %v,value:%+v", key, childType, t)
			}
		}
		return items, nil
	}

}

// ToStructByTag 基于tag名称将与MapData中的key值相同的字段设置为MapData中的值
//
//	举例说明：
//	self := MapData{"testName":"value"}
//	targetStruct := struct{
//	    Name string `field:"testName"`
//	}
//	当调用self.ToStructByTag(targetStruct, "field")方法后
//	targetStruct.Name的值将会被设置为value
func (m MapData) ToStructByTag(targetStruct interface{}, tagName string) error {
	return SetValueToStructByTagsWithTagName(targetStruct, m, tagName)
}

// Different 当前MapData与target MapData的区别
func (m MapData) Different(target MapData) (more MapData, less MapData, changes MapData, err error) {

	// init
	more = make(MapData)
	less = make(MapData)
	changes = make(MapData)

	// check more
	// 当前 MapData 中存在 target MapData 中不存在的 key
	if err = m.ForEach(func(key string, val any) error {
		if targetVal, ok := target[key]; ok {
			if !reflect.DeepEqual(val, targetVal) {
				changes[key] = val
			}
			return nil
		}

		more.Set(key, val)
		return nil
	}); err != nil {
		return more, less, changes, err
	}

	// check less
	if err = target.ForEach(func(key string, val any) error {
		if !m.Exists(key) {
			less[key] = val
		}
		return nil
	}); err != nil {
		return more, less, changes, err
	}

	return more, less, changes, err
}
