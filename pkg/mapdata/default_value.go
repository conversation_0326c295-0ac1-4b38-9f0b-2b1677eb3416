package mapdata

import (
	"fmt"
	"reflect"
)

// getZeroValue 根据valueType获取对应的零值
func getZeroValue(valueType reflect.Type) interface{} {
	switch valueType.Kind() {
	case reflect.Ptr:
		// 如果是指针类型，那么递归获取其零值
		fmt.Println(1)
		return getZeroValue(valueType.Elem())
	case reflect.String:
		return ""
	case reflect.Int, reflect.Int16, reflect.Int8, reflect.Int32, reflect.Int64, reflect.Uint, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uint8:
		return 0
	}
	fmt.Println(2)
	return nil
}
