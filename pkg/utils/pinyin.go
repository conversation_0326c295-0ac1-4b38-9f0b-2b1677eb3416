package utils

import (
	"github.com/mozillazg/go-pinyin"
)

// ToPinyin 将content内容转换为pinyin
func ToPinyin(content string) string {
	pyStr := ""
	pyObj := pinyin.NewArgs()
	// 默认会丢弃非中文的字符，因此这里可以设置一个FallBack函数，将非中文的字符转换为字符串并返回，而不是忽略
	pyObj.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)}
	}
	for _, p := range pinyin.Pinyin(content, pyObj) {
		pyStr += p[0]
	}
	return pyStr
}
