package utils

import (
	"errors"
	"fmt"
	"os"
	"reflect"
	"time"

	"ks-knoc-server/internal/common/errno"

	uuid "github.com/satori/go.uuid"
)

// DirExist 判断目录是否存在
func DirExist(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// CreateDir 创建目录
func CreateDir(path string) error {
	dirExist, err := DirExist(path)
	if err != nil {
		return err
	}

	if !dirExist {
		err := os.Mkdir(path, os.ModePerm)
		if err != nil {
			return errors.New("Create[" + path + "]directory fail:" + err.Error())
		}
		return nil
	}
	return err
}

// StringNowTime ...
func StringNowTime() string {
	now := time.Now()
	nowTime := fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second())
	return nowTime
}

// UUID 生成UUID
func UUID() string {
	uid := uuid.Must(uuid.NewV4(), nil)
	return uid.String()
}

// FindStrInStringSlice 查看某个字符串是否在一个字符串切片里
func FindStrInStringSlice(slice []string, str string) bool {
	m := make(map[string]bool)
	for _, v := range slice {
		m[v] = true
	}
	return m[str]
}

// IsArraySubSet 判断s数组是否是p数组的子集，包含p与s相等的情况
func IsArraySubSet(p, s []string) bool {
	for _, v := range s {
		if !FindStrInStringSlice(p, v) {
			return false
		}
	}
	return true
}

// CompareMaps 比较两个map，返回map1和map2比较，map1中不同的字段
func CompareMaps(map1, map2 map[string]interface{}) map[string]interface{} {
	differentFields := make(map[string]interface{})
	// 遍历map1，找出map1中有而map2中没有的字段
	for key, value1 := range map1 {
		if value2, ok := map2[key]; ok {
			// 如果两个字段相等，则继续比较下一个字段
			if reflect.DeepEqual(value1, value2) {
				continue
			} else {
				// 如果两个字段不相等，则判断两个字段的类型是否相同
				if reflect.TypeOf(value1) == reflect.TypeOf(value2) {
					// 有可能是map嵌套map的情况，所以需要递归比较
					if reflect.TypeOf(value1).Kind() == reflect.Map {
						// 如果是map，则递归比较
						if nestedFields := CompareMaps(value1.(map[string]interface{}), value2.(map[string]interface{})); len(nestedFields) > 0 {
							differentFields[key] = nestedFields
						}
					} else {
						// 如果不是map，那就是单纯的类型不一样，记录一下map1中的值
						differentFields[key] = value1
					}
				} else {
					// 如果说类型都不同，则记录一下map1中的值
					differentFields[key] = value1
				}
			}
		} else {
			// map1中有，但是map2中没有的字段
			differentFields[key] = value1
		}
	}
	// 再遍历map2，找出map2中有而map1中没有的字段
	// for key, value2 := range map2 {
	//    if _, ok := map1[key]; !ok {
	//        differentFields[key] = value2
	//    }
	// }

	return differentFields
}

// UpdateMap 更新map，将m1中的内容更新到m2中去。
func UpdateMap(m1, m2 map[string]interface{}) error {
	for key, value := range m1 {
		if _, ok := m2[key]; ok {
			// 如果key在m2中存在，看一下这个key对应的值是不是一个map[string]interface{}类型的值
			if nestedMap, ok := value.(map[string]interface{}); ok {
				// 如果值是一个嵌套的map，则递归更新嵌套的map
				if nestedMap2, ok := m2[key].(map[string]interface{}); ok {
					err := UpdateMap(nestedMap, nestedMap2)
					if err != nil {
						return err
					}
				}
			} else {
				// 如果不是，则直接更新值
				m2[key] = value
			}
		} else {
			// 如果键在m2中不存在，则有可能是用户故意传了一个根本不存在的值
			return errno.ErrParameterInvalid.Add("字段 [" + key + "] 并不存在于属性字段中，请检查提交的内容是否正确")
		}
	}
	return nil
}

func MapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for key := range m {
		keys = append(keys, key)
	}
	return keys
}
