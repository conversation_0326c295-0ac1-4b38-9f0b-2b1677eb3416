package utils

import (
	"net"
	"regexp"
)

var (
	dnsRegex = regexp.MustCompile(`^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])\.?$`)
)

// IsValidNetwork 判断是否是一个合法的网络号
// 比如***********/24，10.0.0.0/8，**********/12都是合法的网络号
func IsValidNetwork(network string) bool {
	if _, _, err := net.ParseCIDR(network); err != nil {
		return false
	}

	return true
}

// IsValidIPv4 判断是否是一个合法的IPv4地址
func IsValidIPv4(ip string) bool {
	return net.ParseIP(ip) != nil && net.ParseIP(ip).To4() != nil
}

// IsValidIPv6 判断是否是一个合法的IPv6地址
func IsValidIPv6(ip string) bool {
	return net.ParseIP(ip) != nil && net.ParseIP(ip).To16() != nil
}

// IsValidCNAME 判断是否是一个合法的CNAME
func IsValidCNAME(cname string) bool {
	return dnsRegex.MatchString(cname)
}
