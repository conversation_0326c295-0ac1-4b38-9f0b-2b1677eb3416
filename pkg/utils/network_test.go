package utils

import "testing"

func TestIsValidCNAME(t *testing.T) {
	tests := []struct {
		recordValue string
		wantErr     bool
	}{
		{
			recordValue: "www.baidu.com",
			wantErr:     true,
		},
	}

	for _, test := range tests {
		isValid := IsValidCNAME(test.recordValue)
		if isValid != test.wantErr {
			t.Errorf("IsValidCNAME(%s) = %v, wantErr %v", test.recordValue, isValid, test.wantErr)
		}
	}
}
