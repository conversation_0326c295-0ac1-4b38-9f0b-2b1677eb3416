package utils

import (
	"fmt"
	"strings"
	"time"

	"github.com/miekg/dns"
)

// Dig 简单的DNS查询，类似 dig +short
func Dig(domain, recordType string) ([]string, error) {
	return DigWithServer(domain, recordType, "8.8.8.8:53")
}

// DigWithServer 使用指定DNS服务器查询
func DigWithServer(domain, recordType, server string) ([]string, error) {
	// 确保域名是FQDN格式
	if !dns.IsFqdn(domain) {
		domain = dns.Fqdn(domain)
	}

	// 确保服务器地址包含端口
	if !strings.Contains(server, ":") {
		server = server + ":53"
	}

	// 获取记录类型
	var qtype uint16
	switch strings.ToUpper(recordType) {
	case "A":
		qtype = dns.TypeA
	case "AAAA":
		qtype = dns.TypeAAAA
	case "CNAME":
		qtype = dns.TypeCNAME
	case "MX":
		qtype = dns.TypeMX
	case "TXT":
		qtype = dns.TypeTXT
	case "NS":
		qtype = dns.TypeNS
	default:
		return nil, fmt.Errorf("不支持的记录类型: %s", recordType)
	}

	// 创建查询
	msg := new(dns.Msg)
	msg.SetQuestion(domain, qtype)

	// 发送查询
	client := &dns.Client{Timeout: 5 * time.Second}
	response, _, err := client.Exchange(msg, server)
	if err != nil {
		return nil, err
	}

	if response.Rcode != dns.RcodeSuccess {
		return nil, fmt.Errorf("查询失败: %s", dns.RcodeToString[response.Rcode])
	}

	// 解析结果
	var results []string
	for _, rr := range response.Answer {
		switch qtype {
		case dns.TypeA:
			if a, ok := rr.(*dns.A); ok {
				results = append(results, a.A.String())
			}
		case dns.TypeAAAA:
			if aaaa, ok := rr.(*dns.AAAA); ok {
				results = append(results, aaaa.AAAA.String())
			}
		case dns.TypeCNAME:
			if cname, ok := rr.(*dns.CNAME); ok {
				results = append(results, strings.TrimSuffix(cname.Target, "."))
			}
		case dns.TypeMX:
			if mx, ok := rr.(*dns.MX); ok {
				results = append(results, fmt.Sprintf("%d %s", mx.Preference, strings.TrimSuffix(mx.Mx, ".")))
			}
		case dns.TypeTXT:
			if txt, ok := rr.(*dns.TXT); ok {
				results = append(results, strings.Join(txt.Txt, ""))
			}
		case dns.TypeNS:
			if ns, ok := rr.(*dns.NS); ok {
				results = append(results, strings.TrimSuffix(ns.Ns, "."))
			}
		}
	}

	return results, nil
}
