package knoc

import (
	"bytes"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kconf"
	"github.com/spf13/viper"
)

const (
	kconfKeyName = "it.knoc.knocProcess"
)

func InitConfig() error {
	strConfig, err := kconf.GetStringConfig(kconfKeyName)
	if err != nil {
		return err
	}

	// 设置配置文件的格式为yaml
	viper.SetConfigType("yaml")
	if err := viper.ReadConfig(bytes.NewBuffer([]byte(strConfig))); err != nil {
		return err
	}

	return nil
}
