/*
 Navicat Premium Data Transfer

 Source Server         : 元中心测试mysql
 Source Server Type    : MySQL
 Source Server Version : 50720 (5.7.20-log)
 Source Host           : ************:30306
 Source Schema         : cmdb

 Target Server Type    : MySQL
 Target Server Version : 50720 (5.7.20-log)
 File Encoding         : 65001

 Date: 20/09/2023 14:00:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for model_icons
-- ----------------------------
DROP TABLE IF EXISTS `model_icons`;
CREATE TABLE `model_icons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '图标的名称',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '图标的链接',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of model_icons
-- ----------------------------
BEGIN;
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (1, '虚拟机', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/virtual_server.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (2, '机房', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/room.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (3, '物理机', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/server.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (4, '区域', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/region.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (5, '机柜', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/rack.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (6, '容器集群', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/k8s.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (7, '应用', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/app.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (8, '业务', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/business.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (9, '服务', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/service.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (10, '数据库', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/mysql.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (11, '用户', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/user.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (12, '部门', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/department.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (13, '组织', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/group.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (14, '云服务器', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/cloud_server.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (15, '云厂商', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/cloud_factory.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (16, '云区域', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/cloud_region.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (17, '可用区', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/available_region.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (18, '其他1', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/other1.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (19, '其他2', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/other2.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (20, '其他3', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/other3.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (21, '其他4', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/other4.png');
INSERT INTO `model_icons` (`id`, `name`, `url`) VALUES (22, '其他5', 'https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/other5.png');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
