/*
 Navicat Premium Data Transfer

 Source Server         : 元中心测试mysql
 Source Server Type    : MySQL
 Source Server Version : 50720 (5.7.20-log)
 Source Host           : ************:30306
 Source Schema         : cmdb

 Target Server Type    : MySQL
 Target Server Version : 50720 (5.7.20-log)
 File Encoding         : 65001

 Date: 20/09/2023 14:00:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for model_attr_rules
-- ----------------------------
DROP TABLE IF EXISTS `model_attr_rules`;
CREATE TABLE `model_attr_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(255) NOT NULL DEFAULT '' COMMENT '规则名称',
  `rule_re` varchar(255) NOT NULL DEFAULT '' COMMENT '规则正则表达式',
  `rule_type` varchar(255) NOT NULL DEFAULT '' COMMENT '规则类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of model_attr_rules
-- ----------------------------
BEGIN;
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (1, 'IPv4', '^((25[0-5]|2[0-4]\\\\d|[1]{1}\\\\d{1}\\\\d{1}|[1-9]{1}\\\\d{1}|\\\\d{1})(\\\\.|$)){4}$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (2, '手机号', '^0?(13|14|15|18|17)[0-9]{9}$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (3, '邮件', '\\w[-\\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\\.)+[A-Za-z]{2,14}', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (4, '小写字母', '^[a-z]*$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (5, '大写字母', '^[A-Z]*$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (6, '英文和数字', '^[a-zA-Z0-9]*$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (7, '英文和数字，下划线，中横线，英文小数点', '^[\\\\.a-zA-Z0-9_-]*$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (8, '中文', '^[\\u4e00-\\u9fa5]*$', 'string');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (9, '正整数', '^[1-9]\\\\d*$', 'int');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (10, '负整数', '^-[1-9]\\\\d*$', 'int');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (11, '整数', '^[+-]?\\\\d+$', 'int');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (12, '正浮点数', '^[+]?[0-9]*\\\\.{1}?[0-9]+$', 'float');
INSERT INTO `model_attr_rules` (`id`, `rule_name`, `rule_re`, `rule_type`) VALUES (13, '负浮点数', '^-[+]?[0-9]*\\\\.{1}?[0-9]+$', 'float');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
