package config

import (
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Config 配置信息模型
type Config struct {
	Name string
}

// NewConf 初始化配置文件
func NewConf(cfg string) error {
	c := Config{
		Name: cfg,
	}

	if err := c.initConfig(); err != nil {
		return err
	}

	c.watchConfig()
	return nil
}

func (c *Config) initConfig() error {
	if c.Name != "" {
		viper.SetConfigFile(c.Name)
	} else {
		viper.AddConfigPath(ConfPath)
		viper.SetConfigName(ConfName)
	}
	viper.SetConfigType(ConfType)
	viper.AutomaticEnv()
	viper.SetEnvPrefix(ProjectName)
	replacer := strings.NewReplacer(".", "_")
	viper.SetEnvKeyReplacer(replacer)
	if err := viper.ReadInConfig(); err != nil {
		return err
	}

	zap.L().Debug("Config file loaded", zap.String("file", c.Name))

	return nil
}

func (c *Config) watchConfig() {
	viper.WatchConfig()
	viper.OnConfigChange(func(e fsnotify.Event) {
		zap.L().Info("Config file changed, " + e.Name)
	})
}
