use itcmdb
// 初始化创建collections并添加索引
db.createCollection("model")
db.createCollection("model_group")
db.createCollection("model_attr")
db.createCollection("model_attr_group")
db.createCollection("model_data")
db.createCollection("model_relation")
db.createCollection("audit_logs")

// 添加索引
db.model.createIndex({"code": 1}, {unique: true})
db.model.createIndex({"name": 1}, {unique: true})

db.model_group.createIndex({"code": 1}, {unique: true})
db.model_group.createIndex({"name": 1}, {unique: true})

// 模型属性字段索引
db.getCollection("model_attr").createIndex({
    code: NumberInt("1")
}, {
    name: "code_1",
    unique: true
});

db.model_attr_group.createIndex({"code": 1}, {unique: true})
db.model_attr_group.createIndex({"model_code": 1, "name": 1}, {unique: true})

db.model_data.createIndex({"identify_value": 1}, {unique: true})
db.model_relation.createIndex({"rel_field1_code": 1,"rel_field2_code":1}, {unique: true})

db.audit_logs.createIndex({ "categorization": 1, "username": 1, "ts": -1 , "operation": 1})

// 删除集合
// db.model.drop()
// db.model_group.drop()
// db.model_attr.drop()
// db.model_data.drop()
// db.model_attr_group.drop()
// db.model_relation.drop()

// 加一个路径树的集合
db.createCollection("model_data_treepath")
db.model_data_treepath.createIndex({"parent_id": 1, "child_id": 1}, {unique: true, name: "data_treepath_index"})

// 创建label标签的集合
db.createCollection("label_value")
db.label_value.createIndex({"key_id": 1, "value": 1}, {unique: true})