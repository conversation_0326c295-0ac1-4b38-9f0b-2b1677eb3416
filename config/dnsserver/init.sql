create table if not exists `dns_changes` (
    id bigint unsigned not null auto_increment,
    change_type tinyint unsigned not null comment "变更类型 目前仅支持record和zone",
    zone_name varchar(16) not null comment "区域名称",
    zone_type int unsigned not null comment "区域类型",
    office_id int unsigned not null comment "职场的id",
    rpz tinyint(1) default false comment "是否是rpz策略zone",
    rr_name varchar(32) comment "记录名称",
    rr_type varchar(32) comment "记录类型",
    change_info json not null comment "变更内容",
    comments varchar(255) default '' comment "备注信息",
    bpm_id varchar(255) default '' comment "变更申请单的bpm单号",
    demanders varchar(255) default '' comment "需求方",
    modifier varchar(255) comment "变更人",
    change_message varchar(256) default '' comment "错误信息",
    change_status tinyint unsigned not null comment "变更状态",
    resolv_result varchar(256) default '' comment "变更结果",
    create_time bigint not null,
    primary key (id)
);