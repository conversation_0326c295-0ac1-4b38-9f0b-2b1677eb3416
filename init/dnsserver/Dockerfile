ARG IMAGEPOINT
ARG IMAGEPROJECT
ARG ENVIROMENT
ARG ENVIROMENT_URL
FROM itops-y-harbor-core.test.gifshow.com/kwai-it-public/golang:1.22 as builder

LABEL maintainer="Kwai IT Team <<EMAIL>>"

WORKDIR /go/src

# 声明环境变量，声明proxy地址
ENV GO111MODULE on
ENV GOPROXY https://goproxy.corp.kuaishou.com,direct
ENV GOPRIVATE ""
ENV http_proxy http://*************:11080
ENV https_proxy http://*************:11080
ENV no_proxy "localhost,127.0.0.1,localaddress,localdomain.com,internal,corp.kuaishou.com,test.gifshow.com,staging.kuaishou.com"

COPY . /go/src/

RUN cd /go/src \
    && make dnsserver

FROM ${IMAGEPOINT}/${IMAGEPROJECT}/centos:centos7.2.1511_v1
ARG ENVIROMENT
ARG ENVIROMENT_URL

ENV ELASTIC_APM_SERVER_URL ${ENVIROMENT_URL}
ENV ELASTIC_APM_SERVICE_NAME ks-knoc-server-dnsserver
ENV ELASTIC_APM_CAPTURE_BODY all
ENV ELASTIC_APM_ENVIRONMENT ${ENVIROMENT}

WORKDIR /usr/bin

COPY --from=builder /go/src/target/dnsserver .

RUN chmod +x /usr/bin/dnsserver \
    && mkdir -p /etc/dnsserver

# 声明要对外开放的端口
EXPOSE 8080

CMD ["/usr/bin/dnsserver","-c","/etc/dnsserver/config.yaml"]