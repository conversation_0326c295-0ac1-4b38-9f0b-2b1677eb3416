ARG IMAGEPOINT
ARG IMAGEPROJECT
ARG ENVIROMENT
ARG ENVIROMENT_URL
FROM golang:1.22 as builder

LABEL maintainer="Kwai IT Team <<EMAIL>>"

WORKDIR /go/src

# 声明环境变量，声明proxy地址
ENV GO111MODULE on
ENV GOPROXY https://goproxy.corp.kuaishou.com,direct
ENV GOPRIVATE ""
ENV http_proxy http://************:11080
ENV https_proxy http://************:11080
ENV no_proxy "localhost,127.0.0.1,localaddress,localdomain.com,internal,corp.kuaishou.com,test.gifshow.com,staging.kuaishou.com"

COPY . /go/src/

RUN cd /go/src \
    && make asynqserver

FROM ${IMAGEPOINT}/${IMAGEPROJECT}/centos:centos7.2.1511_v1

WORKDIR /usr/bin

COPY --from=builder /go/src/target/asynqserver .

RUN chmod +x /usr/bin/asynqserver \
    && mkdir -p /etc/asynqserver

CMD ["/usr/bin/asynqserver", "-config", "/etc/asynqserver/config.yaml"]
