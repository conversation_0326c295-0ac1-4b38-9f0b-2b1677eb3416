ARG IMAGEPOINT
ARG IMAGEPROJECT
ARG ENVIROMENT
ARG ENVIROMENT_URL
FROM itops-y-harbor-core.test.gifshow.com/kwai-it-public/golang:1.22 as builder

LABEL maintainer="Kwai IT Team <<EMAIL>>"

WORKDIR /go/src

# 声明环境变量，声明proxy地址
ENV GO111MODULE on
ENV GOPROXY https://goproxy.corp.kuaishou.com,direct
ENV GOPRIVATE ""
ENV http_proxy http://*************:11080
ENV https_proxy http://*************:11080
ENV no_proxy "localhost,127.0.0.1,localaddress,localdomain.com,internal,corp.kuaishou.com,test.gifshow.com,staging.kuaishou.com"

COPY . /go/src/

RUN cd /go/src \
    && make dnsagent

FROM ${IMAGEPOINT}/${IMAGEPROJECT}/centos:centos7.2.1511_v1

WORKDIR /usr/bin

COPY --from=builder /go/src/target/dnsagent .

RUN chmod +x /usr/bin/dnsagent 

CMD ["/usr/bin/dnsagent","-c","/etc/config.yaml"]