package main

import (
	"context"
	"crypto/sha256"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/common/mq"
	"ks-knoc-server/pkg/utils"

	"github.com/IBM/sarama"
	"github.com/fatih/color"
	"github.com/olivere/elastic/v7"
	"github.com/schollz/progressbar/v3"
	"github.com/spf13/viper"
	"github.com/xdg/scram"
)

var (
	client   *elastic.Client
	producer sarama.SyncProducer
	SHA256   scram.HashGeneratorFcn = sha256.New
	totalDoc int64
)

func initKafka() error {
	config := sarama.NewConfig()
	config.Producer.Retry.Max = 2
	config.Producer.RequiredAcks = sarama.WaitForAll          // 等待服务器所有副本都保存成功后的响应
	config.Producer.Partitioner = sarama.NewRandomPartitioner // 随机的分区类型：返回一个分区器，改分区器每次选择一个随机分区
	config.Producer.Return.Successes = true                   // 是否等待成功和失败后的响应
	config.Metadata.Full = true                               // 使用给定代理地址和配置创建一个同步生产者，目前kafka的地址我先写死
	config.Version = sarama.V2_4_1_0                          // 指定kafka版本
	config.Net.SASL.Enable = true
	config.Net.SASL.User = viper.GetString("kafka.username")
	config.Net.SASL.Password = viper.GetString("kafka.password")
	config.Net.SASL.Handshake = true
	config.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient {
		return &mq.XDGSCRAMClient{HashGeneratorFcn: mq.SHA256}
	}
	config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
	p, err := sarama.NewSyncProducer(viper.GetStringSlice("kafka.host"), config)
	if err != nil {
		log.Fatalf("kafka初始化失败，错误内容为%s\n", err.Error())
		return err
	}
	color.Green("Kafka Connect Successfully")
	producer = p
	return nil
}

type WolinkData struct {
	Timestamp       time.Time `json:"@timestamp"`
	SBCCDR          string    `json:"SBC_CDR"`
	BillDuration    float64   `json:"bill_duration"`
	CalleeNum       int64     `json:"callee_num"`
	CallerID        string    `json:"caller_id"`
	CallerIP        string    `json:"caller_ip"`
	CallerNum       string    `json:"caller_num"`
	CallerPort      int       `json:"caller_port"`
	ConnectTs       float64   `json:"connect_ts"`
	DestinationIP   string    `json:"destination_ip"`
	Disposition     string    `json:"disposition"`
	EndTs           float64   `json:"end_ts"`
	EndTsIso8601    time.Time `json:"end_ts_iso8601"`
	HangupCause     string    `json:"hangup_cause"`
	HangupInitiator string    `json:"hangup_initiator"`
	KafkaTopic      string    `json:"kafka_topic"`
	Logsource       string    `json:"logsource"`
	Ltag            string    `json:"ltag"`
	Message         string    `json:"message"`
	Program         string    `json:"program"`
	SbcInterface    string    `json:"sbc_interface"`
	SbcIP           string    `json:"sbc_ip"`
	SbcPort         int       `json:"sbc_port"`
	StartTs         float64   `json:"start_ts"`
	StartTsIso8601  time.Time `json:"start_ts_iso8601"`
}

type SonusData struct {
	RecordType                      string    `json:"record_type"`
	CallServiceDuration             string    `json:"call_service_duration"`
	RouteLabel                      string    `json:"route_label"`
	CallingNumber                   string    `json:"calling_number"`
	CalledNumber                    string    `json:"called_number"`
	RouteSelected                   string    `json:"route_selected"`
	DisconnectTimeYMD               string    `json:"disconnect_time_y_m_d"`
	DisconnectTimeHMS               string    `json:"disconnect_time_h_m_s"`
	Message                         string    `json:"message"`
	EgressRemoteSignalingIPAddress  string    `json:"egress_remote_signaling_ip_address"`
	IngressLocalSignalingIPAddress  string    `json:"ingress_local_signaling_ip_address"`
	IngressRemoteSignalingIPAddress string    `json:"ingress_remote_signaling_ip_address"`
	GlobalCallID                    string    `json:"global_call_id"`
	StartTimeHMS                    string    `json:"start_time_h_m_s"`
	IngressIPCircuitEndPoint        string    `json:"ingress_ip_circuit_end_point"`
	Timestamp                       time.Time `json:"@timestamp"`
	StartTime                       string    `json:"start_time"`
	EgressLocalSignalingIPAddress   string    `json:"egress_local_signaling_ip_address"`
	StartTimeYMD                    string    `json:"start_time_y_m_d"`
	EgressIPCircuitEndPoint         string    `json:"egress_ip_circuit_end_point"`
	CallDisconnectReason            string    `json:"call_disconnect_reason"`
}

func initES() error {
	if err := config.NewConf("config.yaml"); err != nil {
		return err
	}

	// 读取证书
	caCert, err := os.ReadFile("ca.pem")
	if err != nil {
		return err
	}

	caCertPool := x509.NewCertPool()
	caCertPool.AppendCertsFromPEM(caCert)

	tlsConfig := &tls.Config{
		RootCAs:            caCertPool,
		InsecureSkipVerify: false,
	}

	httpClient := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: tlsConfig,
		},
	}

	client, err = elastic.NewClient(
		elastic.SetURL("https://*************:9200", "https://*************:9200", "https://*************:9200"),
		elastic.SetSniff(false),
		elastic.SetBasicAuth(viper.GetString("es.username"), viper.GetString("es.password")),
		elastic.SetHttpClient(httpClient),
	)
	if err != nil {
		return err
	}
	color.Green("Connect to ES success")
	return nil
}

// ParseWolinkData 解析Wolink数据, 单索引维度
func ParseWolinkData(indexName string) error {
	ctx := context.Background()
	fmt.Println("开始解析: ", indexName)

	count, err := client.Count(indexName).Do(ctx)
	if err != nil {
		return err
	}

	totalDoc += count

	bar := progressbar.New(int(count))
	scoll := client.Scroll().Index(indexName).Size(10000)

	indexCnt := 0
	for {
		var dataList [][]byte
		searchResult, err := scoll.Do(ctx)
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			log.Println("Scroll error: ", err)
			return err
		}
		if searchResult.TotalHits() == 0 {
			break
		}

		for _, hit := range searchResult.Hits.Hits {
			data := new(WolinkData)
			if err := json.Unmarshal([]byte(hit.Source), &data); err != nil {
				return err
			}
			dataList = append(dataList, []byte(hit.Source))
			_ = bar.Add(1)
		}

		indexCnt += len(dataList)

		// 每10000条数据推送到kafka
		if err := pushMessageToKafka(dataList); err != nil {
			return err
		}
	}
	scoll.Clear(ctx)

	fmt.Printf("\n 解析完成: %s ES Total count: %d; Manually total count: %d\n", indexName, count, indexCnt)

	return nil
}

func ParseSonusData(indexName string) error {
	ctx := context.Background()
	fmt.Println("开始解析: ", indexName)

	count, err := client.Count(indexName).Do(ctx)
	if err != nil {
		return err
	}

	totalDoc += count

	bar := progressbar.New(int(count))
	scoll := client.Scroll().Index(indexName).Size(10000)

	indexCnt := 0
	for {
		var dataList [][]byte
		searchResult, err := scoll.Do(ctx)
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			log.Println("Scroll error: ", err)
			return err
		}
		if searchResult.TotalHits() == 0 {
			break
		}

		for _, hit := range searchResult.Hits.Hits {
			data := new(SonusData)
			if err := json.Unmarshal([]byte(hit.Source), &data); err != nil {
				return err
			}
			dataList = append(dataList, []byte(hit.Source))
			bar.Add(1)
		}

		indexCnt += len(dataList)

		// 每10000条数据推送到kafka
		if err := pushMessageToKafka(dataList); err != nil {
			return err
		}
	}
	scoll.Clear(ctx)

	fmt.Printf("\n 解析完成: %s ES Total count: %d; Manually total count: %d\n", indexName, count, indexCnt)

	return nil
}

// pushMessageToKafka 推送到kafka, 批量推送消息到kafka
func pushMessageToKafka(dataList [][]byte) error {
	var kafkaMessages []*sarama.ProducerMessage
	topic := viper.GetString("kafka.topic")

	for _, data := range dataList {
		kafkaMessages = append(kafkaMessages, &sarama.ProducerMessage{
			Topic: topic,
			Value: sarama.ByteEncoder(data),
		})
	}

	return producer.SendMessages(kafkaMessages)
}

func generateIndexName(prefix, year, month string) []string {
	y := utils.ToInt(year)
	m := time.Month(utils.ToInt(month))
	currentMonthFirstDay := time.Date(y, m, 1, 0, 0, 0, 0, time.UTC)
	nextMonthFirstDay := currentMonthFirstDay.AddDate(0, 1, 0)
	endDay := nextMonthFirstDay.Add(-24 * time.Hour).Day()
	indexList := make([]string, 0)
	for i := 1; i <= endDay; i++ {
		day := fmt.Sprintf("%02d", i)
		idx := fmt.Sprintf("%s%s", prefix, day)
		indexList = append(indexList, idx)
	}
	return indexList
}

var (
	syncType = flag.String("sync_type", "", "要同步的类型，可选值为day或者month")
	idxType  = flag.String("type", "", "要解析的索引类型")
	day      = flag.String("day", "", "要解析的索引日期")
	month    = flag.String("month", "", "要解析的索引月份")
	year     = flag.String("year", "", "要解析的索引年份")
)

func syncWolinkData(indexPrefix, year, month string) error {
	for _, idx := range generateIndexName(indexPrefix, year, month) {
		if err := ParseWolinkData(idx); err != nil {
			return err
		}
	}

	return nil
}

func syncSonusData(indexPrefix, year, month string) error {
	for _, idx := range generateIndexName(indexPrefix, year, month) {
		if err := ParseSonusData(idx); err != nil {
			return err
		}
	}

	return nil
}

func main() {
	flag.Parse()
	if err := initES(); err != nil {
		panic(err)
	}
	if err := initKafka(); err != nil {
		panic(err)
	}

	// 首先看是按照什么维度进行同步
	switch *syncType {
	case "day":
		if *year == "" {
			fmt.Println("请输入年份")
			return
		}
		if *month == "" {
			fmt.Println("请输入月份")
			return
		}
		if *day == "" {
			fmt.Println("请输入日期")
			return
		}

		if len(*month) == 1 {
			*month = "0" + *month
		}

		dateTime := fmt.Sprintf("%s.%s.%s", *year, *month, *day)
		if *idxType == "wolink" {
			idxName := fmt.Sprintf("sbc-wolink-log-%s", dateTime)
			if err := ParseWolinkData(idxName); err != nil {
				panic(err)
			}
			return
		} else if *idxType == "sonus" {
			idxName := fmt.Sprintf("sbc-sonus-log-%s", dateTime)
			if err := ParseSonusData(idxName); err != nil {
				panic(err)
			}
			return
		} else {
			fmt.Println("不合法的索引分类, 请输入 wolink 或 sonus")
			return
		}
	case "month":
		// 按照月份进行同步
		if *year == "" {
			fmt.Println("请输入年份")
			return
		}
		if *month == "" {
			fmt.Println("请输入月份")
			return
		}

		if len(*month) == 1 {
			*month = "0" + *month
		}

		dateTime := fmt.Sprintf("%s.%s.", *year, *month)
		if *idxType == "wolink" {
			idxName := fmt.Sprintf("sbc-wolink-log-%s", dateTime)
			if err := syncWolinkData(idxName, *year, *month); err != nil {
				panic(err)
			}
		} else if *idxType == "sonus" {
			idxName := fmt.Sprintf("sbc-sonus-log-%s", dateTime)
			if err := syncSonusData(idxName, *year, *month); err != nil {
				panic(err)
			}
		} else {
			fmt.Println("不合法的索引分类, 请输入 wolink 或 sonus")
			return
		}
	default:
		fmt.Println("不合法的同步类型, 请输入 day 或 month")
		return
	}

	fmt.Printf("解析完成, 所有Doc文档数量为: %d\n", totalDoc)
}
