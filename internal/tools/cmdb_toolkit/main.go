package main

import (
	"log"
	"os"
	"runtime"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/check"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/clean"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/export"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/sync"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
)

type CMDBToolkit struct{}

func runHelp(cmd *cobra.Command, arg []string) {
	_ = cmd.Help()
}

func NewDefaultCMDBToolkit(tkt CMDBToolkit) *cobra.Command {
	return NewCMDBToolKitCommand(tkt)
}

func NewCMDBToolKitCommand(tkt CMDBToolkit) *cobra.Command {
	var mainConf string
	command := &cobra.Command{
		Use:   "cmdb_toolkit",
		Short: "CMDB常用工具集",
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			// 初始化主配置文件
			log.Println("mainConf: " + mainConf)
			if err := config.NewConf(mainConf); err != nil {
				panic(err)
			}
		},
		Run: runHelp,
	}
	command.PersistentFlags().StringVarP(&mainConf, "config", "c", "", "Config file path.")
	_ = command.MarkFlagRequired("config")

	groups := CommandGroups{
		{
			Name: "CMDB数据同步",
			Commands: []*cobra.Command{
				sync.NewSyncCommand(),
				app.NewUpdateCommand(),    // 更新也算是同步的一种
				clean.NewCleanCommand(),   // 数据清洗
				check.NewCheckCommand(),   // 数据合规检测
				export.NewExportCommand(), // 数据导出
			},
		},
	}

	groups.Add(command)
	return command
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU())

	toolkit := CMDBToolkit{}
	cmd := NewDefaultCMDBToolkit(toolkit)
	if err := cmd.Execute(); err != nil {
		color.Red(err.Error())
		os.Exit(1)
	}
}
