package main

import "github.com/spf13/cobra"

type CommandGroup struct {
	Name     string
	Commands []*cobra.Command
}

type CommandGroups []CommandGroup

// Add 遍历每一个Command Group下的所有Commands
func (g CommandGroups) Add(c *cobra.Command) {
	for _, group := range g {
		c.AddCommand(group.Commands...)
	}
}

// Has 判断某个分组下是否有某个command
func (g CommandGroups) Has(c *cobra.Command) bool {
	for _, group := range g {
		for _, command := range group.Commands {
			if command == c {
				return true
			}
		}
	}
	return false
}
