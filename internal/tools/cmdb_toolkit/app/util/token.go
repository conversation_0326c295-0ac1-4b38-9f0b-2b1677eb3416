package util

import (
	"fmt"

	"ks-knoc-server/internal/common/rest"

	"go.uber.org/zap"
)

// Token ...
type Token struct {
	AppID        int    `json:"appId"`
	AccessToken  string `json:"accessToken"`
	ExpireTime   int    `json:"expireTime"`
	RefreshToken string `json:"refreshToken"`
}

// TokenResponse ...
type TokenResponse struct {
	*rest.BaseResponse `json:",inline"`
	Result             Token `json:"result"`
}

func NewTokenRequest(endpoint, ak, sk string) *rest.Request {
	req := rest.NewClient(endpoint, "/token/get")

	// 初始化认证信息
	params := make(map[string]string)
	params["appKey"] = ak
	params["secretKey"] = sk
	return req.Get().WithParams(params)
}

func NewTokenResponse() *TokenResponse {
	return &TokenResponse{}
}

// GetToken 返回token认证信息，格式为Bearer token
// 这里可以尝试重复链接，如果失败则返回空字符串，重复链接的时候如果token没有过期，那么依然会返回那个未过期的token
func GetToken(endpoint, ak, sk string) (string, error) {
	tokenRequest := NewTokenRequest(endpoint, ak, sk)
	tokenResponse := NewTokenResponse()

	if err := tokenRequest.Do().Into(tokenResponse); err != nil {
		zap.L().Error("获取token失败", zap.Error(err))
		return "", err
	}

	if tokenResponse.Code != 0 {
		zap.L().Error(fmt.Sprintf("获取token失败, 异常信息为: %s", tokenResponse.Message))
		return "", fmt.Errorf("获取token失败, 异常信息为: %s", tokenResponse.Message)
	}

	return fmt.Sprintf("Bearer %s", tokenResponse.Result.AccessToken), nil
}
