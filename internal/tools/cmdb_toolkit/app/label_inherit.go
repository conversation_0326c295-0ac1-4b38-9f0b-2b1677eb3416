package app

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/array"

	"github.com/fatih/color"
	"go.mongodb.org/mongo-driver/bson"
)

const root = "city"

// DCLabelInherit DataCenterLabelInherit 数据中心Label标签继承
// 主要做一些基本数据标签的继承，比如城市 → 职场 → 机房 → 机柜 → 机柜上的设备
func DCLabelInherit() error {
	// 初始化mongodb的链接
	ctx := context.Background()
	mongoClient, err := db.NewMongoOptions().Init()
	if err != nil {
		log.Fatalf("获取MongoClient失败: %s", err.Error())
	}

	colModelData := mongoClient.GetCollection("model_data")

	// root其实就是city，整个继承链路目前先暂定未
	// city <- office <- idc <- rack <- 机柜上的设备
	cities := make([]*v1.ModelData, 0)
	cityMap := make(map[string]*v1.ModelData)
	if err := colModelData.Find(ctx, bson.M{"model_code": root}).All(&cities); err != nil {
		return err
	}
	for _, city := range cities {
		cityMap[city.ID] = city
	}

	// 更新office
	offices := make([]*v1.ModelData, 0)
	if err := colModelData.Find(ctx, bson.M{"model_code": "office"}).All(&offices); err != nil {
		return err
	}
	officeMap := make(map[string]*v1.ModelData)
	for _, office := range offices {
		officeMap[office.ID] = office
	}

	updatedOffice := make([]*v1.ModelData, 0)
	for _, office := range offices {
		parent := office.ParentID
		parentCity, ok := cityMap[parent]
		if !ok {
			return errors.New("这条职场数据没有设置父亲节点，请设置一下，职场名称为: " + office.Name())
		}
		parentLabels := array.StringArray(parentCity.LabelIDList)
		officeLabels := array.StringArray(office.LabelIDList)
		toBeAddedLabels := officeLabels.ArrayDiff(parentLabels)
		if len(toBeAddedLabels) != 0 {
			for _, l := range toBeAddedLabels {
				office.LabelIDList = append(office.LabelIDList, l)
			}
			updatedOffice = append(updatedOffice, office)
		}
	}

	// 更新office的标签
	if len(updatedOffice) > 0 {
		now := time.Now().Unix()
		bulk := colModelData.Bulk()
		for _, d := range updatedOffice {
			bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{"$set": bson.M{"label_id_list": d.LabelIDList, "update_at": now}})
		}

		_, err := bulk.Run(ctx)
		if err != nil {
			color.Red("[Failed] 更新失败，异常原因为: " + err.Error())
			return err
		}
		color.Green(fmt.Sprintf("[%d] 条职场数据的标签继承完成", len(updatedOffice)))
	} else {
		color.Yellow(fmt.Sprintf("[Pass] 没有需要更新的职场数据"))
	}

	// 更新职场
	idcList := make([]*v1.ModelData, 0)
	if err := colModelData.Find(ctx, bson.M{"model_code": "idc"}).All(&idcList); err != nil {
		return err
	}
	idcMap := make(map[string]*v1.ModelData)
	for _, idc := range idcList {
		idcMap[idc.ID] = idc
	}

	updatedIdc := make([]*v1.ModelData, 0)
	for _, idc := range idcList {
		parent := idc.ParentID
		parentOffice, ok := officeMap[parent]
		if !ok {
			return errors.New("这条机房数据没有设置父亲节点，请设置一下，机房名称为: " + idc.Name())
		}
		parentLabels := array.StringArray(parentOffice.LabelIDList)
		idcLabels := array.StringArray(idc.LabelIDList)
		toBeAddedLabels := idcLabels.ArrayDiff(parentLabels)
		if len(toBeAddedLabels) != 0 {
			for _, l := range toBeAddedLabels {
				idc.LabelIDList = append(idc.LabelIDList, l)
			}
			updatedIdc = append(updatedIdc, idc)
		}
	}

	// 更新idc的标签
	if len(updatedIdc) > 0 {
		now := time.Now().Unix()
		bulk := colModelData.Bulk()
		for _, d := range updatedIdc {
			bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{"$set": bson.M{"label_id_list": d.LabelIDList, "update_at": now}})
		}

		_, err := bulk.Run(ctx)
		if err != nil {
			color.Red("[Failed] 更新失败，异常原因为: " + err.Error())
			return err
		}
		color.Green(fmt.Sprintf("[%d] 条机房数据的标签继承完成", len(updatedIdc)))
	} else {
		color.Yellow(fmt.Sprintf("[Pass] 没有需要更新的机房数据"))
	}

	// 更新机柜
	rackList := make([]*v1.ModelData, 0)
	if err := colModelData.Find(ctx, bson.M{"model_code": "rack"}).All(&rackList); err != nil {
		return err
	}
	rackMap := make(map[string]*v1.ModelData)
	for _, rack := range rackList {
		rackMap[rack.ID] = rack
	}

	updatedRack := make([]*v1.ModelData, 0)
	for _, rack := range rackList {
		parent := rack.ParentID
		parentIdc, ok := idcMap[parent]
		if !ok {
			return errors.New("这条机柜数据没有设置父亲节点，请设置一下，机柜名称为: " + rack.Name())
		}
		parentLabels := array.StringArray(parentIdc.LabelIDList)
		rackLabels := array.StringArray(rack.LabelIDList)
		toBeAddedLabels := rackLabels.ArrayDiff(parentLabels)
		if len(toBeAddedLabels) != 0 {
			for _, l := range toBeAddedLabels {
				rack.LabelIDList = append(rack.LabelIDList, l)
			}
			updatedRack = append(updatedRack, rack)
		}
	}

	// 更新rack的标签
	if len(updatedRack) > 0 {
		now := time.Now().Unix()
		bulk := colModelData.Bulk()
		for _, d := range updatedRack {
			bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{"$set": bson.M{"label_id_list": d.LabelIDList, "update_at": now}})
		}
		_, err := bulk.Run(ctx)
		if err != nil {
			color.Red("[Failed] 更新失败，异常原因为: " + err.Error())
			return err
		}
		color.Green(fmt.Sprintf("[%d] 条机柜数据的标签继承完成", len(updatedRack)))
	} else {
		color.Yellow(fmt.Sprintf("[Pass] 没有需要更新的机柜数据"))
	}

	// 更新机柜上的数据，这个时候，我们的角度不是机柜下的某个子模型，而是直接站在机柜的角度看谁放在机柜上了就可以。
	updatedSubDataList := make([]*v1.ModelData, 0)
	for _, rack := range rackList {
		rackID := rack.ID
		// 我不管你是啥模型的，反正放上去了都列出来就得了，否则很多设备需要我自己去加关系，加不过来的。
		var updateDeviceList []*v1.ModelData
		if err := colModelData.Find(ctx, bson.M{"parent_id": rackID}).All(&updateDeviceList); err != nil {
			return err
		}
		for _, d := range updateDeviceList {
			parentLabels := array.StringArray(rack.LabelIDList)
			sonDataLabels := array.StringArray(d.LabelIDList)
			toBeAddedLabels := sonDataLabels.ArrayDiff(parentLabels)
			if len(toBeAddedLabels) != 0 {
				for _, l := range toBeAddedLabels {
					d.LabelIDList = append(d.LabelIDList, l)
				}
				updatedSubDataList = append(updatedSubDataList, d)
			}
		}
	}

	// 更新子模型的标签
	if len(updatedSubDataList) > 0 {
		now := time.Now().Unix()
		bulk := colModelData.Bulk()
		for _, d := range updatedSubDataList {
			bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{"$set": bson.M{"label_id_list": d.LabelIDList, "update_at": now}})
		}
		_, err := bulk.Run(ctx)
		if err != nil {
			color.Red("[Failed] 更新失败，异常原因为: " + err.Error())
			return err
		}
		color.Green(fmt.Sprintf("[%d] 条子模型数据的标签继承完成", len(updatedSubDataList)))
	} else {
		color.Yellow(fmt.Sprintf("[Pass] 没有需要更新的子模型数据"))
	}

	return nil
}

// AnalyseKVM 分析kvm的标签，主要功能就是给kvm设备打上service:kvm的标签
// 该打签功能依赖主机名，主要是对主机名进行模糊搜索，搜索包含*kvm*的数据后打上标签，存在一定局限性
// 仅用于前期各项流程完善之前的纠错使用
func AnalyseKVM() error {
	ctx := context.Background()
	mongoClient, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	colModelData := mongoClient.GetCollection("model_data")
	kvms := make([]*v1.ModelData, 0)
	if err := colModelData.Find(ctx, bson.M{"model_code": "server", "data.server_name": bson.M{
		"$regex":   ".*kvm.*",
		"$options": "i",
	}}).All(&kvms); err != nil {
		return err
	}

	var key v1.LabelKey
	if err := mongoClient.GetCollection("label_key").Find(ctx, bson.M{"name": "service"}).One(&key); err != nil {
		return err
	}

	var value v1.LabelValue
	if err := mongoClient.GetCollection("label_value").Find(ctx, bson.M{"key_id": key.ID, "value": "kvm"}).One(&value); err != nil {
		return err
	}

	updateKvmList := make([]*v1.ModelData, 0)
	for _, kvm := range kvms {
		kvmLabelIDList := array.StringArray(kvm.LabelIDList)
		if !kvmLabelIDList.InArray(value.ID) {
			kvm.LabelIDList = append(kvm.LabelIDList, value.ID)
			updateKvmList = append(updateKvmList, kvm)
		}
	}

	if len(updateKvmList) > 0 {
		now := time.Now().Unix()
		bulk := colModelData.Bulk()
		for _, d := range updateKvmList {
			bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{"$set": bson.M{"label_id_list": d.LabelIDList, "update_at": now}})
		}
		_, err := bulk.Run(ctx)
		if err != nil {
			color.Red("[Failed] 更新失败，异常原因为: " + err.Error())
			return err
		}
		color.Green(fmt.Sprintf("[%d] 条kvm数据的标签继承完成", len(updateKvmList)))
	} else {
		color.Yellow(fmt.Sprintf("[Pass] 没有需要更新的kvm数据"))
	}

	return nil
}
