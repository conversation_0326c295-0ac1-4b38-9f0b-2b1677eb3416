package export

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

// DeviceRackRelationData 设备机柜关系数据
type DeviceRackRelationData struct {
	OfficeName  string `json:"office_name"`  // 职场名称
	IDCName     string `json:"idc_name"`     // 机房名称
	RackID      string `json:"rack_id"`      // 机柜ID
	RackName    string `json:"rack_name"`    // 机柜名称
	RackHeight  int    `json:"rack_height"`  // 机柜高度
	HasDevice   string `json:"has_device"`   // 是否有设备
	DeviceCount int    `json:"device_count"` // 设备数量
	DeviceID    string `json:"device_id"`    // 设备ID
	DeviceName  string `json:"device_name"`  // 设备名称
	DeviceType  string `json:"device_type"`  // 设备类型
	DeviceModel string `json:"device_model"` // 设备型号
	DeviceBrand string `json:"device_brand"` // 设备品牌
	DeviceSN    string `json:"device_sn"`    // 设备序列号
	AssetID     string `json:"asset_id"`     // 资产编号
	InternalIP  string `json:"internal_ip"`  // 内网IP
	BMCIP       string `json:"bmc_ip"`       // 带外IP
	Owner       string `json:"owner"`        // 归属人
	StartU      int    `json:"start_u"`      // 起始U位
	EndU        int    `json:"end_u"`        // 结束U位
	Height      int    `json:"height"`       // 设备高度
	ModelCode   string `json:"model_code"`   // 模型代码
	ModelName   string `json:"model_name"`   // 模型名称
}

func exportDeviceRackRelation() error {
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			color.Red("关闭MongoDB连接失败: %s", err.Error())
		}
	}()

	color.Green("开始查询机柜和设备关系数据...")

	// 1. 查询所有模型
	models := make([]*v1.Model, 0)
	if err := mgo.GetCollection("model").Find(ctx, bson.M{}).All(&models); err != nil {
		return fmt.Errorf("查询模型失败: %s", err.Error())
	}

	modelMap := make(map[string]*v1.Model)
	for _, model := range models {
		modelMap[model.Code] = model
	}

	// 2. 查询所有机柜
	rackDataList := make([]*v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
		"model_code": "rack",
	}).All(&rackDataList); err != nil {
		return fmt.Errorf("查询机柜数据失败: %s", err.Error())
	}

	if len(rackDataList) == 0 {
		color.Yellow("未找到机柜数据")
		return nil
	}

	color.Green("找到 %d 个机柜", len(rackDataList))

	// 3. 查询与机柜建立了从属关系的模型
	modelAttrs := make([]*v1.CommonModelAttribute, 0)
	if err := mgo.GetCollection("model_attr").Find(ctx, bson.M{
		"type_name":      "relationship",
		"attrs.rel_type": 1, // 从属关系
		"attrs.rel_to":   "rack",
	}).All(&modelAttrs); err != nil {
		return fmt.Errorf("查询模型属性失败: %s", err.Error())
	}

	if len(modelAttrs) == 0 {
		color.Yellow("未找到与机柜建立从属关系的模型")
		return nil
	}

	// 获取与机柜建立关系的模型代码列表
	relatedModelCodes := make([]string, 0)
	for _, attr := range modelAttrs {
		relatedModelCodes = append(relatedModelCodes, attr.ModelCode)
	}

	color.Green("找到与机柜建立从属关系的模型: %s", strings.Join(relatedModelCodes, ", "))

	// 4. 查询所有设备数据（仅查询与机柜建立关系的模型）
	deviceDataList := make([]*v1.ModelData, 0)
	if len(relatedModelCodes) > 0 {
		if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
			"model_code": bson.M{"$in": relatedModelCodes},
		}).All(&deviceDataList); err != nil {
			return fmt.Errorf("查询设备数据失败: %s", err.Error())
		}
	}

	color.Green("找到 %d 条设备数据", len(deviceDataList))

	// 5. 按机柜ID分组设备, key为机柜id，value为机柜上的设备列表
	devicesByRack := make(map[string][]*v1.ModelData)
	for _, device := range deviceDataList {
		if device.ParentID != "" {
			if _, ok := devicesByRack[device.ParentID]; !ok {
				devicesByRack[device.ParentID] = make([]*v1.ModelData, 0)
			}
			devicesByRack[device.ParentID] = append(devicesByRack[device.ParentID], device)
		}
	}

	// 6. 获取机房信息
	idcIDs := make([]string, 0)
	for _, rack := range rackDataList {
		if rack.ParentID != "" {
			idcIDs = append(idcIDs, rack.ParentID)
		}
	}

	// 去重
	idcIDMap := make(map[string]bool)
	uniqueIDCIDs := make([]string, 0)
	for _, id := range idcIDs {
		if !idcIDMap[id] {
			idcIDMap[id] = true
			uniqueIDCIDs = append(uniqueIDCIDs, id)
		}
	}

	idcMap := make(map[string]*v1.ModelData)
	if len(uniqueIDCIDs) > 0 {
		idcDataList := make([]*v1.ModelData, 0)
		if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
			"_id":        bson.M{"$in": uniqueIDCIDs},
			"model_code": "idc",
		}).All(&idcDataList); err != nil {
			return fmt.Errorf("查询机房数据失败: %s", err.Error())
		}

		for _, idc := range idcDataList {
			idcMap[idc.ID] = idc
		}
	}

	// 7. 获取职场信息
	officeIDs := make([]string, 0)
	for _, idc := range idcMap {
		if idc.ParentID != "" {
			officeIDs = append(officeIDs, idc.ParentID)
		}
	}

	// 去重
	officeIDMap := make(map[string]bool)
	uniqueOfficeIDs := make([]string, 0)
	for _, id := range officeIDs {
		if !officeIDMap[id] {
			officeIDMap[id] = true
			uniqueOfficeIDs = append(uniqueOfficeIDs, id)
		}
	}

	officeMap := make(map[string]*v1.ModelData)
	if len(uniqueOfficeIDs) > 0 {
		officeDataList := make([]*v1.ModelData, 0)
		if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
			"_id":        bson.M{"$in": uniqueOfficeIDs},
			"model_code": "office",
		}).All(&officeDataList); err != nil {
			return fmt.Errorf("查询职场数据失败: %s", err.Error())
		}

		for _, office := range officeDataList {
			officeMap[office.ID] = office
		}
	}

	// 8. 处理数据并构建导出结果
	exportData := make([]*DeviceRackRelationData, 0)
	racksWithDevices := 0
	racksWithoutDevices := 0
	totalDevices := 0

	for _, rack := range rackDataList {
		// 获取机柜基本信息
		rackData := &DeviceRackRelationData{
			RackID:   rack.ID,
			RackName: getFieldValue(rack, "rack_name"),
		}

		// 获取机柜高度
		rackHeightStr := getFieldValue(rack, "rack_height")
		if rackHeightStr != "" {
			if val, err := strconv.Atoi(rackHeightStr); err == nil {
				rackData.RackHeight = val
			}
		}
		if rackData.RackHeight == 0 {
			rackData.RackHeight = 42 // 默认高度
		}

		// 获取机房和职场信息
		if rack.ParentID != "" {
			if idc, exists := idcMap[rack.ParentID]; exists {
				rackData.IDCName = getFieldValue(idc, "idc_name")

				if idc.ParentID != "" {
					if office, exists := officeMap[idc.ParentID]; exists {
						rackData.OfficeName = getFieldValue(office, "office_name")
					}
				}
			}
		}

		// 检查机柜上是否有设备
		devices := devicesByRack[rack.ID]
		if len(devices) > 0 {
			rackData.HasDevice = "是"
			rackData.DeviceCount = len(devices)
			racksWithDevices++
			totalDevices += len(devices)

			// 为每个设备创建一行记录
			for _, device := range devices {
				deviceData := *rackData // 复制机柜信息

				// 获取模型信息
				if model, exists := modelMap[device.ModelCode]; exists {
					deviceData.ModelName = model.Name
				}
				deviceData.ModelCode = device.ModelCode

				// 获取设备基本信息
				deviceData.DeviceID = device.ID
				deviceData.DeviceName = getFieldValue(device, fmt.Sprintf("%s_name", device.ModelCode))
				deviceData.DeviceModel = getFieldValue(device, fmt.Sprintf("%s_model", device.ModelCode))
				deviceData.DeviceBrand = getFieldValue(device, fmt.Sprintf("%s_brand", device.ModelCode))
				deviceData.DeviceSN = getFieldValue(device, fmt.Sprintf("%s_sn", device.ModelCode))
				deviceData.AssetID = getFieldValue(device, fmt.Sprintf("%s_asset_id", device.ModelCode))
				deviceData.InternalIP = getFieldValue(device, fmt.Sprintf("%s_internal_ip", device.ModelCode))
				deviceData.BMCIP = getFieldValue(device, fmt.Sprintf("%s_bmc", device.ModelCode))
				deviceData.Owner = getFieldValue(device, fmt.Sprintf("%s_owner", device.ModelCode))

				// 获取U位信息
				startU := getFieldValue(device, fmt.Sprintf("%s_start_u", device.ModelCode))
				if startU != "" {
					if val, err := strconv.Atoi(startU); err == nil {
						deviceData.StartU = val
					}
				}

				height := getFieldValue(device, fmt.Sprintf("%s_height", device.ModelCode))
				if height != "" {
					if val, err := strconv.Atoi(height); err == nil {
						deviceData.Height = val
						if deviceData.StartU > 0 {
							deviceData.EndU = deviceData.StartU + val - 1
						}
					}
				}

				exportData = append(exportData, &deviceData)
			}
		} else {
			// 空机柜
			rackData.HasDevice = "否"
			rackData.DeviceCount = 0
			racksWithoutDevices++
			exportData = append(exportData, rackData)
		}
	}

	// 9. 导出到CSV文件
	if err := exportToCSV(exportData); err != nil {
		return fmt.Errorf("导出CSV文件失败: %s", err.Error())
	}

	color.Green("导出完成！")
	color.Green("机柜统计: 总计 %d 个机柜，其中 %d 个有设备，%d 个空机柜",
		len(rackDataList), racksWithDevices, racksWithoutDevices)
	color.Green("设备统计: 总计 %d 台设备已上架", totalDevices)
	color.Green("导出记录: 共 %d 条记录（包含机柜和设备信息）", len(exportData))

	return nil
}

// getFieldValue 获取字段值
func getFieldValue(data *v1.ModelData, fieldName string) string {
	if value, exists := data.Data[fieldName]; exists {
		return utils.ToString(value)
	}
	return ""
}

// exportToCSV 导出数据到CSV文件
func exportToCSV(data []*DeviceRackRelationData) error {
	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("rack_device_relation_%s.csv", timestamp)

	// 创建文件
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入BOM头，确保中文正确显示
	file.WriteString("\xEF\xBB\xBF")

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	header := []string{
		"职场名称", "机房名称", "机柜名称", "机柜高度", "是否有设备", "设备数量",
		"设备ID", "设备名称", "设备类型", "设备型号", "设备品牌", "设备序列号", "资产编号",
		"内网IP", "带外IP", "归属人", "起始U位", "结束U位", "设备高度", "模型代码", "模型名称",
	}
	if err := writer.Write(header); err != nil {
		return err
	}

	// 写入数据
	for _, item := range data {
		record := []string{
			item.OfficeName,
			item.IDCName,
			item.RackName,
			strconv.Itoa(item.RackHeight),
			item.HasDevice,
			strconv.Itoa(item.DeviceCount),
			item.DeviceID,
			item.DeviceName,
			item.ModelName,
			item.DeviceModel,
			item.DeviceBrand,
			item.DeviceSN,
			item.AssetID,
			item.InternalIP,
			item.BMCIP,
			item.Owner,
			strconv.Itoa(item.StartU),
			strconv.Itoa(item.EndU),
			strconv.Itoa(item.Height),
			item.ModelCode,
			item.ModelName,
		}
		if err := writer.Write(record); err != nil {
			return err
		}
	}

	color.Green("数据已导出到文件: %s", filename)
	return nil
}

func NewDeviceRackRelationCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "device-rack-relation",
		Short: "导出机柜设备关系数据",
		Long:  "以机柜为主视角，导出所有机柜信息及其上的设备数据。包括空机柜，便于机房巡检。",
		RunE: func(cmd *cobra.Command, args []string) error {
			return exportDeviceRackRelation()
		},
	}

	return cmd
}
