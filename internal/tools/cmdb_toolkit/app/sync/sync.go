package sync

import (
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/sync/asset"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/sync/es"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/sync/kwaibi"

	"github.com/spf13/cobra"
)

func sync(command *cobra.Command, args []string) {
	_ = command.Help()
}

func NewSyncCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sync",
		Short: "同步资源",
		Run:   sync,
	}

	cmd.AddCommand(kwaibi.NewSyncToKwaiBI())
	cmd.AddCommand(NewSyncOverDueStatus())
	cmd.AddCommand(asset.NewSyncFromAssetAPI())
	cmd.AddCommand(asset.NewSyncFromAssetAPIOriginal())
	cmd.AddCommand(es.NewSyncToES())
	cmd.AddCommand(NewSyncToZabbix())

	return cmd
}
