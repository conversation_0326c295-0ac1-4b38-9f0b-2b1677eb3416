package sync

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

// 初始化一个数据容器用于存储解析后的结果
var (
	jss      = make([]JsonStruct, 0)
	notExist = make([]JsonStruct, 0)
)

type JsonStruct struct {
	AssetID           string `json:"asset_id"`
	SN                string `json:"sn"`
	DeviceType        string `json:"device_type"`
	DeviceType3       string `json:"device_type3"`
	DeviceModel       string `json:"device_model"`
	Brand             string `json:"brand"`
	Model             string `json:"model"`
	Specific          string `json:"specific"`
	OverDueTime       string `json:"overdue_time"`
	OverDueStatus     string `json:"overdue_status"`
	Area              string `json:"area"`
	Location          string `json:"location"`
	OwnerReadableName string `json:"owner_readable_name"`
	Owner             string `json:"owner"`
}

func UpdateOverDueInfoFromJsonFile(filePath string) error {
	file, err := os.OpenFile(filePath, os.O_RDONLY, 0755)
	if err != nil {
		return err
	}
	defer file.Close()

	// 创建一个解码器
	decoder := json.NewDecoder(file)

	if _, err := decoder.Token(); err != nil {
		return fmt.Errorf("error while decoding json file: %s", err.Error())
	}

	for decoder.More() {
		var js JsonStruct
		if err := decoder.Decode(&js); err != nil {
			return fmt.Errorf("error while decoding json file: %s", err.Error())
		}
		jss = append(jss, js)
	}

	// 读取结束的数组标记
	if _, err := decoder.Token(); err != nil {
		return fmt.Errorf("error reading array end token: %s", err.Error())
	}

	return nil
}

func UpdateCMDBOverDueData(filepath string) error {
	// 初始化数据
	if err := UpdateOverDueInfoFromJsonFile(filepath); err != nil {
		return err
	}

	// 初始化mongodb的链接
	ctx := context.Background()
	mongoClient, err := db.NewMongoOptions().Init()
	if err != nil {
		log.Fatalf("获取MongoClient失败: %s", err.Error())
	}
	colModelData := mongoClient.GetCollection("model_data")

	for _, js := range jss {
		snField := fmt.Sprintf("data.%s_sn", js.DeviceType)
		filter := bson.M{snField: js.SN}
		result := make([]*v1.ModelData, 0)
		// 计算单条数据是否发生了更新
		if js.Location == "北京/暂存园区/IDC库" {
			continue
		}
		if js.Brand == "DIY" || js.Brand == "BMD" || js.Brand == "花火" || js.Brand == "网件" || js.Brand == "linksys" || js.Brand == "FiberHome" || js.Brand == "ZTE" {
			continue
		}
		if js.Model == "凌霄Q6" || js.Model == "Q6网线版" {
			continue
		}
		if js.DeviceType3 == "MIKROTIK路由器" {
			continue
		}
		if js.Brand == "AMD" && js.Owner == "tanxianguang" && js.Model == "CEO_2P" {
			continue
		}
		if js.DeviceType == "身份认证" || js.DeviceType == "其他" {
			continue
		}
		if strings.HasPrefix(js.SN, "20220905交换机") {
			continue
		}
		if strings.HasPrefix(js.SN, "20240111无线") {
			continue
		}
		changes := 0
		if err := colModelData.Find(ctx, filter).All(&result); err != nil {
			return err
		}
		if len(result) == 0 {
			color.Red(fmt.Sprintf("sn: %s 在cmdb中不存在", js.SN))
			notExist = append(notExist, js)
			continue
		}

		if len(result) > 1 {
			color.Red(fmt.Sprintf("sn: %s 在cmdb中存在多条数据", js.SN))
			continue
		}

		modelData := result[0]
		assetID := fmt.Sprintf("%s_asset_id", js.DeviceType)
		overDueTime := fmt.Sprintf("%s_overdue_time", js.DeviceType)
		overDueStatus := fmt.Sprintf("%s_overdue_status", js.DeviceType)
		owner := fmt.Sprintf("%s_owner", js.DeviceType)
		brand := fmt.Sprintf("%s_brand", js.DeviceType)
		model := fmt.Sprintf("%s_model", js.DeviceType)

		// 此时还需要看是不是已经有数据了
		if utils.ToString(modelData.Data[assetID]) == "" {
			color.Yellow("sn: %s 的资产编号为空，需要更新, 设备类型为%s", js.SN, js.DeviceType)
			modelData.Data[assetID] = js.AssetID
			color.Green("更新asset_id成功")
			changes += 1
		}

		// 更新过保信息
		if utils.ToString(modelData.Data[overDueTime]) == "" || utils.ToString(modelData.Data[overDueStatus]) == "" {
			color.Yellow("sn: %s 的过保信息为空，需要更新, 设备类型为%s", js.SN, js.DeviceType)
			modelData.Data[overDueTime] = js.OverDueTime
			modelData.Data[overDueStatus] = js.OverDueStatus
			color.Green("更新过保信息成功")
			changes += 1
		}

		// 更新owner信息
		if utils.ToString(modelData.Data[owner]) == "" {
			color.Yellow("sn: %s 的owner信息为空，需要更新, 设备类型为%s", js.SN, js.DeviceType)
			modelData.Data[owner] = js.Owner
			color.Green("更新owner信息成功")
			changes += 1
		}

		// 更新brand信息
		if utils.ToString(modelData.Data[brand]) == "" || utils.ToString(modelData.Data[model]) == "" {
			color.Yellow("sn: %s 的brand信息为空，需要更新, 设备类型为%s", js.SN, js.DeviceType)
			modelData.Data[brand] = js.Brand
			modelData.Data[model] = js.Model
			color.Green("更新brand信息成功")
			changes += 1
		}

		// 更新数据
		if changes > 0 {
			updateTime := time.Now().Unix()
			if err := colModelData.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
				"data." + assetID:       utils.ToString(modelData.Data[assetID]),
				"data." + overDueTime:   utils.ToString(modelData.Data[overDueTime]),
				"data." + overDueStatus: utils.ToString(modelData.Data[overDueStatus]),
				"data." + owner:         utils.ToString(modelData.Data[owner]),
				"data." + brand:         utils.ToString(modelData.Data[brand]),
				"data." + model:         utils.ToString(modelData.Data[model]),
				"update_time":           updateTime,
			}}); err != nil {
				return err
			}
		}
	}

	// 写入到新的json中
	newJsonData, err := json.MarshalIndent(notExist, "", "  ")
	if err != nil {
		return err
	}

	// 写入新的文件
	if err := os.WriteFile("not_exist.json", newJsonData, 0755); err != nil {
		return err
	}

	return nil
}

func NewSyncOverDueStatus() *cobra.Command {
	var filepath string
	cmd := &cobra.Command{
		Use:   "overdue",
		Short: "同步资产过保时间",
		RunE: func(cmd *cobra.Command, args []string) error {
			return UpdateCMDBOverDueData(filepath)
		},
	}
	cmd.Flags().StringVarP(&filepath, "file", "f", "", "json文件路径")
	_ = cmd.MarkFlagRequired("file")
	return cmd
}
