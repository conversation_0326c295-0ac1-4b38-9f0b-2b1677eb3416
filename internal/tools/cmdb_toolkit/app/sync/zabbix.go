package sync

import (
	"fmt"
	"log"
	"reflect"
	"strings"

	zbxModel "ks-knoc-server/internal/common/base/model/monitor/zabbix"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/hostname"
	"ks-knoc-server/internal/common/zabbix"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

const zabbixHostGroupName = "网络-Zabbix-服务器"

var (
	dbClient    *gorm.DB
	mongoClient *db.MongoOptions
)

func initDB() error {
	var err error
	dbClient, err = db.NewMySQLOptions().Init()
	if err != nil {
		return err
	}
	return nil
}

func initMongo() error {
	var err error
	mongoClient, err = db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	return nil
}

func ipFilter(ip string) bool {
	if ip == "" {
		return false
	}
	if array.InArray(ip, []string{"127.0.0.1", "************", "*********", "*********", "*******", "*******", "***************", "*******"}) {
		return false
	}
	return true
}

type syncZabbixObject string

const (
	syncZabbixObjectHost      syncZabbixObject = "host"
	syncZabbixObjectHostGroup syncZabbixObject = "hostgroup"
	syncZabbixObjectProxy     syncZabbixObject = "proxy"
	syncZabbixObjectTemplate  syncZabbixObject = "template"
)

func syncHostToZabbix() {
	var (
		err   error
		token string
	)
	color.Green("开始同步Zabbix主机")
	if token, err = zabbix.GetZabbixToken(); err != nil {
		log.Fatalf("同步Zabbix主机失败, err: %v", err)
		return
	}

	// 初始化数据库
	// if dbClient == nil {
	// 	if err := initDB(); err != nil {
	// 		panic(err)
	// 	}
	// }

	// // 初始化mongo
	// if mongoClient == nil {
	// 	if err := initMongo(); err != nil {
	// 		panic(err)
	// 	}
	// }

	// 查询Zabbix Server的主机
	resp, err := zabbix.HostGet(token, map[string]any{
		"selectInterfaces": "extend",
	})
	if err != nil {
		log.Fatalf("同步Zabbix主机失败, err: %v", err)
		return
	}

	hosts := resp.Result.([]any)
	for _, host := range hosts {
		hostMap := host.(map[string]any)
		zabbixHost := &zbxModel.ZabbixHost{}
		zabbixHost.HostID = utils.ToString(hostMap["hostid"])
		zabbixHost.Host = utils.ToString(hostMap["host"])
		zabbixHost.Description = utils.ToString(hostMap["description"])
		zabbixHost.Name = utils.ToString(hostMap["name"])
		zabbixHost.ProxyHostID = utils.ToString(hostMap["proxy_hostid"])
		zabbixHost.SnmpAvailable = utils.ToString(hostMap["snmp_available"])
		zabbixHost.SnmpError = utils.ToString(hostMap["snmp_error"])
		zabbixHost.Status = utils.ToString(hostMap["status"])

		hostInterfaces := hostMap["interfaces"].([]any)
		for _, hostInterface := range hostInterfaces {
			intMap := hostInterface.(map[string]any)
			intObj := &zbxModel.ZabbixInterface{
				InterfaceID: utils.ToString(intMap["interfaceid"]),
				HostID:      utils.ToString(intMap["hostid"]),
				Main:        utils.ToString(intMap["main"]),
				Type:        utils.ToString(intMap["type"]),
				Useip:       utils.ToString(intMap["useip"]),
				Ip:          utils.ToString(intMap["ip"]),
				Port:        utils.ToString(intMap["port"]),
				DNS:         utils.ToString(intMap["dns"]),
			}

			detailsType := reflect.TypeOf(intMap["details"]).Kind()

			if detailsType == reflect.Map {
				details := intMap["details"].(map[string]any)
				intObj.Details.Version = utils.ToString(details["version"])
				intObj.Details.Bulk = utils.ToString(details["bulk"])
				intObj.Details.Community = utils.ToString(details["community"])
			} else if detailsType == reflect.Slice {
				detailList := intMap["details"].([]any)
				if len(detailList) > 0 {
					log.Printf("details array length > 0")
				}
			}

			zabbixHost.Interfaces = append(zabbixHost.Interfaces, *intObj)
		}

		ip := zabbixHost.IP()
		// 过滤掉一些不需要关心的ip地址
		if !ipFilter(ip) {
			continue
		}

		hostName := zabbixHost.GetName()
		if _, err := hostname.IsValidHostname(hostName); err != nil {
			color.Red("同步Zabbix主机失败, 主机名: %s 不是有效的hostname", hostName)
		}

		// dataList := make([]*v1.ModelData, 0)
		// if err := mongoClient.GetCollection("model_data").Find(context.Background(), bson.M{
		// 	"$or": []bson.M{
		// 		{"meta_data.universal_in_ip": ip},
		// 		{"meta_data.universal_out_ip": ip},
		// 	},
		// }).All(&dataList); err != nil {
		// 	log.Printf("同步Zabbix主机失败, err: %v", err)
		// 	return
		// }

		// // 说明对应的ip没在cmdb查到
		// if len(dataList) == 0 {
		// 	log.Printf("同步Zabbix主机失败, ip: %s 没在cmdb查到", ip)
		// }
	}
}

// zabbixHostGroup 是zabbix的主机组结构体
type zabbixHostGroup struct {
	ID      int    `gorm:"column:id"`
	GroupID string `gorm:"column:groupid"`
	Name    string `gorm:"column:name"`
}

// validHostGroupName 过滤掉一些不用关心的主机组
func validHostGroupName(name string) bool {
	if strings.Contains(name, "动环") {
		return false
	}
	if strings.Contains(name, "弱电") {
		return false
	}
	if strings.Contains(name, "探测") {
		return false
	}
	if strings.Contains(name, "本地生活") {
		return false
	}
	if strings.Contains(name, "资产") {
		return false
	}
	if !strings.HasPrefix(name, "京区-") && !strings.Contains(name, "分支-") {
		return false
	}
	return true
}

// syncHostGroupToZabbix 同步主机组，与zabbix的hostgroup严格一致，以zabbix的hostgroup为准
func syncHostGroupToZabbix() {
	color.Yellow("开始同步Zabbix主机组")
	var (
		err   error
		token string
	)
	if token, err = zabbix.GetZabbixToken(); err != nil {
		log.Fatalf("同步主机组失败, err: %v", err)
		return
	}

	var (
		// 从zabbix获取的主机组
		zabbixServerHostGroups = make(mapdata.MapData)
		// 从数据库获取的主机组
		dbHostGroups = make(mapdata.MapData)
	)

	resp, err := zabbix.HostGroupGet(token, map[string]any{
		"output": "extend",
	})
	if err != nil {
		log.Fatalf("同步Zabbix主机组失败, err: %v", err)
		return
	}

	color.Green("同步Zabbix主机组成功")

	if dbClient == nil {
		if err := initDB(); err != nil {
			panic(err)
		}
	}

	// 查询Zabbix Server的hostgroup
	groups := resp.Result.([]any)
	for _, hostGroup := range groups {
		group := hostGroup.(map[string]any)
		groupID := utils.ToString(group["groupid"])
		name := utils.ToString(group["name"])
		if !validHostGroupName(name) {
			continue
		}
		// 首先查询一下对应的这个zabbix分组是否存在
		g := &zabbixHostGroup{
			GroupID: groupID,
			Name:    name,
		}
		zabbixServerHostGroups.Set(groupID, g)
	}

	// 查询数据库中的hostgroup
	dbGroups := make([]*zabbixHostGroup, 0)
	if result := dbClient.Table("zbx_host_group").Find(&dbGroups); result.Error != nil {
		log.Printf("同步主机组失败, err: %v", result.Error)
		return
	}
	for _, dbGroup := range dbGroups {
		// 手动将ID设置为0，便于后面进行对比
		dbGroup.ID = 0
		dbHostGroups.Set(dbGroup.GroupID, dbGroup)
	}

	// 对比两个map，找出需要创建和更新的hostgroup
	more, less, changes, err := zabbixServerHostGroups.Different(dbHostGroups)
	if err != nil {
		log.Printf("同步主机组失败, err: %v", err)
		return
	}

	// zabbixServerHostGroups比dbHostGroups多的hostgroup，需要创建
	for _, group := range more {
		group := group.(*zabbixHostGroup)
		if err := dbClient.Table("zbx_host_group").Create(group).Error; err != nil {
			log.Printf("同步主机组失败, err: %v", err)
			return
		}
		log.Printf("同步主机组成功, 创建 groupid: %s, name: %s", group.GroupID, group.Name)
	}

	// dbHostGroups比zabbixServerHostGroups多的hostgroup，需要删除
	for _, group := range less {
		group := group.(*zabbixHostGroup)
		if err := dbClient.Table("zbx_host_group").Where("groupid = ?", group.GroupID).Delete(&zabbixHostGroup{}).Error; err != nil {
			log.Printf("同步主机组失败, err: %v", err)
			return
		}
		log.Printf("同步主机组成功, 删除 groupid: %s, name: %s", group.GroupID, group.Name)
	}

	// 更新数据库中的hostgroup
	for _, group := range changes {
		group := group.(*zabbixHostGroup)
		if err := dbClient.Table("zbx_host_group").Where("groupid = ?", group.GroupID).Updates(group).Error; err != nil {
			log.Printf("同步主机组失败, err: %v", err)
			return
		}
		log.Printf("同步主机组成功, 更新 groupid: %s, name: %s", group.GroupID, group.Name)
	}

	color.Green("同步Zabbix主机组至数据库成功")
}

// syncProxyToZabbix 同步Zabbix代理
func syncProxyToZabbix() {
	color.Yellow("开始同步Zabbix代理")

	// 获取zabbix的token
	var (
		err   error
		token string
	)
	if token, err = zabbix.GetZabbixToken(); err != nil {
		log.Fatalf("同步Zabbix代理失败, err: %v", err)
		return
	}

	// 获取zabbix的hostgroup
	zabbixGroupResp, err := zabbix.HostGroupGet(token, map[string]any{
		"output": []string{"groupid"},
		"filter": map[string]any{
			"name": []string{zabbixHostGroupName},
		},
	})
	if err != nil {
		log.Fatalf("同步Zabbix代理失败, err: %v", err)
		return
	}

	zabbixGroupIDs := zabbixGroupResp.Result.([]any)
	if len(zabbixGroupIDs) == 0 {
		log.Fatalf("同步Zabbix代理失败, 未找到%s主机组", zabbixHostGroupName)
		return
	}
	// 获取到zabbix主机分组的ID
	zabbixGroupID := zabbixGroupIDs[0].(map[string]any)["groupid"]

	var (
		// 从zabbix获取的代理
		zabbixServerProxies = make(mapdata.MapData)
		// 从数据库获取的代理
		dbProxies = make(mapdata.MapData)
	)

	// 初始化数据库
	if dbClient == nil {
		if err := initDB(); err != nil {
			panic(err)
		}
	}

	// 查询Zabbix Server的代理
	resp, err := zabbix.ProxyGet(token, map[string]any{
		"output":          "extend",
		"selectInterface": "extend",
		"groupids":        []string{utils.ToString(zabbixGroupID)},
	})
	if err != nil {
		log.Fatalf("同步Zabbix代理失败, err: %v", err)
		return
	}

	proxies := resp.Result.([]any)
	for _, proxy := range proxies {
		proxy := proxy.(map[string]any)
		proxyID := utils.ToString(proxy["proxyid"])
		host := utils.ToString(proxy["host"])
		proxyStatus := utils.ToString(proxy["status"])
		proxyAddress := utils.ToString(proxy["proxy_address"])
		description := utils.ToString(proxy["description"])

		status := 0
		if proxyStatus == "5" {
			status = int(zbxModel.ZabbixProxyStatusActive)
		} else if proxyStatus == "6" {
			status = int(zbxModel.ZabbixProxyStatusInactive)
		}

		zabbixServerProxies.Set(proxyID, &zbxModel.ZabbixProxy{
			ProxyID:      proxyID,
			Host:         host,
			Status:       zbxModel.ZabbixProxyStatus(status),
			ProxyAddress: proxyAddress,
			Description:  description,
		})
	}

	// 查询数据库中的代理
	dbProxyList := make([]*zbxModel.ZabbixProxy, 0)
	if result := dbClient.Table("zbx_host_proxy").Find(&dbProxyList); result.Error != nil {
		log.Printf("同步Zabbix代理失败, err: %v", result.Error)
		return
	}
	for _, dbProxy := range dbProxyList {
		dbProxies.Set(dbProxy.ProxyID, dbProxy)
	}

	more, less, changes, err := zabbixServerProxies.Different(dbProxies)
	if err != nil {
		log.Printf("同步Zabbix代理失败, err: %v", err)
		return
	}

	// zabbixServerProxies比dbProxies多的代理，需要创建
	for _, proxy := range more {
		proxy := proxy.(*zbxModel.ZabbixProxy)
		if err := dbClient.Table("zbx_host_proxy").Create(proxy).Error; err != nil {
			log.Printf("同步Zabbix代理失败, err: %v", err)
			return
		}
		log.Printf("同步Zabbix代理成功, 创建 proxyid: %s, host: %s", proxy.ProxyID, proxy.Host)
	}

	// dbProxies比zabbixServerProxies多的代理，需要删除
	for _, proxy := range less {
		proxy := proxy.(*zbxModel.ZabbixProxy)
		if err := dbClient.Table("zbx_host_proxy").Where("proxyid = ?", proxy.ProxyID).Delete(&zbxModel.ZabbixProxy{}).Error; err != nil {
			log.Printf("同步Zabbix代理失败, err: %v", err)
			return
		}
	}

	// 更新数据库中的代理
	for _, proxy := range changes {
		proxy := proxy.(*zbxModel.ZabbixProxy)
		if err := dbClient.Table("zbx_host_proxy").Where("proxyid = ?", proxy.ProxyID).Updates(proxy).Error; err != nil {
			log.Printf("同步Zabbix代理失败, err: %v", err)
			return
		}
	}

	color.Green("同步Zabbix代理至数据库成功")
}

func syncTemplateToZabbix() {
	color.Green("开始同步Zabbix模板")
	var (
		err   error
		token string
	)
	if token, err = zabbix.GetZabbixToken(); err != nil {
		log.Fatalf("同步Zabbix模板失败, err: %v", err)
		return
	}
	fmt.Println(token)
}

func NewSyncToZabbix() *cobra.Command {
	var (
		syncObject string
	)

	cmd := &cobra.Command{
		Use:   "zabbix",
		Short: "同步资源到Zabbix",
		Run: func(command *cobra.Command, args []string) {
			if syncObject == string(syncZabbixObjectHost) {
				syncHostToZabbix()
			} else if syncObject == string(syncZabbixObjectHostGroup) {
				syncHostGroupToZabbix()
			} else if syncObject == string(syncZabbixObjectProxy) {
				syncProxyToZabbix()
			} else if syncObject == string(syncZabbixObjectTemplate) {
				syncTemplateToZabbix()
			} else {
				color.Red("syncObject is invalid")
			}
		},
	}

	cmd.Flags().StringVarP(&syncObject, "object", "o", "", "同步对象")
	_ = cmd.MarkFlagRequired("object")

	return cmd
}
