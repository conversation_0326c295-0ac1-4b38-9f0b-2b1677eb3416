package es

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/mapdata"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// SyncDataToES 同步数据到ES中用于搜索
func SyncDataToES() (err error) {
	var (
		es  *elastic.Client
		mgo *db.MongoOptions
	)

	ctx := context.Background()
	log.Println("[Info] ES dsn: " + viper.GetString("es.host"))

	es, err = db.NewESOptions().Init()
	if err != nil {
		return err
	}

	mgo, err = db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer mgo.Close()

	// 0. 处理模型，先把所有的模型拿出来，然后拼成一个map用于快速查找和定位
	var modelList []v1.Model
	err = mgo.GetCollection("model").Find(ctx, bson.M{}).All(&modelList)
	if err != nil {
		return err
	}
	modelMap := make(map[string]v1.Model)
	for _, model := range modelList {
		modelMap[model.Code] = model
	}

	// 1. 拿到当前data的所有数据
	var dataList []v1.ModelData
	err = mgo.GetCollection("model_data").Find(ctx, bson.M{}).All(&dataList)
	if err != nil {
		return err
	}

	// 2. 删除model_data索引下的数据，当然这样比较简单粗暴，最好的办法还是按数据进行更新。不过整体来讲还不是很麻烦
	exists, err := es.IndexExists("model_data").Do(ctx)
	if err != nil {
		return err
	}
	if exists {
		q := elastic.NewDeleteByQueryService(es).
			Index("model_data").
			Query(elastic.NewMatchAllQuery())
		res, err := q.Do(ctx)
		if err != nil {
			return err
		}
		log.Printf("[Info] 清空索引model_data中的数据, 共计 %d\n", res.Deleted)
	} else {
		log.Printf("[Info] 索引model_data不存在, 无需删除\n")
	}

	// 3. 重新把数据刷进去
	bulk := es.Bulk()
	for _, data := range dataList {
		m := modelMap[data.ModelCode]
		// 初始化一个SearchData
		searchData := v1.NewSearchData()
		err := searchData.InjectData(&data)
		if err != nil {
			zap.L().Error("InjectData error", zap.Error(err))
		}

		// 将所有信息单独抽离出来一个字段
		searchData.Data["info"] = data.GetDataInfo(false)

		// 构建meta
		meta := make(mapdata.MapData)
		meta.Set("model_name", m.Name)
		meta.Set("model_code", m.Code)
		meta.Set("model_group_code", m.ModelGroup)
		searchData.Meta = meta

		// 构建labels
		labels := make(mapdata.MapData)
		searchData.Labels = labels

		bulkReq := elastic.NewBulkIndexRequest().Index("model_data").Id(data.ID).Doc(searchData)
		bulk.Add(bulkReq)
	}
	resp, err := bulk.Do(ctx)
	if err != nil {
		return err
	}
	log.Printf("[Info] 同步数据到ES 成功: %d, 失败: %d\n", len(resp.Succeeded()), len(resp.Failed()))
	if len(resp.Failed()) > 0 {
		for _, item := range resp.Failed() {
			log.Printf("[Err] 失败: data id %s, data code %s, err is %s\n", item.Id, item.Error.Reason, item.Error.Type)
		}
	}

	return nil
}

// SyncMappingToES 同步mapping到ES中
func SyncMappingToES() (err error) {
	var (
		es  *elastic.Client
		mgo *db.MongoOptions
	)

	ctx := context.Background()
	log.Println("[Info] ES dsn: " + viper.GetString("es.host"))

	es, err = db.NewESOptions().Init()
	if err != nil {
		return err
	}

	mgo, err = db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer mgo.Close()

	// 拿到当前所有的model_attrs
	var attrList []v1.CommonModelAttribute
	err = mgo.GetCollection("model_attr").Find(ctx, bson.M{}).All(&attrList)
	if err != nil {
		return err
	}

	mapFieldTmpl := `"%s": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}}`

	// PartStart是mapping这个json的开始部分
	mapPartStart := `{"dynamic": false,"properties": {"data": {"properties": {`
	mapPart1 := `}},
  		"id":{"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
 		"identify_name": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
		"identify_value": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
		"input_type": {"type": "long"},
		"labels": {"type": "object"},
		"meta": {
          	"properties": {
				"mode_code": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
				"model_code": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
				"model_group_code": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
				"model_name": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}}
			}
		},
		"model_code": {"type": "keyword","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
		"parent_id": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": 512}}},
		"update_at": {"type": "long"},
        `
	mapPart2 := `"meta_data": {"properties": {`
	mapPart3 := `}}`
	mapPartEnd := `}}`
	var b []byte
	b = append(b, []byte(mapPartStart)...)
	// b = append(b, []byte(fmt.Sprintf(mapFieldTmpl, "info")+",")...)
	// 调整keyword的索引长度，默认是256，当超过这个长度后不会被索引
	b = append(b, []byte(`"info": {"type": "text","fields": {"keyword": {"type": "keyword","ignore_above": `+"2048}}},")...)

	// 遍历所有的模型字段
	universalFields := make(mapdata.MapData)
	for no, attr := range attrList {
		var attrInfo string
		// 把所有的通用字段都挑出来
		if attr.Universal {
			universalFieldNameWithoutModelCode := strings.Split(attr.Code, attr.ModelCode+"_")[1]
			universalFieldName := fmt.Sprintf("universal_%s", universalFieldNameWithoutModelCode)
			if _, ok := universalFields[universalFieldName]; !ok {
				universalFields[universalFieldName] = struct{}{}
			}
		}
		if no == len(attrList)-1 {
			attrInfo = fmt.Sprintf(mapFieldTmpl, attr.Code)
		} else {
			attrInfo = fmt.Sprintf(mapFieldTmpl, attr.Code) + ","
		}
		b = append(b, []byte(attrInfo)...)
	}
	b = append(b, []byte(mapPart1)...)

	// 追加part2的metadata部分
	b = append(b, []byte(mapPart2)...)
	for idx, f := range universalFields.Keys() {
		fieldDefinition := fmt.Sprintf(mapFieldTmpl, f)
		if idx < len(universalFields.Keys())-1 {
			fieldDefinition += ","
		}
		b = append(b, []byte(fieldDefinition)...)
	}
	b = append(b, []byte(mapPart3)...)
	b = append(b, []byte(mapPartEnd)...)

	// 删除老的索引，在删除之前先查询老的索引是否存在
	exist, err := es.IndexExists("model_data").Do(ctx)
	if err != nil {
		log.Printf("[Err]查询索引失败: %s\n", err.Error())
		return err
	}

	if exist {
		_, err = es.DeleteIndex("model_data").Do(ctx)
		if err != nil {
			log.Printf("[Err] 删除索引失败: %s\n", err.Error())
			return err
		}
		log.Println("[Info] 删除索引成功")
	} else {
		log.Println("[Info] 索引model_data不存在，无需删除")
	}

	// 创建索引
	_, err = es.CreateIndex("model_data").Do(ctx)
	if err != nil {
		log.Println("[Err] 创建索引失败, err is ", err.Error())
		return err
	}
	log.Println("[Info] 创建索引成功")

	// 我现在要把mapping的字段数设置超过1000
	settings := map[string]interface{}{
		"index.mapping.total_fields.limit": 2000,
	}
	log.Println("[Info] 设置mapping字段数为2000")
	if _, err := es.IndexPutSettings("model_data").BodyJson(settings).Do(ctx); err != nil {
		log.Println("[Err] 设置mapping字段数失败, err is ", err.Error())
		return err
	}

	// 刷新mapping
	_, err = es.PutMapping().Index("model_data").BodyString(string(b)).Do(ctx)
	if err != nil {
		log.Println("[Err] 刷新mapping失败, err is ", err.Error())
		return err
	}
	log.Println("[Info] 刷新mapping成功")

	return nil
}

func SyncToES(action string) error {
	switch action {
	case "data":
		return SyncDataToES()
	case "mapping":
		return SyncMappingToES()
	case "all":
		if err := SyncMappingToES(); err != nil {
			return err
		}
		if err := SyncDataToES(); err != nil {
			return err
		}
	default:
		return errors.New("不合法的同步对象，可用的值：data、mapping、all")
	}
	return nil
}

// NewSyncToES 同步数据到ES中用于搜索
func NewSyncToES() *cobra.Command {
	var syncObj string

	cmd := &cobra.Command{
		Use:   "es",
		Short: "同步数据到ES中用于搜索",
		RunE: func(cmd *cobra.Command, args []string) error {
			return SyncToES(syncObj)
		},
	}
	cmd.Flags().StringVarP(&syncObj, "sync-obj", "", "all", "刷新ES中的数据类型")

	return cmd
}
