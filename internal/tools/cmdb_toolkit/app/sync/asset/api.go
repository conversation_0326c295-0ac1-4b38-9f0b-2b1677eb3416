package asset

import (
	"errors"
	"net/http"

	"ks-knoc-server/internal/common/itsdk/sdk/log"
	"ks-knoc-server/internal/common/rest"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/util"

	"github.com/spf13/viper"
)

// AssetExportInfoResponse 资产导出信息响应
type AssetExportInfoResponse struct {
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Result  []AssetExportInfoDTO `json:"result"`
}

// newAssetExportInfoResponse 初始化资产导出信息响应
func newAssetExportInfoResponse() *AssetExportInfoResponse {
	return &AssetExportInfoResponse{
		Result: make([]AssetExportInfoDTO, 0),
	}
}

// GetAssetInfo 获取资产导出信息
func GetAssetInfo() ([]AssetExportInfoDTO, error) {
	// 先判断环境获取对应的ak, sk
	var ak, sk, ep, env string
	env = viper.GetString("openapi.env")
	if env == "prod" {
		ak = viper.GetString("openapi.prod.ak")
		sk = viper.GetString("openapi.prod.sk")
		ep = viper.GetString("openapi.prod.ep")
	} else {
		ak = viper.GetString("openapi.staging.ak")
		sk = ""
		ep = viper.GetString("openapi.staging.ep")
	}

	if token == "" {
		tk, err := util.GetToken(ep, ak, sk)
		if err != nil {
			return nil, err
		}
		if tk == "" {
			return nil, errors.New("get token error")
		}
		token = tk
	}

	req := rest.NewClient(ep, "/asset-new/api/v1/open/openapi/fixed-category/asset/query")
	r := req.Get().WithHeaders(http.Header{"Authorization": []string{token}}).WithScheme(rest.SchemeHTTPS)
	resp := newAssetExportInfoResponse()
	if err := r.Do().Into(&resp); err != nil {
		return nil, err
	}
	log.Infof("OpenAPI调用结果: Code: %d, Message: %s", resp.Code, resp.Message)
	log.Infof("资产系统数据总量为: %d", len(resp.Result))
	if resp.Code != 0 {
		return nil, errors.New(resp.Message)
	}

	return resp.Result, nil
}
