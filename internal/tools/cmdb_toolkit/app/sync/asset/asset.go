package asset

import "github.com/spf13/cobra"

func NewSyncFromAssetAPI() *cobra.Command {
	var (
		recordAsset bool
		recordNew   bool
	)

	cmd := &cobra.Command{
		Use:   "asset",
		Short: "从资产系统同步数据用于修正cmdb中的数据",
		RunE: func(cmd *cobra.Command, args []string) error {
			return ParseAssetInfo(recordAsset, recordNew)
		},
	}

	cmd.Flags().BoolVarP(&recordAsset, "record-asset", "", false, "是否记录资产系统的数据")
	cmd.Flags().BoolVarP(&recordNew, "record-new", "", false, "是否记录cmdb中新增的数据")

	return cmd
}
