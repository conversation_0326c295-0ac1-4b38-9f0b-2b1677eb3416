package asset

const (
	modelDataCollection = "model_data"
)

// defaultOwner 默认的owner
var defaultOwner = []string{
	"itwlgonggong",  // IT网络公共
	"itxtgonggong",  // IT系统公共
	"itwlwarehouse", // IT网络库房
	"itxtwarehouse", // IT系统库房
}

var deviceTypeMapping = map[string]string{
	"无线设备":  "ap",
	"交换机":   "switch",
	"路由器":   "router",
	"服务器":   "server",
	"防火墙":   "firewall",
	"无线控制器": "wlc",
	"行为管理":  "audit",
	"传输设备":  "transmission",
	"负载均衡":  "loadbalancing",
}

var deviceCodeMapping = map[string]string{
	"ap": "无线设备",
	"switch": "交换机",
	"router": "路由器",
	"server": "服务器",
	"firewall": "防火墙",
	"wlc": "无线控制器",
	"audit": "行为管理",
	"transmission": "传输设备",
	"loadbalancing": "负载均衡",
}

var UpDownShelfMapping = map[string]string{
	"在库新": "在库新",
	"在库旧": "在库旧",
	"使用中": "已上线",
	"已报废": "已处置",
	"已处置": "已处置",
	"借用中": "已上线",
}

var DeviceBrandMapping = map[string]string{
	"H3C":       "H3C",
	"Aruba":     "Aruba",
	"aruba":     "Aruba",
	"Inspur":    "浪潮",
	"inspur":    "浪潮",
	"hillstone": "山石",
	"Cisco":     "Cisco",
	"cisco":     "Cisco",
	"HUAWEI":    "华为",
	"Huawei":    "华为",
	"Ruijie":    "锐捷",
	"Nettrix":   "宁畅",
	"Fortinet":  "飞塔",
	"Lenovo":    "联想",
	"lenovo":    "联想",
	"Dell":      "戴尔",
	"dell":      "戴尔",
	"DELL":      "戴尔",
	"Synology":  "群晖",
	"Sangfor":   "深信服",
	"国鑫":        "国鑫",
	"Juniper":   "Juniper",
	"A10":       "A10",
	"奇安信":       "奇安信",
	"citrix":    "Citrix",
	"Palo Alto": "PaloAlto",
	"Sugon":     "曙光",
	"AMD":       "AMD",
	"Accelink":  "光迅",
	"FONST":     "烽火",
	"TOPSEC":    "天融信",
	"HIKVISION": "海康威视",
	"APM":       "齐治",
	"OPM":       "齐治",
	"Asus":      "华硕",
	"HP":        "惠普",
	"票易通":       "票易通",
	"ZTE":       "中兴",
}

var DeviceModelMapping = map[string]string{
	"S5130S-52S-PWR-HI":           "S5130S-52S-PWR-HI",
	"AP-325":                      "AP-325",
	"S6520X-54QC-EI":              "S6520X-54QC-EI",
	"S5130S-52S-HI":               "S5130S-52S-HI",
	"NS-ICG-750":                  "NS-ICG-750",
	"NS-ICG-730":                  "NS-ICG-730",
	"SG-6000-X9180":               "SG-6000-X9180",
	"Inspur-SA5112M5":             "SA5112M5",
	"SG-6000-A3800":               "SG-6000-A3800",
	"AC7220":                      "AC7220",
	"S7506X":                      "S7506X",
	"S5560X-34C-HI":               "S5560X-34C-HI",
	"2930M-24G-PoE+":              "2930M-24G-PoE+",
	"TH1040":                      "TH1040",
	"DELL-R740XD":                 "R740XD",
	"AIR-AP3802I-H-K9":            "AIR-AP3802I-H-K9",
	"HUAWEI-6865-48S8CQ-SI":       "6865-48S8CQ-SI",
	"Sugon-I620-G30":              "I620-G30",
	"C9300-24U":                   "C9300-24U",
	"HUAWEI-CE12804":              "FM12804",
	"S6550XE-56HF-HI":             "S6550XE-56HF-HI",
	"Inspur-SA5212M5":             "SA5212M5",
	"Aruba-AP-535-RW":             "AP-535-RW",
	"Aruba-AP-575-RW":             "AP-575-RW",
	"HUAWEI-CE12808":              "FM12808",
	"H3C-LS-6800-54QF-H3":         "LS-6800-54QF-H3",
	"S2910C-24GT2XS-HP-E":         "S2910C-24GT2XS-HP-E",
	"MSR2600":                     "MSR2600",
	"AP850-A":                     "AP850-A",
	"hillstone-SG-6000-X10800-CN": "SG-6000-X10800",
	"HUAWEI-AirEngine6761-21T":    "AirEngine6761-21T",
	"Aruba-UXI智能探测终端":             "UXI智能探测终端",
	"Cisco-WS-C2960CX-8PC-L":      "",
	"AP730-L":                     "AP730-L",
	"Synology-RS3621RPXS":         "RS3621RPXS",
	"Nettrix-R620 G40":            "R620-G40",
	"6300M24-port":                "6300M 24-port",
	"HUAWEI-8861-4C-EI-B":         "FM8861-4C-EI",
	"HUAWEI-2288HV6":              "2288H-V6",
	"S5560X-30C-EI":               "S5560-30C-EI",
	"S9850-4C":                    "S9850-4C",
	"S5130S-52S-EI":               "S5130-52S-EI",
	"Lenovo-SR590":                "SR590",
	"DELL-R730":                   "R730",
	"Inspur-SA5112M4":             "SA5112M4",
	"hillstone-SG-6000-X10000":    "SG-6000-X10000",
	"HUAWEI-TMNK1AFB01":           "TMNK1AFB01",
	"Fortinet-U431F-N":            "FortiAP-U431F",
	"S5570S-28S-HPWR-EI-A":        "S5570S-28S-HPWR-EI-A",
	"Juniper-SRX1500":             "SRX1500",
	"S7503E":                      "S7503E",
	"Fortinet-600F":               "FortiGate-600F",
	"Fortinet-1048E":              "FS-1048E",
	"Fortinet-448E-POE":           "FS-448E-POE",
	"S5130-28S-PWR-EI":            "S5130-28S-PWR-EI",
	"AIR-CT5520-K9":               "AIR-CT5520-K9",
	"WS6512":                      "WS6512",
	"WS6812":                      "WS6812",
	"Sugon-W580-G20":              "W580-G20",
	"DELL-R630":                   "R630",
	"Lenovo-SR570":                "SR570",
	"N9K-C93108TC-EX":             "N9K-C93108TC-EX",
	"S5560-30C-EI":                "S5560-30C-EI",
	"CE6855-48S6Q-HI":             "CE6855-48S6Q-HI",
	"TH3040":                      "TH3040",
	"Synology-DS1520+":            "DS1520+",
	"Synology-DS2419+":            "DS2419+",
	"FortiGate-2201E":             "FortiGate-2201E",
	"DELL-XT480F":                 "XT480F",
	"PA-3050":                     "PA-3050",
	"S6520X-30QC-EI":              "S6520X-30QC-EI",
	"S5130S-28S-PWR-HI":           "S5130S-28S-PWR-HI",
	"Cisco-AIR-CAP3702I-H-K9":     "AIR-CAP3702I-H-K9",
	"DELL-T630":                   "T630",
	"H3C-LS-S5110-52P":            "S5110-52P",
	"H3C-S6850-56HF":              "S6850-56HF",
	"H3C-S10508X":                 "S10508X",
	"H3C-S10506X":                 "S10506X",
	"H3C-S5570S-28S-HPWR-EI-A":    "S5570S-28S-HPWR-EI-A",
	"H3C-LS-12504X-AF":            "LS-12504X-AF",
	"H3C-5570S-54S-EI":            "S5570S-54S-EI",
	"LS-12508X-AF":                "LS-12508X-AF",
	"S5750C-48GT4XS-H":            "S5750C-48GT4XS-H",
	"N9K-C92160YC-X":              "N9K-C92160YC-X",
	"H3C-LS-6800-4C-H1":           "LS-6800-4C-H1",
	"Array-APV2800":               "Array-APV-2800",
	"6520X-26MC-UPWR-SI":          "S6520X-26MC-UPWR-SI",
	"AMD-CEO_2P":                  "CEO_2P",
	"Cisco-UAP-AC-HD":             "UAP-AC-HD",
	"H3CS7506X-G":                 "S7506X-G",
	"H3CS5130V2-10P-HPWR-LI":      "S5130V2-10P-HPWR-LI",
	"HUAWEI-2288HV5":              "2288H-V5",
	"票易通":                         "票易通",
	"hillstone-SG-6000-A3610-AD":   "SG-6000-A3610-AD",
}

// 有一部分已知的无需关心的sn的设备，直接pass掉
var snBlackList = []string{
	// 2024-09-20 by少辉: 网络大哥说当时买的配件，没有买机框，这个SN没法搞了
	"20220905交换机12-1",
	"20220905交换机12-2",
	"20220905交换机12-3",
	"20220905交换机12-4",
	"20220905交换机12-5",
	"20220905交换机12-6",
	"20220905交换机12-7",
	"20220905交换机12-8",
	"20220905交换机12-9",
	"20220905交换机12-10",
	"20220905交换机12-11",
	"20220905交换机12-12",

	// 2024-09 by 少辉: 一个资产编号对应两个SN，所以从sn的维度没法搞，这个手动处理了。
	"160580010018&160580010019",
	"160580010020&160580010028",

	// 一些桌面级路由器，先手动把sn给过滤掉把。
	"SGT7S23418006914",
	"SGT7S23627003708",
	"SGT7S24118000413",
	"SGT7S23B17002887", // 4台华为的凌霄路由器

	// 一台linksys的路由器，放在了雅加达
	"SN14195575",

	// 一台花火路由器，不知道干啥的，在元中心机房，花火4G聚合路由器H7支持5G聚合户外直播应急通讯
	"LRSZ20230222209",
}
