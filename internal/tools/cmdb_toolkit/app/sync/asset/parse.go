package asset

import (
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/itsdk/sdk/log"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	// 需要过滤掉不关注的二级资产分类
	excludeAssetSecondClass = []string{
		"硬盘",
		"芯片",
		"网卡",
		"UPS电源",
		"SSD硬盘",
		"raid卡",
		"GPU卡",
		"光模块",
		"机柜",
		"机柜分水器",
		"铝合金冷板模组",
		"上架",
		"其他",
		"内存",
		"传输设备配件",
		"服务器_板卡",
		"交换机配件",
		"身份认证",
		"U配电柜",
	}

	// 需要过滤掉的三级资产分类
	excludeAssetThirdClass = []string{
		"DIY服务器",
	}

	errNoNeedToUpdate = errors.New("no need to update")
)

// RemoveAssetInfo 过滤资产信息，并不是所有资产信息，CMDB都需要关注，CMDB关注的只是其中的一部分
func RemoveAssetInfo(recordAsset bool) ([]AssetExportInfoDTO, error) {
	result, err := GetAssetInfo()
	if err != nil {
		return nil, err
	}

	var filteredResult []AssetExportInfoDTO
	for _, ret := range result {
		// 过滤掉部分已知的不采集的设备的sn
		if array.InArray(ret.SnNumber, snBlackList) {
			continue
		}

		// 这一部分已经归属到idc了，所以就不用管了 只是因为财务不让删除
		if ret.Location == "北京/暂存园区/IDC库" {
			continue
		}

		// 过滤部分二级分类
		if array.InArray(ret.AssetClassSecond, excludeAssetSecondClass) {
			continue
		}

		// 过滤三级分类
		if array.InArray(ret.AssetClassThird, excludeAssetThirdClass) {
			continue
		}

		// 在工区的我不要
		if strings.HasSuffix(ret.Location, "工区") {
			continue
		}

		filteredResult = append(filteredResult, ret)
	}

	// 如果设置了记录资产信息，讲资产记录写入到本地Excel文件中
	if recordAsset {
		f := NewExcelFile("sheet1")
		defer func() {
			_ = f.CloseFile()
		}()

		if err := f.WriteTitle(); err != nil {
			return nil, err
		}

		for _, ret := range filteredResult {
			if err := f.WriteRow(ret); err != nil {
				return nil, err
			}
		}

		if err := f.SaveFile("asset_origin"); err != nil {
			return nil, err
		}
	}

	return filteredResult, nil
}

// ParseAssetInfo 从资产系统同步数据用于修正cmdb中的数据
func ParseAssetInfo(recordAsset, recordNew bool) error {
	var (
		start = time.Now()
		f     = new(ExcelFile)
		err   error
	)

	if recordAsset {
		log.Info("记录资产系统中的数据")
	} else {
		log.Info("不记录资产系统中的数据")
	}

	if recordNew {
		log.Info("记录cmdb中新增的数据")
	} else {
		log.Info("不记录cmdb中新增的数据")
	}

	// 获取处理后的数据
	result, err := RemoveAssetInfo(recordAsset)
	log.Infof("过滤掉非必要数据后的数据量为: %d", len(result))
	if err != nil {
		return err
	}

	// 初始化mongodb
	if mgoClient == nil {
		if mgoClient, err = db.NewMongoOptions().Init(); err != nil {
			return err
		}
		log.Info("mongodb连接成功")
	}
	defer func() {
		_ = mgoClient.Close()
	}()

	// 是否要记录cmdb中不存在的资产信息到本地excel文件中
	if recordNew {
		f = NewExcelFile("sheet1")
		// 写入表头
		if err := f.WriteTitle(); err != nil {
			return err
		}
		defer func() {
			_ = f.CloseFile()
		}()
	}

	// 初始化一个计数器，用于记录更新了多少条数据
	updateCounter := 0
	for _, ret := range result {
		// 这条数据比较特殊，是一个无线管理的平台，我在cmdb中录入到wlc这个分类中了。
		if ret.SnNumber == "TWMNK35018" {
			continue
		}

		// 首先看一下设备是否在映射的设备类型中, 如果不在映射的设备类型中，则跳过
		modelCode, ok := deviceTypeMapping[ret.AssetClassSecond]
		if !ok {
			log.Infof("unknown device type: %s", ret.AssetClassSecond)
			continue
		}

		// 我们以设备的sn作为唯一标识
		sn := strings.ToUpper(strings.TrimSpace(ret.SnNumber))
		snField := fmt.Sprintf("data.%s_sn", modelCode)

		// 记录变更信息
		changeLog := make([]changes, 0)

		// 首先看一下是不是报废的设备
		if ret.Scrapped == "报废" {
			// 如果设备是报废的，此时需要看一下这个设备在cmdb中是不是存在
			d := v1.ModelData{}
			if err = mgoClient.GetCollection(modelDataCollection).
				Find(ctx, bson.M{"model_code": modelCode, snField: sn}).One(&d); err != nil {
				// 如果设备报废并且没有在cmdb中录入过，那么就直接跳过，因为CMDB无需纳管一条已报废的设备信息
				if errors.Is(err, mongo.ErrNoDocuments) {
					continue
				}
				return err
			}

			// 如果可以正常查到，说明数据已经被报废了，之前是在CMDB中存在的。我们需要更新CMDB中设备的状态为报废
			rackStatusField := utils.ToString(d.Data[fmt.Sprintf("%s_status", modelCode)])

			// 与资产库状态一致，则跳过，无需再请求db更新一遍
			if rackStatusField == "已报废" {
				continue
			}

			// 与资产库状态不一致，则更新
			if rackStatusField == "" {
				changeLog = append(changeLog, changes{
					Log:   fmt.Sprintf("[更新] 将%s_status 从空值更新为%s", modelCode, "已报废"),
					Field: fmt.Sprintf("data.%s_status", modelCode),
					Value: "已报废",
				})
			} else {
				changeLog = append(changeLog, changes{
					Log:   fmt.Sprintf("[更新] 将%s_status 从%s更新为%s", modelCode, rackStatusField, "已报废"),
					Field: fmt.Sprintf("data.%s_status", modelCode),
					Value: "已报废",
				})
			}
			// 更新CMDB中的数据
			if err := updateSingleCI(sn, changeLog, d); err != nil {
				return err
			}
			updateCounter++
			continue
		}

		// 从cmdb中查询是否存在这个sn, 如果不存在则创建，如果存在则更新
		d := v1.ModelData{}
		if err = mgoClient.GetCollection(modelDataCollection).Find(ctx, bson.M{"model_code": modelCode, snField: sn}).One(&d); err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				color.Yellow("[警告] sn没找到, 模型是: %s, sn是: %s, 准备新增数据至CMDB", modelCode, sn)
				if recordNew {
					if err := f.WriteRow(ret); err != nil {
						return err
					}
				}
				if err := createAssetInCMDB(ret); err != nil {
					// 新增失败不阻塞整体的流程进行下一步的处理
					color.Red("[错误] 新增数据失败: %s", err.Error())
					continue
				}
				color.Green("[成功] 创建成功, 模型是: %s, sn是: %s", modelCode, sn)
				continue
			}
			return err
		}

		// 设备上下架的状态, 包含已经报废的设备
		checkRackStatus(ret, d, modelCode, &changeLog)

		// 更新设备的过保时间，我们这里只更新没写过保时间的字段就可以了，一般过保时间录入了并不会发生变化
		if err := checkOverDueStatus(ret, d, modelCode, &changeLog); err != nil {
			return err
		}

		// 更新归属，从IT库中拿到的归属都是it，统一使用小写
		checkBelongTo(d, modelCode, &changeLog)

		// 更新设备负责人
		checkOwner(ret, d, modelCode, &changeLog)

		// 汇总信息展示, 如果说changeLog的长度为0，说明没有需要更新的数据，直接跳过, 也不需要更新cmdb中的数据
		if err := updateSingleCI(sn, changeLog, d); err != nil {
			if errors.Is(err, errNoNeedToUpdate) {
				continue
			}
			return err
		}
		updateCounter++
	}

	if recordNew {
		// 保存Excel文件
		if err := f.SaveFile("asset_not_in_cmdb"); err != nil {
			return err
		}
	}

	color.Green("共更新%d条数据, 耗时: %s", updateCounter, time.Since(start))
	return nil
}

func checkRackStatus(ret AssetExportInfoDTO, d v1.ModelData, modelCode string, changeLog *[]changes) {
	rackStatus := UpDownShelfMapping[ret.AssetStatus]

	rackStatusField := utils.ToString(d.Data[fmt.Sprintf("%s_status", modelCode)])
	// 当db中的上下架状态为空，直接讲资产系统的数据覆盖进去
	if rackStatusField == "" {
		*changeLog = append(*changeLog, changes{
			Log:   fmt.Sprintf("[更新] 将%s_status 从空值更新为%s", modelCode, rackStatus),
			Field: fmt.Sprintf("data.%s_status", modelCode),
			Value: rackStatus,
		})
	} else if rackStatusField != "" && rackStatusField != rackStatus {
		// 上下架的状态以资产系统的为准，后面几点补录一下审计日志记录
		*changeLog = append(*changeLog, changes{
			Log:   fmt.Sprintf("[更新] 将%s_status 从%s更新为%s", modelCode, rackStatusField, rackStatus),
			Field: fmt.Sprintf("data.%s_status", modelCode),
			Value: rackStatus,
		})
	}

	// 当是在库的时候，说明设备发生了退库的操作，可能是下线，也可能是职场的裁撤，需要清理对应的关联信息
	if rackStatus == "在库新" || rackStatus == "在库旧" {
		if d.ParentID != "" {
			*changeLog = append(*changeLog, changes{
				Log: fmt.Sprintf("[更新] 同步资产系统设备已归库, 当前状态为%s，去除从属关系", rackStatus),
				Field: "parent_id",
				Value: "",
			})
		}
	}
}

func checkOverDueStatus(ret AssetExportInfoDTO, d v1.ModelData, modelCode string, changeLog *[]changes) error {
	overDueTime := ret.WarrantyDate
	overDueTimeField := utils.ToString(d.Data[fmt.Sprintf("%s_overdue_time", modelCode)])
	if overDueTimeField == "" {
		*changeLog = append(*changeLog, changes{
			Log:   fmt.Sprintf("[更新] 将%s_overdue_time 从空值更新为%s", modelCode, overDueTime),
			Field: fmt.Sprintf("data.%s_overdue_time", modelCode),
			Value: overDueTime,
		})
	}

	// 更新过保状态
	overDueStatus := ""
	overDueDateObj, err := time.Parse("2006-01-02", overDueTime)
	if err != nil {
		return err
	}
	if overDueDateObj.Unix() > time.Now().Unix() {
		overDueStatus = "在保"
	} else {
		overDueStatus = "过保"
	}
	overDueStatusField := utils.ToString(d.Data[fmt.Sprintf("%s_overdue_status", modelCode)])
	if overDueStatusField != overDueStatus {
		*changeLog = append(*changeLog, changes{
			Log:   fmt.Sprintf("[更新] 将过保状态从[%s]更新为[%s]", overDueStatusField, overDueStatus),
			Field: fmt.Sprintf("data.%s_overdue_status", modelCode),
			Value: overDueStatus,
		})
	}
	return nil
}

func checkOwner(ret AssetExportInfoDTO, d v1.ModelData, modelCode string, changeLog *[]changes) {
	// 检测设备负责人，采取的策略是如果cmdb中有负责人那么以cmdb中的为准，否则以资产库中的为准
	assetOwner := ret.OwnerCode
	cmdbOwner := utils.ToString(d.Data[fmt.Sprintf("%s_owner", modelCode)])
	// 如果cmdb中没有负责人，那么就以资产库中的为准
	// 如果cmdb中负责人是itgonggong，那么就以资产库中的为准，因为itgonggong是之前资产默认的负责人
	// 但是目前资产系统中默认的负责人更新为了itxtgonggong,itwlgonggong,itxtwarehouse,itwlwarehouse
	// 因此需要需要将之前的记录更新掉。
	if cmdbOwner == "" || cmdbOwner == "itgonggong" {
		var ori string
		if cmdbOwner == "" {
			ori = "空值"
		} else {
			ori = cmdbOwner
		}
		*changeLog = append(*changeLog, changes{
			Log:   fmt.Sprintf("[更新] 将%s_owner 从%s更新为%s", modelCode, ori, assetOwner),
			Field: fmt.Sprintf("data.%s_owner", modelCode),
			Value: assetOwner,
		})
	} 
}

func checkBelongTo(d v1.ModelData, modelCode string, changeLog *[]changes) {
	belongTo := "it"
	belongToField := utils.ToString(d.Data[fmt.Sprintf("%s_belong_to", modelCode)])
	if belongToField != belongTo {
		*changeLog = append(*changeLog, changes{
			Log:   fmt.Sprintf("[更新] 将%s_belong_to 从[%s]更新为[%s]", modelCode, belongToField, belongTo),
			Field: fmt.Sprintf("data.%s_belong_to", modelCode),
			Value: belongTo,
		})
	}
}
