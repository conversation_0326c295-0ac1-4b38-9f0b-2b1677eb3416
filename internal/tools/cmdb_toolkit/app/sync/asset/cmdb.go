package asset

import (
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/itsdk/sdk/log"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"go.mongodb.org/mongo-driver/bson"
)

// createAssetInCMDB 创建资产信息到cmdb
func createAssetInCMDB(assetInfo AssetExportInfoDTO) error {
	log.Infof("新增设备: 设备类型是: %s, 负责人是: %s, 设备是: %s, 品牌是: %s, 型号是: %s",
		assetInfo.AssetClassSecond, assetInfo.OwnerName, assetInfo.AssetName, assetInfo.Vendor, assetInfo.Label)
	now := time.Now().Unix()
	// 首先CMDB只托管了部分分类的设备
	modelCode, ok := deviceTypeMapping[assetInfo.AssetClassSecond]
	if !ok {
		return fmt.Errorf("未知设备分类: %s", assetInfo.AssetClassSecond)
	}
	// 设备在资产库的名称
	dataName := assetInfo.AssetName
	// 初始化CI的名称和唯一标识符
	identifyName := fmt.Sprintf("%s_code", modelCode)
	identifyValue := utils.GenerateIdentifyValue(modelCode, dataName)

	// 获取品牌和型号的信息
	if assetInfo.Vendor == "" || assetInfo.Label == "" {
		return errors.New("品牌或型号为空")
	}
	// 查看一下品牌是否在我们定义好的品牌列表中
	brand, ok := DeviceBrandMapping[assetInfo.Vendor]
	if !ok {
		return errors.New("未知的品牌: " + assetInfo.Vendor)
	}

	// 获取型号信息
	deviceModel := strings.ReplaceAll(assetInfo.Label, " ", "")
	model, ok := DeviceModelMapping[deviceModel]
	if !ok {
		return errors.New("已知品牌: " + assetInfo.Vendor + "，未知型号: " + deviceModel)
	}

	// 获取保修状态
	overDueStatus := ""
	overDueDateObj, err := time.Parse("2006-01-02", assetInfo.WarrantyDate)
	if err != nil {
		return err
	}
	if overDueDateObj.Unix() > now {
		overDueStatus = "在保"
	} else {
		overDueStatus = "过保"
	}

	// 获取资产名称
	assetName := assetInfo.GetInitName()
	if assetName == "" {
		return errors.New("拼接资产名称失败")
	}

	// 创建CI对象
	data := &v1.ModelData{
		ModelCode:          modelCode,
		IdentifyName:       identifyName,
		IdentifyValue:      identifyValue,
		InPutType:          2,
		CreateAt:           now,
		UpdateAt:           now,
		AssociateInstances: make([]v1.AssociateInstance, 0),
		Active:             true,
		LabelIDList:        make([]string, 0),
		Data: mapdata.MapData{
			modelCode + "_code":           identifyValue, // 没有code的数据，虽然不影响正常录入，但是影响更新
			modelCode + "_name":           strings.TrimSpace(assetName),
			modelCode + "_asset_id":       strings.TrimSpace(assetInfo.AssetNumber),
			modelCode + "_sn":             strings.ToUpper(strings.TrimSpace(assetInfo.SnNumber)),
			modelCode + "_brand":          strings.TrimSpace(brand),
			modelCode + "_model":          strings.TrimSpace(model),
			modelCode + "_status":         strings.TrimSpace(assetInfo.AssetStatus),
			modelCode + "_owner":          strings.TrimSpace(assetInfo.OwnerCode),
			modelCode + "_overdue_time":   strings.TrimSpace(assetInfo.WarrantyDate),
			modelCode + "_overdue_status": strings.TrimSpace(overDueStatus),
			modelCode + "_belong_to": func() string {
				ownerCode := assetInfo.OwnerCode
				// 如果在defaultOwner中，那么就返回it，注意，该方法只针对从资产库同步并需要新建的操作
				if array.InArray(ownerCode, defaultOwner) {
					return "it"
				} else {
					return "personal"
				}
			}(),
		},
		MetaData: make(mapdata.MapData),
	}

	if _, err := mgoClient.GetCollection("model_data").InsertOne(ctx, data); err != nil {
		return err
	}
	return nil
}

// updateSingleCI 更新单条CI对象
func updateSingleCI(sn string, change []changes, data v1.ModelData) error {
	if mgoClient == nil {
		return errors.New("mongodb client is not initialized")
	}
	if len(change) <= 0 {
		return errNoNeedToUpdate
	}
	color.Yellow("################## 更新: %s ###################", sn)
	updateInfo := bson.M{}
	for _, cl := range change {
		color.Yellow(cl.Log)
		updateInfo[cl.Field] = cl.Value
	}
	if err := mgoClient.GetCollection(modelDataCollection).
		UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": updateInfo}); err != nil {
		return err
	}
	color.Green("[成功] 更新成功: %s", sn)
	return nil
}
