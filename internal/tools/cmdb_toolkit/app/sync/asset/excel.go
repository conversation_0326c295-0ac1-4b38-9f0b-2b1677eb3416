package asset

import (
	"errors"
	"fmt"
	"reflect"
	"time"

	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
)

var (
	// Excel标题行
	cols = []string{
		"资产ID", "序列号", "一级分类", "二级分类", "三级分类",
		"资产名称", "品牌", "型号", "扩展信息", "规格",
		"资产状态", "上下架", "维保到期时间", "城市", "园区",
		"使用地点", "责任人姓名", "责任人邮箱前缀", "使用人邮箱前缀",
		"入库操作日期", "是否报废",
	}

	// 对应标题行的字段名
	colFieldOrder = []string{
		"AssetNumber", "SnNumber", "AssetClassFirst", "AssetClassSecond", "AssetClassThird",
		"AssetName", "Vendor", "Label", "AssetExt", "Specification",
		"AssetStatus", "UpDownShelf", "WarrantyDate", "City", "Garden",
		"Location", "OwnerName", "OwnerCode", "UserCode", "AcceptanceDate", "Scrapped",
	}
)

type ExcelFile struct {
	File      *excelize.File
	SheetName string
	RowIndex  int
}

func (e *ExcelFile) WriteTitle() error {
	// 表头写入excel, A1,B1,C1,.....
	colName := 'A'
	for _, col := range cols {
		cellName := fmt.Sprintf("%s%d", string(colName), 1)
		if err := e.File.SetCellValue(e.SheetName, cellName, col); err != nil {
			return err
		}
		colName += 1
	}
	// 表头在第一行写入，所以写完以后直接等于2即可，表示下面的数据要从第二行开始写入, A2,B2,C2,.....
	if e.RowIndex != 1 {
		return errors.New("写标题行的时候，rowIndex必须等于1")
	}
	e.RowIndex = 2
	return nil
}

func (e *ExcelFile) CloseFile() error {
	return e.File.Close()
}

func (e *ExcelFile) WriteCell(cell string, value interface{}) error {
	return e.File.SetCellValue(e.SheetName, cell, value)
}

func (e *ExcelFile) WriteRow(asset AssetExportInfoDTO) error {
	var (
		col = 'A'                    // 每一行数据都是从第A列还是写，所以每一行都要重置col
		val = reflect.ValueOf(asset) // 获取asset的value
		typ = reflect.TypeOf(asset)  // 获取asset的类型
	)

	for _, fieldName := range colFieldOrder {
		field, _ := typ.FieldByName(fieldName)
		fieldVal := val.FieldByName(fieldName)
		cellName := fmt.Sprintf("%s%d", string(col), e.RowIndex)

		var err error
		switch field.Type.Kind() {
		case reflect.String:
			err = e.WriteCell(cellName, fieldVal.String())
		case reflect.Int:
			err = e.WriteCell(cellName, fieldVal.Int())
		default:
			err = errors.New("unsupported type" + field.Type.Kind().String())
		}
		if err != nil {
			return err
		}
		col++
	}

	// Excel的行索引递进到下一行
	e.RowIndex++
	return nil
}

func (e *ExcelFile) SaveFile(filename string) error {
	// 保存Excel文件, 目前就保存在当前目录下，没啥必要必须保存到其他的目录
	fullName := fmt.Sprintf("%s_%d.xlsx", filename, time.Now().Unix())
	if err := e.File.SaveAs(fullName); err != nil {
		return err
	}
	return nil
}

func NewExcelFile(sheetName string) *ExcelFile {
	e := &ExcelFile{
		File:      excelize.NewFile(),
		SheetName: sheetName,
		RowIndex:  1,
	}

	// 目前仅支持单个sheet
	_, _ = e.File.NewSheet(sheetName)
	// 所以默认我们就把第一个表设置为active的
	e.File.SetActiveSheet(0)

	return e
}

func GetOriginAssetExcel() error {
	result, err := GetAssetInfo()
	if err != nil {
		return err
	}
	f := NewExcelFile("sheet1")
	// 写入表头
	if err := f.WriteTitle(); err != nil {
		return err
	}
	defer func() {
		_ = f.CloseFile()
	}()
	for _, ret := range result {
		if err := f.WriteRow(ret); err != nil {
			return err
		}
	}

	if err := f.SaveFile("asset_origin_without_filter"); err != nil {
		return err
	}

	return nil
}

// NewSyncFromAssetAPIOriginal 从资产系统获取数据并写入到excel，这个数据是没有做过任何过滤清洗处理的原数据
func NewSyncFromAssetAPIOriginal() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "asset-origin",
		Short: "从资产系统获取数据并写入到excel，这个数据是没有做过任何过滤清洗处理的原数据",
		RunE: func(cmd *cobra.Command, args []string) error {
			return GetOriginAssetExcel()
		},
	}

	return cmd
}
