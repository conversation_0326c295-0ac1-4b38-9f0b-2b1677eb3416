package asset

import (
	"fmt"
	"strings"
)

// AssetExportInfoDTO 资产导出信息
type AssetExportInfoDTO struct {
	AssetNumber      string `json:"assetNumber"`      // 资产编号
	SnNumber         string `json:"snNumber"`         // sn编号
	AssetClassFirst  string `json:"assetClassFirst"`  // 资产一级类别
	AssetClassSecond string `json:"assetClassSecond"` // 资产二级类别
	AssetClassThird  string `json:"assetClassThird"`  // 资产三级类别
	AssetName        string `json:"assetName"`        // 资产名称
	Vendor           string `json:"vendor"`           // 品牌
	Label            string `json:"label"`            // 型号
	AssetExt         string `json:"assetExt"`         // 型号扩展信息
	Specification    string `json:"specification"`    // 规格属性
	AssetStatus      string `json:"assetStatus"`      // 资产状态
	UpDownShelf      string `json:"upDownShelf"`      // 上下架
	WarrantyDays     int    `json:"warrantyDays"`     // 维保总时长(天)
	WarrantyDate     string `json:"warrantyDate"`     // 维保到期时间
	City             string `json:"city"`             // 城市
	Garden           string `json:"garden"`           // 园区
	Location         string `json:"location"`         // 使用地点
	OwnerName        string `json:"ownerName"`        // 责任人姓名
	OwnerCode        string `json:"ownerCode"`        // 责任人邮箱前缀
	UserCode         string `json:"userCode"`         // 使用人邮箱前缀
	AcceptanceDate   string `json:"acceptanceDate"`   // 入库操作日期
	Scrapped         string `json:"scrapped"`         // 是否报废
}

// GetInitName 获取资产的初始名称
func (a AssetExportInfoDTO) GetInitName() string {
	// {设备类型}-{品牌}-{型号}-{状态}
	deviceType := a.AssetClassSecond
	deviceBrand, exist := DeviceBrandMapping[a.Vendor]
	if !exist {
		return ""
	}
	dm := strings.ReplaceAll(a.Label, " ", "")
	deviceModel, exist := DeviceModelMapping[dm]
	if !exist {
		return ""
	}
	return fmt.Sprintf("%s-%s-%s-%s", deviceType, deviceBrand, deviceModel, UpDownShelfMapping[a.AssetStatus])
}

// TODO: Validate 验证设备资产信息，跳过部分设备
func (a AssetExportInfoDTO) Validate() error {
	return nil
}

// changes 记录变更的日志
type changes struct {
	Log   string
	Field string
	Value string
}
