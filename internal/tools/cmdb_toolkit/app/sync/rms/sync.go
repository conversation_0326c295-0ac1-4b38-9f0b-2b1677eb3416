package rms

import (
	"context"
	"fmt"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"
	"log"
	"strings"

	"github.com/fatih/color"
	"go.mongodb.org/mongo-driver/bson"
)

func SyncDeviceSN() (err error) {
	ctx := context.Background()
	mgo, err = db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	// 声明要查询的模型是什么
	modelCode := "server"

	// 从数据库查询到所有的server信息
	dataList := make([]v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
		"model_code": modelCode,
	}).All(&dataList); err != nil {
		return err
	}

	// 构造对应的sn字段的名称
	snField := fmt.Sprintf("%s_sn", modelCode)

	var (
		haveSN      = make([]string, 0)     // 存在sn的保存sn信息
		doNotHaveSN = make([]string, 0)     // 不存在sn的报错设备名称
		local       = make(mapdata.MapData) // 存放cmdb数据的map
		rms         = make(mapdata.MapData) // 存放rms信息的map
	)

	// 遍历每一条数据
	for _, data := range dataList {
		snVal, exist := data.Data.Get(snField)
		sn := utils.ToString(snVal)
		if !exist || strings.TrimSpace(sn) == "" {
			// 如果说压根没有sn字段，那就不用同步了
			color.Red(data.Name() + " 没有sn字段")
			doNotHaveSN = append(doNotHaveSN, data.Name())
			continue
		}

		// 有sn号
		haveSN = append(haveSN, sn)
		local.Set(strings.ToUpper(sn), data)
	}

	// rms的接口的qps在20以内，建议一次拉去2000 ～ 5000条数据, 所以超过5000条就直接返回
	if len(haveSN) > 5000 {
		color.Red("查询数量过多，建议减少到5000以内")
		return nil
	}

	conds := make([]Cond, 0)
	conds = append(conds, Cond{
		Key:     "sn",
		Operate: "in",
		Value:   haveSN,
	})

	resp, err := searchRmsBySN(conds)
	if err != nil {
		return err
	}

	for _, r := range resp.Data {
		rms.Set(utils.ToString(r["sn"]), r)
	}

	fmt.Printf("* cmdb中共有%s的数据%d条\n", modelCode, len(dataList))
	fmt.Printf("* 其中有sn的数据有%d条\n", len(haveSN))
	fmt.Printf("* 未配置sn的数据有%d条\n", len(doNotHaveSN))
	fmt.Printf("* rms可以查到的有%d条\n", len(resp.Data))

	overdue := make([]string, 0)
	for _, d := range resp.Data {
		if utils.ToString(d["overdueStatus"]) == "过保" {
			overdue = append(overdue, utils.ToString(d["sn"]))
		}
		continue
	}

	fmt.Printf("* rms中可查的这%d条数据中, 过保率为: %.2f\n", len(resp.Data), float64(len(overdue))/float64(len(resp.Data))*100)

	for _, rd := range resp.Data {
		sn := utils.ToString(rd["sn"])
		if sn == "" {
			continue
		}
		cmdbData, _ := local.Get(strings.ToUpper(sn))
		cd, ok := cmdbData.(v1.ModelData)
		if !ok {
			color.Red("断言失败, sn为%s", sn)
			continue
		}

		// 已经拿过维保信息的没必要重新拿
		sts := utils.ToString(cd.Data[ServerOverDueStatus])
		if sts != "" {
			continue
		}

		dueTime := utils.ToString(rd["overdueTime"])
		dueStatus := ""

		switch utils.ToString(rd["overdueStatus"]) {
		case "过保":
			dueStatus = "过保"
		case "正常":
			dueStatus = "在保"
		default:
			dueStatus = "未知"
		}

		if err := mgo.GetCollection("model_data").UpdateOne(
			ctx,
			bson.M{"model_code": modelCode, "_id": cd.ID},
			bson.M{"$set": bson.M{"data." + ServerOverDueTime: dueTime, "data." + ServerOverDueStatus: dueStatus}}); err != nil {
			color.Red("%s更新过保信息失败, 错误信息为%s\n", cd.Name(), err.Error())
			continue
		}

		color.Green("%s更新过保信息成功\n", cd.Name())
	}

	return nil
}
