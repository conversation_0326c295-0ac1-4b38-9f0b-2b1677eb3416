package rms

type Cond struct {
	Key     string `json:"key"`
	Operate string `json:"operate"`
	Value   any    `json:"value"`
}

type RmsRequest struct {
	Operator  string `json:"operator"`
	Type      string `json:"type"`
	Page      int    `json:"page"`
	PageSize  int    `json:"pageSize"`
	LastID    int    `json:"lastId"`
	Fields    string `json:"fields"`
	Condition []Cond `json:"condition"`
}

type RmsResponse struct {
	Status      int              `json:"status"`
	ErrorMsg    string           `json:"error_msg"`
	Total       int              `json:"total"`
	LastID      int              `json:"lastId"`
	Data        []map[string]any `json:"data"`
	Success     bool             `json:"success"`
	NotSuccess  bool             `json:"notSuccess"`
	SystemError bool             `json:"systemError"`
}
