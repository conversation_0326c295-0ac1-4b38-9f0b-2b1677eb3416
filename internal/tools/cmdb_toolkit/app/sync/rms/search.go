package rms

import (
	"errors"
	"net/http"

	"ks-knoc-server/internal/common/rest"
	"ks-knoc-server/internal/tools/cmdb_toolkit/app/util"

	"github.com/spf13/viper"
)

func searchRmsBySN(conditions []Cond) (resp *RmsResponse, err error) {
	// 我们的调用并不会持续那么长时间，因此可以暂时先不用关注token是否会过期。
	if token == "" {
		ak := viper.GetString("openapi.prod.ak")
		sk := viper.GetString("openapi.prod.sk")
		ep := viper.GetString("openapi.prod.ep")
		tk, err := util.GetToken(ep, ak, sk)
		if err != nil {
			return nil, err
		}

		if tk == "" {
			return nil, errors.New("get token error")
		}

		token = tk
	}

	// 构造requests
	req := rest.NewClient("is-gateway.corp.kuaishou.com", "/ms-scm/api/scm/deploy/asset-server/v1/query/server")
	fields := "sn,hostname,type,nodes,idcName,overdueTime,overdueStatus,belongTo,arrivalTime,nodes,suitName,kwaiTypeName"

	body := &RmsRequest{
		Operator:  "rms",    // 固定值
		Type:      "detail", // 固定值
		Page:      -1,       // 页码，-1表示不分页，单值查询的时候，不分页的效率更高
		PageSize:  -1,       // 分页大小，-1表示不分页，单值查询，不分页更好
		Fields:    fields,
		Condition: conditions,
	}

	requestObj := req.Post().Body(body)
	requestObj.WithHeaders(http.Header{"Authorization": []string{token}})
	if err := requestObj.Do().Into(&resp); err != nil {
		return nil, err
	}

	// Status不等于0的话，说明
	if resp.Status != 0 {
		return nil, errors.New(resp.ErrorMsg)
	}

	// resp.Data的长度为0，说明没查到
	if len(resp.Data) == 0 {
		return nil, errors.New("没有数据匹配" + resp.ErrorMsg)
	}

	return resp, nil
}
