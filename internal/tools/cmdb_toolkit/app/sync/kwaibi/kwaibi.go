package kwaibi

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
	"gorm.io/gorm"
)

var (
	ctx context.Context
	mgo *db.MongoOptions
)

type KwaiBI struct {
	ID            int    `gorm:"column:id"`          // ID
	DataID        string `gorm:"column:data_id"`     // 数据ID
	DeviceType    string `gorm:"column:device_type"` // 设备类型
	DeviceSN      string `gorm:"column:device_sn"`   // 设备SN
	DeviceBrand   string `gorm:"column:device_brand"`
	DeviceModel   string `gorm:"column:device_model"`
	Area          string `gorm:"column:area"`
	Office        string `gorm:"column:office"`
	OverDueStatus string `gorm:"column:overdue_status"`
	OverDueTime   string `gorm:"column:overdue_time"`
	OnRack        string `gorm:"column:on_rack"`
	Owner         string `gorm:"column:owner"`
	Depart        string `gorm:"column:depart"`
	CreateTime    int64  `gorm:"column:create_time"`
	UpdateTime    int64  `gorm:"column:update_time"`
}

func (k *KwaiBI) Equal(target *KwaiBI) ([]string, bool) {
	diff := make([]string, 0)
	if k.DataID != target.DataID {
		diff = append(diff, fmt.Sprintf("data_id: %s -> %s", k.DataID, target.DataID))
	}
	if k.DeviceType != target.DeviceType {
		diff = append(diff, fmt.Sprintf("device_type: %s -> %s", k.DeviceType, target.DeviceType))
	}
	if k.DeviceSN != target.DeviceSN {
		diff = append(diff, fmt.Sprintf("device_sn: %s -> %s", k.DeviceSN, target.DeviceSN))
	}
	if k.DeviceBrand != target.DeviceBrand {
		diff = append(diff, fmt.Sprintf("device_brand: %s -> %s", k.DeviceBrand, target.DeviceBrand))
	}
	if k.DeviceModel != target.DeviceModel {
		diff = append(diff, fmt.Sprintf("device_model: %s -> %s", k.DeviceModel, target.DeviceModel))
	}
	if k.Area != target.Area {
		diff = append(diff, fmt.Sprintf("area: %s -> %s", k.Area, target.Area))
	}
	if k.Office != target.Office {
		diff = append(diff, fmt.Sprintf("office: %s -> %s", k.Office, target.Office))
	}
	if k.OverDueStatus != target.OverDueStatus {
		diff = append(diff, fmt.Sprintf("overdue_status: %s -> %s", k.OverDueStatus, target.OverDueStatus))
	}
	if k.OverDueTime != target.OverDueTime {
		diff = append(diff, fmt.Sprintf("overdue_time: %s -> %s", k.OverDueTime, target.OverDueTime))
	}
	if k.OnRack != target.OnRack {
		diff = append(diff, fmt.Sprintf("on_rack: %s -> %s", k.OnRack, target.OnRack))
	}
	if k.Owner != target.Owner {
		diff = append(diff, fmt.Sprintf("owner: %s -> %s", k.Owner, target.Owner))
	}
	if k.Depart != target.Depart {
		diff = append(diff, fmt.Sprintf("depart: %s -> %s", k.Depart, target.Depart))
	}
	return diff, len(diff) == 0
}

func initMysql() (*gorm.DB, error) {
	return db.NewMySQLOptions().Init()
}

func initMongo() error {
	var err error
	mgo, err = db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	return nil
}

func SyncDataToKwaiBI() error {
	// 初始化mongodb连接
	if err := initMongo(); err != nil {
		log.Printf("获取MongoClient失败: %s\n", err.Error())
		return ErrConnectMongoDB
	}
	defer mgo.Close()

	// 初始化mongodb
	colModelData := mgo.GetCollection("model_data")

	// 初始化mysql
	kdb, err := initMysql()
	if err != nil {
		log.Fatalf("获取KDB失败: %s", err.Error())
		return err
	}

	// 目前先写死要同步的设备类型
	validModels := []string{"server", "switch", "ap", "wlc", "firewall", "transmission", "loadbalancing"}
	validModelDatas := make([]*v1.ModelData, 0)
	if err := colModelData.Find(ctx, bson.M{"model_code": bson.M{"$in": validModels}}).All(&validModelDatas); err != nil {
		log.Fatalf("查询model_data失败: %s", err.Error())
		return err
	}

	// 初始化要同步的kwaiBI数据列表
	newData := make([]*KwaiBI, 0)
	for _, data := range validModelDatas {
		now := time.Now().Unix()
		modelCode := data.ModelCode

		// 拼接各种字段
		snField := fmt.Sprintf("%s_sn", modelCode)
		brandField := fmt.Sprintf("%s_brand", modelCode)
		modelField := fmt.Sprintf("%s_model", modelCode)
		overDueStatusField := fmt.Sprintf("%s_overdue_status", modelCode)
		overDueTimeField := fmt.Sprintf("%s_overdue_time", modelCode)
		onRackField := fmt.Sprintf("%s_status", modelCode)
		sn := utils.ToString(data.Data[snField])
		brand := utils.ToString(data.Data[brandField])
		model := utils.ToString(data.Data[modelField])
		overDueStatus := func() string {
			status := utils.ToString(data.Data[overDueStatusField])
			if status == "过保" {
				return "过保"
			} else if status == "在保" {
				return "在保"
			} else {
				// 代表未知的状态
				return "未知"
			}
		}()
		overDueTime := utils.ToString(data.Data[overDueTimeField])
		onRackStatus := utils.ToString(data.Data[onRackField])

		bi := new(KwaiBI)
		bi.CreateTime = now
		bi.UpdateTime = now
		bi.DataID = data.ID
		bi.DeviceType = data.ModelCode
		bi.DeviceSN = sn
		bi.DeviceBrand = brand
		bi.OnRack = onRackStatus // 这里主要指的是设备状态
		bi.DeviceModel = model
		bi.OverDueStatus = overDueStatus
		bi.OverDueTime = overDueTime

		// 把sn为空的都给滤掉
		if sn == "" {
			continue
		}

		newData = append(newData, bi)
	}

	create := func(data any) error {
		return kdb.Table("cmdb_data").Create(data).Error
	}

	fmt.Printf("[Info] 开始同步%d条数据\n", len(newData))
	updated, created, scanNum := 0, 0, 0
	for _, data := range newData {
		// 用于统计扫描计数
		scanNum++
		percentage := int((float64(scanNum) / float64(len(newData))) * 100)
		// 先查询一下在不在，如果在的还就不创建了
		exist := new(KwaiBI)
		// 先查一下对应的数据在不在
		result := kdb.Table("cmdb_data").Where("device_sn = ?", data.DeviceSN).First(exist)
		// 如果查询失败(不包含Record不存在的情况)
		if result.Error != nil {
			// 有一种可能是数据不存在
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				// 这种情况下就创建数据就好了
				if err := create(data); err != nil {
					return err
				}
				created++
				fmt.Printf("[Scan %d%%] 插入数据成功: %s\n", percentage, data.DeviceSN)
				continue
			} else {
				// 其他情况就返回错误
				return result.Error
			}
		}

		// 能走到这里来说明没报什么错，查到这个数据了,接下来要做的事情是判断数据有没有更新
		diff, eq := exist.Equal(data)
		if eq {
			continue
		} else {
			for _, df := range diff {
				fmt.Println(df)
			}
			// 统一刷一遍数据即可
			exist.DataID = data.DataID
			exist.DeviceType = data.DeviceType
			exist.DeviceBrand = data.DeviceBrand
			exist.DeviceModel = data.DeviceModel
			exist.Area = data.Area
			exist.Office = data.Office
			exist.OverDueStatus = data.OverDueStatus
			exist.OverDueTime = data.OverDueTime
			exist.Owner = data.Owner
			exist.Depart = data.Depart
			exist.OnRack = data.OnRack
			exist.UpdateTime = time.Now().Unix()
			exist.DeviceSN = data.DeviceSN

			// 保存数据
			if tx := kdb.Table("cmdb_data").Save(exist); tx.Error != nil {
				log.Printf(tx.Error.Error()+", data Value is: %v\n", data)
				return tx.Error
			}

			color.Green("[Scan %d%%] 更新数据: %s\n", percentage, data.DeviceSN)
			updated++
		}
	}
	fmt.Printf("[Info] 同步完成，更新%d条数据，创建%d条数据\n", updated, created)
	return nil
}

func NewSyncToKwaiBI() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "kwaibi",
		Short: "同步cmdb数据到kwaibi的kdb数据库",
		RunE: func(cmd *cobra.Command, args []string) error {
			return SyncDataToKwaiBI()
		},
	}
	return cmd
}
