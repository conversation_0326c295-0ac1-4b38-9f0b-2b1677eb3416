package app

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"

	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

func GenerateRandomMac() string {
	rand.Seed(time.Now().UnixNano())
	mac := make([]string, 6)
	for i := 0; i < 6; i++ {
		// 生成0-255之间的随机数
		b := rand.Intn(256)
		// 将随机数转换为两位十六进制字符串
		mac[i] = fmt.Sprintf("%02x", b)
	}
	return strings.Join(mac, "-")
}

func UpdateFieldValue() error {
	updateModelCode := "server"
	updateModelField := "server_status"

	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	// 查询出对应模型的所有数据
	var dataList = make([]*v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{"model_code": updateModelCode}).All(&dataList); err != nil {
		return err
	}

	for _, d := range dataList {
		fieldValue := utils.ToString(d.Data[updateModelField])
		// 如果字段本来就是空的，那我就可以先不用关心
		if fieldValue == "" {
			continue
		}
		if err := mgo.GetCollection("model_data").UpdateOne(
			ctx, bson.M{"_id": d.ID}, bson.M{"$set": bson.M{"data." + updateModelField: ""}}); err != nil {
			return err
		}
		log.Printf("更新数据%s的字段%s的值为空", d.ID, updateModelField)
	}

	return nil
}

func NewUpdateDataField() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "field",
		Short: "更新Data Field",
		RunE: func(cmd *cobra.Command, args []string) error {
			return UpdateFieldValue()
		},
	}
	return cmd
}

// ShowModelGroupTree 展示模型分组树
func ShowModelGroupTree() {
	// 初始化MongoDB Client
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return
	}

	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	var modelGroupList = make([]*v1.ModelGroup, 0)
	if err := mgo.GetCollection("model_group").Find(ctx, bson.M{}).All(&modelGroupList); err != nil {
		log.Fatalf(err.Error())
		return
	}
	for _, mg := range modelGroupList {
		var modelList = make([]*v1.Model, 0)
		if err := mgo.GetCollection("model").Find(ctx, bson.M{"model_group": mg.Code}).All(&modelList); err != nil {
			log.Fatalf(err.Error())
			return
		}
		fmt.Printf("模型分组字段标识: %s, 名称: %s\n", mg.Code, mg.Name)
		fmt.Println("|")
		for _, m := range modelList {
			fmt.Printf("├── 模型字段标识: %s, 名称: %s\n", m.Code, m.Name)
		}
	}
}
