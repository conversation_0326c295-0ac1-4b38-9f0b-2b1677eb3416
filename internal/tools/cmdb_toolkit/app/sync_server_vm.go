package app

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/array"

	"github.com/fatih/color"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// SyncKVMRelationByVM 同步vm和宿主机之间的从属关系
// 以vm的角度来维护，根据vm的vm_host字段来查询对应的宿主机
// 后续关系字段的展示完善后，本方法则不再需要，即可废弃
func SyncKVMRelationByVM() error {
	// 初始化log的部分属性
	log.SetFlags(log.Lshortfile | log.LstdFlags)

	// 初始化mongodb的链接
	ctx := context.Background()
	mongoClient, err := db.NewMongoOptions().Init()
	if err != nil {
		log.Fatalf("获取MongoClient失败: %s", err.Error())
	}
	colModelData := mongoClient.GetCollection("model_data")

	// 先把要打的标签给查出来，目前就先把machine_type:vm 这个标签给打了
	key := new(v1.LabelKey)
	if err := mongoClient.GetCollection("label_key").Find(ctx, bson.M{"name": "machine_type"}).One(key); err != nil {
		log.Println(err.Error())
		return err
	}

	if key == nil {
		return errors.New("label_key表中不存在machine_type标签")
	}

	value := new(v1.LabelValue)
	if err := mongoClient.GetCollection("label_value").Find(ctx, bson.M{"key_id": key.ID, "value": "vm"}).One(value); err != nil {
		log.Println(err.Error())
		return err
	}

	if value == nil {
		return errors.New("label_value表中不存在machine_type:vm标签")
	}

	svcKey := new(v1.LabelKey)
	if err := mongoClient.GetCollection("label_key").Find(ctx, bson.M{"name": "service"}).One(svcKey); err != nil {
		log.Println(err.Error())
		return err
	}

	if key == nil {
		return errors.New("label_key表中不存在service标签")
	}

	kvmValue := new(v1.LabelValue)
	if err := mongoClient.GetCollection("label_value").Find(ctx, bson.M{"key_id": svcKey.ID, "value": "kvm"}).One(kvmValue); err != nil {
		log.Println(err.Error())
		return err
	}

	if kvmValue == nil {
		return errors.New("label_value表中不存在machine_type:vm标签")
	}

	vmList := make([]*v1.ModelData, 0)
	if err := colModelData.Find(ctx, bson.M{"model_code": "vm"}).All(&vmList); err != nil {
		log.Println(err.Error())
		return err
	}

	if len(vmList) == 0 {
		return errors.New("vm表中没有vm数据")
	}

	// 用一个map来缓存数据，避免频繁请求数据库
	kvmMapping := make(map[string]*v1.ModelData)

	// 保存要更新的数据列表
	updatedVmList := make([]*v1.ModelData, 0)

	// 保存一下所有的报错信息
	var errorsList = make([]error, 0)

	// 遍历所有的vm进行操作，因为当前是以vm视角来维护宿主机和虚拟机之间关系的
	for _, vm := range vmList {
		vmHostIpAddress, exist := vm.Data.Get("vm_host")
		if !exist {
			errorsList = append(errorsList, errors.New(vm.Name()+"的vm_host字段不存在，请处理"))
			continue
		}
		hostIPAddr := vmHostIpAddress.(string)
		if hostIPAddr == "" {
			errorsList = append(errorsList, errors.New(vm.Name()+"的vm_host字段为空，请处理"))
			continue
		}
		// 查询对应的宿主机存不存在
		var (
			kvm = new(v1.ModelData)
			ok  bool
		)
		kvm, ok = kvmMapping[hostIPAddr]
		if !ok {
			if err := colModelData.Find(ctx, bson.M{"model_code": "server", "data.server_internal_ip": hostIPAddr}).One(&kvm); err != nil {
				if errors.Is(err, mongo.ErrNoDocuments) {
					errorsList = append(errorsList, errors.New(vm.Name()+"对应的宿主机"+hostIPAddr+"不存在，请处理"))
					continue
				} else {
					log.Println(err.Error())
					return err
				}
			}
			// 验证一下宿主机的标签中是否存在service:kvm标签
			kvmLabels := array.StringArray(kvm.LabelIDList)
			if !kvmLabels.InArray(kvmValue.ID) {
				errorsList = append(errorsList, errors.New(kvm.Name()+"的标签中不存在service:kvm标签"))
			}
			// 放到map中缓存以供后续循环使用
			kvmMapping[hostIPAddr] = kvm
		}

		if kvm == nil {
			errorsList = append(errorsList, errors.New(vm.Name()+"对应的宿主机"+hostIPAddr+"不存在，请处理"))
			continue
		}

		// 把关系给加上，如果说已经设置过Parent了，那么就先不用设置了
		if vm.ParentID != "" {
			// 不为空，说明之前设置过从属权限，但是我们需要检查一下是否一致
			if vm.ParentID != kvm.ID {
				errorsList = append(errorsList, errors.New(vm.Name()+"的主从关系错误，请处理"))
			}

		} else {
			vm.ParentID = kvm.ID
			vm.ParentDesc = fmt.Sprintf("宿主机:%s", kvm.Name())
			// 把vm的标签给加上
			vmLabels := array.StringArray(vm.LabelIDList)
			newLabel := array.StringArray{value.ID}
			add := vmLabels.ArrayDiff(newLabel)
			if len(add) > 0 {
				vm.LabelIDList = append(vm.LabelIDList, add...)
			}
			updatedVmList = append(updatedVmList, vm)
		}
	}

	if len(errorsList) != 0 {
		for _, err := range errorsList {
			color.Red(err.Error())
		}
		return errors.New("请处理以上错误")
	}

	fmt.Println("[Info] 更新vm数据的主从关系")

	// 更新vm数据
	if len(updatedVmList) > 0 {
		now := time.Now().Unix() // 刷新更新日期
		bulk := colModelData.Bulk()
		for _, d := range updatedVmList {
			bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{"$set": bson.M{
				"label_id_list": d.LabelIDList,
				"update_at":     now,
				"parent_id":     d.ParentID,
				"parent_desc":   d.ParentDesc},
			})
		}
		_, err := bulk.Run(ctx)
		if err != nil {
			color.Red("[Failed] 更新失败，异常原因为: " + err.Error())
			return err
		}
		color.Green(fmt.Sprintf("[%d] 条vm数据的主从关系更新完成", len(updatedVmList)))
	} else {
		color.Yellow(fmt.Sprintf("[Pass] 没有需要更新的vm数据"))
	}
	return nil
}
