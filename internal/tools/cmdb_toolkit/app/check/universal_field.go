package check

import (
	"context"
	"fmt"
	"log"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/mapdata"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

var universalFieldSuffix = mapdata.MapData{
	"sn":             "序列号",
	"in_ip":          "带内管理IP",
	"out_ip":         "带外管理IP",
	"status":         "设备状态",
	"belong_to":      "设备归属",
	"overdue_status": "设备过保状态",
	"brand":          "设备品牌",
	"model":          "设备型号",
	"owner":          "设备负责人",
	"internal_ip":    "内网IP",
	"public_ip":      "外网IP",
	"name":           "名称",
	"asset_id":       "资产编号",
}

func NewUniversalField() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "universal-field",
		Short: "检查通用字段有没有被正确设置, 比如本该是通用字段，但是字段的通用属性为false就需要被修正",
		RunE: func(cmd *cobra.Command, args []string) error {
			return checkUniversalField()
		},
	}

	return cmd
}

// 检查通用字段是否被正确设置
func checkUniversalField() error {
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		log.Println(err)
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			color.Red("关闭mongo失败: %s", err.Error())
		}
	}()

	// 先获取到所有的模型
	modelList := make([]*v1.Model, 0)
	if err := mgo.GetCollection("model").Find(ctx, bson.M{}).All(&modelList); err != nil {
		return err
	}

	// 遍历每一个模型，然后拿到这个模型所有的属性
	for _, model := range modelList {
		// 拼接通用字段的全名
		universalFieldFullNameList := array.StringArray{}
		// 拼接通用字段的全名
		for suffix := range universalFieldSuffix {
			universalFieldFullNameList = append(universalFieldFullNameList, fmt.Sprintf("%s_%s", model.Code, suffix))
		}
		// 获取到这个模型所有的属性
		attrList := make([]*v1.CommonModelAttribute, 0)
		if err := mgo.GetCollection("model_attr").Find(ctx, bson.M{"model_code": model.Code}).All(&attrList); err != nil {
			return err
		}
		// 遍历每一个属性，然后检查这个属性是否是通用属性
		for _, attr := range attrList {
			// 如果这个属性在通用字段的全名列表中，那么就检查这个属性是否是通用属性
			// 如果这个属性在universalFieldFullNameList中，那么这个属性就是通用属性，我们需要检查的是这个属性是否被正确配置了
			if array.InArray(attr.Code, universalFieldFullNameList) {
				// 如果这个属性是通用属性，但是实际为false，那么就修正这个属性为通用属性
				if !attr.Universal {
					// 修正这个属性为通用属性
					if err := mgo.GetCollection("model_attr").UpdateOne(
						ctx,
						bson.M{"code": attr.Code},
						bson.M{"$set": bson.M{"universal": true}}); err != nil {
						return err
					}
					color.Green("[更新] 模型 %s 的属性 %s 在通用字段列表中，但是不是通用属性，已修正\n", model.Code, attr.Code)
				}
			}
		}

	}
	return nil
}
