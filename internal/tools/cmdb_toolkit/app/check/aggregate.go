package check

import (
	"context"
	"fmt"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"log"
	"time"

	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

func NewAggregateDataCountByMonth() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "aggregate-data-count-by-month",
		Short: "按月统计数据增量",
		RunE: func(cmd *cobra.Command, args []string) error {
			return aggModelDataByMonth()
		},
	}

	return cmd
}

func normalizeTimestamp(timestamp int64) int64 {
	// 如果是纳秒级时间戳（19位）
	if timestamp > 1e18 {
		return timestamp / 1e9
	}
	// 如果是毫秒级时间戳（13位）
	if timestamp > 1e12 {
		return timestamp / 1000
	}
	// 如果是秒级时间戳（10位），直接返回
	return timestamp
}

func aggModelDataByMonth() error {
	// 初始化mongodb连接
	ctx := context.Background()
	mongoClient, err := db.NewMongoOptions().Init()
	if err != nil {
		log.Fatalf("获取MongoClient失败: %s", err.Error())
	}
	defer func() {
		_ = mongoClient.Close()
	}()
	// 初始化mongodb
	dataList := make([]*v1.ModelData, 0)
	if err := mongoClient.GetCollection("model_data").Find(ctx, bson.M{}).All(&dataList); err != nil {
		log.Fatalf("查询model_data失败: %s", err.Error())
		return err
	}
	dataCountMap := make(map[int]map[int]int)
	for _, data := range dataList {
		createTime := data.CreateAt
		createTime = normalizeTimestamp(createTime)

		timeObj := time.Unix(createTime, 0)
		year := timeObj.Year()
		month := timeObj.Month()
		// 首先看有没有年份，如果连年份都没有，那么说明月份也没有
		if _, ok := dataCountMap[year]; !ok {
			dataCountMap[year] = make(map[int]int)
			dataCountMap[year][int(month)] = 1
			continue
		}
		// 如果年份有，但是月份没有，那么说明月份也没有
		if _, ok := dataCountMap[year][int(month)]; !ok {
			dataCountMap[year][int(month)] = 1
			continue
		}
		// 如果年份有，月份也有，那么直接加1
		dataCountMap[year][int(month)]++
	}

	// 输出结果
	for year, monthMap := range dataCountMap {
		for month, count := range monthMap {
			fmt.Printf("%d-%d: %d\n", year, month, count)
		}
	}
	return nil
}
