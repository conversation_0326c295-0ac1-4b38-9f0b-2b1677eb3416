package check

import (
	"context"
	"fmt"
	"log"
	"strings"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

func getDeviceWithoutSN() error {
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	validModels := []string{"server", "switch", "wlc", "firewall", "transmission", "loadbalancing"}
	modelDatas := make([]*v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
		"model_code": bson.M{"$in": validModels},
	}).All(&modelDatas); err != nil {
		return err
	}

	for _, modelData := range modelDatas {
		snField := fmt.Sprintf("%s_sn", modelData.ModelCode)
		snFieldValue := strings.ToUpper(utils.ToString(modelData.Data[snField]))
		if snFieldValue == "" {
			color.Red("sn为空, 模型为: %s 名称为: %s", modelData.ModelCode, modelData.Name())
			continue
		}
	}

	return nil
}

func NewDeviceWithoutSN() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "without-sn",
		Short: "没写sn的",
		RunE: func(cmd *cobra.Command, args []string) error {
			return getDeviceWithoutSN()
		},
	}

	return cmd
}

func check(command *cobra.Command, args []string) {
	_ = command.Help()
}

func NewCheckCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "check",
		Short: "数据合规检测",
		Run:   check,
	}

	cmd.AddCommand(NewDeviceWithoutSN())
	cmd.AddCommand(NewUniversalField())
	cmd.AddCommand(NewAggregateDataCountByMonth())

	return cmd
}
