package clean

import (
	"context"
	"fmt"

	baseV1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"
	"log"
	"strings"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

var GlobalSNMap = make(map[string]*baseV1.ModelData)

// CorrectSN 修正设备的SN
func CorrectSN() error {
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	validModels := []string{"server", "switch", "wlc", "firewall", "transmission", "loadbalancing"}
	modelDatas := make([]*baseV1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
		"model_code": bson.M{"$in": validModels},
	}).All(&modelDatas); err != nil {
		return err
	}

	toBeDeleteID := make([]string, 0)
	for _, modelData := range modelDatas {
		snField := fmt.Sprintf("%s_sn", modelData.ModelCode)
		snFieldValue := strings.ToUpper(utils.ToString(modelData.Data[snField]))
		if snFieldValue == "" {
			color.Red("sn为空, 模型为: %s 名称为: %s", modelData.ModelCode, modelData.Name())
			continue
		}
		// 从map中取出数据
		data, ok := GlobalSNMap[snFieldValue]
		if !ok {
			GlobalSNMap[snFieldValue] = modelData
		} else {
			color.Red("Sn冲突，sn编号为: %s", snFieldValue)
			// source代表原来录入的，target代表后来录入的重复的
			source, target := new(baseV1.ModelData), new(baseV1.ModelData)
			apNameSections := strings.Split(modelData.Name(), "-")
			// 首先分割的话长度至少要大于等于2吧
			if len(apNameSections) >= 2 {
				// 然后还得再看末尾的是不是和sn能对上。
				snSuffix := strings.ToUpper(apNameSections[len(apNameSections)-1])
				if snSuffix == snFieldValue {
					// 如果这里相等了，说明这个是我后来用excel导入的，主机名是拼接的，是后来者
					target = modelData
					source = data
				} else {
					target = data
					source = modelData
				}
			} else {
				target = data
				source = modelData
			}
			toBeDeleteID = append(toBeDeleteID, target.ID)

			// 我的目标是要删除后来更新的，因为后更新的数据没有区分大小写，导致重复录入。
			var (
				updateFields = make(bson.M)
			)
			for k, _ := range target.Data {
				// 如果说原来的老的数据本来就有值的话，那么就按照老的值走
				sourceValue, exist := source.Data[k]
				// 有可能有的字段本来数据就不存在，这个时候要赋值，所以先得判断字段存不存在
				if exist {
					// 如果存在，再判断是不是空，如果不为空，就用老的值，不进行更新
					if utils.ToString(sourceValue) != "" {
						if k == snField {
							newSN := strings.ToUpper(utils.ToString(source.Data[snField]))
							if utils.ToString(source.Data[snField]) == newSN {
								continue
							}
							updateFields["data."+snField] = newSN
							color.Yellow("覆盖: %s: %s -> %s", snField, utils.ToString(source.Data["ap_sn"]), newSN)
						}
						continue
					} else {
						// 如果为空，则需要用新值覆盖旧址，前提是新值也不为空
						if utils.ToString(target.Data[k]) != "" {
							updateFields["data."+k] = utils.ToString(target.Data[k])
							color.Yellow("覆盖: %s: 旧[%s] -> 新[%s]", utils.ToString(k), utils.ToString(sourceValue), utils.ToString(target.Data[k]))
						}
					}
				} else {
					if utils.ToString(target.Data[k]) != "" {
						updateFields["data."+k] = utils.ToString(target.Data[k])
						color.Yellow("覆盖: %s: 旧[%s] -> 新[%s]", utils.ToString(k), utils.ToString(sourceValue), utils.ToString(target.Data[k]))
					}
				}
			}
			// if len(updateFields) > 0 {
			// 	result, err := mgo.GetCollection("model_data").UpdateAll(ctx, bson.M{"_id": source.ID}, bson.M{"$set": updateFields})
			// 	if err != nil {
			// 		return err
			// 	}
			// 	color.Green("更新成功: %s, result: %d", source.ID, result.UpsertedCount)
			// }
		}
	}
	return nil
}

func NewCorrectSN() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "correct-sn",
		Short: "修正设备的SN",
		RunE: func(cmd *cobra.Command, args []string) error {
			return CorrectSN()
		},
	}
	return cmd
}
