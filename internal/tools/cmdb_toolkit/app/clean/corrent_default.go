package clean

import (
	"context"
	"fmt"
	"log"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/utils"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

// NewCorrectDefault 修正默认值, 比如设备状态，设备过保状态，设备归属，设备负责人，设备品牌，设备型号
// 主要用户机房退租后，一些设备退库，变成了在库旧的状态，但是设备本身还有IP地址，主机名，和机柜的关联
// 这个时候，需要将这些信息修正为默认值
func NewCorrectDefault() *cobra.Command {
	var m string
	cmd := &cobra.Command{
		Use:   "correct-default",
		Short: "修正默认值",
		RunE: func(cmd *cobra.Command, args []string) error {
			return CorrectDefault(m)
		},
	}
	// 我们每次都要指定要修正的模型的code
	cmd.Flags().StringVarP(&m, "model_code", "m", "", "模型标识")
	_ = cmd.MarkFlagRequired("model_code")
	return cmd
}

// CorrectDefault 修正默认值
func CorrectDefault(modelCode string) error {
	if !array.InArray(modelCode, []string{"server", "switch", "router", "firewall", "wlc", "storage", "loadbalancing",
		"audit", "transimission", "netflow"}) {
		return fmt.Errorf("模型Code不支持: %s, 目前只支持server, switch, router, firewall, wlc, storage, "+
			"loadbalancing, audit, transimission, netflow", modelCode)
	}

	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	// 先获取到对应模型所有的数据
	modelDataList := make([]*v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{"model_code": modelCode}).All(&modelDataList); err != nil {
		return err
	}

	// 获取到对应模型的状态字段
	statusFieldName := fmt.Sprintf("%s_status", modelCode)

	// 遍历所有的数据，然后修正默认值
	toUpdateModelData := make(map[string]bson.M)
	for _, modelData := range modelDataList {
		setter := bson.M{}
		// 修正设备状态
		devStatus, ok := modelData.Data[statusFieldName]
		if !ok {
			color.Red("设备状态字段不存在: %s", modelData.Name())
			continue
		}
		// 将interface转换为string
		deviceStatus := utils.ToString(devStatus)
		// 只有符合如下条件的字段信息需要重置
		if deviceStatus == "在库旧" || deviceStatus == "在库新" {
			// 先修正名称, 默认得名称应该为 设备品牌-设备型号
			brand, ok := modelData.Data[modelCode+"_brand"]
			if !ok {
				color.Red("设备品牌字段不存在: %s", modelData.Name())
				continue
			}
			if utils.ToString(brand) == "" {
				color.Red("设备品牌字段为空: %s", modelData.Name())
				continue
			}
			model, ok := modelData.Data[modelCode+"_model"]
			if !ok {
				color.Red("设备型号字段不存在: %s", modelData.Name())
				continue
			}
			if utils.ToString(model) == "" {
				color.Red("设备型号字段为空: %s", modelData.Name())
				continue
			}
			name := fmt.Sprintf("%s-%s", brand, model)
			// 如果名称不一致，则修正名称，唯一标识是不需要修正的
			if utils.ToString(modelData.Data[modelCode+"_name"]) != name {
				setter["data."+modelCode+"_name"] = name
			}
			// 所属机柜需要修正, 我们需要把机柜置空
			if modelData.ParentID != "" {
				setter["parent_id"] = ""
			}
			// 修正设备负责人
			if v, ok := modelData.Data[modelCode+"_owner"]; ok {
				if utils.ToString(v) == "" {
					setter["data."+modelCode+"_owner"] = "itwarehouse"
				} else if utils.ToString(v) != "itwarehouse" {
					setter["data."+modelCode+"_owner"] = "itwarehouse"
				}
			} else {
				setter["data."+modelCode+"_owner"] = "itwarehouse"
			}
			// 修正起始U位
			if v, ok := modelData.Data[modelCode+"_start_u"]; ok {
				if utils.ToString(v) == "" {
					setter["data."+modelCode+"_start_u"] = "0"
				} else if utils.ToString(v) != "0" {
					setter["data."+modelCode+"_start_u"] = "0"
				}
			} else {
				setter["data."+modelCode+"_start_u"] = "0"
			}
			// 修正带内IP
			if v, ok := modelData.Data[modelCode+"_in_ip"]; ok && utils.ToString(v) != "" {
				setter["data."+modelCode+"_in_ip"] = ""
			}
			// 修正带外IP
			if v, ok := modelData.Data[modelCode+"_out_ip"]; ok && utils.ToString(v) != "" {
				setter["data."+modelCode+"_out_ip"] = ""
			}
			// 修正内网IP地址
			if v, ok := modelData.Data[modelCode+"_internal_ip"]; ok && utils.ToString(v) != "" {
				setter["data."+modelCode+"_internal_ip"] = ""
			}
		}
		// 如果需要修正，则将数据添加到待更新列表中
		if len(setter) > 0 {
			toUpdateModelData[modelData.ID] = setter
		}
	}

	if len(toUpdateModelData) == 0 {
		color.Yellow("没有需要更新的数据")
		return nil
	}

	// 批量更新数据
	bulk := mgo.GetCollection("model_data").Bulk()
	for k, v := range toUpdateModelData {
		fmt.Println("setter: ", v)
		bulk.UpdateId(k, bson.M{"$set": v})
	}
	if ret, err := bulk.Run(ctx); err != nil {
		color.Red(err.Error())
	} else {
		color.Yellow("更新数据成功: %d", ret.ModifiedCount)
	}

	return nil
}
