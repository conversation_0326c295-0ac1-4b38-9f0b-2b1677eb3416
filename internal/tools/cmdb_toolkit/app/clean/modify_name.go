package clean

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/hostname"
	"ks-knoc-server/pkg/utils"
	"log"
	"os"
	"strings"

	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func NewModifyName() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "modify-name",
		Short: "修改资源名称",
		RunE: func(cmd *cobra.Command, args []string) error {
			return modifyName()
		},
	}
	return cmd
}

type cmdbDevice struct {
	sn   string
	name string
	ip   string
}

// modifyName 修改资源名称
// 读取一个修改的文本文件，文本文件主要为三列，第一列是要修改的名称，第二列是SN序列号，第三列为IP
// 其中SN这一列是唯一标识，用来匹配资源，类似资源的ID
// 对应的文件要放到和执行程序一个目录下
func modifyName() error {
	deviceMap := make(map[string]cmdbDevice)
	// 读取txt文本
	content, err := os.ReadFile("modify.txt")
	if err != nil {
		return err
	}

	buf := bytes.NewBuffer(content)
	reader := bufio.NewReader(buf)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			break
		}
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		fields := strings.Fields(line)
		if len(fields) != 3 {
			log.Println("文件格式错误", line, "切片长度为", len(fields))
			continue
		}
		sn := fields[1]
		name := fields[0]
		ip := fields[2]
		deviceMap[sn] = cmdbDevice{
			sn:   sn,
			name: name,
			ip:   ip,
		}
	}

	// 初始化MongoDB Client
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	updateConunter := 0
	// 挨个处理每一台设备
	for _, v := range deviceMap {
		if _, err := hostname.IsValidHostname(v.name); err != nil {
			log.Println("主机名不合规", v.name, err)
			if err := hostname.DebugHostname(v.name); err != nil {
				log.Println(err.Error())
			}
			continue
		}

		modelData := v1.ModelData{}
		if err := mgo.GetCollection("model_data").Find(ctx, bson.M{"meta_data.universal_sn": v.sn}).One(&modelData); err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				log.Println("对应的SN不存在", v.sn)
				continue
			}
			log.Println("获取模型数据失败", v.sn, err)
			continue
		}

		// 要更新的几个字段
		update := false
		name := fmt.Sprintf("%s_name", modelData.ModelCode)
		hostname := fmt.Sprintf("%s_hostname", modelData.ModelCode)
		ipField := fmt.Sprintf("%s_in_ip", modelData.ModelCode)

		// 更新数据
		oldInIP := utils.ToString(modelData.Data[ipField])
		if oldInIP == "" {
			oldInIP = "空值"
		}
		if oldInIP != v.ip && oldInIP != "空值" {
			log.Printf("设备IP字段%s需要更新 %s -> %s\n", ipField, utils.ToString(oldInIP), v.ip)
			update = true
		}
		oldName := utils.ToString(modelData.Data[name])
		if oldName != v.name {
			log.Printf("设备名称字段%s需要更新 %s -> %s\n", name, oldName, v.name)
			update = true
		}
		oldHostname := utils.ToString(modelData.Data[hostname])
		if oldHostname != v.name {
			log.Printf("设备主机名字段%s需要更新 %s -> %s\n", hostname, oldHostname, v.name)
			update = true
		}

		if !update {
			continue
		}

		if err := mgo.GetCollection("model_data").UpdateOne(
			ctx,
			bson.M{"_id": modelData.ID},
			bson.M{"$set": bson.M{
				"data." + name:     v.name,
				"data." + hostname: v.name,
				"data." + ipField:  v.ip,
			}},
		); err != nil {
			return err
		}

		updateConunter++
	}

	log.Printf("共更新数据%d条", updateConunter)

	return nil
}
