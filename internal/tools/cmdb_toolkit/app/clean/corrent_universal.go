package clean

import (
	"context"
	"fmt"

	svcV1 "ks-knoc-server/internal/cmdbserver/service/v1"
	baseV1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"
	"log"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

// correctUniversalField 修正通用字段
// 主要的功能是更新老旧数据库，将字段是通用，但是信息并没有保存到metadata中的这种情况修正
func correctUniversalField() error {
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Println(err)
		}
	}()

	// 获取所有的模型
	modelList := make([]*baseV1.Model, 0)
	if err := mgo.GetCollection("model").Find(ctx, bson.M{}).All(&modelList); err != nil {
		return err
	}

	// 遍历所有的模型，然后处理每一个模型的通用字段
	for _, m := range modelList {
		color.Green("######################################")
		color.Green("开始处理模型: %s (%s)\n", m.Name, m.Code)

		// 首先拿到模型所有的字段
		attrList := make([]*baseV1.CommonModelAttribute, 0)
		// 我们要查到模型所有的属性，但是不包含关系型字段
		if err := mgo.GetCollection("model_attr").Find(
			ctx, 
			bson.M{"model_code": m.Code, "type_name": bson.M{"$ne": "relationship"},
			}).All(&attrList); err != nil {
			return err
		}
		// 遍历所有的字段，看一下字段是不是通用的
		for _, attr := range attrList {
			// 只针对通用字段进行处理，会处理所有的通用字段
			if attr.Universal {
				log.Printf("处理字段: %s\n", attr.Name)
				// 把这个模型下的所有数据都查出来
				dataList := make([]*baseV1.ModelData, 0)
				if err := mgo.GetCollection("model_data").Find(ctx, bson.M{"model_code": m.Code}).All(&dataList); err != nil {
					return err
				}
				// 构建通用字段名
				universalFieldName := svcV1.GetUniversalFieldName(attr.Code, m.Code)
				// 先准备一个容器用来保存，哪些数据需要更新
				updatedData := make([]*baseV1.ModelData, 0)
				// 遍历所有的数据
				for _, d := range dataList {
					// 首先根据原始数据判断有没有必要进行更新。
					dataValue, exist := d.Data.Get(attr.Code)
					if !exist {
						// 说明原数据中本来就没有配置这个字段，说明这种情况可能是后创建的字段，那这个时候，metadata也没必要做更新
						continue
					}

					// 如果说字段存在，但是字段也有可能为null，所以在字段存在的基础上要进一步判断具体的值
					dataValueString := utils.ToString(dataValue)
					if dataValueString == "" {
						// 说明原数据中这个字段是空的，本来就是空字符串，或者是nil，这个时候也没必要更新
						continue
					}

					// 到这里说明原本数据中包含这个字段，且不是空值，这个时候判断一下metadata中的值是不是对的。
					// meda_data中的字段要等于data中的字段的值，以data字段中的值为准
					v, exist := d.MetaData.Get(universalFieldName)
					// 只有下面这两种情况需要更新data数据，其他的情况则不需要更新data
					if !exist {
						// 说明之前没这个字段，需要更新
						if d.MetaData == nil {
							d.MetaData = make(mapdata.MapData)
						}
						// 更新metadata中的值，值以data字段中对应字段的值为准
						d.MetaData.Set(universalFieldName, dataValueString)
						// 将这条数据插入到对应的更新列表中
						updatedData = append(updatedData, d)
						continue
					} else {
						// 说明之前有这个字段，但是需要对比一下值是不是一致的
						if utils.ToString(v) != dataValueString {
							// 不一致，需要更新
							d.MetaData.Set(universalFieldName, dataValueString)
							updatedData = append(updatedData, d)
						}
					}
				}
				// 遍历完所有数据后，我要把数据更新回数据库, 如果说没有需要更新的字段的话，那么直接跳过。遍历下一个字段
				if len(updatedData) == 0 {
					color.Yellow("没有需要更新的数据: %s", attr.Name)
					continue
				}
				// 批量更新数据更高效
				bulk := mgo.GetCollection("model_data").Bulk()
				for _, u := range updatedData {
					setter := bson.M{}
					for k, v := range u.MetaData {
						setter[k] = v
					}
					fmt.Println("setter: ", setter)
					bulk.UpdateId(u.ID, bson.M{"$set": bson.M{"meta_data": setter}})
				}
				if ret, err := bulk.Run(ctx); err != nil {
					color.Red(err.Error())
				} else {
					color.Yellow("更新数据成功: %d", ret.ModifiedCount)
				}
			}
		}
	}

	return nil
}

// NewCorrectUniversalField 修正通用字段, 这种属于一次性的刷新
func NewCorrectUniversalField() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "correct-universal-field",
		Short: "修正通用字段",
		RunE: func(cmd *cobra.Command, args []string) error {
			return correctUniversalField()
		},
	}

	return cmd
}
