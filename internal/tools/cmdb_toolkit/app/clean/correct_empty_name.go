package clean

import (
	"context"
	"fmt"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"log"
	"strings"

	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

func correctEmptyName() error {
	// 初始化MongoDB Client
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	dataAll := make([]v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{}).All(&dataAll); err != nil {
		return err
	}

	modelAll := make([]v1.Model, 0)
	if err := mgo.GetCollection("model").Find(ctx, bson.M{}).All(&modelAll); err != nil {
		return err
	}

	modelMap := make(map[string]v1.Model)
	for _, m := range modelAll {
		modelMap[m.Code] = m
	}

	emptyNameData := make([][3]string, 0)
	for _, d := range dataAll {
		if strings.TrimSpace(d.Name()) != "" {
			continue
		}

		model, exist := modelMap[d.ModelCode]
		if !exist {
			log.Println("模型不存在", d.ModelCode)
			continue
		}
		deviceType := model.Name
		deviceBrand, exist := d.Data.Get(fmt.Sprintf("%s_brand", model.Code))
		if !exist {
			log.Println("品牌不存在", d.ModelCode)
			continue
		}

		deviceModel, exist := d.Data.Get(fmt.Sprintf("%s_model", model.Code))
		if !exist {
			log.Println("型号不存在", d.ModelCode)
			continue
		}

		deviceStatus, exist := d.Data.Get(fmt.Sprintf("%s_status", model.Code))
		if !exist {
			log.Println("状态不存在", d.ModelCode)
			continue
		}

		// 拼接一个新的名称
		newName := fmt.Sprintf("%s-%s-%s-%s", deviceType, deviceBrand, deviceModel, deviceStatus)
		emptyNameData = append(emptyNameData, [3]string{d.ID, d.ModelCode, newName})
	}

	if len(emptyNameData) > 0 {
		log.Println("共需要更新", len(emptyNameData), "条数据")
		for _, d := range emptyNameData {
			dataID := d[0]
			dataModelCode := d[1]
			dataName := d[2]
			log.Println("更新", dataID, dataName)
			nameField := fmt.Sprintf("%s_name", dataModelCode)
			if err := mgo.GetCollection("model_data").
				UpdateOne(
					ctx,
					bson.M{"_id": dataID},
					bson.M{"$set": bson.M{"data." + nameField: dataName}},
				); err != nil {
				return err
			}
		}
		log.Println("更新完成")
	} else {
		log.Println("没有资源名称为空的数据，无需更新")
	}

	return nil
}

func NewCorrectEmptyName() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "correct-empty-name",
		Short: "修正资源名称为空的数据",
		RunE: func(cmd *cobra.Command, args []string) error {
			return correctEmptyName()
		},
	}
	return cmd
}
