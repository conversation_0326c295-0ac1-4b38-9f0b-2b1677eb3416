package clean

import (
	"context"
	"errors"
	"fmt"
	baseV1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/pkg/utils"
	"log"
	"strings"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// CopyFieldValue 复制某个模型下面的字段到另一个字段，因为本质上data字段的值最终都是字符串，所以直接复制就行
// Tip: 注意，copy仅限同一个数据不同字段之间的内容复制，不支持跨模型复制
func CopyFieldValue(modelCode, sourceSuffix, targetSuffix string, ignoreNoneExistField bool) error {
	// 构造新老两个字段的名称
	oldFieldName := fmt.Sprintf("%s_%s", modelCode, sourceSuffix)
	newFieldName := fmt.Sprintf("%s_%s", modelCode, targetSuffix)

	// 初始化MongoDB Client
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	// 为了避免出现垃圾字段，所以先检查一下源目的字段是否真实存在, 前提是ignoreNoneExistField为true
	if !ignoreNoneExistField {
		if err := mgo.GetCollection("model_attr").Find(ctx, bson.M{"code": oldFieldName}).One(&baseV1.CommonModelAttribute{}); err != nil {
			return err
		}
		if err := mgo.GetCollection("model_attr").Find(ctx, bson.M{"code": newFieldName}).One(&baseV1.CommonModelAttribute{}); err != nil {
			return err
		}
	}

	// 查询出对应模型的所有数据
	var dataList = make([]*baseV1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
		"model_code": modelCode,
	}).All(&dataList); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			color.Red("模型为 %s 的数据不存在", modelCode)
			return err
		}
	}

	// 遍历每一条数据开始赋值
	for _, d := range dataList {
		// 如果老的字段本来就是空的话，那么就不用管了
		oldValue, exist := d.Data[oldFieldName]
		// 源字段根本不存在，说明有可能写错了
		if !exist {
			color.Yellow("老字段 %s 不存在", oldFieldName)
			continue
		}
		// 判断老的字段是不是空的如果是空，那么就不用管了
		ov := strings.TrimSpace(utils.ToString(oldValue))
		if ov == "" {
			log.Printf("源字段%s的值为空，无需复制字段, 跳过", oldFieldName)
			continue
		}

		// 看看对应的新字段存不存在
		newValue, exist := d.Data[newFieldName]
		nv := strings.TrimSpace(utils.ToString(newValue))

		// 打一个标记用来标记是否要进行更新
		update := false

		// 新字段不存在，直接赋值
		if exist {
			if nv == "" {
				// 这种情况的话，和取不到new字段其实效果是一样的，所以直接赋值
				update = true
			} else {
				// 这种情况下新字段存在且有值，这个时候我们得看看需不需要刷新这个值
				if nv == ov {
					// 说明二者本来已经相等了，也没必要更新了
					continue
				} else {
					// 不相等的话，说明需要用旧值覆盖掉新值
					update = true
				}
			}
		} else {
			// 如果不存在的话，直接赋值就可以了
			update = true
		}

		// 把老字段的值赋值给新字段
		if update {
			if err := mgo.GetCollection("model_data").UpdateOne(ctx, bson.M{"_id": d.ID}, bson.M{
				"$set": bson.M{
					"data." + newFieldName: oldValue,
				},
			}); err != nil {
				return err
			}
		}

		color.Green("成功复制字段: %s:%s -> %s:%s", oldFieldName, oldValue, newFieldName, oldValue)
	}
	return nil
}

// NewCopyField 复制字段的值, 将数据中字段a的值复制到字段b
func NewCopyField() *cobra.Command {
	// 定义命令行参数
	var (
		modelCode, sourceSuffix, targetSuffix string
		// 这个字段存在的目的是，有的时候可能老的字段已经被认为删除了，但是data字段还存在
		// 这个时候我仍然有需求要把老字段的值复制到新字段，可以使用这个字段，该字段默认为false
		ignoreNoneExistField bool
	)

	cmd := &cobra.Command{
		Use:   "copy-field",
		Short: "复制字段的值",
		Long:  "复制字段值, 目前仅针对同模型实例间的复制",
		RunE: func(cmd *cobra.Command, args []string) error {
			return CopyFieldValue(modelCode, sourceSuffix, targetSuffix, ignoreNoneExistField)
		},
	}

	// 设置字段值为必填
	cmd.Flags().StringVarP(&modelCode, "model_code", "m", "", "模型标识")
	cmd.Flags().StringVarP(&sourceSuffix, "source", "s", "", "源字段后缀")
	cmd.Flags().StringVarP(&targetSuffix, "target", "t", "", "目标字段后缀")
	cmd.Flags().BoolVarP(&ignoreNoneExistField, "ignore_none_exist", "n", false, "是否忽略不存在字段")
	_ = cmd.MarkFlagRequired("model_code")
	_ = cmd.MarkFlagRequired("source")
	_ = cmd.MarkFlagRequired("target")

	return cmd
}
