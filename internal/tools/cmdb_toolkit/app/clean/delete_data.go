package clean

import (
	"context"
	"ks-knoc-server/internal/common/db"
	"log"
	"strings"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

func deleteData(ids string) error {
	idList := strings.Split(ids, ",")
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	for _, dataId := range idList {
		if strings.TrimSpace(dataId) == "" {
			continue
		}
		if err := mgo.GetCollection("model_data").Remove(ctx, bson.M{"_id": dataId}); err != nil {
			return err
		}

		color.Green("删除数据成功: %s", dataId)
	}

	return nil
}

func NewDeleteModelData() *cobra.Command {
	var ids string
	cmd := &cobra.Command{
		Use:   "delete-data",
		Short: "删除模型数据",
		RunE: func(cmd *cobra.Command, args []string) error {
			return deleteData(ids)
		},
	}

	cmd.Flags().StringVarP(&ids, "ids", "i", "", "数据ID")
	_ = cmd.MarkFlagRequired("ids")
	return cmd
}
