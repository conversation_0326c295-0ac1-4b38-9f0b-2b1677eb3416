package clean

import "github.com/spf13/cobra"

func clean(command *cobra.Command, args []string) {
	_ = command.Help()
}

func NewCleanCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "clean",
		Short: "清理资源",
		Run:   clean,
	}

	cmd.AddCommand(NewCorrectSN())
	cmd.AddCommand(NewCopyField())
	cmd.AddCommand(NewDeleteModelData())
	cmd.AddCommand(NewUnsetField())
	cmd.AddCommand(NewCorrectUniversalField())
	cmd.AddCommand(NewCorrectDefault())
	cmd.AddCommand(NewCorrectEmptyName())
	cmd.AddCommand(NewModifyName())
	return cmd
}
