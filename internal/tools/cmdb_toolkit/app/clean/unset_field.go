package clean

import (
	"context"
	"fmt"
	baseV1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"log"

	"github.com/fatih/color"
	"github.com/spf13/cobra"
	"go.mongodb.org/mongo-driver/bson"
)

// unsetField 删除字段, 用户删除数据中不必要的字段
// 注意这里删除也是删除的data里面的字段，而不是其他的字段。
func unsetField(modelCode, fieldSuffix string) error {
	ctx := context.Background()
	mgo, err := db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		if err := mgo.Close(); err != nil {
			log.Print(err)
		}
	}()

	// 查询出对应模型的所有数据
	dataList := make([]*baseV1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{"model_code": modelCode}).All(&dataList); err != nil {
		return err
	}

	for _, d := range dataList {
		// 构造要删除的字段名称
		deletedFieldName := fmt.Sprintf("%s_%s", modelCode, fieldSuffix)
		// 检查字段是否存在，如果说不存在也就无所谓删不删除了，直接跳过就是了
		if _, exist := d.Data[deletedFieldName]; !exist {
			continue
		}
		// 如果存在的话，那么就需要删除，使用的是mongodb提供的unset api来进行删除
		if err := mgo.GetCollection("model_data").UpdateOne(ctx, bson.M{"_id": d.ID}, bson.M{
			"$unset": bson.M{"data." + deletedFieldName: 1},
		}); err != nil {
			return err
		}
		color.Green("删除字段成功: %s, DataID is: %s", deletedFieldName, d.ID)
	}
	return nil
}

// NewUnsetField 初始化字段删除命令
func NewUnsetField() *cobra.Command {
	var (
		modelCode, fieldSuffix string
	)
	cmd := &cobra.Command{
		Use:   "unset-field",
		Short: "删除字段",
		RunE: func(cmd *cobra.Command, args []string) error {
			return unsetField(modelCode, fieldSuffix)
		},
	}

	cmd.Flags().StringVarP(&modelCode, "model_code", "m", "", "模型标识")
	cmd.Flags().StringVarP(&fieldSuffix, "field", "f", "", "字段后缀")
	_ = cmd.MarkFlagRequired("model_code")
	_ = cmd.MarkFlagRequired("field")

	return cmd
}
