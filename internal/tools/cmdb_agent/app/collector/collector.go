package collector

import (
	"context"
	"sync"
	"time"

	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector"

	"github.com/pkg/errors"
)

// AgentCollector 管理所有采集任务
type AgentCollector struct {
	manager *collector.CollectorManager
	mu      sync.RWMutex
}

// NewAgentCollector 创建一个新的采集器
func NewAgentCollector() *AgentCollector {
	return &AgentCollector{
		manager: collector.NewCollectorManager(),
	}
}

// RegisterTask 注册采集任务
func (a *AgentCollector) RegisterCollector(collector collector.Collector) {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.manager.Tasks = append(a.manager.Tasks, collector)
}

func (a *AgentCollector) GetCollectors() []collector.Collector {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.manager.Tasks
}

// Run 运行所有采集任务
// 如果interval为0，则只执行一次
func (a *AgentCollector) Run(ctx context.Context, interval time.Duration) error {
	if interval == 0 {
		return a.collect()
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			if err := a.collect(); err != nil {
				return errors.Wrap(err, "collect failed")
			}
		}
	}
}

// collect 执行一次采集
func (a *AgentCollector) collect() error {
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, task := range a.manager.Tasks {
		if !task.ShouldExecute() {
			continue
		}
		if err := task.Execute(); err != nil {
			return errors.Wrapf(err, "task %s execute failed", task.GetTaskName())
		}
	}
	return nil
}
