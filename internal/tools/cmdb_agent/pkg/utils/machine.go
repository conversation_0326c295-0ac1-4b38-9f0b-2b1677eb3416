package utils

import (
	"os"
	"os/exec"
	"runtime"
	"slices"
	"strings"

	"go.uber.org/zap"
)

// IsPhysicalMachine 判断是否为物理机
func IsPhysicalMachine() bool {
	switch runtime.GOOS {
	case "linux":
		return isPhysicalMachineLinuxSimple()
	case "windows":
		return isPhysicalMachineWindowsSimple()
	default:
		return false
	}
}

// isPhysicalMachineLinuxSimple Linux系统下简单判断是否为物理机
func isPhysicalMachineLinuxSimple() bool {
	// 方法1: 检查 /proc/cpuinfo 中的虚拟化标志
	cpuinfo, err := os.ReadFile("/proc/cpuinfo")
	if err == nil {
		// 如果包含"hypervisor"标志，则为虚拟机
		if strings.Contains(string(cpuinfo), "hypervisor") {
			return false
		}
	}

	zap.L().Debug("检查CPU不包含hypervisor标志")

	// 方法2: 检查 dmidecode 命令输出
	cmd := exec.Command("dmidecode", "-s", "system-manufacturer")
	output, err := cmd.Output()
	if err == nil {
		manufacturer := strings.TrimSpace(string(output))
		// 检查制造商是否为常见的虚拟化提供商
		vmVendors := []string{"VMware", "QEMU", "Xen", "innotek", "Microsoft", "Parallels"}
		if slices.Contains(vmVendors, manufacturer) {
			zap.L().Debug("检查制造商是否为常见的虚拟化提供商", zap.String("manufacturer", manufacturer))
			return false
		} else {
			zap.L().Debug("非常见的虚拟化提供商", zap.String("manufacturer", manufacturer))
		}
	}

	zap.L().Debug("设备为物理机")

	// 如果以上检查都未通过，则认为是物理机
	return true
}

// isPhysicalMachineWindowsSimple Windows系统下简单判断是否为物理机
func isPhysicalMachineWindowsSimple() bool {
	// 使用systeminfo命令获取系统信息，更轻量级
	cmd := exec.Command("systeminfo", "/fo", "list")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	sysinfo := string(output)

	// 检查系统信息中是否包含虚拟机相关字符串
	vmIdentifiers := []string{
		"VMware",
		"VirtualBox",
		"Hyper-V",
		"KVM",
		"Virtual Machine",
		"QEMU",
	}

	for _, identifier := range vmIdentifiers {
		if strings.Contains(sysinfo, identifier) {
			return false
		}
	}

	return true
}
