package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/allegro/bigcache/v3"
	"go.uber.org/zap"

	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector"
)

type CacheType string

func (c CacheType) String() string {
	return string(c)
}

const (
	// 缓存项类型, 与collector的GetTaskName()返回值一致
	CacheTypeSystem   CacheType = "system_info"
	CacheTypeNetwork  CacheType = "network_info"
	CacheTypeProcess  CacheType = "process_info"
	CacheTypeKVM      CacheType = "kvm_info"
	CacheTypeDisk     CacheType = "disk_info"
	CacheTypeMemory   CacheType = "memory_info"
	CacheTypeOptical  CacheType = "optical_module_info"
	CacheTypeHardware CacheType = "hardware_info"
)

func GetAllCacheTypes() []CacheType {
	return []CacheType{
		CacheTypeSystem,
		CacheTypeNetwork,
		CacheTypeProcess,
		<PERSON>acheTypeKVM,
		CacheTypeDisk,
		<PERSON>acheTypeMemory,
		<PERSON>acheTypeOptical,
		CacheTypeHardware,
	}
}

// 缓存项包含数据和过期时间
type CacheItem struct {
	Data       any       `json:"data"`
	ExpireTime time.Time `json:"expire_time"`
}

// CacheManager 缓存管理器
type CacheManager struct {
	cacheLock sync.RWMutex // 缓存读写锁
	cache     *bigcache.BigCache
	callbacks map[CacheType]func() (collector.Collector, error) // 数据采集回调函数
	cacheKeys map[CacheType]struct{}
}

func (m *CacheManager) GetCallback(cacheType CacheType) func() (collector.Collector, error) {
	m.cacheLock.RLock()
	defer m.cacheLock.RUnlock()
	callback, exists := m.callbacks[cacheType]
	if !exists {
		return nil
	}
	return callback
}

// NewCacheManager 创建新的缓存管理器
func NewCacheManager() (*CacheManager, error) {
	// 使用内存存储缓存数据
	config := bigcache.DefaultConfig(24 * time.Hour)
	config.CleanWindow = 10 * time.Minute
	config.HardMaxCacheSize = 128 // 128MB

	cache, err := bigcache.New(context.Background(), config)
	if err != nil {
		return nil, err
	}

	return &CacheManager{
		cacheLock: sync.RWMutex{},
		cache:     cache,
		callbacks: make(map[CacheType]func() (collector.Collector, error)),
	}, nil
}

// 全局缓存管理器实例
var (
	defaultManager *CacheManager
	once           sync.Once
)

// GetCacheManager 获取全局缓存管理器实例
func GetCacheManager() *CacheManager {
	once.Do(func() {
		var err error
		defaultManager, err = NewCacheManager()
		if err != nil {
			log.Fatalf("Failed to create cache manager: %v", err)
		}
	})
	return defaultManager
}

// RegisterCallback 注册数据采集回调函数
func (m *CacheManager) RegisterCallback(cacheType CacheType, callback func() (collector.Collector, error)) {
	m.cacheLock.Lock()
	defer m.cacheLock.Unlock()
	m.callbacks[cacheType] = callback
}

// Get 获取指定类型的缓存数据
func (m *CacheManager) Get(cacheType CacheType) ([]byte, error) {
	// BigCache的Get方法本身是线程安全的，不需要额外加锁
	data, err := m.cache.Get(cacheType.String())

	// 如果缓存存在的话，返回对应的数据
	if err == nil && len(data) > 0 {
		return data, nil
	}

	// 缓存不存在，需要获取回调函数
	// 只对callbacks映射进行读锁保护
	m.cacheLock.RLock()
	callback, exists := m.callbacks[cacheType]
	m.cacheLock.RUnlock()

	// 如果回调函数不存在，返回错误
	if !exists {
		zap.L().Error("未找到缓存类型 [%s] 的采集器", zap.String("cacheType", cacheType.String()))
		return nil, fmt.Errorf("no callback registered for cache type: %s", cacheType)
	}

	// 执行回调获取新数据
	newData, err := callback()
	if err != nil {
		zap.L().Error("执行采集器失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
		return nil, err
	}

	// 更新结果
	dataCopy, err := json.Marshal(newData)
	if err != nil {
		zap.L().Error("序列化数据失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
		return nil, err
	}

	// 更新缓存 - BigCache的Set方法本身是线程安全的
	if err := m.Set(cacheType, dataCopy); err != nil {
		zap.L().Error("更新缓存失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
	}

	return dataCopy, nil
}

// Set 设置缓存数据
func (m *CacheManager) Set(cacheType CacheType, data []byte) error {
	// BigCache的Set方法本身是线程安全的，不需要额外加锁
	if err := m.cache.Set(cacheType.String(), data); err != nil {
		zap.L().Error("更新缓存失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
		return err
	}

	// 只对cacheKeys映射进行写锁保护
	m.cacheLock.Lock()
	defer m.cacheLock.Unlock()
	if len(m.cacheKeys) == 0 {
		m.cacheKeys = make(map[CacheType]struct{})
	}
	m.cacheKeys[cacheType] = struct{}{}
	return nil
}

func (m *CacheManager) GetAll() []string {
	m.cacheLock.RLock()
	defer m.cacheLock.RUnlock()

	keys := make([]string, 0, len(m.cacheKeys))
	for key := range m.cacheKeys {
		keys = append(keys, key.String())
	}
	return keys
}

// Clear 清除指定类型的缓存
func (m *CacheManager) Clear(cacheType CacheType) error {
	m.cacheLock.Lock()
	defer m.cacheLock.Unlock()
	return m.cache.Delete(cacheType.String())
}

// StartBackgroundRefresh 启动后台刷新任务
func (m *CacheManager) StartBackgroundRefresh(ctx context.Context) {
	// 创建不同周期的刷新器
	highFrequencyTicker := time.NewTicker(30 * time.Second) // 高频刷新 - 30秒
	lowFrequencyTicker := time.NewTicker(24 * time.Hour)    // 低频刷新 - 24小时

	go func() {
		defer highFrequencyTicker.Stop()
		defer lowFrequencyTicker.Stop()

		for {
			select {
			case <-ctx.Done():
				return

			case <-highFrequencyTicker.C:
				// 刷新高频缓存项
				m.refreshCache(CacheTypeSystem)
				m.refreshCache(CacheTypeNetwork)
				m.refreshCache(CacheTypeProcess)

			case <-lowFrequencyTicker.C:
				// 刷新低频缓存项
				m.refreshCache(CacheTypeKVM)
				m.refreshCache(CacheTypeDisk)
				m.refreshCache(CacheTypeMemory)
				m.refreshCache(CacheTypeOptical)
				m.refreshCache(CacheTypeHardware)
			}
		}
	}()
}

// refreshCache 刷新指定类型的缓存
func (m *CacheManager) refreshCache(cacheType CacheType) {
	// 只对callbacks映射进行读锁保护
	m.cacheLock.RLock()
	callback, exists := m.callbacks[cacheType]
	m.cacheLock.RUnlock()

	if !exists {
		zap.L().Error("未找到缓存类型 [%s] 的采集器", zap.String("cacheType", cacheType.String()))
		return
	}

	// 执行回调获取新数据
	newData, err := callback()
	if err != nil {
		zap.L().Error("执行采集器失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
		return
	}

	// 序列化数据
	dataCopy, err := json.Marshal(newData)
	if err != nil {
		zap.L().Error("序列化数据失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
		return
	}

	// 更新缓存
	if err := m.Set(cacheType, dataCopy); err != nil {
		zap.L().Error("更新缓存失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
		return
	}

	zap.L().Debug("缓存刷新成功", zap.String("cacheType", cacheType.String()), zap.String("data", string(dataCopy)))
}

// 初始化所有缓存数据
func InitializeCache(cacheManager *CacheManager) error {
	// 按顺序初始化各类缓存
	cts := GetAllCacheTypes()
	zap.L().Debug("获取所有Cache类型", zap.Any("cacheTypes", cts))

	// 遍历初始化数据
	for _, cacheType := range cts {
		// 尝试从缓存获取数据
		data, err := cacheManager.Get(cacheType)
		if err != nil {
			// 如果缓存获取失败，执行对应的采集器
			zap.L().Error("初始化缓存失败", zap.String("cacheType", cacheType.String()), zap.Error(err))
			continue
		}
		zap.L().Debug("首次初始化缓存成功", zap.String("cacheType", cacheType.String()), zap.String("data", string(data)))
	}

	return nil
}

// Close 关闭缓存管理器和BigCache
func (m *CacheManager) Close() error {
	m.cacheLock.Lock()
	defer m.cacheLock.Unlock()

	if m.cache != nil {
		return m.cache.Close()
	}
	return nil
}
