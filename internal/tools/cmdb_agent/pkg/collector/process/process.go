package process

import (
	"fmt"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

func printProcessInfo(collector *ProcessInfoCollector) {
	fmt.Printf("\n=== 进程信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	if len(collector.Processes) == 0 {
		fmt.Printf("未检测到任何进程\n")
	} else {
		fmt.Printf("\n进程列表:\n")

		// 特别输出HIDS进程状态
		if hidsInfo, exists := collector.Processes["hidsagent"]; exists {
			fmt.Printf("HIDS Agent:\n")
			fmt.Printf("  状态: %v\n", hidsInfo.Status)
			if hidsInfo.Status {
				fmt.Printf("  进程ID: %s\n", hidsInfo.PID)
			}
		}

		// 其他进程信息可以在这里输出
	}

	fmt.Printf("========================\n\n")
}

// ProcessInfo 存储进程的信息
type ProcessInfo struct {
	Name   string `json:"name"`
	Status bool   `json:"status"`
	PID    string `json:"pid"`
}

// ProcessChecker 进程检查器接口
type ProcessChecker interface {
	CheckProcess(name string) (ProcessInfo, error)
}

// LinuxProcessChecker Linux系统的进程检查器
type LinuxProcessChecker struct{}

// CheckProcess 检查Linux系统上的进程状态
func (c *LinuxProcessChecker) CheckProcess(name string) (ProcessInfo, error) {
	result := ProcessInfo{
		Name:   name,
		Status: false,
	}

	// 使用ps命令查找进程
	cmd := exec.Command("ps", "aux")
	output, err := cmd.Output()
	if err != nil {
		return result, err
	}

	// 分析输出结果
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		// 检查行是否包含进程名
		if strings.Contains(line, name) {
			// 排除grep自身
			if !strings.Contains(line, "grep") {
				result.Status = true
				// 尝试提取PID
				fields := strings.Fields(line)
				if len(fields) > 1 {
					result.PID = fields[1]
				}
				break
			}
		}
	}

	return result, nil
}

// WindowsProcessChecker Windows系统的进程检查器
type WindowsProcessChecker struct{}

// CheckProcess 检查Windows系统上的进程状态
func (c *WindowsProcessChecker) CheckProcess(name string) (ProcessInfo, error) {
	result := ProcessInfo{
		Name:   name,
		Status: false,
	}

	// 使用tasklist命令查找进程
	cmd := exec.Command("tasklist", "/FI", "IMAGENAME eq "+name+".exe")
	output, err := cmd.Output()
	if err != nil {
		return result, err
	}

	// 分析输出结果
	// 如果找到进程，输出中会包含进程名
	if strings.Contains(string(output), name+".exe") {
		result.Status = true
		// 尝试提取PID
		lines := strings.Split(string(output), "\n")
		for i, line := range lines {
			if i > 0 && strings.Contains(line, name) {
				fields := strings.Fields(line)
				if len(fields) > 1 {
					result.PID = fields[1]
				}
				break
			}
		}
	}

	return result, nil
}

// NewProcessChecker 创建适合当前操作系统的进程检查器
func NewProcessChecker() ProcessChecker {
	if strings.ToLower(runtime.GOOS) == "windows" {
		return &WindowsProcessChecker{}
	}
	return &LinuxProcessChecker{}
}

// ProcessInfoCollector 进程信息采集器
type ProcessInfoCollector struct {
	Processes map[string]ProcessInfo
}

// 初始化进程采集器
func NewProcessInfoCollector() *ProcessInfoCollector {
	return &ProcessInfoCollector{
		Processes: make(map[string]ProcessInfo),
	}
}

func (p *ProcessInfoCollector) Show() {
	printProcessInfo(p)
}

// Execute 执行进程信息采集
func (p *ProcessInfoCollector) Execute() error {
	// 创建进程检查器
	checker := NewProcessChecker()

	// 检查hidsagent进程
	hidsInfo, err := checker.CheckProcess("hidsagent")
	if err == nil {
		p.Processes["hidsagent"] = hidsInfo
	} else {
		p.Processes["hidsagent"] = ProcessInfo{
			Name:   "hidsagent",
			Status: false,
		}
	}

	// 可以根据需要添加更多进程的检查

	return nil
}

// GetTaskName 获取任务名称
func (p *ProcessInfoCollector) GetTaskName() string {
	return "process_info"
}

// GetTaskType 获取任务类型
func (p *ProcessInfoCollector) GetTaskType() string {
	return "process"
}

// ShouldExecute 是否应该执行采集
func (p *ProcessInfoCollector) ShouldExecute() bool {
	return true
}

// PreCheck 预检测工具依赖是否满足
func (p *ProcessInfoCollector) PreCheck() []string {
	var missingTools []string

	switch runtime.GOOS {
	case "linux":
		// 检查ps命令
		_, err := exec.LookPath("ps")
		if err != nil {
			missingTools = append(missingTools, "ps")
		}

	case "windows":
		// 检查tasklist命令
		_, err := exec.LookPath("tasklist")
		if err != nil {
			missingTools = append(missingTools, "tasklist")
		}
	}

	return missingTools
}
