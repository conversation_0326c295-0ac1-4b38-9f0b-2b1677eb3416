package components

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"
)

func printOpticalModuleInfo(collector *OpticalModuleCollector) {
	fmt.Printf("\n=== 光模块信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	if len(collector.Optical.Modules) == 0 {
		fmt.Printf("未检测到光模块\n")
	} else {
		fmt.Printf("\n光模块列表:\n")
		for i, module := range collector.Optical.Modules {
			fmt.Printf("模块 %d:\n", i+1)
			fmt.Printf("  接口: %s\n", module.Interface)

			if module.Type != "" {
				fmt.Printf("  类型: %s\n", module.Type)
			}

			if module.Vendor != "" {
				fmt.Printf("  厂商: %s\n", module.Vendor)
			}

			if module.PartNumber != "" {
				fmt.Printf("  部件号: %s\n", module.PartNumber)
			}

			if module.SerialNumber != "" {
				fmt.Printf("  序列号: %s\n", module.SerialNumber)
			}

			if module.Wavelength != "" {
				fmt.Printf("  波长: %s\n", module.Wavelength)
			}

			if module.Status != "" {
				fmt.Printf("  状态: %s\n", module.Status)
			}
		}
	}

	fmt.Printf("========================\n\n")
}

// OpticalModule 表示单个光模块信息
type OpticalModule struct {
	Interface    string `json:"interface"`     // 接口名称，如eth0
	Type         string `json:"type"`          // 模块类型，如SFP, SFP+, QSFP
	Vendor       string `json:"vendor"`        // 制造商
	PartNumber   string `json:"part_number"`   // 部件号
	SerialNumber string `json:"serial_number"` // 序列号
	Wavelength   string `json:"wavelength"`    // 波长
	Status       string `json:"status"`        // 状态
}

// OpticalModuleInfo 表示所有光模块信息
type OpticalModuleInfo struct {
	Modules []OpticalModule `json:"modules"` // 光模块列表
}

// OpticalModuleCollector 采集光模块信息
type OpticalModuleCollector struct {
	Optical OpticalModuleInfo `json:"optical"`
}

func (o *OpticalModuleCollector) Show() {
	printOpticalModuleInfo(o)
}

// Execute 执行光模块信息采集
func (c *OpticalModuleCollector) Execute() error {
	switch runtime.GOOS {
	case "linux":
		return c.collectLinux()
	case "windows":
		return c.collectWindows()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

func (c *OpticalModuleCollector) IsVirtualInterface(iface string) bool {
	// 虚拟接口的特征
	virtualInterfaces := []string{
		"vir",     // 虚拟机接口
		"docker",  // docker接口
		"br-",     // 桥接接口
		"bond",    // 绑定接口
		"veth",    // 虚拟以太网接口
		"vlan",    // 虚拟局域网接口
		"wg",      // 虚拟专用网络接口
		"tun",     // 隧道接口
		"tap",     // 隧道接口
		"vnet",    // 虚拟网络接口
		"kube",    // kubernetes接口
		"cni",     // cni接口
		"cali",    // calico接口
		"flannel", // flannel接口
		"cilium",  // cilium接口
		"istio",   // istio接口
	}
	for _, virtualInterface := range virtualInterfaces {
		if strings.Contains(iface, virtualInterface) {
			return true
		}
	}
	return false
}

// collectLinux 采集Linux系统光模块信息
func (c *OpticalModuleCollector) collectLinux() error {
	// 初始化模块列表
	c.Optical.Modules = make([]OpticalModule, 0)

	// 1. 首先获取所有网络接口
	interfaces, err := c.getNetworkInterfaces()
	if err != nil {
		return fmt.Errorf("failed to get network interfaces: %v", err)
	}

	// 2. 遍历网络接口，获取光模块信息
	for _, iface := range interfaces {
		// 跳过本地回环接口和虚拟接口
		if iface == "lo" || c.IsVirtualInterface(iface) {
			continue
		}

		// 使用ethtool获取光模块信息
		module, err := c.getOpticalModuleInfoCall(iface)
		if err != nil {
			// 如果获取失败，可能不是光模块接口，跳过即可
			continue
		}

		if module != nil {
			c.Optical.Modules = append(c.Optical.Modules, *module)
		}
	}

	return nil
}

// getNetworkInterfaces 获取所有网络接口
func (c *OpticalModuleCollector) getNetworkInterfaces() ([]string, error) {
	// 通过/sys/class/net目录获取所有网络接口
	netDir := "/sys/class/net"
	entries, err := os.ReadDir(netDir)
	if err != nil {
		return nil, err
	}

	interfaces := make([]string, 0, len(entries))
	for _, entry := range entries {
		interfaces = append(interfaces, entry.Name())
	}

	return interfaces, nil
}

func (c *OpticalModuleCollector) getOpticalModuleInfoCall(iface string) (*OpticalModule, error) {
	// 使用ethtool -m 命令获取光模块详细信息
	cmd := exec.Command("ethtool", "-m", iface)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, err
	}

	// 解析ethtool输出
	module, err := c.getOpticalModuleInfo(string(output), iface)
	if err != nil {
		return nil, err
	}

	// 如果没有获取到足够的信息，则尝试通过sysfs获取
	if module.Type == "" || module.Vendor == "" {
		c.enrichModuleInfoFromSysfs(module)
	}

	// 获取链路状态
	cmd = exec.Command("ethtool", iface)
	raw, err := cmd.CombinedOutput()
	if err == nil {
		if strings.Contains(string(raw), "Link detected: yes") {
			module.Status = "Active"
		} else {
			module.Status = "Down"
		}
	}

	return c.getOpticalModuleInfo(string(output), iface)
}

// getOpticalModuleInfo 获取指定接口的光模块信息
func (c *OpticalModuleCollector) getOpticalModuleInfo(output, iface string) (*OpticalModule, error) {
	// 解析ethtool输出
	module := &OpticalModule{
		Interface: iface,
	}

	scanner := bufio.NewScanner(strings.NewReader(output))
	for scanner.Scan() {
		line := scanner.Text()
		line = strings.TrimSpace(line)

		// 解析各种光模块信息
		if strings.HasPrefix(line, "Identifier") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				moduleType := strings.TrimSpace(parts[1])
				switch moduleType {
				case "0x03":
					module.Type = "SFP"
				case "0x0c", "0x0d":
					module.Type = "QSFP"
				case "0x11":
					module.Type = "SFP+"
				default:
					module.Type = moduleType
				}
			}
		} else if strings.HasPrefix(line, "Vendor name") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				module.Vendor = strings.TrimSpace(parts[1])
			}
		} else if strings.HasPrefix(line, "Vendor PN") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				module.PartNumber = strings.TrimSpace(parts[1])
			}
		} else if strings.HasPrefix(line, "Vendor SN") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				module.SerialNumber = strings.TrimSpace(parts[1])
			}
		} else if strings.HasPrefix(line, "Wavelength") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				module.Wavelength = strings.TrimSpace(parts[1])
			}
		}
	}

	return module, nil
}

// enrichModuleInfoFromSysfs 从sysfs获取额外的光模块信息
func (c *OpticalModuleCollector) enrichModuleInfoFromSysfs(module *OpticalModule) {
	// 检查是否是光纤接口
	sfpDir := filepath.Join("/sys/class/net", module.Interface, "device/sfp")
	if _, err := os.Stat(sfpDir); os.IsNotExist(err) {
		// 不是光纤接口
		return
	}

	// 读取光模块类型
	if module.Type == "" {
		typeFile := filepath.Join(sfpDir, "module_type")
		if data, err := os.ReadFile(typeFile); err == nil {
			module.Type = strings.TrimSpace(string(data))
		}
	}

	// 读取厂商信息
	if module.Vendor == "" {
		vendorFile := filepath.Join(sfpDir, "vendor_name")
		if data, err := os.ReadFile(vendorFile); err == nil {
			module.Vendor = strings.TrimSpace(string(data))
		}
	}

	// 读取部件号
	if module.PartNumber == "" {
		partNoFile := filepath.Join(sfpDir, "vendor_pn")
		if data, err := os.ReadFile(partNoFile); err == nil {
			module.PartNumber = strings.TrimSpace(string(data))
		}
	}

	// 读取序列号
	if module.SerialNumber == "" {
		serialFile := filepath.Join(sfpDir, "vendor_sn")
		if data, err := os.ReadFile(serialFile); err == nil {
			module.SerialNumber = strings.TrimSpace(string(data))
		}
	}
}

// collectWindows 采集Windows系统光模块信息
func (c *OpticalModuleCollector) collectWindows() error {
	// 初始化模块列表
	c.Optical.Modules = make([]OpticalModule, 0)

	// 使用PowerShell获取网络适配器信息
	cmd := exec.Command("powershell", "-Command",
		"Get-NetAdapter | Where-Object {$_.InterfaceDescription -like '*Fiber*' -or $_.InterfaceDescription -like '*Optical*'} | Select-Object Name, InterfaceDescription, Status | ConvertTo-Csv -NoTypeInformation")

	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get network adapters: %v", err)
	}

	// 解析CSV输出
	lines := strings.Split(string(output), "\n")
	if len(lines) <= 1 {
		// 没有找到光纤适配器
		return nil
	}

	// 跳过标题行
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// 解析CSV行
		fields := strings.Split(line, ",")
		if len(fields) < 3 {
			continue
		}

		// 去除引号
		name := strings.Trim(fields[0], "\"")
		description := strings.Trim(fields[1], "\"")
		status := strings.Trim(fields[2], "\"")

		module := OpticalModule{
			Interface: name,
			Type:      "Unknown", // Windows下难以获取具体类型
			Vendor:    extractVendorFromDescription(description),
			Status:    status,
		}

		c.Optical.Modules = append(c.Optical.Modules, module)
	}

	return nil
}

// extractVendorFromDescription 从适配器描述中提取厂商信息
func extractVendorFromDescription(description string) string {
	vendors := []string{"Intel", "Broadcom", "Mellanox", "Cisco", "HP", "Dell", "Juniper"}
	for _, vendor := range vendors {
		if strings.Contains(description, vendor) {
			return vendor
		}
	}
	return "Unknown"
}

// GetTaskName 获取任务名称
func (c *OpticalModuleCollector) GetTaskName() string {
	return "optical_module_info"
}

// GetTaskType 获取任务类型
func (c *OpticalModuleCollector) GetTaskType() string {
	return "components"
}

// ShouldExecute 判断是否需要执行光模块信息采集
func (c *OpticalModuleCollector) ShouldExecute() bool {
	switch runtime.GOOS {
	case "linux":
		// 检查是否有ethtool命令
		_, err := exec.LookPath("ethtool")
		if err != nil {
			return false
		}

		// 获取所有网络接口
		entries, err := os.ReadDir("/sys/class/net")
		if err != nil {
			return false
		}

		for _, entry := range entries {
			// 跳过虚拟接口
			if c.IsVirtualInterface(entry.Name()) {
				continue
			}

			// 方法2: 使用ethtool检查接口是否为光纤接口
			cmd := exec.Command("ethtool", entry.Name())
			output, err := cmd.CombinedOutput()
			if err == nil {
				outputStr := string(output)
				// 检查"Supported ports"字段是否包含"FIBRE"
				if strings.Contains(outputStr, "Supported ports: [ FIBRE ]") ||
					strings.Contains(outputStr, "Port: FIBRE") {
					return true
				}
			}

			// 方法3: 尝试使用ethtool -m检测是否支持光模块
			cmd = exec.Command("ethtool", "-m", entry.Name())
			output, err = cmd.CombinedOutput()
			// 如果没有报错或者错误不包含"Operation not supported"，说明可能支持光模块
			if err == nil || !strings.Contains(string(output), "Operation not supported") {
				return true
			}
		}

		return false
	case "windows":
		// Windows下使用PowerShell查询是否有包含"Fiber"或"Optical"的适配器
		cmd := exec.Command("powershell", "-Command",
			"Get-NetAdapter | Where-Object {$_.InterfaceDescription -like '*Fiber*' -or $_.InterfaceDescription -like '*Optical*'} | Measure-Object | Select-Object -ExpandProperty Count")
		output, err := cmd.Output()
		if err == nil {
			count, _ := strconv.Atoi(strings.TrimSpace(string(output)))
			return count > 0
		}

		// 如果上面的命令执行失败，默认返回true以便在Windows上尝试采集
		return true
	default:
		return false
	}
}

// PreCheck 预检测工具依赖是否满足
func (c *OpticalModuleCollector) PreCheck() []string {
	var missingTools []string

	switch runtime.GOOS {
	case "linux":
		// 检查ethtool命令
		_, err := exec.LookPath("ethtool")
		if err != nil {
			missingTools = append(missingTools, "ethtool")
		}

	case "windows":
		// Windows下需要检查PowerShell是否可用
		_, err := exec.LookPath("powershell")
		if err != nil {
			missingTools = append(missingTools, "powershell")
		}
	}

	return missingTools
}
