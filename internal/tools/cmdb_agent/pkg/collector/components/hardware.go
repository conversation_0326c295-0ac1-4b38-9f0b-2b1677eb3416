package components

import (
	"fmt"
	"os/exec"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/yumaojun03/dmidecode"
)

func printHardwareInfo(collector *HardwareInfoCollector) {
	fmt.Printf("\n=== 硬件组件信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	if len(collector.Hardware.Components) == 0 {
		fmt.Println("未检测到硬件组件")
		fmt.Printf("========================\n\n")
		return
	}

	// 按类型分组打印
	fmt.Println("\n主板信息:")
	for _, component := range collector.Hardware.Components {
		if component.Type == TypeMotherboard {
			fmt.Printf("  制造商: %s\n", component.Vendor)
			fmt.Printf("  型号: %s\n", component.Model)
			fmt.Printf("  序列号: %s\n", component.SerialNum)
			fmt.Printf("  版本: %s\n", component.Version)
		}
	}

	// 打印网卡信息
	fmt.Println("\n网卡列表:")
	i := 1
	for _, component := range collector.Hardware.Components {
		if component.Type == TypeNetworkCard {
			fmt.Printf("网卡 %d:\n", i)
			fmt.Printf("  名称: %s\n", component.Name)
			if component.Vendor != "" {
				fmt.Printf("  制造商: %s\n", component.Vendor)
			}
			if component.Description != "" {
				fmt.Printf("  描述: %s\n", component.Description)
			}
			i++
		}
	}

	// 打印显卡信息
	fmt.Println("\n显卡列表:")
	i = 1
	for _, component := range collector.Hardware.Components {
		if component.Type == TypeGraphicCard {
			fmt.Printf("显卡 %d:\n", i)
			fmt.Printf("  名称: %s\n", component.Name)
			if component.Vendor != "" {
				fmt.Printf("  制造商: %s\n", component.Vendor)
			}
			if component.Version != "" {
				fmt.Printf("  驱动版本: %s\n", component.Version)
			}
			i++
		}
	}

	// 打印RAID卡信息
	fmt.Println("\nRAID卡列表:")
	i = 1
	for _, component := range collector.Hardware.Components {
		if component.Type == TypeRaidCard {
			fmt.Printf("RAID卡 %d:\n", i)
			fmt.Printf("  名称: %s\n", component.Name)
			if component.Vendor != "" {
				fmt.Printf("  制造商: %s\n", component.Vendor)
			}
			if component.Model != "" {
				fmt.Printf("  型号: %s\n", component.Model)
			}
			i++
		}
	}

	fmt.Printf("========================\n\n")
}

// 定义硬件组件类型
const (
	TypeMotherboard = "motherboard"
	TypeNetworkCard = "network_card"
	TypeGraphicCard = "graphic_card"
	TypeRaidCard    = "raid_card"
	TypeOther       = "other"
)

// HardwareComponent 表示单个硬件组件
type HardwareComponent struct {
	Type        string `json:"type"`        // 组件类型
	Name        string `json:"name"`        // 组件名称
	Vendor      string `json:"vendor"`      // 制造商
	Model       string `json:"model"`       // 型号
	SerialNum   string `json:"serial_num"`  // 序列号
	Version     string `json:"version"`     // 版本
	Description string `json:"description"` // 描述
}

// HardwareInfo 表示所有硬件组件信息
type HardwareInfo struct {
	Components []HardwareComponent `json:"components"` // 组件列表
}

// HardwareInfoCollector 采集硬件组件信息
type HardwareInfoCollector struct {
	Hardware HardwareInfo `json:"hardware"`
}

func (h *HardwareInfoCollector) Show() {
	printHardwareInfo(h)
}

// Execute 执行硬件信息采集
func (c *HardwareInfoCollector) Execute() error {
	switch runtime.GOOS {
	case "linux":
		return c.collectLinux()
	case "windows":
		return c.collectWindows()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// collectLinux 采集Linux系统硬件信息
func (c *HardwareInfoCollector) collectLinux() error {
	// 初始化组件列表
	c.Hardware.Components = make([]HardwareComponent, 0)

	// 使用dmidecode获取主板信息
	if err := c.collectMotherboardInfo(); err != nil {
		fmt.Printf("failed to collect motherboard info: %v\n", err)
	}

	// 使用lspci获取网卡、显卡和RAID卡信息
	if err := c.collectPCIDevices(); err != nil {
		fmt.Printf("failed to collect PCI devices: %v\n", err)
	}

	return nil
}

// collectMotherboardInfo 采集主板信息
func (c *HardwareInfoCollector) collectMotherboardInfo() error {
	// 使用dmidecode获取主板信息
	dmi, err := dmidecode.New()
	if err != nil {
		return fmt.Errorf("failed to create dmidecode instance: %v", err)
	}

	// 获取主板信息
	baseboard, err := dmi.BaseBoard()
	if err != nil {
		return fmt.Errorf("failed to get baseboard info: %v", err)
	}

	for _, board := range baseboard {
		component := HardwareComponent{
			Type:      TypeMotherboard,
			Name:      "Motherboard",
			Vendor:    board.Manufacturer,
			Model:     board.ProductName,
			SerialNum: board.SerialNumber,
			Version:   board.Version,
		}

		c.Hardware.Components = append(c.Hardware.Components, component)
	}

	return nil
}

// collectPCIDevices 采集PCI设备信息
func (c *HardwareInfoCollector) collectPCIDevices() error {
	// 使用lspci获取PCI设备信息
	cmd := exec.Command("lspci", "-vmm")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get PCI devices: %v", err)
	}

	// 解析lspci输出
	devices := parseLspciOutput(string(output))

	// 添加到组件列表
	c.Hardware.Components = append(c.Hardware.Components, devices...)

	return nil
}

// parseLspciOutput 解析lspci输出
func parseLspciOutput(output string) []HardwareComponent {
	devices := make([]HardwareComponent, 0)
	lines := strings.Split(output, "\n\n")

	for _, deviceBlock := range lines {
		if deviceBlock == "" {
			continue
		}

		device := parseDeviceBlock(deviceBlock)
		if device != nil {
			devices = append(devices, *device)
		}
	}

	return devices
}

// parseDeviceBlock 解析设备块
func parseDeviceBlock(block string) *HardwareComponent {
	lines := strings.Split(block, "\n")
	device := &HardwareComponent{
		Type: TypeOther,
	}

	for _, line := range lines {
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "Class":
			// 根据设备类别确定组件类型
			device.Type = determineDeviceType(value)
		case "Device":
			device.Name = value
		case "Vendor":
			device.Vendor = value
		case "Rev":
			device.Version = value
		case "SVendor":
			if device.Vendor == "" {
				device.Vendor = value
			}
		case "SDevice":
			if device.Name == "" {
				device.Name = value
			}
		}
	}

	// 设置设备描述
	if device.Type != TypeOther && device.Name != "" {
		return device
	}

	return nil
}

// determineDeviceType 根据设备类别确定组件类型
func determineDeviceType(class string) string {
	lowerClass := strings.ToLower(class)
	if strings.Contains(lowerClass, "network") ||
		strings.Contains(lowerClass, "ethernet") ||
		strings.Contains(lowerClass, "wireless") {
		return TypeNetworkCard
	} else if strings.Contains(lowerClass, "vga") ||
		strings.Contains(lowerClass, "display") ||
		strings.Contains(lowerClass, "graphic") ||
		strings.Contains(lowerClass, "3d") {
		return TypeGraphicCard
	} else if strings.Contains(lowerClass, "raid") || strings.Contains(lowerClass, "storage") || strings.Contains(lowerClass, "sas") {
		return TypeRaidCard
	}
	return TypeOther
}

// collectWindows 采集Windows系统硬件信息
func (c *HardwareInfoCollector) collectWindows() error {
	// 初始化组件列表
	c.Hardware.Components = make([]HardwareComponent, 0)

	// 使用PowerShell获取主板信息
	if err := c.collectWindowsMotherboard(); err != nil {
		fmt.Printf("failed to collect motherboard info: %v\n", err)
	}

	// 使用PowerShell获取网卡信息
	if err := c.collectWindowsNetworkCards(); err != nil {
		fmt.Printf("failed to collect network cards: %v\n", err)
	}

	// 使用PowerShell获取显卡信息
	if err := c.collectWindowsGraphicCards(); err != nil {
		fmt.Printf("failed to collect graphic cards: %v\n", err)
	}

	return nil
}

// collectWindowsMotherboard 采集Windows系统主板信息
func (c *HardwareInfoCollector) collectWindowsMotherboard() error {
	cmd := exec.Command("powershell", "-Command", "Get-CimInstance -ClassName Win32_BaseBoard | Select-Object Manufacturer, Product, SerialNumber, Version | ConvertTo-Json")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get motherboard info: %v", err)
	}

	// 解析JSON输出并添加到组件列表
	// 由于输出格式较为复杂，这里简单处理
	outputStr := string(output)
	manufacturer := extractValueFromPowershellOutput(outputStr, "Manufacturer")
	product := extractValueFromPowershellOutput(outputStr, "Product")
	serialNumber := extractValueFromPowershellOutput(outputStr, "SerialNumber")
	version := extractValueFromPowershellOutput(outputStr, "Version")

	component := HardwareComponent{
		Type:      TypeMotherboard,
		Name:      "Motherboard",
		Vendor:    manufacturer,
		Model:     product,
		SerialNum: serialNumber,
		Version:   version,
	}

	c.Hardware.Components = append(c.Hardware.Components, component)
	return nil
}

// collectWindowsNetworkCards 采集Windows系统网卡信息
func (c *HardwareInfoCollector) collectWindowsNetworkCards() error {
	cmd := exec.Command("powershell", "-Command", "Get-CimInstance -ClassName Win32_NetworkAdapter | Where-Object { $_.PhysicalAdapter -eq $true } | Select-Object Name, Manufacturer, Description, Speed | ConvertTo-Json")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get network cards: %v", err)
	}

	// 解析JSON输出并添加到组件列表
	outputStr := string(output)
	// 判断是否有多个网卡
	if strings.HasPrefix(outputStr, "[") {
		// 多个网卡的情况，需要解析数组
		networkCards := extractNetworkCardsFromPowershellOutput(outputStr)
		c.Hardware.Components = append(c.Hardware.Components, networkCards...)
	} else {
		// 单个网卡的情况
		name := extractValueFromPowershellOutput(outputStr, "Name")
		manufacturer := extractValueFromPowershellOutput(outputStr, "Manufacturer")
		description := extractValueFromPowershellOutput(outputStr, "Description")

		component := HardwareComponent{
			Type:        TypeNetworkCard,
			Name:        name,
			Vendor:      manufacturer,
			Description: description,
		}

		c.Hardware.Components = append(c.Hardware.Components, component)
	}

	return nil
}

// collectWindowsGraphicCards 采集Windows系统显卡信息
func (c *HardwareInfoCollector) collectWindowsGraphicCards() error {
	cmd := exec.Command("powershell", "-Command", "Get-CimInstance -ClassName Win32_VideoController | Select-Object Name, DriverVersion, AdapterRAM | ConvertTo-Json")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get graphic cards: %v", err)
	}

	// 解析JSON输出并添加到组件列表
	outputStr := string(output)
	// 判断是否有多个显卡
	if strings.HasPrefix(outputStr, "[") {
		// 多个显卡的情况，需要解析数组
		graphicCards := extractGraphicCardsFromPowershellOutput(outputStr)
		c.Hardware.Components = append(c.Hardware.Components, graphicCards...)
	} else {
		// 单个显卡的情况
		name := extractValueFromPowershellOutput(outputStr, "Name")
		version := extractValueFromPowershellOutput(outputStr, "DriverVersion")

		component := HardwareComponent{
			Type:    TypeGraphicCard,
			Name:    name,
			Version: version,
		}

		c.Hardware.Components = append(c.Hardware.Components, component)
	}

	return nil
}

// extractValueFromPowershellOutput 从PowerShell输出中提取值
func extractValueFromPowershellOutput(output, key string) string {
	re := regexp.MustCompile(fmt.Sprintf(`"%s"\s*:\s*"([^"]*)"`, key))
	matches := re.FindStringSubmatch(output)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// extractNetworkCardsFromPowershellOutput 从PowerShell输出中提取网卡信息
func extractNetworkCardsFromPowershellOutput(output string) []HardwareComponent {
	// 简单处理，实际应该使用JSON解析
	re := regexp.MustCompile(`{\s*"Name"\s*:\s*"([^"]*)"\s*,\s*"Manufacturer"\s*:\s*"([^"]*)"\s*,\s*"Description"\s*:\s*"([^"]*)"\s*,\s*"Speed"\s*:\s*"?([^,"]*)"?\s*}`)
	matches := re.FindAllStringSubmatch(output, -1)

	cards := make([]HardwareComponent, 0, len(matches))
	for _, match := range matches {
		if len(match) > 4 {
			card := HardwareComponent{
				Type:        TypeNetworkCard,
				Name:        match[1],
				Vendor:      match[2],
				Description: match[3],
			}
			cards = append(cards, card)
		}
	}

	return cards
}

// extractGraphicCardsFromPowershellOutput 从PowerShell输出中提取显卡信息
func extractGraphicCardsFromPowershellOutput(output string) []HardwareComponent {
	// 简单处理，实际应该使用JSON解析
	re := regexp.MustCompile(`{\s*"Name"\s*:\s*"([^"]*)"\s*,\s*"DriverVersion"\s*:\s*"([^"]*)"\s*,\s*"AdapterRAM"\s*:\s*(\d+)\s*}`)
	matches := re.FindAllStringSubmatch(output, -1)

	cards := make([]HardwareComponent, 0, len(matches))
	for _, match := range matches {
		if len(match) > 3 {
			card := HardwareComponent{
				Type:    TypeGraphicCard,
				Name:    match[1],
				Version: match[2],
			}
			cards = append(cards, card)
		}
	}

	return cards
}

// GetTaskName 获取任务名称
func (c *HardwareInfoCollector) GetTaskName() string {
	return "hardware_info"
}

// GetTaskType 获取任务类型
func (c *HardwareInfoCollector) GetTaskType() string {
	return "components"
}

// ShouldExecute 判断是否需要执行硬件信息采集
func (c *HardwareInfoCollector) ShouldExecute() bool {
	// 硬件信息采集在所有情况下都应该执行
	return true
}

// PreCheck 预检测工具依赖是否满足
func (c *HardwareInfoCollector) PreCheck() []string {
	var missingTools []string

	switch runtime.GOOS {
	case "linux":
		// 检查lspci命令
		_, err := exec.LookPath("lspci")
		if err != nil {
			missingTools = append(missingTools, "lspci")
		}

		// 检查dmidecode库依赖
		_, err = dmidecode.New()
		if err != nil {
			missingTools = append(missingTools, "dmidecode")
		}

	case "windows":
		// Windows下需要检查PowerShell是否可用
		_, err := exec.LookPath("powershell")
		if err != nil {
			missingTools = append(missingTools, "powershell")
		}
	}

	return missingTools
}
