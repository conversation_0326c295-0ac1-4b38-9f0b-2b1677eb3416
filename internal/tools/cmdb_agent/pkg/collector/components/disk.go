package components

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"time"

	"ks-knoc-server/internal/tools/cmdb_agent/pkg/utils"
)

func printDiskInfo(collector *DiskInfoCollector) {
	fmt.Printf("\n=== 磁盘信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 打印磁盘信息
	fmt.Printf("\n磁盘列表:\n")
	for i, disk := range collector.Disks {
		fmt.Printf("磁盘 %d:\n", i+1)
		fmt.Printf("  型号: %s\n", disk.BasicInfo.Model)
		fmt.Printf("  大小: %s\n", disk.BasicInfo.Size)
		fmt.Printf("  类型: %s\n", disk.BasicInfo.Intf)
		fmt.Printf("  序列号: %s\n", disk.DetailInfo.SN)
		fmt.Printf("  接口: %s\n", disk.BasicInfo.Intf)
		fmt.Printf("  状态: %s\n", disk.BasicInfo.State)
	}
	fmt.Printf("========================\n\n")
}

// 匹配驱动器基础信息, 示例: Drive /c0/e16/s0
var basePathRegex = regexp.MustCompile(`Drive /c(\d+)/e(\d+)/s(\d+)`)

type StorcliResponse struct {
	Controllers []struct {
		ResponseData map[string]json.RawMessage `json:"Response Data"`
	} `json:"Controllers"`
}

type DriveBasic struct {
	State string `json:"State"` // 磁盘状态
	Model string `json:"Model"` // 磁盘型号
	Size  string `json:"Size"`  // 磁盘大小
	Intf  string `json:"Intf"`  // 磁盘接口, SATA, SAS, NVMe
	Med   string `json:"Med"`   // 磁盘介质, SSD, HDD
}

type SerialInfo struct {
	SN string `json:"SN"`
}

// DriveGroup 保存驱动器信息
type DriveGroup struct {
	BasicPath  string     `json:"basic_path"`
	BasicInfo  DriveBasic `json:"basic_info"`
	DetailInfo SerialInfo `json:"detail_info"`
}

// DGs 保存驱动器组信息
type DGs []*DriveGroup

func NewDGs() DGs {
	return make(DGs, 0)
}

func (dgs DGs) Len() int {
	return len(dgs)
}

// Less 按照名称进行排序
func (dgs DGs) Less(i, j int) bool {
	return dgs[i].BasicPath < dgs[j].BasicPath
}

func (dgs DGs) Swap(i, j int) {
	dgs[i], dgs[j] = dgs[j], dgs[i]
}

var PDStatusMapping = map[string]string{
	"Onln":  "在线",
	"Offln": "离线",
	"DHS":   "热备",
	"UGood": "未配置-正常",
	"UBad":  "未配置-故障",
}

// DiskInfoCollector 采集磁盘和RAID信息
type DiskInfoCollector struct {
	Disks DGs `json:"disks"`
}

func (d *DiskInfoCollector) Show() {
	printDiskInfo(d)
}

func (d *DiskInfoCollector) Execute() error {
	switch runtime.GOOS {
	case "linux":
		return d.collectLinux()
	case "windows":
		return d.collectWindows()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

func ParseStorcliResponse(rawData []byte) (DGs, error) {
	// 初始化容器保存磁盘信息
	dgs := NewDGs()

	// 解析原始数据
	var result StorcliResponse
	if err := json.Unmarshal(rawData, &result); err != nil {
		return nil, fmt.Errorf("failed to parse raw data: %v", err)
	}

	// 构建一个dg的map
	dgMap := make(map[string]*DriveGroup)

	// 遍历保存驱动器数据
	for k := range result.Controllers[0].ResponseData {
		// 声明一个dg用于保存驱动器数据
		var dg *DriveGroup

		matches := basePathRegex.FindString(k)
		if matches == "" {
			return nil, fmt.Errorf("invalid base path: %s", k)
		}
		// 构建dgMap用于快速存取
		if _, ok := dgMap[matches]; !ok {
			newDg := &DriveGroup{}
			dgMap[matches] = newDg
			dg = newDg
		} else {
			dg = dgMap[matches]
		}

		// 判断查询的是基础信息还是详细信息
		if strings.TrimSpace(k) == matches {
			// 正好相等说明查询的是基础信息
			basicInfo := make([]DriveBasic, 0)
			if err := json.Unmarshal(result.Controllers[0].ResponseData[k], &basicInfo); err != nil {
				return nil, fmt.Errorf("failed to unmarshal basic info: %v", err)
			}

			dg.BasicPath = k
			dg.BasicInfo = basicInfo[0]
		} else {
			// 说明查询的是详细信息
			detailInfoKey := fmt.Sprintf("%s - Detailed Information", matches)
			deviceAttrKey := fmt.Sprintf("%s Device attributes", matches)

			// 获取磁盘详细信息
			info, ok := result.Controllers[0].ResponseData[detailInfoKey]
			if !ok {
				return nil, fmt.Errorf("invalid detail info key: %s", detailInfoKey)
			}

			var detailInfo map[string]json.RawMessage
			if err := json.Unmarshal(info, &detailInfo); err != nil {
				return nil, fmt.Errorf("failed to unmarshal detail info: %v", err)
			}

			// 获取磁盘设备属性
			deviceAttr, ok := detailInfo[deviceAttrKey]
			if !ok {
				return nil, fmt.Errorf("invalid device attr key: %s", deviceAttrKey)
			}

			var deviceAttrMap SerialInfo
			if err := json.Unmarshal(deviceAttr, &deviceAttrMap); err != nil {
				return nil, fmt.Errorf("failed to unmarshal device attr: %v", err)
			}

			// 去除字符串两端的空格
			deviceAttrMap.SN = strings.TrimSpace(deviceAttrMap.SN)
			dg.DetailInfo = deviceAttrMap
		}
	}

	// 将dgMap中的数据转换为dgs
	for _, dg := range dgMap {
		dgs = append(dgs, dg)
	}

	// 排序
	sort.Sort(dgs)

	return dgs, nil
}

func (d *DiskInfoCollector) collectLinux() error {
	// 使用storcli64获取RAID和磁盘信息
	cmd := exec.Command("/opt/MegaRAID/storcli/storcli64", "/c0/eall/sall", "show", "all", "J")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get MegaRAID info: %v", err)
	}

	// 解析磁盘信息
	dgs, err := ParseStorcliResponse(output)
	if err != nil {
		return fmt.Errorf("failed to parse MegaRAID info: %v", err)
	}

	d.Disks = dgs

	return nil
}

func (d *DiskInfoCollector) collectWindows() error {
	// 获取磁盘信息
	cmd := exec.Command("powershell", "Get-PhysicalDisk | ConvertTo-Json")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get disk info: %v", err)
	}

	// 解析磁盘信息
	dgs, err := ParseStorcliResponse(output)
	if err != nil {
		return fmt.Errorf("failed to parse MegaRAID info: %v", err)
	}

	d.Disks = dgs
	return nil
}

func (d *DiskInfoCollector) GetTaskName() string {
	return "disk_info"
}

func (d *DiskInfoCollector) GetTaskType() string {
	return "components"
}

// ShouldExecute 判断是否需要执行磁盘信息采集
func (c *DiskInfoCollector) ShouldExecute() bool {
	// 检查是否为物理机
	if !utils.IsPhysicalMachine() {
		fmt.Println("当前系统不是物理机, 不执行磁盘信息采集")
		return false
	}
	return true
}

// PreCheck 预检测工具依赖是否满足
func (c *DiskInfoCollector) PreCheck() []string {
	var missingTools []string

	switch runtime.GOOS {
	case "linux":
		// 检查storcli64命令
		_, err := os.Stat("/opt/MegaRAID/storcli/storcli64")
		if os.IsNotExist(err) {
			missingTools = append(missingTools, "storcli64")
		}

	case "windows":
		// Windows下需要检查PowerShell是否可用
		_, err := exec.LookPath("powershell")
		if err != nil {
			missingTools = append(missingTools, "powershell")
		}
	}

	return missingTools
}
