package components

import (
	"testing"
)

var raw1 = `
	Identifier                                : 0x03 (SFP)
	Extended identifier                       : 0x04 (GBIC/SFP defined by 2-wire interface ID)
	Connector                                 : 0x21 (Copper pigtail)
	Transceiver codes                         : 0x00 0x00 0x00 0x00 0x00 0x08 0x00 0x00
	Transceiver type                          : Active Cable
	Encoding                                  : 0x06 (64B/66B)
	BR, Nominal                               : 10300MBd
	Rate identifier                           : 0x00 (unspecified)
	Length (SMF,km)                           : 0km
	Length (SMF)                              : 0m
	Length (50um)                             : 0m
	Length (62.5um)                           : 0m
	Length (Copper)                           : 7m
	Length (OM3)                              : 0m
	Active Cu cmplnce.                        : 0x0c (unknown) [SFF-8472 rev10.4 only]
	Vendor name                               : FINISAR CORP.
	Vendor OUI                                : 00:90:65
	Vendor PN                                 : FCBG110SD1C07
	Vendor rev                                : A
	Option values                             : 0x00 0x12
	Option                                    : RX_LOS implemented
	Option                                    : TX_DISABLE implemented
	BR margin, max                            : 0%
	BR margin, min                            : 0%
	Vendor SN                                 : W2BABS3
	Date code                                 : 190912
`

func TestOpticalGetOpticalModuleInfo(t *testing.T) {
	collector := &OpticalModuleCollector{}
	module, err := collector.getOpticalModuleInfo(raw1, "eth0")
	if err != nil {
		t.Fatalf("failed to get optical module info: %v", err)
	}
	t.Log("type: ", module.Type)
	t.Log("vendor: ", module.Vendor)
	t.Log("part_number: ", module.PartNumber)
	t.Log("serial_number: ", module.SerialNumber)
	t.Log("wavelength: ", module.Wavelength)
}
