package components

import (
	"fmt"
	"os/exec"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/yumaojun03/dmidecode"
)

func printMemoryInfo(collector *MemoryInfoCollector) {
	fmt.Printf("\n=== 内存信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("总内存: %s\n", collector.Memory.TotalSize)

	fmt.Printf("\n内存模块列表:\n")
	for i, module := range collector.Memory.Modules {
		fmt.Printf("模块 %d:\n", i+1)
		fmt.Printf("  大小: %s\n", module.Size)
		if module.Type != "" {
			fmt.Printf("  类型: %s\n", module.Type)
		}
		if module.Speed != "" {
			fmt.Printf("  速度: %s\n", module.Speed)
		}
		if module.Slot != "" {
			fmt.Printf("  插槽: %s\n", module.Slot)
		}
		if module.Vendor != "" {
			fmt.Printf("  厂商: %s\n", module.Vendor)
		}
		if module.SerialNum != "" {
			fmt.Printf("  序列号: %s\n", module.SerialNum)
		}
		if module.PartNum != "" {
			fmt.Printf("  部件号: %s\n", module.PartNum)
		}
	}
	fmt.Printf("========================\n\n")
}

// MemoryModule 表示单个内存模块信息
type MemoryModule struct {
	Size      string `json:"size"`       // 内存大小
	Type      string `json:"type"`       // 内存类型 (DDR4, DDR3等)
	Speed     string `json:"speed"`      // 内存速度
	Slot      string `json:"slot"`       // 插槽位置
	Vendor    string `json:"vendor"`     // 制造商
	SerialNum string `json:"serial_num"` // 序列号
	PartNum   string `json:"part_num"`   // 部件号
}

// MemoryInfo 表示内存总体信息
type MemoryInfo struct {
	TotalSize string         `json:"total_size"` // 总内存大小
	Modules   []MemoryModule `json:"modules"`    // 内存模块列表
}

// MemoryInfoCollector 采集内存信息
type MemoryInfoCollector struct {
	Memory MemoryInfo `json:"memory"`
}

func (m *MemoryInfoCollector) Show() {
	printMemoryInfo(m)
}

// Execute 执行内存信息采集
func (c *MemoryInfoCollector) Execute() error {
	// 创建dmidecode实例
	dmi, err := dmidecode.New()
	if err != nil {
		return fmt.Errorf("failed to create dmidecode instance: %v", err)
	}

	// 获取内存总大小
	err = c.getSystemTotalMemory()
	if err != nil {
		return fmt.Errorf("failed to get total memory size: %v", err)
	}

	// 获取内存模块信息
	return c.getMemoryModulesInfo(dmi)
}

// getSystemTotalMemory 获取系统总内存大小
func (c *MemoryInfoCollector) getSystemTotalMemory() error {
	switch runtime.GOOS {
	case "linux":
		// 从/proc/meminfo获取总内存大小
		cmd := exec.Command("grep", "MemTotal", "/proc/meminfo")
		output, err := cmd.Output()
		if err != nil {
			return fmt.Errorf("failed to get memory info: %v", err)
		}

		// 解析内存总大小
		memTotalRegex := regexp.MustCompile(`MemTotal:\s+(\d+)\s+kB`)
		memTotalMatch := memTotalRegex.FindStringSubmatch(string(output))
		if len(memTotalMatch) > 1 {
			memTotalKB, _ := strconv.ParseInt(memTotalMatch[1], 10, 64)
			c.Memory.TotalSize = fmt.Sprintf("%.2f GB", float64(memTotalKB)/1024/1024)
		}
		return nil
	case "windows":
		// 使用PowerShell获取内存总大小
		cmd := exec.Command("powershell", "-Command", "Get-CimInstance Win32_ComputerSystem | Select-Object TotalPhysicalMemory")
		output, err := cmd.Output()
		if err != nil {
			return fmt.Errorf("failed to get memory info: %v", err)
		}

		// 解析内存总大小
		totalRe := regexp.MustCompile(`TotalPhysicalMemory\s+:\s+(\d+)`)
		totalMatch := totalRe.FindStringSubmatch(string(output))
		if len(totalMatch) > 1 {
			total, _ := strconv.ParseInt(totalMatch[1], 10, 64)
			c.Memory.TotalSize = fmt.Sprintf("%.2f GB", float64(total)/1024/1024/1024)
		}
		return nil
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// getMemoryModulesInfo 获取内存模块信息
func (c *MemoryInfoCollector) getMemoryModulesInfo(dmi *dmidecode.Decoder) error {
	// 获取内存模块信息
	memoryDevices, err := dmi.MemoryDevice()
	if err != nil {
		return fmt.Errorf("failed to get memory devices: %v", err)
	}

	// 初始化模块列表
	c.Memory.Modules = make([]MemoryModule, 0)

	// 解析内存模块信息
	for _, device := range memoryDevices {
		// 跳过空插槽或大小为0的模块
		if device.Size == 0 {
			continue
		}

		// 计算内存大小
		var sizeStr string
		// 0x7FFF 换算成10进制是32767，按照SMBIOS规范，当内存大小值为0x7FFF时，表示内存大小大于32GB
		// 此时你如果要获取真实的内存大小，需要使用ExtendedSize字段
		// 参考：https://www.dmtf.org/sites/default/files/standards/documents/DSP0134_3.1.1.pdf 第93页
		if device.Size == 0x7FFF {
			// 对于大于32GB的内存，使用ExtendedSize字段
			sizeStr = fmt.Sprintf("%d MB", device.ExtendedSize)
		} else {
			// 常规大小计算
			// 0x8000 表示二进制的1000 0000 0000 0000
			// 使用&运算符，可以判断device.Size的最高位是否为1
			// 判断的目的是：SMBIOS规范中规定，如果最高为1，表示内存单位为KB，否则为MB
			if device.Size&0x8000 != 0 {
				// 如果最高位是1，单位是KB
				// 0x7FFF 二进制的表示为 0111 1111 1111 1111
				// 该表达是可以提取除最高位外所有的位的值，进儿计算出内存大小
				sizeStr = fmt.Sprintf("%d KB", device.Size&0x7FFF)
			} else {
				// 否则单位是MB
				sizeStr = fmt.Sprintf("%d MB", device.Size)
			}
		}

		module := MemoryModule{
			Size: sizeStr,
		}

		// 提取内存类型
		if device.Type != 0 && device.Type != 2 { // 避免"未知"类型
			module.Type = device.Type.String()
		}

		// 提取内存速度
		if device.Speed != 0 {
			module.Speed = fmt.Sprintf("%d MHz", device.Speed)
		}

		// 提取插槽位置
		if device.DeviceLocator != "" {
			module.Slot = device.DeviceLocator
		} else if device.BankLocator != "" {
			module.Slot = device.BankLocator
		}

		// 提取制造商
		if device.Manufacturer != "" && device.Manufacturer != "Unknown" {
			module.Vendor = device.Manufacturer
		}

		// 提取序列号
		if device.SerialNumber != "" && device.SerialNumber != "Unknown" {
			module.SerialNum = device.SerialNumber
		}

		// 提取部件号
		if device.PartNumber != "" && device.PartNumber != "Unknown" {
			module.PartNum = strings.TrimSpace(device.PartNumber)
		}

		c.Memory.Modules = append(c.Memory.Modules, module)
	}

	return nil
}

// GetTaskName 获取任务名称
func (c *MemoryInfoCollector) GetTaskName() string {
	return "memory_info"
}

// GetTaskType 获取任务类型
func (c *MemoryInfoCollector) GetTaskType() string {
	return "components"
}

// ShouldExecute 判断是否需要执行内存信息采集
func (c *MemoryInfoCollector) ShouldExecute() bool {
	// 内存信息采集在所有情况下都应该执行
	return true
}

// PreCheck 预检测工具依赖是否满足
func (c *MemoryInfoCollector) PreCheck() []string {
	var missingTools []string

	// 检查dmidecode
	_, err := dmidecode.New()
	if err != nil {
		missingTools = append(missingTools, "dmidecode")
	}

	switch runtime.GOOS {
	case "linux":
		// Linux下检查grep命令
		_, err := exec.LookPath("grep")
		if err != nil {
			missingTools = append(missingTools, "grep")
		}

	case "windows":
		// Windows下需要检查PowerShell是否可用
		_, err := exec.LookPath("powershell")
		if err != nil {
			missingTools = append(missingTools, "powershell")
		}
	}

	return missingTools
}
