package network

import (
	"fmt"
	"net"
	"os/exec"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"
)

func printNetworkInfo(collector *NetworkInfoCollector) {
	fmt.Printf("\n=== 网络信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	if len(collector.Interfaces) == 0 {
		fmt.Printf("未检测到网络接口\n")
	} else {
		fmt.Printf("\n网络接口列表:\n")
		for i, iface := range collector.Interfaces {
			fmt.Printf("接口 %d:\n", i+1)
			fmt.Printf("  名称: %s\n", iface.Name)
			fmt.Printf("  所有IP地址: %v\n", iface.IPAddresses)
			fmt.Printf("  MAC地址: %s\n", iface.MACAddress)
		}
	}
	fmt.Printf("========================\n\n")
}

type NetworkInterface struct {
	Name        string   `json:"name"`
	IPAddresses []string `json:"ip_addresses"` // 所有IP地址
	MACAddress  string   `json:"mac_address"`
}

type NetworkInfoCollector struct {
	Interfaces []NetworkInterface `json:"interfaces"`
}

func (n *NetworkInfoCollector) Show() {
	printNetworkInfo(n)
}

func (n *NetworkInfoCollector) Execute() error {
	interfaces, err := net.Interfaces()
	if err != nil {
		zap.L().Error("获取网络接口失败", zap.Error(err))
		return fmt.Errorf("failed to get network interfaces: %v", err)
	}

	// 重置接口列表
	n.Interfaces = make([]NetworkInterface, 0)

	// 使用map来存储已经添加的接口，用于去重
	// 以接口名称作为key
	addedInterfaces := make(map[string]int)

	for _, iface := range interfaces {
		// 跳过回环接口
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 跳过一些虚拟端口
		if strings.Contains(iface.Name, "docker") ||
			strings.Contains(iface.Name, "kube") ||
			strings.Contains(iface.Name, "cni") ||
			strings.Contains(iface.Name, "flannel") ||
			strings.Contains(iface.Name, "veth") ||
			strings.Contains(iface.Name, "dummy") ||
			strings.Contains(iface.Name, "cali") {
			zap.L().Debug("跳过虚拟端口", zap.String("name", iface.Name))
			continue
		}

		zap.L().Debug("处理网络接口", zap.String("name", iface.Name))

		// 获取IP地址
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		// 收集所有IPv4地址
		var ipAddresses []string
		var primaryIP string
		for _, addr := range addrs {
			if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				if ipnet.IP.To4() != nil {
					ipAddr := ipnet.IP.String()
					ipAddresses = append(ipAddresses, ipAddr)
					if primaryIP == "" {
						primaryIP = ipAddr
					}
				}
			}
		}

		// 如果接口没有IP地址，跳过
		if len(ipAddresses) == 0 {
			continue
		}

		// 获取MAC地址
		macAddr := iface.HardwareAddr.String()
		if macAddr == "" {
			// 如果无法通过net包获取MAC地址，尝试使用系统命令
			macAddr = getMACAddressByCommand(iface.Name)
		}

		// 检查接口是否已经添加过
		if idx, exists := addedInterfaces[iface.Name]; exists {
			// 如果已存在，则将新的IP地址添加到现有接口
			for _, ip := range ipAddresses {
				// 检查IP是否已存在
				ipExists := false
				if slices.Contains(n.Interfaces[idx].IPAddresses, ip) {
					ipExists = true
				}
				if !ipExists {
					n.Interfaces[idx].IPAddresses = append(n.Interfaces[idx].IPAddresses, ip)
				}
			}
		} else {
			// 添加到列表并标记为已添加
			newIdx := len(n.Interfaces)
			n.Interfaces = append(n.Interfaces, NetworkInterface{
				Name:        iface.Name,
				IPAddresses: ipAddresses,
				MACAddress:  macAddr,
			})
			addedInterfaces[iface.Name] = newIdx
		}
	}

	return nil
}

func getMACAddressByCommand(ifaceName string) string {
	// 在Linux系统上使用ip命令获取MAC地址
	cmd := exec.Command("ip", "link", "show", ifaceName)
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "link/ether") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				return parts[1]
			}
		}
	}
	return ""
}

func (n *NetworkInfoCollector) GetTaskName() string {
	return "network_info"
}

func (n *NetworkInfoCollector) GetTaskType() string {
	return "network"
}

func (n *NetworkInfoCollector) ShouldExecute() bool {
	return true
}

// PreCheck 预检测工具依赖是否满足
func (n *NetworkInfoCollector) PreCheck() []string {
	var missingTools []string

	// 检查ip命令
	_, err := exec.LookPath("ip")
	if err != nil {
		missingTools = append(missingTools, "ip")
	}

	return missingTools
}
