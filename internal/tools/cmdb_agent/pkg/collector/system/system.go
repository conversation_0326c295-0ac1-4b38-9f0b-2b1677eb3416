package system

import (
	"fmt"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/utils"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

func printSystemInfo(collector *SystemInfoCollector) {
	fmt.Printf("\n=== 系统信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("主机名: %s\n", collector.Hostname)
	fmt.Printf("操作系统: %s\n", collector.OS)
	fmt.Printf("操作系统家族: %s\n", collector.OSFamily)
	fmt.Printf("操作系统版本: %s\n", collector.OSVersion)
	fmt.Printf("内核版本: %s\n", collector.KernelVersion)
	fmt.Printf("CPU核心数: %d\n", collector.CPUCores)

	fmt.Printf("========================\n\n")
}

type SystemInfoCollector struct {
	OS            string `json:"os"`
	OSFamily      string `json:"os_family"`
	OSVersion     string `json:"os_version"`
	KernelVersion string `json:"kernel_version"`
	Hostname      string `json:"hostname"`
	CPUCores      int    `json:"cpu_cores"`
	SN            string `json:"sn"`
	BareMetal     bool   `json:"bare_metal"`
}

func (s *SystemInfoCollector) Show() {
	printSystemInfo(s)
}

func (s *SystemInfoCollector) Execute() error {
	// 判断是不是物理机
	if utils.IsPhysicalMachine() {
		s.BareMetal = true
	}
	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		return fmt.Errorf("failed to get hostname: %v", err)
	}
	s.Hostname = hostname

	// 获取CPU核心数
	s.CPUCores = runtime.NumCPU()

	// 获取操作系统类型
	s.OS = runtime.GOOS

	// 根据操作系统类型确定操作系统家族
	switch s.OS {
	case "linux":
		s.OSFamily = "Linux"
	case "windows":
		s.OSFamily = "Windows"
	default:
		return fmt.Errorf("unsupported operating system: %s", s.OS)
	}

	// 获取操作系统版本和内核版本
	switch s.OS {
	case "linux":
		s.OSVersion, s.KernelVersion, err = getLinuxInfo()
	case "windows":
		s.OSVersion, s.KernelVersion, err = getWindowsInfo()
	default:
		return fmt.Errorf("unsupported operating system: %s", s.OS)
	}

	// 补充序列号
	if s.BareMetal {
		sn, err := getHardwareSerialNumber()
		if err != nil {
			return fmt.Errorf("failed to get hardware serial number: %v", err)
		}
		s.SN = sn
	}

	return err
}

func getLinuxInfo() (string, string, error) {
	// 获取Linux发行版信息
	cmd := exec.Command("cat", "/etc/os-release")
	output, err := cmd.Output()
	if err != nil {
		return "", "", err
	}

	// 解析os-release文件
	lines := strings.Split(string(output), "\n")
	var osVersion string
	for _, line := range lines {
		if strings.HasPrefix(line, "PRETTY_NAME=") {
			osVersion = strings.Trim(strings.TrimPrefix(line, "PRETTY_NAME="), "\"")
			break
		}
	}

	// 获取内核版本
	cmd = exec.Command("uname", "-r")
	output, err = cmd.Output()
	if err != nil {
		return "", "", err
	}
	kernelVersion := strings.TrimSpace(string(output))

	return osVersion, kernelVersion, nil
}

func getWindowsInfo() (string, string, error) {
	// 获取Windows版本
	cmd := exec.Command("cmd", "/c", "ver")
	output, err := cmd.Output()
	if err != nil {
		return "", "", err
	}
	osVersion := strings.TrimSpace(string(output))

	// Windows的内核版本通常与操作系统版本相同
	return osVersion, osVersion, nil
}

// 获取硬件序列号
func getHardwareSerialNumber() (string, error) {
	switch runtime.GOOS {
	case "windows":
		return getWindowsSerialNumber()
	case "linux":
		return getLinuxSerialNumber()
	default:
		return "", fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// Windows平台获取序列号
func getWindowsSerialNumber() (string, error) {
	// 尝试获取BIOS序列号
	cmd := exec.Command("wmic", "bios", "get", "serialnumber")
	output, err := cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		if len(lines) >= 2 {
			sn := strings.TrimSpace(lines[1])
			if sn != "" && !strings.Contains(strings.ToLower(sn), "to be filled") {
				return sn, nil
			}
		}
	}

	// 尝试获取主板序列号
	cmd = exec.Command("wmic", "baseboard", "get", "serialnumber")
	output, err = cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		if len(lines) >= 2 {
			sn := strings.TrimSpace(lines[1])
			if sn != "" && !strings.Contains(strings.ToLower(sn), "to be filled") {
				return sn, nil
			}
		}
	}

	// 尝试获取系统UUID
	cmd = exec.Command("wmic", "csproduct", "get", "uuid")
	output, err = cmd.Output()
	if err == nil {
		lines := strings.Split(string(output), "\n")
		if len(lines) >= 2 {
			sn := strings.TrimSpace(lines[1])
			if sn != "" {
				return sn, nil
			}
		}
	}

	return "", fmt.Errorf("failed to get Windows serial number")
}

// Linux平台获取序列号
func getLinuxSerialNumber() (string, error) {
	// 尝试使用dmidecode获取系统序列号
	cmd := exec.Command("dmidecode", "-s", "system-serial-number")
	output, err := cmd.Output()
	if err == nil {
		sn := strings.TrimSpace(string(output))
		if sn != "" && !strings.Contains(strings.ToLower(sn), "not specified") {
			return sn, nil
		}
	}

	// 尝试从/sys/class/dmi/id/board_serial获取
	data, err := os.ReadFile("/sys/class/dmi/id/board_serial")
	if err == nil {
		sn := strings.TrimSpace(string(data))
		if sn != "" && !strings.Contains(strings.ToLower(sn), "not specified") {
			return sn, nil
		}
	}

	// 尝试从/sys/class/dmi/id/product_serial获取
	data, err = os.ReadFile("/sys/class/dmi/id/product_serial")
	if err == nil {
		sn := strings.TrimSpace(string(data))
		if sn != "" && !strings.Contains(strings.ToLower(sn), "not specified") {
			return sn, nil
		}
	}

	// 尝试从/sys/class/dmi/id/product_uuid获取
	data, err = os.ReadFile("/sys/class/dmi/id/product_uuid")
	if err == nil {
		sn := strings.TrimSpace(string(data))
		if sn != "" {
			return sn, nil
		}
	}

	return "", fmt.Errorf("failed to get Linux serial number")
}

func (s *SystemInfoCollector) GetTaskName() string {
	return "system_info"
}

func (s *SystemInfoCollector) GetTaskType() string {
	return "system"
}

func (s *SystemInfoCollector) ShouldExecute() bool {
	return true
}

// PreCheck 预检测工具依赖是否满足
func (s *SystemInfoCollector) PreCheck() []string {
	var missingTools []string

	switch runtime.GOOS {
	case "linux":
		// 检查cat命令
		_, err := exec.LookPath("cat")
		if err != nil {
			missingTools = append(missingTools, "cat")
		}

		// 检查uname命令
		_, err = exec.LookPath("uname")
		if err != nil {
			missingTools = append(missingTools, "uname")
		}

		// 检查dmidecode命令
		_, err = exec.LookPath("dmidecode")
		if err != nil {
			missingTools = append(missingTools, "dmidecode")
		}

	case "windows":
		// 检查cmd命令
		_, err := exec.LookPath("cmd")
		if err != nil {
			missingTools = append(missingTools, "cmd")
		}

		// 检查wmic命令
		_, err = exec.LookPath("wmic")
		if err != nil {
			missingTools = append(missingTools, "wmic")
		}
	}

	return missingTools
}
