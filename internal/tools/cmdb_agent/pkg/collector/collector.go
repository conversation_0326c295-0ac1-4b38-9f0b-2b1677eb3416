package collector

import (
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/components"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/network"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/process"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/system"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/virtualization"
)

type CollectorManager struct {
	Tasks []Collector
}

func NewCollectorManager() *CollectorManager {
	return &CollectorManager{
		Tasks: make([]Collector, 0),
	}
}

// CollectTask 定义了采集任务的基本行为
type Collector interface {
	// Execute 执行采集任务
	Execute() error
	// GetTaskName 获取任务名称
	GetTaskName() string
	// GetTaskType 获取任务类型
	GetTaskType() string
	// ShouldExecute 判断任务是否需要执行
	ShouldExecute() bool
	// Show 展示采集结果
	Show()
	// PreCheck 预检测工具依赖是否满足
	// 返回缺失的工具列表和错误信息，如果没有缺失工具，则返回空列表
	PreCheck() []string
}

var HighFrequencyCollectors = []Collector{
	&virtualization.KVMCollector{},
	&network.NetworkInfoCollector{},
	&process.ProcessInfoCollector{},
	&system.SystemInfoCollector{},
}

var LowFrequencyCollectors = []Collector{
	&components.DiskInfoCollector{},
	&components.MemoryInfoCollector{},
	&components.OpticalModuleCollector{},
	&components.HardwareInfoCollector{},
}
