package virtualization

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

func printKVMInfo(collector *KVMCollector) {
	fmt.Printf("\n=== KVM虚拟化信息采集结果 ===\n")
	fmt.Printf("采集时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	fmt.Printf("KVM模块已加载: %v\n", collector.KVMInfo.KVMModuleLoaded)
	fmt.Printf("CPU支持虚拟化: %v\n", collector.KVMInfo.CPUVirtualization)
	fmt.Printf("Libvirt服务运行状态: %v\n", collector.KVMInfo.LibvirtRunning)
	fmt.Printf("QEMU进程数量: %d\n", collector.KVMInfo.QEMUProcessCount)
	fmt.Printf("虚拟机定义文件存在: %v\n", collector.KVMInfo.HasVMDefinitions)

	// 综合判断
	fmt.Printf("\n是否为KVM宿主机: %v\n", collector.KVMInfo.IsKVMHost)

	if collector.KVMInfo.IsKVMHost {
		if collector.KVMInfo.LibvirtVersion != "" {
			fmt.Printf("Libvirt版本: %s\n", collector.KVMInfo.LibvirtVersion)
		}

		if collector.KVMInfo.QEMUVersion != "" {
			fmt.Printf("QEMU版本: %s\n", collector.KVMInfo.QEMUVersion)
		}

		if len(collector.KVMInfo.VirtualMachines) > 0 {
			fmt.Printf("\n虚拟机列表:\n")
			for i, vm := range collector.KVMInfo.VirtualMachines {
				fmt.Printf("虚拟机 %d:\n", i+1)
				fmt.Printf("  名称: %s\n", vm.Name)
				fmt.Printf("  状态: %s\n", vm.State)

				if vm.AutoStartStr != "" {
					fmt.Printf("  自动启动: %s\n", vm.AutoStartStr)
				} else {
					fmt.Printf("  自动启动: 未知\n")
				}

				if len(vm.MACAddress) > 0 {
					fmt.Printf("  MAC地址列表:\n")
					for j, mac := range vm.MACAddress {
						fmt.Printf("    网卡 %d: %s\n", j+1, mac)
					}
				} else {
					fmt.Printf("  MAC地址: 未获取到\n")
				}
			}
		} else {
			fmt.Printf("\n未发现运行中的虚拟机\n")
		}
	}

	fmt.Printf("========================\n\n")
}

// KVMInfo 存储KVM相关信息
type KVMInfo struct {
	IsKVMHost         bool   `json:"is_kvm_host"`        // 是否是KVM宿主机
	KVMModuleLoaded   bool   `json:"kvm_module_loaded"`  // KVM模块是否加载
	VirtualMachines   []VM   `json:"virtual_machines"`   // 虚拟机列表
	LibvirtRunning    bool   `json:"libvirt_running"`    // libvirt服务是否运行
	LibvirtVersion    string `json:"libvirt_version"`    // libvirt版本
	QEMUVersion       string `json:"qemu_version"`       // QEMU版本
	QEMUProcessCount  int    `json:"qemu_process_count"` // QEMU进程数量
	HasVMDefinitions  bool   `json:"has_vm_definitions"` // 是否有虚拟机定义文件
	CPUVirtualization bool   `json:"cpu_virtualization"` // CPU是否支持虚拟化
}

// VM 存储虚拟机信息
type VM struct {
	Name         string   `json:"name"`           // 虚拟机名称
	State        string   `json:"state"`          // 虚拟机状态
	AutoStart    bool     `json:"auto_start"`     // 是否自动启动
	AutoStartStr string   `json:"auto_start_str"` // 自动启动状态原始字符串(enable/disable)
	MACAddress   []string `json:"mac_address"`    // MAC地址列表
}

// KVMCollector KVM信息采集器
type KVMCollector struct {
	KVMInfo KVMInfo
}

// NewKVMCollector 创建KVM信息采集器
func NewKVMCollector() *KVMCollector {
	return &KVMCollector{}
}

func (k *KVMCollector) Show() {
	printKVMInfo(k)
}

// Execute 执行信息采集
func (k *KVMCollector) Execute() error {
	// 1. 检查KVM模块是否加载
	k.checkKVMModule()

	// 2. 检查libvirt服务是否运行
	k.checkLibvirtService()

	// 3. 获取libvirt版本
	k.getLibvirtVersion()

	// 4. 获取QEMU版本
	k.getQEMUVersion()

	// 5. 获取虚拟机列表
	k.getVirtualMachines()

	// 6. 检查QEMU进程数量
	k.KVMInfo.QEMUProcessCount = k.countQEMUProcesses()

	// 7. 检查虚拟机定义文件
	k.KVMInfo.HasVMDefinitions = k.checkVMDefinitions()

	// 8. 检查CPU虚拟化支持
	k.checkCPUVirtualization()

	// 综合判断是否是KVM宿主机
	// 条件1: KVM模块已加载
	// 条件2: 以下条件至少满足一个:
	//   - libvirt服务运行
	//   - 有QEMU进程
	//   - 有虚拟机定义文件
	k.KVMInfo.IsKVMHost = k.KVMInfo.KVMModuleLoaded &&
		(k.KVMInfo.LibvirtRunning ||
			k.KVMInfo.QEMUProcessCount > 0 ||
			k.KVMInfo.HasVMDefinitions)

	return nil
}

// 检查KVM模块是否加载
func (k *KVMCollector) checkKVMModule() {
	// 检查 /dev/kvm 是否存在
	if _, err := os.Stat("/dev/kvm"); err == nil {
		k.KVMInfo.KVMModuleLoaded = true
		return
	}

	// 检查内核模块
	cmd := exec.Command("lsmod")
	output, err := cmd.Output()
	if err != nil {
		return
	}

	k.KVMInfo.KVMModuleLoaded = strings.Contains(string(output), "kvm")
}

// 检查libvirt服务是否运行
func (k *KVMCollector) checkLibvirtService() {
	// 使用systemctl检查服务状态
	cmd := exec.Command("systemctl", "is-active", "libvirtd")
	output, err := cmd.Output()
	if err == nil && strings.TrimSpace(string(output)) == "active" {
		k.KVMInfo.LibvirtRunning = true
		return
	}

	// 检查service命令
	cmd = exec.Command("service", "libvirtd", "status")
	output, err = cmd.Output()
	if err == nil && strings.Contains(string(output), "running") {
		k.KVMInfo.LibvirtRunning = true
		return
	}

	// 检查进程
	cmd = exec.Command("ps", "aux")
	output, err = cmd.Output()
	if err == nil && strings.Contains(string(output), "libvirtd") {
		k.KVMInfo.LibvirtRunning = true
	}
}

// 获取libvirt版本
func (k *KVMCollector) getLibvirtVersion() {
	cmds := []string{"virsh", "libvirtd"}

	for _, cmdName := range cmds {
		cmd := exec.Command(cmdName, "--version")
		output, err := cmd.Output()
		if err == nil {
			k.KVMInfo.LibvirtVersion = strings.TrimSpace(string(output))
			return
		}
	}
}

// 获取QEMU版本
func (k *KVMCollector) getQEMUVersion() {
	cmds := []struct {
		cmd  string
		args []string
	}{
		{cmd: "qemu-system-x86_64", args: []string{"--version"}},
		{cmd: "qemu-kvm", args: []string{"--version"}},
		{cmd: "kvm", args: []string{"--version"}},
	}

	for _, cmd := range cmds {
		execCmd := exec.Command(cmd.cmd, cmd.args...)
		output, err := execCmd.Output()
		if err == nil {
			lines := strings.Split(string(output), "\n")
			if len(lines) > 0 {
				k.KVMInfo.QEMUVersion = strings.TrimSpace(lines[0])
				return
			}
		}
	}
}

// 获取虚拟机列表
func (k *KVMCollector) getVirtualMachines() {
	// 使用virsh列出所有虚拟机
	cmd := exec.Command("virsh", "list", "--all")
	output, err := cmd.Output()
	if err != nil {
		return
	}

	lines := strings.Split(string(output), "\n")
	if len(lines) <= 2 {
		return
	}

	// 从第三行开始解析虚拟机信息
	for _, line := range lines[2:] {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) >= 3 {
			vm := VM{
				Name:       fields[1],
				State:      strings.Join(fields[2:], ""),
				MACAddress: []string{},
				AutoStart:  false,
			}

			// 获取详细信息，包括自动启动设置
			infoCmd := exec.Command("virsh", "dominfo", vm.Name)
			infoOutput, err := infoCmd.Output()
			if err == nil {
				infoLines := strings.Split(string(infoOutput), "\n")
				for _, infoLine := range infoLines {
					infoLine = strings.TrimSpace(infoLine)

					// 解析自动启动状态
					if strings.HasPrefix(infoLine, "Autostart:") {
						autoStartPart := strings.TrimPrefix(infoLine, "Autostart:")
						autoStartValue := strings.TrimSpace(autoStartPart)
						vm.AutoStartStr = autoStartValue
						vm.AutoStart = autoStartValue == "enable"
					}
				}
			}

			// 获取VM的MAC地址
			macCmd := exec.Command("virsh", "domiflist", vm.Name)
			macOutput, err := macCmd.Output()
			if err == nil {
				macLines := strings.Split(string(macOutput), "\n")
				// 跳过表头行，通常前两行是表头
				for _, macLine := range macLines[2:] {
					macLine = strings.TrimSpace(macLine)
					if macLine == "" {
						continue
					}

					macFields := strings.Fields(macLine)
					// 通常MAC地址在第三列
					if len(macFields) >= 5 {
						// 添加MAC地址到列表
						vm.MACAddress = append(vm.MACAddress, macFields[4])
					}
				}
			}

			k.KVMInfo.VirtualMachines = append(k.KVMInfo.VirtualMachines, vm)
		}
	}
}

// 检查物理机上QEMU进程数量
func (k *KVMCollector) countQEMUProcesses() int {
	cmd := exec.Command("ps", "aux")
	output, err := cmd.Output()
	if err != nil {
		return 0
	}

	count := 0
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "qemu-kvm") || strings.Contains(line, "qemu-system") {
			count++
		}
	}

	return count
}

// 检查虚拟机定义目录
func (k *KVMCollector) checkVMDefinitions() bool {
	paths := []string{
		"/etc/libvirt/qemu",
		"/var/lib/libvirt/images",
		"/var/lib/libvirt/qemu",
	}

	for _, path := range paths {
		files, err := filepath.Glob(path + "/*")
		if err == nil && len(files) > 0 {
			return true
		}
	}

	return false
}

// 检查CPU虚拟化支持
func (k *KVMCollector) checkCPUVirtualization() {
	// 检查/proc/cpuinfo中的虚拟化标志
	file, err := os.ReadFile("/proc/cpuinfo")
	if err == nil {
		content := string(file)
		// 对于Intel CPU，查找vmx标志
		// 对于AMD CPU，查找svm标志
		if strings.Contains(content, "vmx") || strings.Contains(content, "svm") {
			k.KVMInfo.CPUVirtualization = true
			return
		}
	}

	// 尝试使用lscpu命令
	cmd := exec.Command("lscpu")
	output, err := cmd.Output()
	if err == nil {
		content := string(output)
		if strings.Contains(content, "Virtualization:") &&
			(strings.Contains(content, "VT-x") || strings.Contains(content, "AMD-V")) {
			k.KVMInfo.CPUVirtualization = true
			return
		}
	}
}

// GetTaskName 获取任务名称
func (k *KVMCollector) GetTaskName() string {
	return "kvm_info"
}

// GetTaskType 获取任务类型
func (k *KVMCollector) GetTaskType() string {
	return "virtualization"
}

// ShouldExecute 是否应该执行采集
func (k *KVMCollector) ShouldExecute() bool {
	return true
}

// PreCheck 预检测工具依赖是否满足
func (k *KVMCollector) PreCheck() []string {
	var (
		missingTools []string
		err          error
	)

	if k.KVMInfo.IsKVMHost {
		_, err := exec.LookPath("virsh")
		if err != nil {
			missingTools = append(missingTools, "virsh")
		}
	}

	// 检查ps命令
	_, err = exec.LookPath("ps")
	if err != nil {
		missingTools = append(missingTools, "ps")
	}

	// 检查lscpu命令
	_, err = exec.LookPath("lscpu")
	if err != nil {
		missingTools = append(missingTools, "lscpu")
	}

	return missingTools
}
