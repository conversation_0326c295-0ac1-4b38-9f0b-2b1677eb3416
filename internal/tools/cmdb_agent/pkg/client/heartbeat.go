package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"ks-knoc-server/internal/tools/cmdb_agent/pkg/cache"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/components"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/network"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/process"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/system"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/virtualization"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/utils"
)

type HeartbeatStatus string

const (
	HeartbeatStatusOK      HeartbeatStatus = "ok"
	HeartbeatStatusWarning HeartbeatStatus = "warning"
	HeartbeatStatusError   HeartbeatStatus = "error"
)

// HeartbeatRequest 心跳请求结构
type HeartbeatRequest struct {
	// 通用设备标识
	DeviceID     string            `json:"device_id"`     // 设备唯一标识
	AgentID      string            `json:"agent_id"`      // Agent唯一标识
	AgentVersion string            `json:"agent_version"` // Agent版本
	IsVM         bool              `json:"is_vm"`         // 是否是虚拟机
	Labels       map[string]string `json:"labels"`        // 标签

	// SystemInfo 系统信息
	SystemInfo *system.SystemInfoCollector `json:"system_info"`
	// NetworkInfo 网络信息
	NetworkInfo *network.NetworkInfoCollector `json:"network_info"`
	// ProcessInfo 进程信息
	ProcessInfo *process.ProcessInfoCollector `json:"process_info"`
	// KVMInfo 虚拟机信息
	KVMInfo *virtualization.KVMInfo `json:"kvm_info"`
	// Hardware 硬件信息
	Hardware *components.HardwareInfoCollector `json:"hardware"`
	// DiskInfo 磁盘信息
	DiskInfo *components.DiskInfoCollector `json:"disk_info"`
	// MemoryInfo 内存信息
	MemoryInfo *components.MemoryInfoCollector `json:"memory_info"`
	// OpticalInfo 光模块信息
	OpticalInfo *components.OpticalModuleCollector `json:"optical_info"`

	// 心跳信息
	Timestamp int64    `json:"timestamp"` // 心跳时间戳
	Status    string   `json:"status"`    // 状态：online, offline, warning等
	Messages  []string `json:"messages"`  // 预警消息列表
}

// HeartbeatResponse 心跳响应结构
type HeartbeatResponse struct {
	Code    int    `json:"code"`    // 响应代码，0表示成功
	Message string `json:"message"` // 响应消息
}

// HeartbeatClient 心跳客户端
type HeartbeatClient struct {
	ServerURL  string           // 服务器URL
	Interval   time.Duration    // 心跳间隔
	DeviceInfo HeartbeatRequest // 设备信息
	AgentID    string           // Agent实例标识，重启后会变
	HttpClient *http.Client     // HTTP客户端

	// 缓存管理器
	CacheManager *cache.CacheManager
}

// NewHeartbeatClient 创建新的心跳客户端
func NewHeartbeatClient(serverURL string, interval time.Duration, cacheManager *cache.CacheManager) (*HeartbeatClient, error) {
	if serverURL == "" {
		return nil, fmt.Errorf("server URL cannot be empty")
	}

	// 初始化HTTP客户端，设置超时
	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	client := &HeartbeatClient{
		ServerURL:    serverURL,
		Interval:     interval,
		AgentID:      uuid.New().String(), // 生成Agent实例标识
		HttpClient:   httpClient,
		CacheManager: cacheManager,
	}

	// 初始化设备信息
	if err := client.UpdateDeviceInfo(); err != nil {
		return nil, fmt.Errorf("failed to initialize device info: %v", err)
	}

	return client, nil
}

// InitDeviceInfo 初始化设备信息
func (c *HeartbeatClient) UpdateDeviceInfo() error {
	info := HeartbeatRequest{
		AgentID:      c.AgentID,
		AgentVersion: "0.1.0", // 硬编码版本号，后期可以改成从配置或编译时获取
		Status:       string(HeartbeatStatusOK),
		Messages:     make([]string, 0),
		Labels:       make(map[string]string),
	}

	missingTools := make([]string, 0)

	// 从缓存获取系统信息
	sysData, err := c.CacheManager.Get(cache.CacheTypeSystem)
	if err != nil {
		zap.L().Error("Failed to get system info from cache: %v, using direct collection", zap.Error(err))
		// 如果无法从缓存获取，直接采集
		return fmt.Errorf("failed to get system info from cache: %v", err)
	}

	zap.L().Debug("系统信息Data", zap.String("sysData", string(sysData)))

	// 解析系统信息
	systemInfo := &system.SystemInfoCollector{}
	if err := json.Unmarshal(sysData, systemInfo); err != nil {
		zap.L().Error("Failed to unmarshal system info: %v", zap.Error(err))
		return fmt.Errorf("failed to unmarshal system info: %v", err)
	}
	info.SystemInfo = systemInfo

	msgs := systemInfo.PreCheck()
	if len(msgs) > 0 {
		missingTools = append(missingTools, msgs...)
	}

	// 从缓存获取网络信息
	netData, err := c.CacheManager.Get(cache.CacheTypeNetwork)
	if err != nil {
		zap.L().Error("Failed to get network info from cache: %v, using direct collection", zap.Error(err))
		return fmt.Errorf("failed to get network info from cache: %v", err)
	}

	// 解析网络信息
	networkInfo := network.NetworkInfoCollector{}
	if err := json.Unmarshal(netData, &networkInfo); err != nil {
		zap.L().Error("Failed to unmarshal network info: %v", zap.Error(err))
		return fmt.Errorf("failed to unmarshal network info: %v", err)
	}
	info.NetworkInfo = &networkInfo

	msgs = networkInfo.PreCheck()
	if len(msgs) > 0 {
		missingTools = append(missingTools, msgs...)
	}

	// 获取进程信息
	processData, err := c.CacheManager.Get(cache.CacheTypeProcess)
	if err != nil {
		zap.L().Error("Failed to get process info from cache: %v, using direct collection", zap.Error(err))
		return fmt.Errorf("failed to get process info from cache: %v", err)
	}

	// 解析进程信息
	processInfo := process.ProcessInfoCollector{}
	if err := json.Unmarshal(processData, &processInfo); err != nil {
		zap.L().Error("Failed to unmarshal process info: %v", zap.Error(err))
		return fmt.Errorf("failed to unmarshal process info: %v", err)
	}
	info.ProcessInfo = &processInfo

	msgs = processInfo.PreCheck()
	if len(msgs) > 0 {
		missingTools = append(missingTools, msgs...)
	}

	// 获取设备类型（物理机/虚拟机）信息
	physical := utils.IsPhysicalMachine()
	// 只有当设备是物理机的时候，需要关注如下信息
	if physical {
		// 设置设备属性以及标签
		info.IsVM = false
		info.Labels["machine_type"] = "pm"

		// 解析硬件信息
		hardwareData, err := c.CacheManager.Get(cache.CacheTypeHardware)
		if err != nil {
			zap.L().Error("Failed to get hardware info from cache: %v, using direct collection", zap.Error(err))
			return fmt.Errorf("failed to get hardware info from cache: %v", err)
		}

		// 解析硬件信息
		hardwareInfo := &components.HardwareInfoCollector{}
		if err := json.Unmarshal(hardwareData, &hardwareInfo); err != nil {
			zap.L().Error("Failed to unmarshal hardware info: %v", zap.Error(err))
			return fmt.Errorf("failed to unmarshal hardware info: %v", err)
		}

		msgs = hardwareInfo.PreCheck()
		if len(msgs) > 0 {
			missingTools = append(missingTools, msgs...)
		}
		info.Hardware = hardwareInfo

		// 解析磁盘信息
		diskData, err := c.CacheManager.Get(cache.CacheTypeDisk)
		if err != nil {
			zap.L().Error("Failed to get disk info from cache: %v, using direct collection", zap.Error(err))
			return fmt.Errorf("failed to get disk info from cache: %v", err)
		}

		// 解析磁盘信息
		diskInfo := components.DiskInfoCollector{}
		if err := json.Unmarshal(diskData, &diskInfo); err != nil {
			zap.L().Error("Failed to unmarshal disk info: %v", zap.Error(err))
			return fmt.Errorf("failed to unmarshal disk info: %v", err)
		}

		msgs = diskInfo.PreCheck()
		if len(msgs) > 0 {
			missingTools = append(missingTools, msgs...)
		}

		info.DiskInfo = &diskInfo

		// 解析内存信息
		memoryData, err := c.CacheManager.Get(cache.CacheTypeMemory)
		if err != nil {
			zap.L().Error("Failed to get memory info from cache: %v, using direct collection", zap.Error(err))
			return fmt.Errorf("failed to get memory info from cache: %v", err)
		}

		// 解析内存信息
		memoryInfo := components.MemoryInfoCollector{}
		if err := json.Unmarshal(memoryData, &memoryInfo); err != nil {
			zap.L().Error("Failed to unmarshal memory info: %v", zap.Error(err))
			return fmt.Errorf("failed to unmarshal memory info: %v", err)
		}

		msgs = memoryInfo.PreCheck()
		if len(msgs) > 0 {
			missingTools = append(missingTools, msgs...)
		}

		info.MemoryInfo = &memoryInfo

		// 解析光模块信息
		opticalData, err := c.CacheManager.Get(cache.CacheTypeOptical)
		if err != nil {
			zap.L().Error("Failed to get optical info from cache: %v, using direct collection", zap.Error(err))
			return fmt.Errorf("failed to get optical info from cache: %v", err)
		}

		// 解析光模块信息
		opticalInfo := components.OpticalModuleCollector{}
		if err := json.Unmarshal(opticalData, &opticalInfo); err != nil {
			zap.L().Error("Failed to unmarshal optical info: %v", zap.Error(err))
			return fmt.Errorf("failed to unmarshal optical info: %v", err)
		}

		msgs = opticalInfo.PreCheck()
		if len(msgs) > 0 {
			missingTools = append(missingTools, msgs...)
		}

		info.OpticalInfo = &opticalInfo

		// 解析虚拟机信息
		kvmData, err := c.CacheManager.Get(cache.CacheTypeKVM)
		if err != nil {
			zap.L().Error("Failed to get KVM info from cache: %v, using direct collection", zap.Error(err))
			return fmt.Errorf("failed to get KVM info from cache: %v", err)
		}

		// 解析虚拟机信息
		kvmInfo := virtualization.KVMCollector{}
		if err := json.Unmarshal(kvmData, &kvmInfo); err != nil {
			zap.L().Error("Failed to unmarshal KVM info: %v", zap.Error(err))
			return fmt.Errorf("failed to unmarshal KVM info: %v", err)
		}

		// 判断设备是kvm物理机还是虚拟机
		if kvmInfo.KVMInfo.IsKVMHost {
			info.Labels["service"] = "kvm"
		}

		msgs = kvmInfo.PreCheck()
		if len(msgs) > 0 {
			missingTools = append(missingTools, msgs...)
		}

		info.KVMInfo = &kvmInfo.KVMInfo
	} else {
		info.IsVM = true
		info.Labels["machine_type"] = "vm"
	}

	// 设置设备ID（唯一标识）
	info.DeviceID = c.generateDeviceID(info)

	// 如果存在预警消息，则设置状态为warning
	if len(missingTools) > 0 {
		info.Status = string(HeartbeatStatusWarning)

		for _, mssTool := range missingTools {
			info.Messages = append(info.Messages, fmt.Sprintf("%s 工具缺失", mssTool))
		}
	}

	// 更新deviceInfo的数据
	c.DeviceInfo = info

	zap.L().Debug("更新deviceInfo完毕", zap.Any("deviceInfo", c.DeviceInfo))

	return nil
}

func (c *HeartbeatClient) generateDeviceID(info HeartbeatRequest) string {
	if info.IsVM {
		// 当前是虚拟机，虚拟机以uuid作为唯一标识
		intfs := info.NetworkInfo.Interfaces
		if len(intfs) == 0 {
			return ""
		}
		// 虚拟机以第一个网卡的IP地址
		// tip: 为什么不用machine-id，因为镜像有base镜像，如果基于base镜像复制的话，machine-id会冲突
		intf := intfs[0]
		if len(intf.IPAddresses) == 0 {
			return ""
		}
		return intf.IPAddresses[0]
	} else {
		// 当前是物理机，物理机以sn序列号作为唯一标识
		return info.SystemInfo.SN
	}
}

// Start 开始心跳服务
func (c *HeartbeatClient) Start() {
	ticker := time.NewTicker(c.Interval)
	defer ticker.Stop()

	// 立即发送一次心跳
	c.sendHeartbeat()

	// 定期发送心跳
	for range ticker.C {
		c.sendHeartbeat()
	}
}

// 发送心跳请求
func (c *HeartbeatClient) sendHeartbeat() {
	// 更新时间戳
	c.DeviceInfo.Timestamp = time.Now().Unix()

	// 更新数据
	if err := c.UpdateDeviceInfo(); err != nil {
		zap.L().Error("Failed to update device info", zap.Error(err))
		return
	}

	// 序列化请求
	payload, err := json.Marshal(c.DeviceInfo)
	if err != nil {
		zap.L().Error("Failed to marshal heartbeat request", zap.Error(err))
		return
	}

	// 打印请求payload
	zap.L().Debug("发送心跳请求", zap.String("payload", string(payload)))

	// 发送请求
	resp, err := c.HttpClient.Post(
		c.ServerURL,
		"application/json",
		bytes.NewBuffer(payload),
	)
	if err != nil {
		zap.L().Error("Failed to send heartbeat", zap.Error(err))
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		zap.L().Error("Failed to read heartbeat response", zap.Error(err))
		return
	}

	// 解析响应
	var response HeartbeatResponse
	if err := json.Unmarshal(body, &response); err != nil {
		zap.L().Error("Failed to parse heartbeat response", zap.Error(err))
		return
	}

	// 检查响应状态
	if response.Code != 0 {
		zap.L().Error("Heartbeat error", zap.String("message", response.Message))
	} else {
		zap.L().Info("Heartbeat sent successfully")
	}
}
