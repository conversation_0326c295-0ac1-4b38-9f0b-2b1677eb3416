package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"ks-knoc-server/internal/common/logger"
	"ks-knoc-server/internal/tools/cmdb_agent/app/collector"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/cache"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/client"
	collInterface "ks-knoc-server/internal/tools/cmdb_agent/pkg/collector"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/components"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/network"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/process"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/system"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/collector/virtualization"
	"ks-knoc-server/internal/tools/cmdb_agent/pkg/utils"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	showVersion       bool
	showHelp          bool
	cmdbServerURL     string
	heartbeatInterval int
	logLevel          string
	versionTemplate   = fmt.Sprintf(`🐔 cmdb agent version information:
    Go version: %s
    OS/Arch:    %s/%s
	`, runtime.Version(), runtime.GOOS, runtime.GOARCH)
)

func init() {
	rootCmd.Flags().BoolVarP(&showVersion, "version", "v", false, "显示版本信息")
	rootCmd.Flags().BoolVarP(&showHelp, "help", "h", false, "显示帮助信息")
	rootCmd.Flags().StringVarP(&cmdbServerURL, "server", "s", "", "CMDB服务器URL")
	rootCmd.Flags().IntVarP(&heartbeatInterval, "interval", "i", 30, "心跳间隔（秒）")
	rootCmd.Flags().StringVarP(&logLevel, "log-level", "l", "info", "日志级别(debug, info, warn, error)")
}

var rootCmd = &cobra.Command{
	Use:   "cmdb_agent",
	Short: "knoc cmdb agent",
	Long:  "Knoc Cmdb Agent用于上报主机状态以及相关配件信息",
	Run: func(cmd *cobra.Command, args []string) {
		if showVersion {
			cmd.Println(versionTemplate)
			return
		}
		if showHelp {
			cmd.Help()
			return
		}

		// 初始化日志
		logOptions := logger.LogOptions{
			Path:  "stdout",
			Level: logLevel,
		}

		zapLog, err := logOptions.Init()
		if err != nil {
			log.Fatalf("初始化日志失败: %v", err)
		}

		// 替换全局日志
		zap.ReplaceGlobals(zapLog)

		// 检查CMDB服务器URL是否为空, 没有默认值
		if cmdbServerURL == "" {
			zap.L().Fatal("CMDB服务器URL不能为空")
			os.Exit(1)
		}

		runAgent()
	},
}

// registerCollectors 注册采集器
func registerCollectors(cm *cache.CacheManager) {
	agentCollector := collector.NewAgentCollector()

	// 注册系统信息采集任务（实时性较高）
	systemInfoCollector := &system.SystemInfoCollector{}
	agentCollector.RegisterCollector(systemInfoCollector)

	// 注册网络信息采集任务（实时性较高）
	networkInfoCollector := &network.NetworkInfoCollector{}
	agentCollector.RegisterCollector(networkInfoCollector)

	// 注册进程信息采集任务（实时性较高）
	processInfoCollector := process.NewProcessInfoCollector()
	agentCollector.RegisterCollector(processInfoCollector)

	// 注册KVM信息采集任务（实时性较低）
	kvmInfoCollector := virtualization.NewKVMCollector()
	agentCollector.RegisterCollector(kvmInfoCollector)

	// 注册磁盘信息采集任务（实时性较低）
	diskInfoCollector := &components.DiskInfoCollector{}
	agentCollector.RegisterCollector(diskInfoCollector)

	// 注册内存信息采集任务（实时性较低）
	memoryInfoCollector := &components.MemoryInfoCollector{}
	agentCollector.RegisterCollector(memoryInfoCollector)

	// 注册光模块信息采集任务（实时性较低）
	opticalModuleCollector := &components.OpticalModuleCollector{}
	agentCollector.RegisterCollector(opticalModuleCollector)

	// 注册硬件组件信息采集任务（实时性较低）
	hardwareInfoCollector := &components.HardwareInfoCollector{}
	agentCollector.RegisterCollector(hardwareInfoCollector)

	// 获取所有注册的采集器
	collectors := agentCollector.GetCollectors()
	for i := range collectors {
		// 为每个采集器创建一个独立的闭包，通过参数传递collector，避免捕获循环变量
		// 创建局部变量，确保每个闭包捕获自己的collector
		// 注意这里不要直接循环赋值，否则可能会导致所有回调函数使用同一个collector
		coll := collectors[i]
		cm.RegisterCallback(cache.CacheType(coll.GetTaskName()), func() (collInterface.Collector, error) {
			if err := coll.Execute(); err != nil {
				return nil, err
			}
			return coll, nil
		})
		zap.L().Debug("注册回调函数", zap.String("collector", coll.GetTaskName()))
	}

	// 预检测
}

// 定义采集器刷新间隔
const (
	// HighFrequencyInterval 实时性要求高的采集器，30秒刷新一次
	HighFrequencyInterval = 30 * time.Second
	// LowFrequencyInterval 实时性要求低的采集器，24小时刷新一次
	LowFrequencyInterval = 24 * time.Hour
)

// runAgent 运行Agent
func runAgent() {
	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建缓存管理器
	cacheManager := cache.GetCacheManager()

	// 注册系统信息
	registerCollectors(cacheManager)

	// 初始化所有缓存数据
	zap.L().Info("初始化所有缓存数据...")
	if err := cache.InitializeCache(cacheManager); err != nil {
		zap.L().Error("缓存初始化失败", zap.Error(err))
	}
	zap.L().Info("缓存初始化成功")

	// 启动缓存管理器的后台刷新任务
	cacheManager.StartBackgroundRefresh(ctx)

	// 打印所有缓存数据
	for _, key := range cacheManager.GetAll() {
		zap.L().Debug("缓存类型", zap.String("key", key))
	}

	// 启动心跳客户端，使用缓存数据
	go startHeartbeatClient(ctx, cacheManager)

	// 处理信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	if utils.IsPhysicalMachine() {
		zap.L().Info("启动服务，当前设备为物理机")
	} else {
		zap.L().Info("启动服务，当前设备为虚拟机")
	}

	// 等待信号
	select {
	case sig := <-sigChan:
		zap.L().Info("收到信号，准备关闭", zap.String("signal", sig.String()))

		// 优雅关闭 BigCache
		if err := cacheManager.Close(); err != nil {
			zap.L().Error("关闭缓存管理器失败", zap.Error(err))
		} else {
			zap.L().Info("缓存管理器已关闭")
		}

		cancel() // 取消上下文
	case <-ctx.Done():
		zap.L().Info("上下文取消", zap.Error(ctx.Err()))

		// 上下文取消时也需要关闭缓存
		if err := cacheManager.Close(); err != nil {
			zap.L().Error("关闭缓存管理器失败", zap.Error(err))
		}
	}
}

// 启动心跳客户端
func startHeartbeatClient(ctx context.Context, cacheManager *cache.CacheManager) {
	zap.L().Info("正在启动心跳服务", zap.String("cmdbServerURL", cmdbServerURL), zap.Int("heartbeatInterval", heartbeatInterval))

	// 创建心跳客户端
	interval := time.Duration(heartbeatInterval) * time.Second
	heartbeatClient, err := client.NewHeartbeatClient(cmdbServerURL, interval, cacheManager)
	if err != nil {
		zap.L().Error("创建心跳客户端失败", zap.Error(err))
		return
	}

	// 初始化设备信息
	if err := heartbeatClient.UpdateDeviceInfo(); err != nil {
		zap.L().Error("创建心跳客户端失败", zap.Error(err))
		return
	}

	// 启动心跳服务（在后台）
	go func() {
		// 监听上下文取消信号
		<-ctx.Done()
		zap.L().Info("心跳服务已停止")
	}()

	// 这个方法会阻塞并定期发送心跳
	heartbeatClient.Start()
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU())

	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
