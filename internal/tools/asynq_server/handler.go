package main

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"strings"

	zbx "ks-knoc-server/internal/common/base/model/monitor/zabbix"
	"ks-knoc-server/internal/common/hostname"
	"ks-knoc-server/internal/common/zabbix"

	"github.com/hibiken/asynq"
)

func syncZabbix(t *asynq.Task) (*hostname.Hostname, zabbix.SyncTask, *zbx.ZabbixProxy, error) {
	var (
		task zabbix.SyncTask
		err  error
		host *hostname.Hostname
	)

	// 序列化任务提交过来的负载
	if err := json.Unmarshal(t.Payload(), &task); err != nil {
		return nil, task, nil, err
	}

	if task.IP == "" {
		return nil, task, nil, ErrHostIPEmpty
	}

	if task.Hostname == "" {
		return nil, task, nil, ErrHostNameEmpty
	}

	// 首先要校验一下hostname是否是有效的
	if host, err = hostname.IsValidHostname(task.Hostname); err != nil {
		log.Println("Invalid hostname: ", task.Hostname)
		return nil, task, nil, ErrHostNameInvalid
	}

	// 获取Zabbix Token
	token, err := zabbix.GetZabbixToken()
	if err != nil {
		log.Println("获取Zabbix Token失败，失败原因为: ", err)
		return host, task, nil, err
	}

	// 查看一下主机是否已经被添加过
	zbxHost, err := zabbix.GetZabbixHostByName(token, task.Hostname)
	if err != nil {
		if errors.Is(err, zabbix.ErrHostNotFound) {
			log.Println("主机: ", task.Hostname, "未被添加过")
		} else {
			log.Println("获取Zabbix主机失败，失败原因为: ", err)
			return host, task, nil, err
		}
	} else {
		log.Println("主机: ", task.Hostname, "已存在")
		// 如果可以查到，说明主机名相同，如果IP也相同的话，说明不需要更新。
		// 否则这就是需要更新的，继续走下面的逻辑
		if task.IP == zbxHost.Interfaces[0].Ip {
			return host, task, nil, ErrHostAlreadyExists
		}
	}

	// 主机名校验通过后，我们的目标是匹配对应的模板和分组
	groupName := host.ConcatZabbixGroup()
	if groupName == "" {
		log.Println("无法通过主机名解析Zabbix主机组")
		return host, task, nil, ErrHostGroupNameInvalid
	}
	group, err := zabbix.GetZabbixGroupByName(token, groupName)
	if err != nil {
		log.Println("获取zabbix主机组: ", groupName, "失败，失败原因为: ", err)
		return host, task, nil, err
	}

	log.Println("主机组的ID为: ", group.GroupID)

	// 获取模板
	templates := host.GetZabbixTemplate()
	template, err := zabbix.GetZabbixTemplateByName(token, templates)
	if err != nil {
		log.Println("获取Zabbix模板失败，失败原因为: ", err)
		return host, task, nil, err
	}
	log.Println("模板的ID为: ", template.TemplateID)

	proxyInstance, err := randomProxy(token, *host)
	if err != nil {
		log.Println("获取Zabbix代理节点失败，失败原因为: ", err)
		return host, task, nil, err
	}

	// 打印一下resp
	log.Println("代理节点的ID结果为: ", proxyInstance.ProxyID)

	// 调用ZabbixServer同步信息
	log.Printf("同步Zabbix信息 主机名: %s, 组名称: %s, 模板名称: %v", task.Hostname, groupName, templates)
	log.Println("设备IP为: ", strings.TrimSpace(task.IP))

	if err := createZabbixHost(token, proxyInstance.ProxyID, task, group, template); err != nil {
		log.Println("同步Zabbix主机失败，失败原因为: ", err)
		return host, task, proxyInstance, err
	}

	return host, task, proxyInstance, nil
}

// zabbixSyncHandler 同步Zabbix信息
func zabbixSyncHandler(ctx context.Context, t *asynq.Task) error {
	host, task, proxyInstance, err := syncZabbix(t)
	// 如果host示例为nil，说明hostname校验失败，直接返回异常即可
	if host == nil {
		return ErrHostNameInvalid
	}
	// 判断完host再执行下面的逻辑，否则有可能控指针异常
	message := &msg{
		IP:           task.IP,
		GroupName:    host.ConcatZabbixGroup(),
		TemplateName: host.GetZabbixTemplate(),
	}
	if err != nil {
		if errors.Is(err, ErrHostAlreadyExists) {
			log.Println("主机已存在，无需二次添加")
			message.Hostname = task.Hostname
			message.Title = "同步Zabbix任务成功"
			message.Reason = "主机已存在，无需二次添加"
			if err := sendKimMessage(message); err != nil {
				log.Println("发送Kim消息失败，失败原因为: ", err)
			}
			return nil
		}
		
		log.Println("同步Zabbix主机失败，失败原因为: ", err)
		message.Hostname = task.Hostname
		message.Title = "同步Zabbix主机失败"
		message.Reason = err.Error()
		if err := sendKimMessage(message); err != nil {
			log.Println("发送Kim消息失败，失败原因为: ", err)
		}
		return err
	} else {
		message := &msg{
			IP:           task.IP,
			GroupName:    host.ConcatZabbixGroup(),
			TemplateName: host.GetZabbixTemplate(),
			Hostname:     task.Hostname,
			Title:        "同步Zabbix主机成功",
			ProxyName:    proxyInstance.Host,
		}
		if err := sendKimMessage(message); err != nil {
			log.Println("发送Kim消息失败，失败原因为: ", err)
		}
	}

	log.Println("同步Zabbix主机成功")

	return nil
}
