package main

import (
	"encoding/json"
	"errors"
	zbx "ks-knoc-server/internal/common/base/model/monitor/zabbix"
	"ks-knoc-server/internal/common/hostname"
	"ks-knoc-server/internal/common/zabbix"
	"log"
	"math/rand"
	"strings"
)

func getProxyNode(token string, host hostname.Hostname) ([]*zbx.ZabbixProxy, error) {
	// 获取代理节点，我们获取代理节点的依据是根据代理节点的tag进行获取
	// 主要的区分逻辑分别为region，building，location，基于如上三个属性基本可以确定要使用的Zabbix代理
	tagList := make([]ZabbixHostTag, 0)
	tagList = append(tagList, ZabbixHostTag{
		Tag:      "region",
		Value:    host.Region,
		Operator: OperatorEquals,
	})

	// 针对元中心特殊处理，不管你多少楼号统一都是Building为Y
	if host.Region == "BJ" && strings.HasPrefix(host.Building, "Y") {
		tagList = append(tagList, ZabbixHostTag{
			Tag:      "building",
			Value:    "Y",
			Operator: OperatorEquals,
		})
	} else {
		tagList = append(tagList, ZabbixHostTag{
			Tag:      "building",
			Value:    host.Building,
			Operator: OperatorEquals,
		})
	}

	// 针对海思科特殊处理一下，目前只有海思科的客服和审核职场需要通过Location来进行区分
	if host.Building == string(hostname.BuildingHSK) {
		tagList = append(tagList, ZabbixHostTag{
			Tag:      "location",
			Value:    host.Location,
			Operator: OperatorEquals,
		})
	}

	// 构建代理信息的查询请求
	reqBody := map[string]any{
		"selectTags": "extend",
		"output":     "extend",
		"tags":       tagList,
	}

	// 获取Zabbix代理主机信息
	proxies, err := zabbix.HostGet(token, reqBody)
	if err != nil {
		log.Println("获取Zabbix主机信息失败，失败原因为: ", err)
		return nil, err
	}

	result := proxies.Result.([]any)
	if len(result) == 0 {
		return nil, errors.New("找不到对应的Zabbix代理")
	}

	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return nil, err
	}

	var proxyList []map[string]any
	if err := json.Unmarshal(jsonBytes, &proxyList); err != nil {
		return nil, err
	}

	proxyNames := make([]string, 0)
	for _, proxy := range proxyList {
		proxyNames = append(proxyNames, proxy["host"].(string))
	}

	// 根据名称查询proxy的id
	zabbixProxies := make([]*zbx.ZabbixProxy, 0)
	if result := dbClient.Table(zabbixTableName).Where("host IN (?)", proxyNames).Find(&zabbixProxies); result.Error != nil {
		return nil, result.Error
	}

	return zabbixProxies, nil
}

// randomProxy 随机获取一个代理节点
func randomProxy(token string, host hostname.Hostname) (*zbx.ZabbixProxy, error) {
	proxies, err := getProxyNode(token, host)
	if err != nil {
		return nil, err
	}

	if len(proxies) == 0 {
		return nil, errors.New("未找到对应的Zabbix代理")
	}

	if len(proxies) == 1 {
		return proxies[0], nil
	} else {
		idx := rand.Intn(len(proxies))
		return proxies[idx], nil
	}
}

