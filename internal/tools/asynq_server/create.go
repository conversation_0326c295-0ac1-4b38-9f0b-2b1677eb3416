package main

import (
	"ks-knoc-server/internal/common/zabbix"
	zbx "ks-knoc-server/internal/common/base/model/monitor/zabbix"
	"log"
)

func createZabbixHost(token, proxyId string, task zabbix.SyncTask, group zbx.ZabbixHostGroup, template zbx.ZabbixTemplate) error {
	// 调用zabbix同步主机到zabbix
	if _, err := zabbix.HostCreate(token, map[string]any{
		"host":         task.Hostname,
		"proxy_hostid": proxyId,
		"interfaces": []map[string]any{
			{
				"type":  2,
				"main":  1,
				"useip": 1,
				"ip":    task.IP,
				"dns":   "",
				"port":  "161",
				"details": map[string]any{
					"version":   "2",
					"bulk":      "1",
					"community": "{$SNMP_COMMUNITY}",
				},
			},
		},
		"groups": []map[string]any{
			{
				"groupid": group.GroupID,
			},
		},
		"templates": []map[string]any{
			{
				"templateid": template.TemplateID,
			},
		},
	}); err != nil {
		log.Println("同步Zabbix主机失败，失败原因为: ", err)
		return err
	}
	return nil
}
