package main

import (
	"fmt"
	"ks-knoc-server/internal/common/hostname"
	"ks-knoc-server/internal/common/zabbix"
	"testing"
)

func TestGetProxyNode(t *testing.T) {
	var err error

	if err := initConfig(); err != nil {
		t.<PERSON>al(err)
	}

	token, err := zabbix.GetZabbixToken()
	if err != nil {
		t.<PERSON>al(err)
	}

	host := "K-HZ.XY.07-B2-IOT.AC.XZ-H5130-01"
	h, err := hostname.IsValidHostname(host)
	if err != nil {
		t.<PERSON>al(err)
	}

	proxies, err := getProxyNode(token, *h)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}

	for _, p := range proxies {
		fmt.Println(p.Host)
	}
}
