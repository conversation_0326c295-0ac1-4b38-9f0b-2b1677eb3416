package main

import (
	"flag"
	"log"

	"github.com/hibiken/asynq"
	"github.com/spf13/viper"
)

func main() {
	// 解析命令行参数
	flag.Parse()

	// 初始化配置文件
	if err := initConfig(); err != nil {
		log.Fatal("初始化配置文件失败，失败原因为: ", err)
	}

	// 初始化服务端
	srv := asynq.NewServer(
		asynq.RedisClientOpt{
			Addr:     viper.GetString("redis.addr"),
			DB:       viper.GetInt("redis.db"),
			Password: viper.GetString("redis.password"),
		},
		asynq.Config{
			Concurrency: 10,
		},
	)

	// 创建一个ServeMux
	mux := asynq.NewServeMux()
	mux.HandleFunc("zabbix_sync", zabbixSyncHandler)

	// Run接收asynq.Handler
	if err := srv.Run(mux); err != nil {
		log.Fatal(err)
	}
}
