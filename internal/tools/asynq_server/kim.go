package main

import (
	"bytes"
	"fmt"
	"ks-knoc-server/internal/common/kim"
	"text/template"
)

const (
	<PERSON><PERSON>ebHook = "https://kim-robot.kwaitalk.com/api/robot/send?key=d1f6551d-b266-4117-99ca-f310963f50f6"
	syncTmpl   = `### {{.Title}}
{{if .Reason}}
<font color='red'>{{.Reason}}</font>
{{end}}
- 主机名: {{.Hostname}}
- IP: {{.IP}}
- 主机组: {{.GroupName}}
- 模板: {{.TemplateName}}
{{if .ProxyName}}
- 代理: {{.ProxyName}}
{{end}}
`
)

type msg struct {
	Title        string
	Hostname     string
	IP           string
	GroupName    string
	TemplateName string
	ProxyName    string
	Reason       string
}

func generateKimMessage(message *msg) string {
	tmpl, err := template.New("zabbixSync").Parse(syncTmpl)
	if err != nil {
		return fmt.Sprintf("消息模板解析失败: %s", err.Error())
	}
	buf := new(bytes.Buffer)
	if err := tmpl.Execute(buf, message); err != nil {
		return fmt.Sprintf("消息模板解析失败: %s", err.Error())
	}
	return buf.String()
}

func sendKimMessage(message *msg) error {
	md := kim.NewKimRobotMarkdown(generateKimMessage(message))
	req, err := kim.NewKimRobotRequest(KimWebHook, md)
	if err != nil {
		return err
	}
	resp := new(kim.KimRobotResponse)
	if err := req.Do().Into(resp); err != nil {
		return err
	}
	return nil
}
