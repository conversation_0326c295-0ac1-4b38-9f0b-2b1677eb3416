package app

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"ks-knoc-server/config"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"

	"go.mongodb.org/mongo-driver/bson"
)

var mgo *db.MongoOptions

func GetInvalidData(modelCode, configPath string) (err error) {
	ctx := context.Background()
	if err := config.NewConf(configPath); err != nil {
		panic(err)
	}
	mgo, err = db.NewMongoOptions().Init()
	if err != nil {
		return err
	}
	defer func() {
		_ = mgo.Close()
	}()

	// 首先看一下对应的模型有没有sn的字段，sn的字段默认以_sn结尾
	snFieldName := fmt.Sprintf("%s_sn", modelCode)
	var field v1.CommonModelAttribute
	if err := mgo.GetCollection("model_attr").Find(ctx, bson.M{
		"model_code": modelCode,
		"code":       snFieldName,
	}).One(&field); err != nil {
		return err
	}

	// 如果说有这个sn字段的话，那么接下来就要检索数据，找到没有sn的数据，因为我认为没有配置sn的数据是不合法的
	dataList := make([]v1.ModelData, 0)
	if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
		"model_code": modelCode,
	}).All(&dataList); err != nil {
		return err
	}

	if len(dataList) == 0 {
		return fmt.Errorf("%s模型下没有数据，请检查数据是否正确\n", modelCode)
	}

	invalidData := make([]v1.ModelData, 0)
	for _, data := range dataList {
		snFieldValue, ok := data.Data[snFieldName]
		// 取不到sn字段的，那么就是有问题的
		if !ok {
			invalidData = append(invalidData, data)
			continue
		}

		if strings.TrimSpace(snFieldValue.(string)) == "" {
			invalidData = append(invalidData, data)
			continue
		}
	}

	// 处理没有sn的数据
	for _, invalid := range invalidData {
		// 服务器数据的从属的父亲这一端是机柜
		if invalid.ParentID == "" {
			fmt.Printf("服务器名称: %s; 机柜名称: %s; 起始U位: %d\n", invalid.Name(), "未配置机柜关联", 0)
			continue
		}

		var parent v1.ModelData
		if err := mgo.GetCollection("model_data").Find(ctx, bson.M{
			"_id": invalid.ParentID,
		}).One(&parent); err != nil {
			return err
		}

		startU := 0
		startUField := fmt.Sprintf("%s_start_u", modelCode)
		// 没有这个字段，那么start_u默认是0
		startUFieldValue, ok := invalid.Data[startUField]

		startUFieldValueString, ok := startUFieldValue.(string)
		if !ok {
			fmt.Printf("%s的start_u的类型不是string, 而是%T", invalid.Name(), startUFieldValue)
		}

		if ok {
			// 有这个字段看看有没有这个值
			if strings.TrimSpace(startUFieldValueString) != "" {
				startU, err = strconv.Atoi(startUFieldValueString)
				if err != nil {
					startU = 0
				}
			}
		}

		fmt.Printf("服务器名称: %s; 机柜名称: %s; 起始U位: %d\n", invalid.Name(), parent.Name(), startU)
	}

	fmt.Printf("共有%d条数据，其中%d条数据没有sn字段\n", len(dataList), len(invalidData))

	return nil
}
