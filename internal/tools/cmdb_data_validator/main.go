package main

import (
	"fmt"
	"os"

	"ks-knoc-server/internal/tools/cmdb_data_validator/app"

	"github.com/spf13/cobra"
)

var (
	modelCode  string
	configPath string
	endpoint   string
	token      string
)

func getDataWithOutSN(cmd *cobra.Command, args []string) {
	if err := app.GetInvalidData(modelCode, configPath); err != nil {
		fmt.Println(err)
		return
	}
	return
}

func main() {
	cmd := &cobra.Command{
		Use:   "cmdb_data_validator",
		Short: "cmdb_data_validator",
		Run:   getDataWithOutSN,
	}

	cmd.Flags().StringVarP(&modelCode, "model_code", "m", "", "模型的唯一标识")
	_ = cmd.MarkFlagRequired("model_code")

	cmd.Flags().StringVarP(&configPath, "config", "c", "", "配置文件的位置")
	_ = cmd.MarkFlagRequired("config")

	if err := cmd.Execute(); err != nil {
		os.Exit(1)
	}
}
