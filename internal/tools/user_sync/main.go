package main

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"runtime"
	"strings"
	"time"

	"ks-knoc-server/internal/common/base/model/user"
	"ks-knoc-server/internal/common/openapi"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kconf"
	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	"git.corp.kuaishou.com/infra/infra-framework-go.git/scheduler"
	"github.com/fatih/color"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
)

var (
	saveToFile = flag.Bool("saveToFile", false, "是否将用户信息保存到文件中")
	local      = flag.Bool("local", false, "是否从本地文件中读取用户信息")

	// 记录一下最大内存使用情况
	maxAlloc uint64

	// kdb实例
	db *kdb.Korm

	err error
)

const (
	kconfKeyName    = "it.knoc.knocUserInfo"
	tableName       = "user_info"
	updateBatchSize = 5000
	taskName        = "basic_userinfo_sync"
)

type UserStatus string

func (s UserStatus) String() string {
	return string(s)
}

const (
	// 用户状态：在职、离职
	UserStatusActive   UserStatus = "A"
	UserStatusInactive UserStatus = "T"
)

// InitConfig 初始化配置
func InitConfig() error {
	strConfig, err := kconf.GetStringConfig(kconfKeyName)
	if err != nil {
		return err
	}
	viper.SetConfigType("yaml")
	if err := viper.ReadConfig(bytes.NewBuffer([]byte(strConfig))); err != nil {
		return err
	}
	return nil
}

// InitKDB 初始化数据库
func InitKDB() (*kdb.Korm, error) {
	var (
		bizDef                    = viper.GetString("kdb.bizDef")
		cluster                   = viper.GetString("kdb.cluster")
		ignoreRecordNotFoundError = viper.GetBool("kdb.ignoreRecordNotFoundError")
	)
	db, err := kdb.Open(kdb.Option{
		BizDef:                    bizDef,
		Cluster:                   cluster,
		WithTraceLog:              false,
		IgnoreRecordNotFoundError: ignoreRecordNotFoundError,
		ConnectionTimeout:         360,
	})

	zap.L().Debug("KdbCluterInfo", zap.String("biz", bizDef), zap.String("cluster", cluster))
	if err != nil {
		log.Printf("[Error] 初始化kdb失败, err: %s\n", err.Error())
		return nil, err
	}
	return db, nil
}

// 获取用户信息
// 如果saveToFile为true，则将用户信息保存到文件中, 即持久化
func fetchUserInfo(saveToFile bool) ([]openapi.UserInfoWithStatus, error) {
	userInfo, err := openapi.GetAllUserInfo()
	if err != nil {
		return nil, err
	}
	if saveToFile {
		log.Println("记录主数据信息到文件")
		mainBytes, err := json.Marshal(userInfo)
		if err != nil {
			return nil, err
		}
		if err := os.WriteFile("user_info.json", mainBytes, 0644); err != nil {
			return nil, err
		}
	} else {
		log.Println("不记录主数据信息到文件")
	}
	return userInfo, nil
}

func fetchUserInfoFromDB() ([]user.User, error) {
	var totalUserInDB int64
	if result := db.Table(tableName).Count(&totalUserInDB); result.Error != nil {
		return nil, result.Error
	}
	users := make([]user.User, totalUserInDB)
	if result := db.Table(tableName).Find(&users); result.Error != nil {
		return nil, result.Error
	}
	return users, nil
}

func buildUserInfoMapForDataBase() (map[string]*user.User, int) {
	userInDB, err := fetchUserInfoFromDB()
	if err != nil {
		log.Printf("[Error] 获取数据库用户信息失败, err: %s\n", err.Error())
		return nil, 0
	}
	userCnt := len(userInDB)
	// 其中key为username，value为user的对象
	usersInDBMap := make(map[string]*user.User, userCnt)
	for i := range userInDB {
		// 清理从数据库读出的数据，去除首尾空格，并统一转为小写作为key
		cleanedUsername := strings.TrimSpace(userInDB[i].Username)
		key := strings.ToLower(cleanedUsername)
		// 同时更新struct中的值为清理后的值，确保后续逻辑使用的数据是干净的
		userInDB[i].Username = cleanedUsername
		usersInDBMap[key] = &userInDB[i]
	}
	return usersInDBMap, userCnt
}

func buildUserInfoMapForOpenApi() (map[string]*openapi.UserInfoWithStatus, int) {
	var (
		err            error
		remoteUserList = make([]openapi.UserInfoWithStatus, 0)
	)

	log.Println("[Debug] saveToFile flag value: ", *saveToFile)
	log.Println("[Debug] local flag value: ", *local)

	if *local {
		log.Println("从本地文件读取配置")
		// 从本地文件读取配置
		file, err := os.Open("user_info.json")
		if err != nil {
			log.Printf("[Error] 打开文件失败, err: %s\n", err.Error())
			return nil, 0
		}
		defer file.Close()
		if err := json.NewDecoder(file).Decode(&remoteUserList); err != nil {
			log.Printf("[Error] 解析文件失败, err: %s\n", err.Error())
			return nil, 0
		}
	} else {
		log.Println("从OpenAPI获取用户信息")
		// 调用openapi获取全量的用户信息，作为新数据
		// 这里的数据是最全的，员工信息只增不减，离职员工不会删除，但是会更新为离职状态
		remoteUserList, err = fetchUserInfo(*saveToFile)
		if err != nil {
			log.Printf("[Error] 获取主数据信息失败, err: %s\n", err.Error())
			return nil, 0
		}
	}

	remoteUserMap := make(map[string]*openapi.UserInfoWithStatus, len(remoteUserList))
	for i := range remoteUserList {
		// 清理从OpenAPI读出的数据
		cleanedUsername := strings.TrimSpace(remoteUserList[i].Username)
		key := strings.ToLower(cleanedUsername)
		// 同时更新struct中的值为清理后的值
		remoteUserList[i].Username = cleanedUsername
		remoteUserMap[key] = &remoteUserList[i]
	}
	return remoteUserMap, len(remoteUserList)
}

// checkUserHasChanged 检查用户是否发生变化
func checkUserHasChanged(oldUser, newUser *user.User) bool {
	if oldUser.Name != newUser.Name {
		return true
	}
	if oldUser.Username != newUser.Username {
		return true
	}
	if oldUser.Number != newUser.Number {
		return true
	}
	if oldUser.Email != newUser.Email {
		return true
	}
	if oldUser.Photo != newUser.Photo {
		return true
	}
	if oldUser.DisplayName != newUser.DisplayName {
		return true
	}
	if oldUser.StatusCode != newUser.StatusCode {
		return true
	}
	return false
}

// SyncUserInfo 同步用户信息, 与数据库中的内容进行对比
func SyncUserInfo() (string, error) {
	var (
		createdUserCount int
		updatedUserCount int
		totalUserCount   int
	)

	// 从数据库中获取用户信息
	usersInDBMap, dbUserCnt := buildUserInfoMapForDataBase()
	if usersInDBMap == nil {
		return "", fmt.Errorf("获取数据库用户信息失败")
	}
	log.Printf("[Info] 从数据库获取到 %d 个用户", dbUserCnt)

	// 从远端获取用户信息
	remoteUserMap, remoteUserCnt := buildUserInfoMapForOpenApi()
	if remoteUserMap == nil {
		return "", fmt.Errorf("获取远端用户信息失败")
	}
	log.Printf("[Info] 从远端获取到 %d 个用户", remoteUserCnt)

	// 记录总用户数
	totalUserCount = remoteUserCnt

	// 将远端用户信息转换为本地用户格式
	usersInOpenApi := make(map[string]*user.User, len(remoteUserMap))
	for key, remoteUserPtr := range remoteUserMap {
		// 此处的 remoteUserPtr.Username 已经是在 buildUserInfoMapForOpenApi 中被清理过的
		u := user.User{
			Name:        remoteUserPtr.Name,
			Username:    remoteUserPtr.Username,
			Number:      remoteUserPtr.Number,
			Email:       remoteUserPtr.Email,
			Photo:       remoteUserPtr.Photo,
			DisplayName: remoteUserPtr.DisplayName,
			StatusCode:  remoteUserPtr.Status.Code,
			StatusName:  remoteUserPtr.Status.Name,
		}
		usersInOpenApi[key] = &u // key 也是清理过的
	}

	// 检测和记录重复的用户名 (这个检测现在主要用于日志记录，因为map已经去重了)
	log.Printf("[Info] 远端获取到 %d 个用户，转换并去重后得到 %d 个唯一用户", remoteUserCnt, len(usersInOpenApi))
	if remoteUserCnt != len(usersInOpenApi) {
		log.Printf("[Warning] 远端数据中存在重复用户名，已自动去重: 原始 %d 个，去重后 %d 个", remoteUserCnt, len(usersInOpenApi))
	}

	// 比较两个map，新增的用户, 在openapi中，但是不在数据库的
	createdUserName := make([]string, 0)
	for usernameKey := range usersInOpenApi {
		if _, exist := usersInDBMap[usernameKey]; !exist {
			if usernameKey == "wb_pedro20" {
				log.Printf("[Debug] 发现 wb_pedro20 在待创建列表中, 数据库Map中不存在")
			}
			// usernameKey 是清理过的key，用它直接从map中取user对象，再取其Username字段
			createdUserName = append(createdUserName, usersInOpenApi[usernameKey].Username)
		}
	}
	createdUserCount = len(createdUserName)

	// 创建新用户
	userToBeCreate := make([]*user.User, 0)
	for _, userName := range createdUserName {
		// userName 是清理过的，直接转小写作为key
		key := strings.ToLower(userName)
		nw := usersInOpenApi[key]
		userToBeCreate = append(userToBeCreate, nw)
	}

	// 创建新用户需要分批添加
	if len(userToBeCreate) > 0 {
		// 创建新用户也分批添加，防止一次性创建太多用户导致报错
		log.Printf("[Create] 总共需要创建 %d 个用户\n", createdUserCount)
		for i := 0; i < len(userToBeCreate); i += updateBatchSize {
			end := min(i+updateBatchSize, len(userToBeCreate))
			batch := userToBeCreate[i:end]

			// 使用 UPSERT (INSERT ... ON DUPLICATE KEY IGNORE) 逻辑
			// 这是一种防御性编程，能防止因为未预料到的数据问题导致程序崩溃
			if result := db.Table(tableName).Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "username"}},
				DoNothing: true,
			}).Create(&batch); result.Error != nil {
				log.Printf("[Error] 创建用户失败, err: %s\n", result.Error.Error())
				return "", result.Error
			}
			log.Printf("[Create] 成功处理 %d-%d 个用户\n", i, end)
		}
	}

	// 重叠的用户，我们要比对是否要进行更新
	updatedUsers := make([]string, 0)
	for usernameKey := range usersInOpenApi {
		if _, exist := usersInDBMap[usernameKey]; exist {
			// usernameKey 是清理过的key，用它直接从map中取user对象，再取其Username字段
			updatedUsers = append(updatedUsers, usersInOpenApi[usernameKey].Username)
		}
	}

	// 创建一个容器用来保存要更新的数据，当然交集的用户并不一定就需要更新。需要更新的大多数是离职的用户，需要更新用户的状态。
	userToBeUpdated := make([]*user.User, 0)
	// 遍历逐个对比新老用户
	for _, userName := range updatedUsers {
		// userName 是清理过的，直接转小写作为key
		key := strings.ToLower(userName)
		oldU := usersInDBMap[key]
		newU := usersInOpenApi[key]
		if checkUserHasChanged(oldU, newU) {
			userToBeUpdated = append(userToBeUpdated, newU)
		}
	}

	// 更新用户计数
	updatedUserCount = len(userToBeUpdated)

	// 更新老用户
	// 分批更新数据，如果全量更新会触发报错：Error 1390 (HY000): Prepared statement contains too many placeholders
	// 错误通常是由于MySQL的占位符数量超过了其默认限制所导致的。MySQL默认的占位符限制是m*n<65535，其中m是行数，n是列数
	log.Printf("[Update] 总共需要更新 %d 个用户\n", updatedUserCount)
	for i := 0; i < len(userToBeUpdated); i += updateBatchSize {
		end := min(i+updateBatchSize, len(userToBeUpdated))
		batch := userToBeUpdated[i:end]
		if result := db.Table(tableName).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "username"}},
			DoUpdates: clause.AssignmentColumns([]string{"status_code", "status_name", "number", "email", "photo", "display_name", "name"}),
		}).Create(&batch); result.Error != nil {
			log.Printf("[Error] 更新用户失败, err: %s\n", result.Error.Error())
			return "", result.Error
		}
		log.Printf("[Update] 更新 %d-%d 个用户\n", i, end)
	}

	// 把同步结果返回
	message := fmt.Sprintf("[Success] 同步完成，共更新 %d 个用户，创建 %d 个用户，共 %d 个用户; 同步期间最大内存使用: %d MiB\n",
		updatedUserCount, createdUserCount, totalUserCount, maxAlloc)
	return message, nil
}

type SyncUserInfoHandler struct {
	MaxAlloc uint64
	name     string
	bizDef   string
}

func (h *SyncUserInfoHandler) GetName() string {
	return h.name
}

func (h *SyncUserInfoHandler) GetBizDef() string {
	return h.bizDef
}

func (h *SyncUserInfoHandler) Execute(ctx context.Context, t *scheduler.TaskContext) *scheduler.TaskResult {
	select {
	case <-ctx.Done():
		return nil
	default:
		msg, err := SyncUserInfo()
		if err != nil {
			return &scheduler.TaskResult{
				Status:  scheduler.ResultFail,
				Message: err.Error(),
			}
		}
		return &scheduler.TaskResult{
			Status:  scheduler.ResultSuccess,
			Message: msg,
		}
	}
}

func main() {
	flag.Parse()

	log.Println("开始同步用户信息")
	log.Println("[Debug] saveToFile flag value: ", *saveToFile)
	log.Println("[Debug] local flag value: ", *local)

	// 定义一个 goroutine 来定期打印内存使用情况
	go func() {
		for {
			var mem runtime.MemStats
			runtime.ReadMemStats(&mem)

			// fmt.Printf("Alloc = %v MiB", mem.Alloc/1024/1024)
			// fmt.Printf("\tTotalAlloc = %v MiB", mem.TotalAlloc/1024/1024)
			// fmt.Printf("\tSys = %v MiB", mem.Sys/1024/1024)
			// fmt.Printf("\tNumGC = %v\n", mem.NumGC)

			currentAlloc := mem.Alloc / 1024 / 1024
			if currentAlloc > maxAlloc {
				maxAlloc = currentAlloc
			}

			time.Sleep(time.Second)
		}
	}()

	// 初始化配置
	if err = InitConfig(); err != nil {
		color.Red("init config failed, err: %s", err.Error())
		return
	}

	// 初始化kdb
	db, err = InitKDB()
	if err != nil {
		color.Red("init kdb failed, err: %s", err.Error())
		return
	}

	// 初始化handler
	task := &SyncUserInfoHandler{
		name:   taskName,
		bizDef: "kuaishou/ismd/it/ops",
	}

	if err := scheduler.AddTask(task); err != nil {
		log.Fatalf("add task failed, err: %s", err.Error())
	}

	log.Fatal(scheduler.Run(6060))
}
