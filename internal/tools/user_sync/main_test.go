package main

import (
	"testing"

	"github.com/fatih/color"
)

func TestFetchUserInfo(t *testing.T) {
	var (
		err error
	)

	// 初始化配置
	if err = InitConfig(); err != nil {
		color.Red("init config failed, err: %s", err.Error())
		return
	}

	// 初始化kdb
	db, err = InitKDB()
	if err != nil {
		color.Red("init kdb failed, err: %s", err.<PERSON>rror())
		return
	}

	_, err = fetchUserInfo(true)
	if err != nil {
		t.<PERSON><PERSON>("fetchUserInfo failed, err: %s", err.<PERSON>rror())
	}
}
