package sso

import (
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strings"

	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/openapi"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gopkg.in/cas.v2"
)

var (
	CasClient *cas.Client
	cookie    *http.Cookie
)

// newSessionId generates a new opaque session identifier for use in the cookie.
func newSessionID() string {
	const alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	// generate 64 character string
	bytes := make([]byte, 64)
	rand.Read(bytes)

	for k, v := range bytes {
		bytes[k] = alphabet[v%byte(len(alphabet))]
	}

	return string(bytes)
}

// InitSSOClient 初始化SSO Client
func init() {
	u, _ := url.Parse("https://sso.corp.kuaishou.com/cas")
	cookie = &http.Cookie{
		Name:     "_cas_session",
		Value:    newSessionID(),
		Path:     "/",
		Domain:   "",
		MaxAge:   86400,
		HttpOnly: false,
		Secure:   false,
	}
	CasClient = cas.NewClient(&cas.Options{
		URL:    u,
		Cookie: cookie,
	})
}

// ShouldSkipCasCheck 配置哪些URL不需要登录验证
func ShouldSkipCasCheck(visitUrl string) bool {
	// 要跳过登录验证的话，前缀配置在这里
	skipCasUrls := []string{
		"127.0.0.1",
		"localhost",
	}

	for _, skipCasUrl := range skipCasUrls {
		if strings.HasPrefix(visitUrl, skipCasUrl) {
			return true
		}
	}
	return false
}

// ShouldSkipCasOriginCheck 配置哪些URL不需要登录验证
func ShouldSkipCasOriginCheck(visitUrl string) bool {
	zap.L().Debug("visitUrl", zap.String("visitUrl", visitUrl))
	skipCasUrls := []string{
		"http://127.0.0.1",
		"http://localhost",
		"https://localhost",
		"https://127.0.0.1",
	}

	for _, skipCasUrl := range skipCasUrls {
		if strings.HasPrefix(visitUrl, skipCasUrl) {
			return true
		}
	}
	return false
}

func LoginValidate(c *gin.Context) {
	zap.L().Info("sso function called.")

	if ShouldSkipCasCheck(c.Request.URL.String()) {
		zap.L().Debug("sso login skip")
		return
	}

	origin := c.Request.Header.Get("Origin")
	if ShouldSkipCasOriginCheck(origin) {
		zap.L().Debug("sso login skip")
		// 前端测试用
		c.Set("CASUsername", "wb_chendibo")
		return
	}

	for k, v := range c.Request.Header {
		zap.L().Debug("header", zap.String("key", k), zap.Any("value", v))
	}

	zap.L().Debug("debug request", zap.Any("request", c.Request.TLS))
	handler := CasClient.HandleFunc(func(w http.ResponseWriter, r *http.Request) {
		authenticated := cas.IsAuthenticated(r)
		if !authenticated {
			zap.L().Debug("sso login failed")
			// redirectUrl 是 sso 的登录地址
			redirectUrl, err := CasClient.LoginUrlForRequest(r)
			if err != nil {
				zap.L().Error("sso login failed", zap.Error(err))
				c.Abort()
				return
			}
			index := strings.Index(redirectUrl, "?")
			if index != -1 {
				redirectUrl = redirectUrl[:index]
			}
			zap.L().Debug("sso login failed", zap.String("redirectUrl", redirectUrl))
			redirectService := fmt.Sprintf("?service=https://%s/sso", r.Host)
			redirectUrl += redirectService
			c.JSON(http.StatusOK, core.Response{
				Code:    302,
				Data:    redirectUrl,
				Message: "redirect to sso login",
			})
			c.Abort()
		} else if r.URL.Path == "/logout" {
			CasClient.RedirectToLogout(w, r)
			c.Abort()
		} else if r.URL.Path == "/sso" {
			// 登录成功后，跳转到回调地址
			userLocation := c.Query("location")
			zap.L().Debug("sso login success", zap.String("location", userLocation))
			c.Redirect(http.StatusFound, userLocation)
			c.Abort()
		} else {
			zap.L().Debug("sso login success")
			c.Set("CASUsername", cas.Username(c.Request))
			c.Set("CASAttributes", cas.Attributes(c.Request))
			return
		}
	})

	handler.ServeHTTP(c.Writer, c.Request)
}

// GetLoginUserName 获取登录用户名
func GetLoginUserName(c *gin.Context) string {
	var userName string
	// 默认后端实现的SSO
	userName = c.GetString("CASUsername")
	if userName != "" {
		return userName
	}

	// 如果通过权限中台实现的话，那么上述方式是获取不到对应的用户的
	userName = c.Request.Header.Get("Username")
	if userName != "" {
		return userName
	}

	// 通过环境变量设置MOCK User
	userName = os.Getenv("MOCK_USER")
	if userName != "" {
		zap.L().Debug("使用mock用户", zap.String("mock用户", userName))
		return userName
	}

	// 与前端约定从x-custom-header中获取bpmUserToken
	bpmUserToken := c.Request.Header.Get("x-custom-header")
	if bpmUserToken != "" {
		zap.L().Debug("从Header中获取用户名", zap.String("bpmUserToken", bpmUserToken))
		// 解析bpmUserToken
		resp, err := openapi.GetByUserToken(&openapi.BPMUserTokenRequest{
			UserToken: bpmUserToken,
		})
		if err != nil {
			zap.L().Error("解析bpmUserToken失败", zap.Error(err))
			return ""
		}
		zap.L().Debug("解析bpmUserToken成功", zap.String("bpmUserName", resp.Data.UserName))
		return resp.Data.UserName
	}

	zap.L().Debug("未获取到登录用户名，使用默认值")

	return ""
}
