package sso

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"
	"gopkg.in/cas.v2"
)

type tkStore struct {
	rdb *redis.Client
}

func (t *tkStore) Read(id string) (*cas.AuthenticationResponse, error) {
	key := fmt.Sprintf("tk_%s", id)
	val, err := t.rdb.Get(key).Result()
	if err != nil {
		return nil, err
	}
	resp := &cas.AuthenticationResponse{}
	if err := json.Unmarshal([]byte(val), resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (t *tkStore) Write(id string, ticket *cas.AuthenticationResponse) error {
	key := fmt.Sprintf("tk_%s", id)
	val, err := json.Marshal(ticket)
	if err != nil {
		return err
	}
	if err := t.rdb.Set(key, val, time.Minute).Err(); err != nil {
		return err
	}
	return nil
}

func (t *tkStore) Delete(id string) error {
	key := fmt.Sprintf("tk_%s", id)
	if err := t.rdb.Del(key).Err(); err != nil {
		return err
	}
	return nil
}

func (t *tkStore) Clear() error {
	var cursor uint64
	for {
		keys, currentCursor, err := t.rdb.Scan(cursor, "tk_", 10).Result()
		if err != nil {
			return err
		}
		if len(keys) > 0 {
			for _, key := range keys {
				t.rdb.Del(key)
			}
		}
		if currentCursor == 0 {
			break
		}
		cursor = currentCursor
	}
	return nil
}

type sessionStore struct {
	rdb *redis.Client
}

func (s *sessionStore) Get(sessionID string) (string, bool) {
	key := fmt.Sprintf("ss_%s", sessionID)
	tk, err := s.rdb.Get(key).Result()
	if err != nil {
		return "", false
	}
	return tk, true
}

func (s *sessionStore) Set(sessionID, ticket string) error {
	key := fmt.Sprintf("ss_%s", sessionID)
	if err := s.rdb.Set(key, ticket, time.Minute).Err(); err != nil {
		return err
	}
	return nil
}

func (s *sessionStore) Delete(sessionID string) error {
	key := fmt.Sprintf("ss_%s", sessionID)
	if err := s.rdb.Del(key).Err(); err != nil {
		return err
	}
	return nil
}
