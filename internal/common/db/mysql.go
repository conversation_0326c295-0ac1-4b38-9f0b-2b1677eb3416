package db

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
	apmmysql "go.elastic.co/apm/module/apmgormv2/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// MySQLOptions base model
type MySQLOptions struct {
	Host                      string        `yaml:"host"`
	Port                      string        `yaml:"port"`
	User                      string        `yaml:"user"`
	Password                  string        `yaml:"password"`
	Database                  string        `yaml:"database"`
	MaxOpenConn               int           `yaml:"maxOpenConn"`
	MaxIdleConn               int           `yaml:"maxIdleConn"`
	MaxConnectionLifeTime     time.Duration `yaml:"maxConnectionLifeTime"`
	Charset                   string        `yaml:"charset"`
	ParseTime                 bool          `yaml:"parseTime"`
	TimeZone                  string        `yaml:"timeZone"`
	DefaultStringSize         uint          `yaml:"defaultStringSize"`
	DisableDatetimePrecision  bool          `yaml:"disableDatetimePrecision"`
	SkipInitializeWithVersion bool          `yaml:"skipInitializeWithVersion"`
	AutoMigrate               bool          `yaml:"autoMigrate"`
	SlowSQL                   time.Duration `yaml:"slowSQL"`
	LogLevel                  string        `yaml:"logLevel"`
	IgnoreRecordNotFoundError bool          `yaml:"ignoreRecordNotFoundError"`
	Enable                    bool          `yaml:"enable"`
}

// NewMySQLOptions 初始化Mysql配置项
func NewMySQLOptions() *MySQLOptions {
	return &MySQLOptions{
		Host:                      viper.GetString("db.host"),
		Port:                      viper.GetString("db.port"),
		User:                      viper.GetString("db.user"),
		Password:                  viper.GetString("db.password"),
		Database:                  viper.GetString("db.database"),
		MaxOpenConn:               viper.GetInt("db.maxOpenConn"),
		MaxIdleConn:               viper.GetInt("db.maxIdleConn"),
		MaxConnectionLifeTime:     viper.GetDuration("db.maxConnectionLifeTime"),
		Charset:                   viper.GetString("db.charset"),
		ParseTime:                 viper.GetBool("db.parseTime"),
		TimeZone:                  viper.GetString("db.timeZone"),
		DefaultStringSize:         viper.GetUint("db.defaultStringSize"),
		DisableDatetimePrecision:  viper.GetBool("db.disableDatetimePrecision"),
		SkipInitializeWithVersion: viper.GetBool("db.skipInitializeWithVersion"),
		AutoMigrate:               viper.GetBool("db.autoMigrate"),
		SlowSQL:                   viper.GetDuration("db.slowSQL"),
		LogLevel:                  viper.GetString("db.logLevel"),
		IgnoreRecordNotFoundError: viper.GetBool("db.ignoreRecordNotFoundError"),
		Enable:                    viper.GetBool("db.enable"),
	}
}

// Init 初始化mysql连接
func (m *MySQLOptions) Init() (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=%t&loc=%s",
		m.User,
		m.Password,
		m.Host,
		m.Port,
		m.Database,
		m.Charset,
		m.ParseTime,
		m.TimeZone,
	)
	gormConf := m.setNewLogger(&gorm.Config{})
	db, err := gorm.Open(apmmysql.Open(dsn), gormConf)
	if err != nil {
		return nil, err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxOpenConns(m.MaxOpenConn)
	sqlDB.SetMaxIdleConns(m.MaxIdleConn)
	sqlDB.SetConnMaxLifetime(m.MaxConnectionLifeTime)

	return db, nil
}

func (m *MySQLOptions) setNewLogger(gConfig *gorm.Config) *gorm.Config {

	var (
		file    *os.File
		logFile string
		err     error
	)

	// 日志级别映射 error、info、warn
	logLevelMap := map[string]logger.LogLevel{
		"error": logger.Error,
		"info":  logger.Info,
		"warn":  logger.Warn,
	}
	var logLevel logger.LogLevel
	var ok bool
	if logLevel, ok = logLevelMap[m.LogLevel]; !ok {
		logLevel = logger.Error
	}

	logFilePath := viper.GetString("log.path")
	switch logFilePath {
	case "stdout":
		file = os.Stdout
	case "":
		logFile = "./logs" + viper.GetString("log.filePrefix") + "-sql." + time.Now().Format(viper.GetString("log.fileFormat")) + ".log"
		file, err = os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, os.ModePerm)
		if err != nil {
			log.Fatalf("open sql log file failed, err: %v", err)
			return nil
		}
	default:
		logFile = logFilePath + "/" + viper.GetString("log.filePrefix") + "-sql." + time.Now().Format(viper.GetString("log.fileFormat")) + ".log"
		file, err = os.OpenFile(logFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, os.ModePerm)
		if err != nil {
			log.Fatalf("open sql log file failed, err: %v", err)
			return nil
		}
	}

	// 初始化logger
	newLogger := logger.New(log.New(file, "\r\n", log.LstdFlags), logger.Config{
		SlowThreshold:             m.SlowSQL,                   // 慢SQL时间
		LogLevel:                  logLevel,                    // 记录日志级别
		IgnoreRecordNotFoundError: m.IgnoreRecordNotFoundError, // 是否忽略ErrRecordNotFound(未查到记录错误)
		Colorful:                  true,                        // 开关颜色
	})
	gConfig.Logger = newLogger
	return gConfig
}
