package db

import (
    "net/http"

    "github.com/olivere/elastic/v7"
    "github.com/spf13/viper"
    "go.elastic.co/apm/module/apmelasticsearch"
)

type ESOptions struct {
    DSN      string
    Sniffer  bool
    Username string
    Password string
}

func (e *ESOptions) GetDSN() string {
    return e.DSN
}

func NewESOptions() *ESOptions {
    return &ESOptions{
        DSN:      viper.GetString("es.host"),
        Sniffer:  viper.GetBool("es.sniffer"),
        Username: viper.GetString("es.username"),
        Password: viper.GetString("es.password"),
    }
}

func (e *ESOptions) Init() (client *elastic.Client, err error) {
    client, err = elastic.NewClient(
        elastic.SetURL(e.GetDSN()),
        elastic.SetSniff(false),
        elastic.SetHttpClient(&http.Client{Transport: apmelasticsearch.WrapRoundTripper(http.DefaultTransport)}),
        elastic.SetBasicAuth(e.<PERSON><PERSON><PERSON>, e.Password),
    )
    if err != nil {
        return
    }
    return
}
