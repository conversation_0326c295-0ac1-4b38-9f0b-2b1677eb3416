package db

import (
	"github.com/go-redis/redis/v7"
	"github.com/spf13/viper"
)

// RedisOptions ...
type RedisOptions struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
	PoolSize int    `yaml:"pool_size"`
}

// NewRedisOptions ...
func NewRedisOptions() *RedisOptions {
	return &RedisOptions{
		Addr:     viper.GetString("redis.addr"),
		Password: viper.GetString("redis.password"),
		DB:       viper.GetInt("redis.iotDB"),
		PoolSize: viper.GetInt("redis.poolSize"),
	}
}

// Init ...
func (r *RedisOptions) Init() (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     r.Addr,
		Password: r.Password,
		DB:       r.DB,
		PoolSize: r.PoolSize,
	})

	_, err := rdb.Ping().Result()
	if err != nil {
		return nil, err
	}

	return rdb, nil
}
