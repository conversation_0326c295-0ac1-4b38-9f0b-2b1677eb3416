package db

import (
	"context"
	"fmt"

	"github.com/qiniu/qmgo"
	"github.com/spf13/viper"
)

type DMode string

const (
	Single       DMode = "single"
	ShardCluster DMode = "shard"
)

type MongoOptions struct {
	Host           string          `yaml:"host"`           // 主机地址
	DBName         string          `yaml:"dbname"`         // 数据库的名称
	Username       string          `yaml:"username"`       // 用户名
	Password       string          `yaml:"password"`       // 密码
	ReplicaSetName string          `yaml:"replicaSetName"` // 副本集名称
	DeployMode     DMode           `yaml:"deploy_mode"`    // 部署模式
	Client         *qmgo.Client    `yaml:"client"`         // MongoDB Client
	Ctx            context.Context `yaml:"ctx"`            // MongoDB Context
}

func (m *MongoOptions) Close() (err error) {
	err = m.Client.Close(m.Ctx)
	if err != nil {
		return err
	}
	return nil
}

// GetClient 获取MongoDB Client
func (m *MongoOptions) GetClient() *qmgo.Client {
	return m.Client
}

// GetDataBase 获取MongoDB Database的对象
func (m *MongoOptions) GetDataBase() *qmgo.Database {
	return m.Client.Database(m.DBName)
}

// GetCollection 获取MongoDB Collection的对象
func (m *MongoOptions) GetCollection(coll string) *qmgo.Collection {
	return m.Client.Database(m.DBName).Collection(coll)
}

// NewMongoOptions 初始化MongoDB配置
func NewMongoOptions() *MongoOptions {
	return &MongoOptions{
		Host:           viper.GetString("mongodb.host"),
		DBName:         viper.GetString("mongodb.dbname"),
		Username:       viper.GetString("mongodb.username"),
		Password:       viper.GetString("mongodb.password"),
		ReplicaSetName: viper.GetString("mongodb.replicaSetName"),
	}
}

// Init 初始化MongoDB连接，并返回mongo opts的对象
func (m *MongoOptions) Init() (*MongoOptions, error) {
	// 初始化一个上下文
	m.Ctx = context.Background()
	dsn := ""
	switch m.DeployMode {
	case Single:
		// ********************************************************
		dsn = fmt.Sprintf("mongodb://%s:%s@%s?authSource=%s", m.Username, m.Password, m.Host, m.DBName)
	case ShardCluster:
		// to be implemented
		// 单机模式和分片模式的dsn格式不一样，这里需要根据不同的模式来进行拼接
		panic("shard cluster mode is not implemented")
	default:
		// 默认就是replicaset, 拼接一个dsn，dsn的一个样例可以参考如下
		// *********************************************************************************************
		dsn = fmt.Sprintf("mongodb://%s:%s@%s/?replicaSet=%s&authSource=%s",
			m.Username,
			m.Password,
			m.Host,
			m.ReplicaSetName,
			m.DBName)
	}

	var err error
	m.Client, err = qmgo.NewClient(m.Ctx, &qmgo.Config{Uri: dsn})

	// 判断连接是否有异常
	if err != nil {
		return nil, err
	}
	return m, nil
}

func (m *MongoOptions) StartTransaction() (*MongoTransaction, error) {
	s, err := m.Client.Session()
	if err != nil {
		return nil, err
	}
	return &MongoTransaction{s: s}, nil
}

// MongoTransaction 事务
type MongoTransaction struct {
	s *qmgo.Session
	// 这里的task需要携带上下文，因为在事务中，需要使用sessionCtx。上下文引用错误的话，会导致事务失效
	// 这里的ctx及其易与gin的ctx混淆，注意区分
	task []func(sessionCtx context.Context) error
}

func (m *MongoTransaction) Session() *qmgo.Session {
	return m.s
}

func (m *MongoTransaction) Wrapper(task func(sessionCtx context.Context) error) *MongoTransaction {
	if m.task == nil {
		m.task = make([]func(sessionCtx context.Context) error, 0)
	}
	m.task = append(m.task, task)
	return m
}

func (m *MongoTransaction) Do(ctx context.Context) error {
	defer m.s.EndSession(ctx)
	callback := func(sessionCtx context.Context) (interface{}, error) {
		for _, t := range m.task {
			if err := t(sessionCtx); err != nil {
				return nil, err
			}
		}
		return nil, nil
	}
	if _, err := m.s.StartTransaction(ctx, callback); err != nil {
		return err
	}

	return nil
}
