package dsl

import (
	"go.mongodb.org/mongo-driver/bson"
)

type QueryBuilder struct {
	filter bson.M
}

func NewQueryBuilder() *QueryBuilder {
	return &QueryBuilder{filter: bson.M{}}
}

func (q *QueryBuilder) SetQuery(filterKey, filterValue string) *QueryBuilder {
	q.filter[filterKey] = filterValue
	return q
}

func (q *QueryBuilder) And(query QueryBuilder) *QueryBuilder {
	return q
}

func (q *QueryBuilder) Or(query QueryBuilder) *QueryBuilder {
	return q
}
