package rndc

import (
	"bytes"
	"errors"
	"os/exec"
)

// CmdRndc 对rndc命令的封装
type CmdRndc struct {
}

func NewCmdRndc() *CmdRndc {
	return &CmdRndc{}
}

func (c *CmdRndc) Sync(zoneName string) error {
	return nil
}

func (c *CmdRndc) Null() error {
	if err := c.ExecuteCmd("bind.orb.local", []string{"null"}); err != nil {
		return err
	}
	return nil
}

func (c *CmdRndc) Reconfig() error {
	return nil
}

func (c *CmdRndc) ReloadZone(zoneName string) error {
	return nil
}

func (c *CmdRndc) Reload() error {
	return nil
}

func (c *CmdRndc) ExecuteCmd(server string, command []string) error {
	// 首先需要看下我们要执行的命令是否存在
	if _, err := exec.LookPath("rndc"); err != nil {
		return err
	}

	cmds := make([]string, 0)
	cmds = append(cmds, []string{"-s", server, "-p", "53"}...)
	cmds = append(cmds, command...)

	// 初始化命令, 使用tcp的方式确保一个update操作中的多条命令执行成功
	cmd := exec.Command("rndc", cmds...)

	// 由于要合并标准错误输出到标准输出，可以使用CombinedOutput
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return err
	}

	// 读取stdout的内容
	var errBuffer bytes.Buffer
	go func() {
		errBuffer.ReadFrom(stderr)
	}()

	// 异步的执行我们都nsupdate命令, cmd.Run和cmd.CombinedOutput都是同步的
	if err := cmd.Start(); err != nil {
		return err
	}

	// 接下来就是等待命令执行成功
	if err := cmd.Wait(); err != nil {
		return errors.New(errBuffer.String())
	}

	return nil
}
