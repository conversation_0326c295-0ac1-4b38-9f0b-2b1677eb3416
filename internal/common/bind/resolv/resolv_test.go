package resolv

import (
	"testing"

	"github.com/miekg/dns"
)

func TestResolv(t *testing.T) {
	domain := "k.com"
	dnsType := "a"
	ns := "172.22.233.179"

	ret, err := Resolv(domain, dnsType, ns, true)
	if err != nil {
		t.<PERSON><PERSON>(err.<PERSON><PERSON>())
		return
	}

	for _, rr := range ret {
		switch r := rr.(type) {
		case *dns.A:
			t.Logf("A: %s\n", r.A.String())
		case *dns.SOA:
			t.Logf("SOA: %s\n", r.String())
			t.Logf("Serial: %d\n", r.Serial)
		}
	}
}

func TestGetSOASerial(t *testing.T) {
	zoneName := "corp.kuaishou.com"
	ns := "bind.orb.local"

	serial, err := GetZoneSerial(zoneName, ns)
	if err != nil {
		t.Error(err.<PERSON><PERSON><PERSON>())
		return
	}

	t.Logf("Serial: %d\n", serial)
}
