package resolv

import (
	"errors"
	"strings"

	"github.com/miekg/dns"
)

func Resolv(domain, dnsType, ns string, recursion bool) ([]dns.RR, error) {
	if domain == "" || dnsType == "" || ns == "" {
		return nil, errors.New("域名, 解析请求类型, NS地址不得为空")
	}

	msg := new(dns.Msg)

	// 正常的nameserver IP地址不包含冒号，因此如果直接切的话，默认长度应该为1
	if len(strings.Split(ns, ":")) == 1 {
		ns = ns + ":53"
	}

	// 设置要查询的域名
	d := dns.Fqdn(domain)
	msg.SetQuestion(d, dns.StringToType[strings.ToUpper(dnsType)])

	// 是否发起的是递归请求
	msg.RecursionDesired = recursion

	client := new(dns.Client)

	// 与服务端发起请求
	response, _, err := client.Exchange(msg, ns)
	if err != nil {
		return nil, err
	}

	if len(response.Answer) == 0 {
		return nil, errors.New("未找到对应类型的记录值")
	}

	return response.Answer, nil
}
