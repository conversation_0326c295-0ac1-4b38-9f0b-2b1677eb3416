package nsupdate

import (
	"bytes"
	"errors"
	"fmt"
	"os/exec"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// NSUpdateCmd 使用命令行方式实现 DNS 更新
type NSUpdateCmd struct {
	Server    string
	Port      int
	Zone      string
	Cmds      []Cmd
	RollBack  []Cmd
	CmdString string
	T         TSIG
}

// Add 添加一条命令, 一个NsUpdate的初始化代表着要发起一次update请求，一次update请求可以涵盖多条命令
// 逐一这里的Cmds是有序的
func (n *NSUpdateCmd) Add(cmd Cmd) NSUpdate {
	n.Cmds = append(n.Cmds, cmd)
	// n.RollBack = append(n.RollBack, sub.RollBack())
	// switch sub.CmdName() {
	// case "add":
	// 	// 原: update add ksap.corp.kuaishou.com 600 A 10.0.0.1
	// 	// 目: update delete ksap.corp.kuaishou.com 600 A 10.0.0.1
	// 	rbSub := newCmd("delete")
	// 	n.RollBack = append(n.RollBack, rbSub)
	// case "delete":
	// 	n.RollBack = append(n.RollBack, sub)
	// }
	return nil
}

func (n *NSUpdateCmd) CmdKey() Cmd {
	b := newCmd("key")
	if n.T.String() == "" {
		return b
	}

	b.Write([]byte("key"))
	b.Write([]byte(" "))
	b.Write([]byte(n.T.String()))
	b.Write([]byte("\n"))
	return b
}

func (n *NSUpdateCmd) CmdServer() Cmd {
	b := newCmd("server")

	if strings.TrimSpace(n.Server) == "" {
		return b
	}

	b.Write([]byte("server"))
	b.Write([]byte(" "))
	b.Write([]byte(n.Server))
	b.Write([]byte("\n"))
	return b
}

func (n *NSUpdateCmd) CmdZone() Cmd {
	b := newCmd("zone")

	if strings.TrimSpace(n.Zone) == "" {
		return b
	}

	b.Write([]byte("zone"))
	b.Write([]byte(" "))
	b.Write([]byte(n.Zone))
	b.Write([]byte("\n"))
	return b
}

func (n *NSUpdateCmd) CmdShow() Cmd {
	b := newCmd("show")
	b.Write([]byte("show"))
	b.Write([]byte("\n"))
	return b
}

func (n *NSUpdateCmd) CmdSend() Cmd {
	b := newCmd("send")
	b.Write([]byte("send"))
	b.Write([]byte("\n"))
	return b
}

func (n *NSUpdateCmd) CmdQuit() Cmd {
	b := newCmd("quit")
	b.Write([]byte("quit"))
	b.Write([]byte("\n"))
	return b
}

func (n *NSUpdateCmd) ExecuteCmd() error {
	// 首先需要看下我们要执行的命令是否存在
	if _, err := exec.LookPath("nsupdate"); err != nil {
		return err
	}

	// 初始化命令, 使用tcp的方式确保一个update操作中的多条命令执行成功
	cmd := exec.Command("nsupdate", "-v")

	// 由于要采用非交互的方式，同时避免与本地磁盘的io交互，我们直接使用标准输入传值更新的方式
	stdin, err := cmd.StdinPipe()
	if err != nil {
		return err
	}
	defer stdin.Close()

	// 由于要合并标准错误输出到标准输出，可以使用CombinedOutput
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return err
	}

	// 读取stdout的内容
	var errBuffer bytes.Buffer
	go func() {
		errBuffer.ReadFrom(stderr)
	}()

	// 异步的执行我们都nsupdate命令, cmd.Run和cmd.CombinedOutput都是同步的
	if err := cmd.Start(); err != nil {
		return err
	}

	// 构造标准输入，也就是我们要更新的命令
	if _, err := stdin.Write([]byte(n.CmdString)); err != nil {
		return err
	}

	// 接下来就是等待命令执行成功
	if err := cmd.Wait(); err != nil {
		return errors.New(errBuffer.String())
	}

	return nil
}

func (n *NSUpdateCmd) Submit() error {
	b := strings.Builder{}

	// 在命令的最后补上send和quit，代表执行一个命令和最终的结束收尾
	n.Add(n.CmdSend())
	n.Add(n.CmdQuit())

	// 如果说子命令都是空的，那我认为不需要向dns server发送请求
	if len(n.Cmds) == 0 {
		return nil
	}

	for _, cmd := range n.Cmds {
		subCmdString := cmd.String()
		// 每一个子命令都不应该为空，因此做统一的校验
		if subCmdString == "" {
			return fmt.Errorf("子命令 %s 不合法", cmd.CmdName())
		}
		b.WriteString(cmd.String())
	}

	// 更新这一次nsupdate要执行的命令，其实就是要把所有要执行的内容拼装到一起
	n.CmdString = b.String()

	zap.L().Info("nsupdate command", zap.String("command", n.CmdString))

	return n.ExecuteCmd()
}

func NewNSUpdate(server, zone string, t TSIG) *NSUpdateCmd {
	if t.String() == "" {
		return nil
	}

	// 初始化请求的时候，要把对应的Server和Zone一并初始化了
	u := &NSUpdateCmd{
		Server: server,
		Zone:   zone,
		Cmds:   make([]Cmd, 0),
		T: TSIG{
			Key:    t.Key,
			Secret: t.Secret,
		},
	}

	// 一次update请求只能针对一个固定的zone和server进行操作
	// 如果需要更新多台服务器，多个zone需要发起多次请求，如更新m个server的n个zone
	// 就需要发起m * n次update请求
	u.Add(u.CmdKey())
	u.Add(u.CmdServer())
	u.Add(u.CmdZone())

	return u
}

type SubCommand struct {
	Host      string
	RrType    string
	Value     string
	Ttl       int
	action    string
	UpdateReq *NSUpdateCmd
}

func NewSubCommand(update *NSUpdateCmd, action string) *SubCommand {
	return &SubCommand{
		UpdateReq: update,
		action:    action,
	}
}

func (s *SubCommand) Domain() string {
	return s.Host + "." + s.UpdateReq.Zone
}

func (s *SubCommand) CmdAdd(b *nsupdateCmd) {
	b.Write([]byte("update add"))
	b.Write([]byte(" "))
	b.Write([]byte(s.Domain()))
	b.Write([]byte(" "))
	b.Write([]byte(strconv.Itoa(s.Ttl)))
	b.Write([]byte(" "))
	b.Write([]byte(s.RrType))
	b.Write([]byte(" "))
	b.Write([]byte(s.Value))
	b.Write([]byte("\n"))
}

func (s *SubCommand) CmdDel(b *nsupdateCmd) {
	b.Write([]byte("update delete"))
	b.Write([]byte(" "))
	b.Write([]byte(s.Domain()))
	b.Write([]byte(" "))
	b.Write([]byte(s.RrType))
	b.Write([]byte(" "))
	b.Write([]byte(s.Value))
	b.Write([]byte("\n"))
}

func (s *SubCommand) RollBack() Cmd {
	return nil
}

func (s *SubCommand) CmdUpdate(b *nsupdateCmd, old *SubCommand, new *SubCommand) {
	// 先删除
	s.CmdDel(b)
	s.CmdAdd(b)
}

func (s *SubCommand) Build() Cmd {
	switch s.action {
	case "add":
		b := newCmd("add")

		if s.Host == "" || s.RrType == "" || s.Value == "" {
			return b
		}

		if s.Ttl == 0 {
			s.Ttl = 3600
		}

		s.CmdAdd(b)

		return b
	case "delete":
		b := newCmd("delete")

		if s.Host == "" || s.RrType == "" {
			return b
		}

		s.CmdDel(b)

		return b
	default:
		return nil
	}
}
