package nsupdate

import (
	"strings"
)

type Cmd interface {
	String() string  // 构造的命令字符串
	CmdName() string // 命令的名称

}

type nsupdateCmd struct {
	s *strings.Builder
	n string
}

func (c *nsupdateCmd) CmdName() string {
	return c.n
}

func (c *nsupdateCmd) String() string {
	return c.s.String()
}

func (c *nsupdateCmd) Write(p []byte) {
	c.s.Write(p)
}

func newCmd(name string) *nsupdateCmd {
	return &nsupdateCmd{
		s: &strings.Builder{},
		n: name,
	}
}
