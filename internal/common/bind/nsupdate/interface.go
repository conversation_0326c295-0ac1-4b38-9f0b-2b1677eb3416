package nsupdate

// NSUpdate 定义了一个通用的 DNS 动态更新接口。
// 实现者可以通过 nsupdate 命令行工具或直接使用 DNS 协议来执行更新。
type NSUpdate interface {
	// AddRecord 指示更新程序添加一条 DNS 记录。
	// name: 记录的名称 (例如 "www" 对应 "www.example.com."，如果区域是 example.com)。
	//       如果以点号结尾，则可以是一个完全限定域名。
	// rrType: 记录类型 (例如 "A", "CNAME", "TXT")。
	// value: 记录值 (例如 "1.2.3.4" 对于 A 记录, "target.example.com." 对于 CNAME)。
	// ttl: 记录的生存时间 (TTL)，单位为秒。
	AddRecord(name string, rrType string, value string, ttl uint32) NSUpdate

	// DeleteRecord 指示更新程序删除一条特定的 DNS 记录
	// (匹配名称、类型和值)。
	DeleteRecord(name string, rrType string, value string) NSUpdate

	// DeleteRRset 指示更新程序删除具有给定名称和类型的所有记录
	// (例如 "update delete example.com. A")。
	DeleteRRset(name string, rrType string) NSUpdate

	// UpdateRecord 更新一条 DNS 记录（先删除旧值，再添加新值）
	// 这是一个便利方法，等价于先调用 DeleteRecord(name, rrType, oldValue)，
	// 再调用 AddRecord(name, rrType, newValue, ttl)
	UpdateRecord(name string, rrType string, oldValue string, newValue string, ttl uint32) NSUpdate

	// Submit 将所有待处理的更新操作发送到 DNS 服务器。
	Submit() error
}
