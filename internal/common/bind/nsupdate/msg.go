package nsupdate

import (
	"errors"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/miekg/dns"
	"go.uber.org/zap"
)

const (
	// 默认TSIG时间容忍度
	DefaultTsigFudgeSec = 60
	// 默认超时时间
	DefaultTimeout = 5 * time.Second
	// 默认最大重试次数
	DefaultMaxRetries = 3
	// 默认重试间隔
	DefaultRetryInterval = time.Second
)

// NsUpdateBuilder 封装 DNS 动态更新消息的构建
type NsUpdateBuilder struct {
	msg        *dns.Msg // 动态更新消息
	zoneName   string   // 区域名称
	viewName   string   // 视图名称
	viewAlgo   string   // 视图算法
	viewSecret string   // TSIG密钥
	fudge      uint16   // TSIG时间容忍度

	// DNS客户端相关
	server  string        // DNS服务器地址
	port    int           // DNS服务器端口
	timeout time.Duration // 超时时间

	// 重试相关
	retryInterval time.Duration
}

// NewNsUpdateBuilder 创建一个新的 DNS 更新构建器
func NewNsUpdateBuilder(zoneName, viewName, viewAlgo string) *NsUpdateBuilder {
	builder := &NsUpdateBuilder{
		msg:           new(dns.Msg),
		zoneName:      zoneName,
		viewName:      viewName,
		viewAlgo:      viewAlgo,
		fudge:         DefaultTsigFudgeSec,
		timeout:       DefaultTimeout,
		retryInterval: DefaultRetryInterval,
	}

	// 设置请求的类型为动态更新
	builder.msg.SetUpdate(dns.Fqdn(zoneName))
	return builder
}

// NewNsUpdateBuilderWithServer 创建一个带有DNS服务器信息的更新构建器
func NewNsUpdateBuilderWithServer(server string, port int, zoneName, viewName, viewAlgo string) *NsUpdateBuilder {
	builder := NewNsUpdateBuilder(zoneName, viewName, viewAlgo)
	builder.server = server
	builder.port = port
	return builder
}

func (b *NsUpdateBuilder) Message() string {
	return b.msg.String()
}

// SetServer 设置DNS服务器信息
func (b *NsUpdateBuilder) SetServer(server string, port int) *NsUpdateBuilder {
	b.server = server
	b.port = port
	return b
}

// SetTimeout 设置超时时间
func (b *NsUpdateBuilder) SetTimeout(timeout time.Duration) *NsUpdateBuilder {
	b.timeout = timeout
	return b
}

// SetFudge 设置 TSIG 的时间容忍度
func (b *NsUpdateBuilder) SetFudge(fudge uint16) *NsUpdateBuilder {
	b.fudge = fudge
	return b
}

// SetTSIGSecret 设置TSIG密钥
func (b *NsUpdateBuilder) SetTSIGSecret(secret string) *NsUpdateBuilder {
	b.viewSecret = secret
	return b
}

// AddRecord 实现NSUpdate接口 - 添加DNS记录
func (b *NsUpdateBuilder) AddRecord(name string, rrType string, value string, ttl uint32) NSUpdate {
	rr, err := b.createRR(name, rrType, value, ttl)
	if err != nil {
		zap.L().Error("创建RR失败", zap.String("name", name), zap.String("type", rrType), zap.String("value", value), zap.Error(err))
		return b
	}
	b.msg.Insert([]dns.RR{rr})
	return b
}

// DeleteRecord 实现NSUpdate接口 - 删除特定的DNS记录
func (b *NsUpdateBuilder) DeleteRecord(name string, rrType string, value string) NSUpdate {
	rr, err := b.deleteRR(name, rrType, value)
	if err != nil {
		zap.L().Error("创建删除RR失败", zap.String("name", name), zap.String("type", rrType), zap.String("value", value), zap.Error(err))
		return b
	}
	b.msg.Remove([]dns.RR{rr})
	return b
}

// DeleteRRset 实现NSUpdate接口 - 删除指定名称和类型的所有记录
func (b *NsUpdateBuilder) DeleteRRset(name string, rrType string) NSUpdate {
	fqdn := b.makeFQDN(name)

	// 获取记录类型对应的DNS类型常量
	rrTypeUpper := strings.ToUpper(rrType)
	var rrTypeVal uint16

	// 使用预定义的常量，避免使用字符串映射
	switch rrTypeUpper {
	case "A":
		rrTypeVal = dns.TypeA
	case "CNAME":
		rrTypeVal = dns.TypeCNAME
	default:
		zap.L().Error("不支持的记录类型", zap.String("type", rrType))
		return b
	}

	// 创建一个用于删除RRset的记录
	// 对于删除RRset，需要设置TTL为0，Class为ANY
	var rr dns.RR
	switch rrTypeVal {
	case dns.TypeA:
		rr = &dns.A{
			Hdr: dns.RR_Header{
				Name:   fqdn,
				Rrtype: dns.TypeA,
				Class:  dns.ClassANY, // 使用ANY class表示删除所有
				Ttl:    0,            // TTL为0表示删除
			},
		}
	case dns.TypeCNAME:
		rr = &dns.CNAME{
			Hdr: dns.RR_Header{
				Name:   fqdn,
				Rrtype: dns.TypeCNAME,
				Class:  dns.ClassANY,
				Ttl:    0,
			},
		}
	}

	fmt.Printf("DeleteRRset - 删除记录: %s\n", rr.String())

	b.msg.RemoveRRset([]dns.RR{rr})
	return b
}

// UpdateRecord 实现NSUpdate接口 - 更新DNS记录（先删除旧值，再添加新值）
func (b *NsUpdateBuilder) UpdateRecord(name string, rrType string, oldValue string, newValue string, ttl uint32) NSUpdate {
	// 先删除旧记录
	if oldValue != "" {
		b.DeleteRecord(name, rrType, oldValue)
	}
	// 再添加新记录
	b.AddRecord(name, rrType, newValue, ttl)
	return b
}

// Submit 实现NSUpdate接口 - 提交所有更新操作到DNS服务器
func (b *NsUpdateBuilder) Submit() error {
	if b.server == "" {
		return errors.New("DNS服务器地址未设置")
	}

	// 构建最终消息
	msg := b.Build()

	// 验证消息
	if err := b.IsValidMsg(); err != nil {
		return err
	}

	// 发送更新请求
	return b.sendUpdate(msg)
}

// Build 构建最终的 DNS 消息
func (b *NsUpdateBuilder) Build() *dns.Msg {
	// 设置TSIG
	if b.viewName != "" && b.viewAlgo != "" && b.viewSecret != "" {
		// 将算法名称转换为DNS库期望的格式
		var algoName string
		switch strings.ToLower(b.viewAlgo) {
		case "hmac-md5", "hmac-md5.":
			algoName = dns.HmacMD5
		case "hmac-sha1", "hmac-sha1.":
			algoName = dns.HmacSHA1
		case "hmac-sha224", "hmac-sha224.":
			algoName = dns.HmacSHA224
		case "hmac-sha256", "hmac-sha256.":
			algoName = dns.HmacSHA256
		case "hmac-sha384", "hmac-sha384.":
			algoName = dns.HmacSHA384
		case "hmac-sha512", "hmac-sha512.":
			algoName = dns.HmacSHA512
		default:
			// 默认使用SHA256而不是已弃用的MD5
			algoName = dns.HmacSHA256
		}

		// 使用与sendUpdate方法相同的key命名规则
		keyName := fmt.Sprintf("office_%s_key", b.viewName)
		// 确保key名称是FQDN格式
		if !dns.IsFqdn(keyName) {
			keyName = dns.Fqdn(keyName)
		}

		b.msg.SetTsig(keyName, algoName, b.fudge, time.Now().Unix())
	}
	return b.msg
}

// IsValidMsg 验证DNS消息是否有效
func (b *NsUpdateBuilder) IsValidMsg() error {
	tsig := b.msg.IsTsig()
	// 说明dns.Extra中存在TSIG记录
	if tsig != nil {
		// 检查TSIG名称是否为空或者是有效的FQDN
		if tsig.Header().Name == "" || !dns.IsFqdn(tsig.Header().Name) {
			zap.L().Error("TSIG名称无效", zap.String("tsig name", tsig.Header().Name))
			return fmt.Errorf("TSIG名称无效: %s", tsig.Header().Name)
		}
		// 这里为什么要用Fqdn校验tsig.Algorithm?
		// 因为tsig.Algorithm是TSIG算法的名称，而TSIG算法的名称是FQDN
		// 所以这里要用Fqdn校验tsig.Algorithm，举几个例子
		// HmacSHA1   = "hmac-sha1."
		// HmacSHA224 = "hmac-sha224."
		// HmacSHA256 = "hmac-sha256."
		// HmacSHA384 = "hmac-sha384."
		// HmacSHA512 = "hmac-sha512."
		if tsig.Algorithm == "" || !dns.IsFqdn(tsig.Algorithm) {
			zap.L().Error("TSIG算法无效", zap.String("tsig algorithm", tsig.Algorithm))
			return errors.New("TSIG算法无效")
		}
	}
	return nil
}

// createRR 创建DNS资源记录
func (b *NsUpdateBuilder) createRR(name, rrType, value string, ttl uint32) (dns.RR, error) {
	if ttl == 0 {
		ttl = 3600 // 默认TTL
	}

	fqdn := b.makeFQDN(name)

	// 创建通用记录头
	hdr := dns.RR_Header{
		Name:  fqdn,
		Ttl:   ttl,
		Class: dns.ClassINET,
	}

	// 根据记录类型创建具体的资源记录
	switch strings.ToUpper(rrType) {
	case "A":
		hdr.Rrtype = dns.TypeA
		ip := net.ParseIP(value)
		if ip == nil || ip.To4() == nil {
			return nil, fmt.Errorf("无效的IPv4地址: %s", value)
		}
		return &dns.A{Hdr: hdr, A: ip.To4()}, nil

	case "CNAME":
		hdr.Rrtype = dns.TypeCNAME
		target := b.makeFQDN(value)
		return &dns.CNAME{Hdr: hdr, Target: target}, nil

	default:
		return nil, fmt.Errorf("不支持的记录类型: %s", rrType)
	}
}

// createDeleteRR 创建用于删除操作的DNS资源记录
func (b *NsUpdateBuilder) deleteRR(name, rrType, value string) (dns.RR, error) {
	fqdn := b.makeFQDN(name)

	// 创建用于删除的记录头 - 关键是使用ClassNONE
	hdr := dns.RR_Header{
		Name:  fqdn,
		Ttl:   0,             // 删除操作TTL为0
		Class: dns.ClassNONE, // 使用ClassNONE表示删除特定记录
	}

	// 根据记录类型创建具体的资源记录
	switch strings.ToUpper(rrType) {
	case "A":
		hdr.Rrtype = dns.TypeA
		ip := net.ParseIP(value)
		if ip == nil || ip.To4() == nil {
			return nil, fmt.Errorf("无效的IPv4地址: %s", value)
		}
		return &dns.A{Hdr: hdr, A: ip.To4()}, nil

	case "CNAME":
		hdr.Rrtype = dns.TypeCNAME
		target := b.makeFQDN(value)
		return &dns.CNAME{Hdr: hdr, Target: target}, nil

	default:
		return nil, fmt.Errorf("不支持的记录类型: %s", rrType)
	}
}

// makeFQDN 确保域名是完全限定域名
func (b *NsUpdateBuilder) makeFQDN(name string) string {
	if name == "@" {
		return dns.Fqdn(b.zoneName)
	}

	if dns.IsFqdn(name) {
		return name
	}

	return dns.Fqdn(name + "." + b.zoneName)
}

// sendUpdate 发送DNS更新请求
func (b *NsUpdateBuilder) sendUpdate(msg *dns.Msg) error {
	if b.server == "" {
		return errors.New("DNS服务器地址未设置")
	}

	// 构建tsig的keyMap
	keyMap := make(map[string]string)

	// 如果有TSIG配置，设置客户端的TSIG
	if b.viewName != "" && b.viewAlgo != "" && b.viewSecret != "" {
		// 使用office_{view_name}_key的命名规则
		keyName := fmt.Sprintf("office_%s_key", b.viewName)
		// 确保key名称是FQDN格式
		if !dns.IsFqdn(keyName) {
			keyName = dns.Fqdn(keyName)
		}
		keyMap[keyName] = b.viewSecret
	}

	client := &dns.Client{
		Net:        "tcp",
		Timeout:    b.timeout,
		TsigSecret: keyMap,
	}

	var serverAddr string
	if strings.Contains(b.server, ":") && !strings.Contains(b.server, "[") {
		// IPv6地址需要用方括号括起来
		serverAddr = fmt.Sprintf("[%s]:%d", b.server, b.port)
	} else {
		serverAddr = fmt.Sprintf("%s:%d", b.server, b.port)
	}

	// 添加重试机制
	var lastErr error
	currentInterval := b.retryInterval

	for i := 0; i < DefaultMaxRetries; i++ {
		// 在发送前验证连接
		conn, err := net.DialTimeout("tcp", serverAddr, b.timeout)
		if err != nil {
			lastErr = fmt.Errorf("TCP连接失败: %w", err)
			if i < DefaultMaxRetries-1 {
				time.Sleep(currentInterval)
				currentInterval *= 2
			}
			continue
		}
		conn.Close()

		response, rtt, err := client.Exchange(msg, serverAddr)
		if err == nil && response != nil && response.Rcode == dns.RcodeSuccess {
			zap.L().Info("DNS更新成功",
				zap.String("server", serverAddr),
				zap.String("zone", b.zoneName),
				zap.Duration("rtt", rtt),
				zap.String("r_code", dns.RcodeToString[response.Rcode]),
				zap.Int("尝试次数", i+1))
			return nil
		}

		if err != nil {
			lastErr = err
		} else if response != nil {
			lastErr = fmt.Errorf("DNS更新失败，响应码: %s", dns.RcodeToString[response.Rcode])
		} else {
			lastErr = fmt.Errorf("收到空响应")
		}

		if err != nil {
			zap.L().Warn("DNS更新失败，准备重试",
				zap.String("server", serverAddr),
				zap.String("zone", b.zoneName),
				zap.Int("尝试次数", i+1),
				zap.Int("最大重试次数", DefaultMaxRetries),
				zap.Error(err))
		} else if response != nil && response.Rcode != dns.RcodeSuccess {
			zap.L().Warn("DNS更新失败，准备重试",
				zap.String("server", serverAddr),
				zap.String("zone", b.zoneName),
				zap.Int("尝试次数", i+1),
				zap.Int("最大重试次数", DefaultMaxRetries),
				zap.String("响应码", dns.RcodeToString[response.Rcode]))
		}

		if i < DefaultMaxRetries-1 {
			fmt.Printf("等待 %v 后重试...\n", currentInterval)
			time.Sleep(currentInterval)
			// 指数退避策略
			currentInterval *= 2
		}
	}

	if lastErr != nil {
		return fmt.Errorf("发送DNS更新请求失败(重试%d次): %w", DefaultMaxRetries, lastErr)
	}
	return fmt.Errorf("DNS更新失败(重试%d次)", DefaultMaxRetries)
}

// ValidateServer 验证DNS服务器是否可达
func (b *NsUpdateBuilder) ValidateServer() error {
	if b.server == "" {
		return errors.New("DNS服务器地址未设置")
	}

	var serverAddr string
	if strings.Contains(b.server, ":") && !strings.Contains(b.server, "[") {
		// IPv6地址需要用方括号括起来
		serverAddr = fmt.Sprintf("[%s]:%d", b.server, b.port)
	} else {
		serverAddr = fmt.Sprintf("%s:%d", b.server, b.port)
	}

	conn, err := net.DialTimeout("tcp", serverAddr, b.timeout)
	if err != nil {
		return fmt.Errorf("无法连接到DNS服务器 %s: %w", serverAddr, err)
	}
	defer conn.Close()

	return nil
}

// GetOperationCount 获取待执行操作数量
func (b *NsUpdateBuilder) GetOperationCount() int {
	// 对于DNS更新消息，主要关注Ns部分的操作数量
	// Ns部分包含了所有的更新操作（添加、删除等）
	count := len(b.msg.Ns)

	// 如果没有TSIG，Extra应该为空；如果有TSIG，Extra会有1个记录
	// 所以我们需要排除TSIG记录
	extraCount := len(b.msg.Extra)
	if b.msg.IsTsig() != nil {
		extraCount-- // 排除TSIG记录
	}

	return count + extraCount
}

// Clear 清空所有待执行操作
func (b *NsUpdateBuilder) Clear() *NsUpdateBuilder {
	b.msg = new(dns.Msg)
	b.msg.SetUpdate(dns.Fqdn(b.zoneName))
	return b
}

// 便利函数：从记录信息快速添加
func (b *NsUpdateBuilder) AddARecord(name, ip string, ttl uint32) *NsUpdateBuilder {
	rr, err := b.createRR(name, "A", ip, ttl)
	if err != nil {
		zap.L().Error("创建A记录失败", zap.String("name", name), zap.String("ip", ip), zap.Error(err))
		return b
	}
	b.msg.Insert([]dns.RR{rr})
	return b
}

func (b *NsUpdateBuilder) AddCNAMERecord(name, target string, ttl uint32) *NsUpdateBuilder {
	rr, err := b.createRR(name, "CNAME", target, ttl)
	if err != nil {
		zap.L().Error("创建CNAME记录失败", zap.String("name", name), zap.String("target", target), zap.Error(err))
		return b
	}
	b.msg.Insert([]dns.RR{rr})
	return b
}

// 删除特定类型记录的便利方法
func (b *NsUpdateBuilder) DeleteARecord(name, ip string) *NsUpdateBuilder {
	b.DeleteRecord(name, "A", ip)
	return b
}

func (b *NsUpdateBuilder) DeleteCNAMERecord(name, target string) *NsUpdateBuilder {
	b.DeleteRecord(name, "CNAME", target)
	return b
}

// 删除所有特定类型记录的便利方法
func (b *NsUpdateBuilder) DeleteAllARecords(name string) *NsUpdateBuilder {
	b.DeleteRRset(name, "A")
	return b
}

func (b *NsUpdateBuilder) DeleteAllCNAMERecords(name string) *NsUpdateBuilder {
	b.DeleteRRset(name, "CNAME")
	return b
}
