package nsupdate

import (
	"fmt"
	"strings"
	"testing"
	"time"

	"ks-knoc-server/pkg/utils"

	"github.com/stretchr/testify/assert"
)

const (
	server = "bind.orb.local"
	port   = 53
	secret = "3iExuHaGub0LrfZo+FSX5w=="
)

func TestShowNsUpdateCliMessage(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)
	builder.AddARecord("it.office.ap", "10.32.255.96", 3600)
	builder.AddARecord("it.office.ap", "10.32.255.97", 3600)
	builder.AddARecord("it.office.ap", "10.32.255.98", 3600)

	builder.Build()
	t.Log(builder.Message())
}

// TestAddARecord 测试添加A记录
// 重复添加A记录并不会报错
// 一次请求可以添加多个A记录
func TestAddARecord(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)
	builder.AddARecord("it.office.ap", "10.32.255.96", 3600)
	builder.AddARecord("it.office.ap", "10.32.255.97", 3600)
	builder.AddARecord("it.office.ap", "10.32.255.98", 3600)

	if err := builder.Submit(); err != nil {
		t.Error("submit failed", err)
	}
}

// TestAddCNAMERecord 测试添加CNAME记录
func TestAddCNAMERecord(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)
	// cname的target会默认拼接上zoneName，并转换为FQDN
	// 当然前提必须存在这个A记录, 但是不存在也不会报错，本次提交不会有什么效果
	// 但是需要注意的是，如果本来源域名有一个cname，但是本次cname到了一个不存在的域名，那么会影响本来的结果
	// 会将原始的cname记录删除，解析会返回nxdomain
	builder.AddCNAMERecord("bpm.corp.kuaishou.com", "it.office.ap", 0)
	if err := builder.Submit(); err != nil {
		t.Error("提交失败", err)
	}

	results, err := utils.DigWithServer("bpm.corp.kuaishou.com", "A", fmt.Sprintf("%s:%d", server, port))
	if err != nil {
		t.Error("查询失败", err)
	}
	// 查询结果应该包含it.office.ap的A记录
	t.Log("results", results)

	// 清空后再将cname指向一个不存在的域名
	builder.Clear()
	builder.AddCNAMERecord("bpm.corp.kuaishou.com", "fake.target.com", 0)

	if err := builder.Submit(); err != nil {
		t.Error("提交失败", err)
	}

	// 查询结果应该为nxdomain
	results, err = utils.DigWithServer("bpm.corp.kuaishou.com", "A", fmt.Sprintf("%s:%d", server, port))
	assert.Error(t, err)
	// 检查是否是NXDOMAIN错误
	assert.True(t, strings.Contains(err.Error(), "NXDOMAIN"), "期望返回NXDOMAIN错误，实际错误: %v", err)
	t.Log("results", results)
}

// TestDeleteRecord 测试删除记录
func TestDeleteRecord(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)
	builder.DeleteRecord("it.office.ap", "A", "10.32.255.98")
	builder.DeleteRecord("it.office.ap", "A", "10.32.255.97")

	if err := builder.Submit(); err != nil {
		t.Error("提交失败", err)
	}
}

// TestDeleteRRset 测试删除所有特定类型记录
func TestDeleteRRset(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)

	// 先添加一些记录
	builder.DeleteRRset("it.office.ap", "A")
	if err := builder.Submit(); err != nil {
		t.Error("提交失败", err)
	}
}

// TestUpdateRecord 测试更新记录
func TestUpdateRecord(t *testing.T) {
	tests := []struct {
		name        string
		recordName  string
		recordType  string
		oldValue    string
		newValue    string
		ttl         uint32
		description string
		expectError bool
	}{
		{
			name:        "正常更新A记录",
			recordName:  "k.com",
			recordType:  "A",
			oldValue:    "*************",
			newValue:    "**********",
			ttl:         0,
			description: "正常情况下更新A记录",
			expectError: false,
		},
		{
			name:        "源记录不存在时添加新记录",
			recordName:  "mxy",
			recordType:  "A",
			oldValue:    "*************",
			newValue:    "************",
			ttl:         0,
			description: "假设源记录mxy不存在，此时会添加一个A记录，此时不会关注旧的IP",
			expectError: false,
		},
		{
			name:        "源记录存在但旧IP不存在时新增记录",
			recordName:  "mxy",
			recordType:  "A",
			oldValue:    "*************",
			newValue:    "**************",
			ttl:         0,
			description: "假设源记录mxy存在，但是源记录对应的旧IP不存在，此时相当于新增一条",
			expectError: false,
		},
		{
			name:        "新值为空时删除记录",
			recordName:  "mxy",
			recordType:  "A",
			oldValue:    "**************",
			newValue:    "",
			ttl:         0,
			description: "如果目标为空字符串，此时会删除mxy，value为**************的这条记录",
			expectError: false,
		},
		{
			name:        "类型不匹配时无变化",
			recordName:  "mxy",
			recordType:  "CNAME",
			oldValue:    "**************",
			newValue:    "**************",
			ttl:         0,
			description: "没有对应的name为mxy，rr_type为cname，值为**************的记录，因此不会发生任何变化",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 添加时间戳避免测试缓存
			testTime := time.Now().Unix()
			t.Logf("测试时间戳: %d", testTime)
			t.Logf("测试描述: %s", tt.description)

			builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
			builder.SetTSIGSecret(secret)

			// 执行更新操作
			builder.UpdateRecord(tt.recordName, tt.recordType, tt.oldValue, tt.newValue, tt.ttl)

			err := builder.Submit()

			if tt.expectError {
				if err == nil {
					t.Errorf("期望出现错误，但操作成功了")
				} else {
					t.Logf("预期的错误: %v", err)
				}
			} else {
				if err != nil {
					t.Errorf("操作失败: %v", err)
				} else {
					t.Logf("操作成功")
				}
			}
		})
	}
}

// TestMakeFQDN 测试FQDN生成
func TestMakeFQDN(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)

	// 测试不同类型的输入
	tests := []struct {
		input  string
		expect string
	}{
		{"www", "www.rpz.it."},
		{"@", "rpz.it."},
		{"sub.domain", "sub.domain.rpz.it."},
		{"host.rpz.it.", "host.rpz.it."},
	}

	for _, test := range tests {
		result := builder.makeFQDN(test.input)
		assert.Equal(t, test.expect, result)
	}
}

// TestInvalidRecords 测试无效记录处理
func TestInvalidRecords(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)

	// 初始操作数
	initialCount := builder.GetOperationCount()

	// 添加无效记录
	builder.AddARecord("www", "invalid-ip", 3600)

	// 验证操作数未变化
	assert.Equal(t, initialCount, builder.GetOperationCount())
}

// TestSubmitWithNoServer 测试提交无服务器设置的情况
func TestSubmitWithNoServer(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer("", 0, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)
	builder.AddARecord("www", "*************", 3600)

	// 提交操作，应该返回错误
	err := builder.Submit()

	// 验证错误
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "DNS服务器地址未设置")
}

// TestIsValidMsg 测试验证消息
func TestIsValidMsg(t *testing.T) {
	builder := NewNsUpdateBuilderWithServer(server, port, "rpz.it", "internal", "hmac-sha256")
	builder.SetTSIGSecret(secret)
	builder.AddARecord("www", "*************", 3600)

	// 构建消息
	builder.Build()

	// 验证消息
	if err := builder.IsValidMsg(); err != nil {
		t.Error("验证消息失败", err)
	}
}
