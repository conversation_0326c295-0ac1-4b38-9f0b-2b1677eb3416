package task

import (
	"context"
	"fmt"
	"time"

	"ks-knoc-server/internal/common/bind/nsupdate"

	"go.uber.org/zap"
)

// TaskConsumer 任务消费者接口
type TaskConsumer interface {
	// ConsumeTask 从消息队列消费任务
	ConsumeTask(ctx context.Context, handler func(task *Task) error)
}

// 任务执行器实现
type taskExecutor struct {
	repository TaskRepository
}

// 创建任务执行器
func NewTaskExecutor(repository TaskRepository) TaskExecutor {
	return &taskExecutor{
		repository: repository,
	}
}

// 执行任务
func (e *taskExecutor) ExecuteTask(ctx context.Context, task *Task) error {
	// 更新任务状态为执行中
	task.Status = TaskStatusRunning
	task.UpdatedAt = time.Now()
	if err := e.repository.UpdateTask(ctx, task); err != nil {
		zap.L().Error("update task status failed", zap.Error(err), zap.String("task_id", task.ID))
		return err
	}

	var err error
	// 根据任务类型执行不同的操作
	switch task.Type {
	case TaskTypeAddRecord:
		err = e.executeAddRecord(ctx, task)
	case TaskTypeUpdateRecord:
		err = e.executeUpdateRecord(ctx, task)
	case TaskTypeDeleteRecord:
		err = e.executeDeleteRecord(ctx, task)
	default:
		err = fmt.Errorf("unknown task type: %s", task.Type)
	}

	// 处理执行结果
	if err != nil {
		// 失败处理
		task.Status = TaskStatusFailed
		task.ErrorMessage = err.Error()
		task.RetryCount++

		// 检查是否需要重试
		if task.RetryCount < task.MaxRetries {
			task.Status = TaskStatusRetrying
			// 设置下次执行时间，使用指数退避策略
			backoff := time.Duration(1<<uint(task.RetryCount)) * time.Second
			task.ExecutionTime = time.Now().Add(backoff)
		}
	} else {
		// 成功处理
		task.Status = TaskStatusCompleted
		task.ErrorMessage = ""
	}

	// 更新任务状态
	task.UpdatedAt = time.Now()
	if updateErr := e.repository.UpdateTask(ctx, task); updateErr != nil {
		zap.L().Error("update task after execution failed",
			zap.Error(updateErr),
			zap.String("task_id", task.ID),
			zap.String("task_type", string(task.Type)))
		// 如果是更新状态失败，返回这个错误
		if err == nil {
			err = updateErr
		}
	}

	return err
}

// 执行添加记录任务
func (e *taskExecutor) executeAddRecord(ctx context.Context, task *Task) error {
	// 解析任务载荷
	var payload AddRecordPayload
	if err := UnmarshalPayload(task.Payload, &payload); err != nil {
		return err
	}

	// 检查参数
	if payload.Host == "" || payload.RRType == "" || payload.Value == "" || payload.ZoneName == "" || payload.ServerIP == "" {
		return fmt.Errorf("invalid add record payload: missing required fields")
	}

	// 处理TSIG参数
	if payload.TSIG.Key == "" || payload.TSIG.Secret == "" {
		return fmt.Errorf("invalid TSIG key or secret")
	}

	// 构建nsupdate命令
	tsig := nsupdate.TSIG{
		Key:    payload.TSIG.Key,
		Secret: payload.TSIG.Secret,
	}

	// 创建NSUpdate客户端
	update := nsupdate.NewNSUpdate(payload.ServerIP, payload.ZoneName, tsig)
	if update == nil {
		return fmt.Errorf("failed to create nsupdate client")
	}

	// 创建子命令
	subCmd := nsupdate.NewSubCommand(update, "add")
	subCmd.Host = payload.Host
	subCmd.RrType = payload.RRType
	subCmd.Value = payload.Value
	subCmd.Ttl = payload.TTL

	// 构建命令
	cmd := subCmd.Build()
	// 添加命令到更新请求
	update.Add(cmd)

	// 提交更新请求
	if err := update.Submit(); err != nil {
		return fmt.Errorf("nsupdate add record failed: %w", err)
	}

	zap.L().Info("add record success",
		zap.String("host", payload.Host),
		zap.String("zone", payload.ZoneName),
		zap.String("type", payload.RRType),
		zap.String("value", payload.Value),
		zap.String("view", payload.ViewName),
		zap.Int("ttl", payload.TTL))

	return nil
}

// 执行更新记录任务
func (e *taskExecutor) executeUpdateRecord(ctx context.Context, task *Task) error {
	// 解析任务载荷
	var payload UpdateRecordPayload
	if err := UnmarshalPayload(task.Payload, &payload); err != nil {
		return err
	}

	// 检查参数
	if payload.Host == "" || payload.RRType == "" || payload.OldValue == "" || payload.NewValue == "" ||
		payload.ZoneName == "" || payload.ServerIP == "" {
		return fmt.Errorf("invalid update record payload: missing required fields")
	}

	// 处理TSIG参数
	if payload.TSIG.Key == "" || payload.TSIG.Secret == "" {
		return fmt.Errorf("invalid TSIG key or secret")
	}

	// 构建nsupdate命令
	tsig := nsupdate.TSIG{
		Key:    payload.TSIG.Key,
		Secret: payload.TSIG.Secret,
	}

	// 创建NSUpdate客户端
	update := nsupdate.NewNSUpdate(payload.ServerIP, payload.ZoneName, tsig)
	if update == nil {
		return fmt.Errorf("failed to create nsupdate client")
	}

	// 1. 先删除旧记录
	delCmd := nsupdate.NewSubCommand(update, "delete")
	delCmd.Host = payload.Host
	delCmd.RrType = payload.RRType
	delCmd.Value = payload.OldValue
	update.Add(delCmd.Build())

	// 2. 再添加新记录
	addCmd := nsupdate.NewSubCommand(update, "add")
	addCmd.Host = payload.Host
	addCmd.RrType = payload.RRType
	addCmd.Value = payload.NewValue
	addCmd.Ttl = payload.TTL
	update.Add(addCmd.Build())

	// 提交更新请求
	if err := update.Submit(); err != nil {
		return fmt.Errorf("nsupdate update record failed: %w", err)
	}

	zap.L().Info("update record success",
		zap.String("host", payload.Host),
		zap.String("zone", payload.ZoneName),
		zap.String("type", payload.RRType),
		zap.String("old_value", payload.OldValue),
		zap.String("new_value", payload.NewValue),
		zap.String("view", payload.ViewName),
		zap.Int("ttl", payload.TTL))

	return nil
}

// 执行删除记录任务
func (e *taskExecutor) executeDeleteRecord(ctx context.Context, task *Task) error {
	// 解析任务载荷
	var payload DeleteRecordPayload
	if err := UnmarshalPayload(task.Payload, &payload); err != nil {
		return err
	}

	// 检查参数
	if payload.Host == "" || payload.RRType == "" || payload.ZoneName == "" || payload.ServerIP == "" {
		return fmt.Errorf("invalid delete record payload: missing required fields")
	}

	// 处理TSIG参数
	if payload.TSIG.Key == "" || payload.TSIG.Secret == "" {
		return fmt.Errorf("invalid TSIG key or secret")
	}

	// 构建nsupdate命令
	tsig := nsupdate.TSIG{
		Key:    payload.TSIG.Key,
		Secret: payload.TSIG.Secret,
	}

	// 创建NSUpdate客户端
	update := nsupdate.NewNSUpdate(payload.ServerIP, payload.ZoneName, tsig)
	if update == nil {
		return fmt.Errorf("failed to create nsupdate client")
	}

	// 创建子命令
	subCmd := nsupdate.NewSubCommand(update, "delete")
	subCmd.Host = payload.Host
	subCmd.RrType = payload.RRType
	if payload.Value != "" {
		subCmd.Value = payload.Value
	}

	// 构建命令
	cmd := subCmd.Build()
	// 添加命令到更新请求
	update.Add(cmd)

	// 提交更新请求
	if err := update.Submit(); err != nil {
		return fmt.Errorf("nsupdate delete record failed: %w", err)
	}

	zap.L().Info("delete record success",
		zap.String("host", payload.Host),
		zap.String("zone", payload.ZoneName),
		zap.String("type", payload.RRType),
		zap.String("value", payload.Value),
		zap.String("view", payload.ViewName))

	return nil
}
