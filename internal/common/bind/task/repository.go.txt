package task

import (
	"context"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 任务仓库MySQL实现
type taskRepository struct {
	db *gorm.DB
}

// NewTaskRepository 创建任务仓库
func NewTaskRepository(db *gorm.DB) TaskRepository {
	return &taskRepository{db: db}
}

// CreateTask 创建任务
func (r *taskRepository) CreateTask(ctx context.Context, task *Task) error {
	// 如果没有ID则生成新的
	if task.ID == "" {
		task.ID = uuid.New().String()
	}

	// 默认状态为等待执行
	if task.Status == "" {
		task.Status = TaskStatusPending
	}

	// 默认最大重试次数
	if task.MaxRetries == 0 {
		task.MaxRetries = 3
	}

	// 设置创建和更新时间
	now := time.Now()
	task.CreatedAt = now
	task.UpdatedAt = now

	// 转换为数据库模型
	model := TaskToModel(task)

	// 写入数据库
	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		zap.L().Error("create task failed", zap.Error(err), zap.String("task_id", task.ID))
		return err
	}

	return nil
}

// UpdateTask 更新任务
func (r *taskRepository) UpdateTask(ctx context.Context, task *Task) error {
	// 设置更新时间
	task.UpdatedAt = time.Now()

	// 转换为数据库模型
	model := TaskToModel(task)

	// 更新数据库
	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		zap.L().Error("update task failed", zap.Error(err), zap.String("task_id", task.ID))
		return err
	}

	return nil
}

// GetTaskByID 根据ID获取任务
func (r *taskRepository) GetTaskByID(ctx context.Context, id string) (*Task, error) {
	var model TaskModel
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		zap.L().Error("get task by id failed", zap.Error(err), zap.String("task_id", id))
		return nil, err
	}

	return model.ToTask(), nil
}

// GetPendingTasks 获取待执行任务
func (r *taskRepository) GetPendingTasks(ctx context.Context, limit int) ([]*Task, error) {
	var models []TaskModel
	if err := r.db.WithContext(ctx).
		Where("status = ? AND execution_time <= ?", TaskStatusPending, time.Now()).
		Order("created_at ASC").
		Limit(limit).
		Find(&models).Error; err != nil {
		zap.L().Error("get pending tasks failed", zap.Error(err))
		return nil, err
	}

	tasks := make([]*Task, 0, len(models))
	for _, model := range models {
		tasks = append(tasks, model.ToTask())
	}

	return tasks, nil
}

// GetTasksByRecordID 根据记录ID获取任务
func (r *taskRepository) GetTasksByRecordID(ctx context.Context, recordID int64) ([]*Task, error) {
	var models []TaskModel
	if err := r.db.WithContext(ctx).
		Where("record_id = ?", recordID).
		Order("created_at DESC").
		Find(&models).Error; err != nil {
		zap.L().Error("get tasks by record id failed", zap.Error(err), zap.Int64("record_id", recordID))
		return nil, err
	}

	tasks := make([]*Task, 0, len(models))
	for _, model := range models {
		tasks = append(tasks, model.ToTask())
	}

	return tasks, nil
}
