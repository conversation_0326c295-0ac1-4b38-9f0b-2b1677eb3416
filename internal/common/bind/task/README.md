# DNS任务模块设计文档

## 概述

DNS任务模块是用于处理DNS记录变更的异步执行系统。通过将DNS变更操作（如添加、更新、删除记录）与实际执行操作分离，实现了更高效的解析记录管理，同时避免了对DNS服务器的直接操作可能带来的风险。

## 设计目标

1. **解耦API和DNS更新操作**：将API层的记录变更和实际DNS服务器操作解耦，减少API响应时间
2. **支持批量处理**：当一个视图的变更影响多个视图（如default视图影响所有office视图）时，自动创建多个执行任务
3. **提供可靠性保障**：通过数据库记录任务状态，并结合消息队列的异步执行，提供双重保障
4. **支持失败重试**：对失败的任务提供自动重试机制，确保最终一致性
5. **可观测性**：任务执行状态和结果可查询，便于监控和故障排查

## 架构设计

```
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
|  API服务       +---->+  任务管理器    +---->+  数据库        |
|                |     |                |     |                |
+-------+--------+     +--------+-------+     +----------------+
        |                       |
        |                       |
        v                       v
+----------------+     +----------------+     +----------------+
|                |     |                |     |                |
|  消息队列      |<----+  任务执行器    +---->+  DNS服务器     |
|  (Kafka)       |     |                |     |                |
+----------------+     +----------------+     +----------------+
```

## 核心组件

1. **任务模型(Task)**：定义任务的基本属性和状态
2. **任务仓库(TaskRepository)**：负责任务的持久化存储和查询
3. **任务生产者(TaskProducer)**：负责将任务发送到消息队列
4. **任务消费者(TaskConsumer)**：负责从消息队列接收任务
5. **任务执行器(TaskExecutor)**：负责实际执行DNS更新操作
6. **任务管理器(TaskManager)**：负责任务的创建、状态查询和重试

## 任务流程

1. **任务创建**：
   - API层处理记录变更请求
   - 计算变更影响范围（多个视图）
   - 为每个视图创建相应的任务
   - 将任务保存到数据库
   - 将任务发送到消息队列

2. **任务执行**：
   - 任务执行器从消息队列接收任务
   - 查询任务详情和执行所需信息
   - 执行实际的DNS更新操作（通过nsupdate）
   - 更新任务状态（完成/失败）
   - 处理失败重试逻辑

3. **任务查询和重试**：
   - 提供接口查询任务状态
   - 对失败任务支持手动或自动重试
   - 维护任务执行历史记录

## 数据库设计

```sql
CREATE TABLE `dns_tasks` (
  `id` varchar(36) NOT NULL COMMENT '任务ID',
  `type` varchar(50) NOT NULL COMMENT '任务类型',
  `status` varchar(20) NOT NULL COMMENT '任务状态',
  `payload` text COMMENT '任务载荷',
  `record_id` bigint(20) DEFAULT NULL COMMENT '关联的记录ID',
  `view_id` bigint(20) DEFAULT NULL COMMENT '关联的视图ID',
  `zone_id` bigint(20) DEFAULT NULL COMMENT '关联的区域ID',
  `server_id` bigint(20) DEFAULT NULL COMMENT '关联的服务器ID',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `max_retries` int(11) DEFAULT '3' COMMENT '最大重试次数',
  `error_message` text COMMENT '错误信息',
  `execution_time` datetime DEFAULT NULL COMMENT '执行时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_view_id` (`view_id`),
  KEY `idx_execution_time` (`execution_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='DNS任务表';
```

## 消息队列设计

- **主题(Topic)**：dns-tasks
- **分区(Partition)**：根据任务类型和视图ID进行分区，确保相关任务的顺序执行
- **消费者组(Consumer Group)**：dns-task-executor

## 异常处理

1. **任务执行失败**：
   - 记录错误信息
   - 根据重试策略安排重试
   - 超过最大重试次数后标记为永久失败

2. **消息队列不可用**：
   - 任务仍保存在数据库中
   - 定期扫描数据库中的待执行任务，重新投递到消息队列

3. **DNS服务器不可用**：
   - 记录错误信息
   - 安排延迟重试

## 优点和考虑

1. **可靠性**：通过数据库和消息队列双重保障，确保任务不丢失
2. **性能**：API层只需处理数据库操作，提高响应速度
3. **可扩展性**：执行器可以水平扩展，处理更多并发任务
4. **可观测性**：完整的任务状态和历史记录，便于监控和排查问题
5. **风险隔离**：DNS操作失败不影响API服务的正常运行 