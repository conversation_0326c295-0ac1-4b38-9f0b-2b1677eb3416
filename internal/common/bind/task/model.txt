package task

import "time"

// 添加记录任务载荷
type AddRecordPayload struct {
	Host     string `json:"host"`      // 主机名
	RRType   string `json:"rr_type"`   // 记录类型
	Value    string `json:"value"`     // 记录值
	TTL      int    `json:"ttl"`       // TTL值
	ViewName string `json:"view_name"` // 视图名称
	ZoneName string `json:"zone_name"` // 区域名称
	ServerIP string `json:"server_ip"` // 服务器IP
	TSIG     TSIG   `json:"tsig"`      // TSIG密钥
}

// 更新记录任务载荷
type UpdateRecordPayload struct {
	Host     string `json:"host"`      // 主机名
	RRType   string `json:"rr_type"`   // 记录类型
	OldValue string `json:"old_value"` // 旧记录值
	NewValue string `json:"new_value"` // 新记录值
	TTL      int    `json:"ttl"`       // TTL值
	ViewName string `json:"view_name"` // 视图名称
	ZoneName string `json:"zone_name"` // 区域名称
	ServerIP string `json:"server_ip"` // 服务器IP
	TSIG     TSIG   `json:"tsig"`      // TSIG密钥
}

// 删除记录任务载荷
type DeleteRecordPayload struct {
	Host     string `json:"host"`      // 主机名
	RRType   string `json:"rr_type"`   // 记录类型
	Value    string `json:"value"`     // 记录值
	ViewName string `json:"view_name"` // 视图名称
	ZoneName string `json:"zone_name"` // 区域名称
	ServerIP string `json:"server_ip"` // 服务器IP
	TSIG     TSIG   `json:"tsig"`      // TSIG密钥
}

// TSIG密钥
type TSIG struct {
	Key    string `json:"key"`    // 密钥名称
	Secret string `json:"secret"` // 密钥内容
}

// TaskModel 任务数据库模型
type TaskModel struct {
	ID            string    `gorm:"column:id;primaryKey"`             // 任务ID
	Type          string    `gorm:"column:type;not null"`             // 任务类型
	Status        string    `gorm:"column:status;not null"`           // 任务状态
	Payload       string    `gorm:"column:payload;type:text"`         // 任务载荷
	RecordID      int64     `gorm:"column:record_id"`                 // 关联的记录ID
	ViewID        int64     `gorm:"column:view_id"`                   // 关联的视图ID
	ZoneID        int64     `gorm:"column:zone_id"`                   // 关联的区域ID
	ServerID      int64     `gorm:"column:server_id"`                 // 关联的服务器ID
	RetryCount    int       `gorm:"column:retry_count;default:0"`     // 重试次数
	MaxRetries    int       `gorm:"column:max_retries;default:3"`     // 最大重试次数
	ErrorMessage  string    `gorm:"column:error_message"`             // 错误信息
	ExecutionTime time.Time `gorm:"column:execution_time"`            // 执行时间
	CreatedAt     time.Time `gorm:"column:created_at;autoCreateTime"` // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;autoUpdateTime"` // 更新时间
}

// TableName 表名
func (TaskModel) TableName() string {
	return "dns_tasks"
}

// 任务模型转换为任务对象
func (m *TaskModel) ToTask() *Task {
	return &Task{
		ID:            m.ID,
		Type:          TaskType(m.Type),
		Status:        TaskStatus(m.Status),
		Payload:       m.Payload,
		RecordID:      m.RecordID,
		ViewID:        m.ViewID,
		ZoneID:        m.ZoneID,
		ServerID:      m.ServerID,
		RetryCount:    m.RetryCount,
		MaxRetries:    m.MaxRetries,
		ErrorMessage:  m.ErrorMessage,
		ExecutionTime: m.ExecutionTime,
		CreatedAt:     m.CreatedAt,
		UpdatedAt:     m.UpdatedAt,
	}
}

// 任务对象转换为任务模型
func TaskToModel(t *Task) *TaskModel {
	return &TaskModel{
		ID:            t.ID,
		Type:          string(t.Type),
		Status:        string(t.Status),
		Payload:       t.Payload,
		RecordID:      t.RecordID,
		ViewID:        t.ViewID,
		ZoneID:        t.ZoneID,
		ServerID:      t.ServerID,
		RetryCount:    t.RetryCount,
		MaxRetries:    t.MaxRetries,
		ErrorMessage:  t.ErrorMessage,
		ExecutionTime: t.ExecutionTime,
		CreatedAt:     t.CreatedAt,
		UpdatedAt:     t.UpdatedAt,
	}
}
