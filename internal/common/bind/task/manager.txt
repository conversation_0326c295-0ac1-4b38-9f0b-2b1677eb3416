package task

import (
	"context"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// 任务管理器实现
type taskManager struct {
	repository TaskRepository
	producer   TaskProducer
}

// 创建任务管理器
func NewTaskManager(repository TaskRepository, producer TaskProducer) TaskManager {
	return &taskManager{
		repository: repository,
		producer:   producer,
	}
}

// 创建并发送任务
func (m *taskManager) CreateTask(ctx context.Context, taskType TaskType, payload interface{}, recordID, viewID, zoneID, serverID int64) (*Task, error) {
	// 1. 将载荷转换为JSON字符串
	payloadStr, err := MarshalPayload(payload)
	if err != nil {
		zap.L().Error("marshal payload failed", zap.Error(err))
		return nil, err
	}

	// 2. 创建任务
	task := &Task{
		ID:            uuid.New().String(),
		Type:          taskType,
		Status:        TaskStatusPending,
		Payload:       payloadStr,
		RecordID:      recordID,
		ViewID:        viewID,
		ZoneID:        zoneID,
		ServerID:      serverID,
		RetryCount:    0,
		MaxRetries:    3,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		ExecutionTime: time.Now(), // 默认立即执行
	}

	// 3. 保存任务到数据库
	if err := m.repository.CreateTask(ctx, task); err != nil {
		zap.L().Error("create task failed", zap.Error(err), zap.String("task_id", task.ID))
		return nil, err
	}

	// 4. 发送任务到消息队列
	if err := m.producer.SendTask(ctx, task); err != nil {
		zap.L().Error("send task to queue failed", zap.Error(err), zap.String("task_id", task.ID))

		// 即使发送到消息队列失败，任务仍然创建成功，后续可以通过其他方式重试
		// 可以考虑在这里更新任务状态，但为了简单起见，我们只记录日志
		// 如果业务上需要保证消息队列投递成功，可以在这里回滚数据库操作
		return task, err
	}

	zap.L().Info("task created and sent",
		zap.String("task_id", task.ID),
		zap.String("task_type", string(taskType)),
		zap.Int64("record_id", recordID),
		zap.Int64("view_id", viewID))

	return task, nil
}

// 获取任务状态
func (m *taskManager) GetTaskStatus(ctx context.Context, taskID string) (TaskStatus, error) {
	// 从数据库中获取任务
	task, err := m.repository.GetTaskByID(ctx, taskID)
	if err != nil {
		zap.L().Error("get task status failed", zap.Error(err), zap.String("task_id", taskID))
		return "", err
	}

	if task == nil {
		return "", nil
	}

	return task.Status, nil
}

// 重试任务
func (m *taskManager) RetryTask(ctx context.Context, taskID string) error {
	// 1. 从数据库中获取任务
	task, err := m.repository.GetTaskByID(ctx, taskID)
	if err != nil {
		zap.L().Error("get task for retry failed", zap.Error(err), zap.String("task_id", taskID))
		return err
	}

	if task == nil {
		return nil
	}

	// 2. 检查任务状态，只有失败的任务才能重试
	if task.Status != TaskStatusFailed {
		zap.L().Warn("cannot retry task with status",
			zap.String("task_id", taskID),
			zap.String("status", string(task.Status)))
		return nil
	}

	// 3. 更新任务状态为等待执行
	task.Status = TaskStatusPending
	task.RetryCount = 0 // 重置重试次数
	task.ErrorMessage = ""
	task.UpdatedAt = time.Now()
	task.ExecutionTime = time.Now() // 立即执行

	// 4. 保存任务到数据库
	if err := m.repository.UpdateTask(ctx, task); err != nil {
		zap.L().Error("update task for retry failed", zap.Error(err), zap.String("task_id", taskID))
		return err
	}

	// 5. 重新发送任务到消息队列
	if err := m.producer.SendTask(ctx, task); err != nil {
		zap.L().Error("send retry task to queue failed", zap.Error(err), zap.String("task_id", taskID))
		return err
	}

	zap.L().Info("task retry scheduled",
		zap.String("task_id", taskID),
		zap.String("task_type", string(task.Type)))

	return nil
}
