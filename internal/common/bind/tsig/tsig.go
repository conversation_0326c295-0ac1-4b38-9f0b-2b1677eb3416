package tsig

import (
	"crypto/rand"
	"encoding/base64"
	"strings"
)

func GenerateTSIGSecret() (string, error) {
	// 生成128位的随机密钥
	secret := make([]byte, 16)
	_, err := rand.Read(secret)
	if err != nil {
		return "", err
	}

	// 将密钥转换为Base64编码的字符串
	secretBase64 := base64.StdEncoding.EncodeToString(secret)
	return secretBase64, nil
}

// GenerateTSIGKey 生成TSIG Key, 命名规则为
// {office code} + {zone name}，zone的.会被替换为中横线
func GenerateTSIGKey(code, zone string) string {
	key := &strings.Builder{}
	key.WriteString(code)
	zoneSections := strings.Split(zone, ".")
	for _, sec := range zoneSections {
		if sec == "" {
			continue
		}
		key.WriteString("-")
		key.WriteString(sec)
	}
	return key.String()
}
