package tsig

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"slices"
	"strings"

	"github.com/miekg/dns"
)

type TSIGKeyAlgo string

const (
	TSIGKeyAlgoHmacMD5    TSIGKeyAlgo = "hmac-md5"
	TSIGKeyAlgoHmacSHA1   TSIGKeyAlgo = "hmac-sha1"
	TSIGKeyAlgoHmacSHA224 TSIGKeyAlgo = "hmac-sha224"
	TSIGKeyAlgoHmacSHA256 TSIGKeyAlgo = "hmac-sha256"
	TSIGKeyAlgoHmacSHA384 TSIGKeyAlgo = "hmac-sha384"
	TSIGKeyAlgoHmacSHA512 TSIGKeyAlgo = "hmac-sha512"
)

func ValidTSIGKeyAlgoList() []TSIGKeyAlgo {
	return []TSIGKeyAlgo{
		TSIGKeyAlgoHmacMD5,
		TSIGKeyAlgoHmacSHA1,
		TSIGKeyAlgoHmacSHA224,
		TSIGKeyAlgoHmacSHA256,
		TSIGKeyAlgoHmacSHA384,
		TSIGKeyAlgoHmacSHA512,
	}
}

func GetHashBits(algo TSIGKeyAlgo) int {
	switch algo {
	case TSIGKeyAlgoHmacMD5:
		return 128
	case TSIGKeyAlgoHmacSHA1:
		return 160
	case TSIGKeyAlgoHmacSHA224:
		return 224
	case TSIGKeyAlgoHmacSHA256:
		return 256
	case TSIGKeyAlgoHmacSHA384:
		return 384
	case TSIGKeyAlgoHmacSHA512:
		return 512
	}
	return 0
}

func ToTSIGKeyAlgo(algo string) TSIGKeyAlgo {
	switch strings.ToUpper(algo) {
	case "HMAC-MD5":
		return TSIGKeyAlgoHmacMD5
	case "HMAC-SHA1":
		return TSIGKeyAlgoHmacSHA1
	case "HMAC-SHA224":
		return TSIGKeyAlgoHmacSHA224
	case "HMAC-SHA256":
		return TSIGKeyAlgoHmacSHA256
	case "HMAC-SHA384":
		return TSIGKeyAlgoHmacSHA384
	case "HMAC-SHA512":
		return TSIGKeyAlgoHmacSHA512
	}
	return ""
}

func (a TSIGKeyAlgo) String() string {
	return string(a)
}

func (a TSIGKeyAlgo) Equal(algo string) bool {
	return a == TSIGKeyAlgo(algo)
}

// IsValid 判断TSIGKeyAlgo是否有效
func (a TSIGKeyAlgo) IsValid() bool {
	return slices.Contains(ValidTSIGKeyAlgoList(), a)
}

func (a TSIGKeyAlgo) ToDNSArgo() string {
	switch a {
	case TSIGKeyAlgoHmacMD5:
		return dns.HmacMD5
	case TSIGKeyAlgoHmacSHA1:
		return dns.HmacSHA1
	case TSIGKeyAlgoHmacSHA224:
		return dns.HmacSHA224
	case TSIGKeyAlgoHmacSHA256:
		return dns.HmacSHA256
	case TSIGKeyAlgoHmacSHA384:
		return dns.HmacSHA384
	case TSIGKeyAlgoHmacSHA512:
		return dns.HmacSHA512
	}
	return dns.HmacSHA256
}

// GenerateTSIGKey 生成TSIG Key
func (a TSIGKeyAlgo) GenerateTSIGKey() string {
	bits := GetHashBits(a)
	if bits == 0 {
		return ""
	}

	bytes := make([]byte, bits/8)
	if _, err := io.ReadFull(rand.Reader, bytes); err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(bytes)
}

// ValidateTSIGKey 验证TSIG Key
// 1、校验算法是不是合法
// 2、校验TSIG Key是否有效，判断依据是可以使用base64解码
// 3、校验TSIG Key的长度是否符合算法
func (a TSIGKeyAlgo) ValidateTSIGKey(key string) error {
	// 判断TSIG Key算法是否有效
	if !a.IsValid() {
		return fmt.Errorf("cannot Validate Secret With Invalid Algorithm %s", a)
	}
	// 判断TSIG Key是否有效，使用base64进行解码
	bytes, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return err
	}
	// 根据加密算法获取hash的位数
	bits := GetHashBits(a)
	if 8*len(bytes) != bits {
		return fmt.Errorf("secret Length Should Be %d Bits For Algorithm %s", bits, a)
	}
	return nil
}
