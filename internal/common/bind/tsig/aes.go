package tsig

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"io"

	"go.uber.org/zap"
)

// EncryptKey 加密TSIG Key
// Param:
// - secret: 用于加密的密钥
// - key: 真正的rndc的key，这个是需要被加密的
// Return:
// - 加密后的密钥
// - 加密后的密钥的md5值
// - 错误信息
func EncryptKey(secret, key string) (ciperKey, hashString string, err error) {
	// 获取加密后的TSIG Key的密文
	encrypted, err := encrypt([]byte(secret), []byte(key))
	if err != nil {
		zap.L().Error("加密TSIG Key失败", zap.Error(err))
		return "", "", err
	}
	// 同时针对key进行md5加密，返回对应的hash值
	// md5是单向加密，因此这个hash值是用于后面校验和比对的而不是用于解密的
	md5Hash := md5.Sum([]byte(key))

	// 将生成的密文赋值给返回值
	ciperKey = hex.EncodeToString(encrypted)
	hashString = hex.EncodeToString(md5Hash[:])
	return
}

// DecryptKey 解密TSIG Key
// secret 用于加密的密钥
// ciperKey 加密后的密文
// md5Hash 加密后的密文的md5值
func DecryptKey(secret, ciperKey, md5Hash string) (key string, err error) {
	// 将ciperKey转换为字节数组
	ciphertext, err := hex.DecodeString(ciperKey)
	if err != nil {
		zap.L().Error("解密TSIG Key失败", zap.Error(err))
		return "", err
	}
	// 解密
	plaintext, err := decrypt([]byte(secret), ciphertext)
	if err != nil {
		zap.L().Error("解密TSIG Key失败", zap.Error(err))
		return "", err
	}
	// 校验md5值
	hash := md5.Sum([]byte(plaintext))
	if hex.EncodeToString(hash[:]) != md5Hash {
		zap.L().Error("解密TSIG Key失败, MD5校验未通过", zap.String("md5Hash", md5Hash))
		return "", errors.New("解密TSIG Key失败")
	}
	// 所有校验通过后，将解密后的数据转换为字符串
	key = string(plaintext)

	return
}

// encrypt 加密TSIG Key
// Param:
// - secret: 用于加密的密钥
// - key: 真正的rndc的key，这个是需要被加密的
// Return:
// - 加密后的密钥
// - 错误信息
// 说明: 加密需要使用对称加密算法，算法使用AES，秘钥长度为256位，加密方式使用CBC加密
// 选择CBC加密的原因是，rndc key相对来讲是比一个比较静态的数据，而CFB更适合加密流式数据
// bnet-kdns选择了使用CFB加密，目前暂不清楚这样选择原因是什么，后面确认后如果CFB更优可以考虑替换
func encrypt(secret, data []byte) (ciphertext []byte, err error) {
	// 创建AES加密实例
	block, err := aes.NewCipher(secret)
	if err != nil {
		zap.L().Error("创建AES加密实例失败", zap.Error(err))
		return nil, err
	}

	// CBC模式需要对key进行PKCS7填充，CFB不需要
	paddedData := PKCS7Padding(data, aes.BlockSize)

	// 初始化cipertext, iv(长度为aes.BlockSize) + len(key) = ciphertext，key加密后和明文长度是一致的
	ciphertext = make([]byte, aes.BlockSize+len(paddedData))
	iv := ciphertext[:aes.BlockSize]
	// 使用rand.Reader生成随机iv向量
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		zap.L().Error("生成随机iv向量失败", zap.Error(err))
		return nil, err
	}

	// 使用CBC模式进行加密
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], paddedData)

	return
}

// decrypt 解密TSIG Key
// Param:
// - secret: 用于解密的密钥
// - ciphertext: 加密后的数据
// Return:
// - 解密后的数据
// - 错误信息
func decrypt(secret, ciphertext []byte) (plaintext []byte, err error) {
	// 创建AES的解密实例
	block, err := aes.NewCipher(secret)
	if err != nil {
		zap.L().Error("创建AES解密实例失败", zap.Error(err))
		return nil, err
	}
	if len(ciphertext) < aes.BlockSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	// 获取iv, 前16个字节
	iv := ciphertext[:aes.BlockSize]
	// 获取加密后的数据
	encryptedData := ciphertext[aes.BlockSize:]
	// 使用CBC模式进行解密
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(encryptedData, encryptedData)
	// 使用PKCS7解填充
	plaintext = PKCS7UnPadding(encryptedData)
	return
}

// PKCS7Padding 对密码进行PKCS7填充
// pkcs#7填充是一种常用的填充方式，用于将数据填充到指定的块大小
// 填充的目标是为了构建整数倍的块，因为AES加密要求数据长度必须是块大小的整数倍
// 比如：
// 原始数据：0x01, 0x02, 0x03, 0x04
// 块大小：8
// 填充后：0x01, 0x02, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04
func PKCS7Padding(src []byte, blockSize int) []byte {
	// 使用BlockSize减去src长度除以块大小的取得余数，则是需要补充的字节数
	// eg1: 块大小为8，src为9，那么需要补充的为8-9%8=7个字节，也就是需要补充7个0x07才能凑够block的整数倍
	// eg2: 块大小为8，src为16，那么需要补充的为8-16%8=0个字节，也就是不需要补充，满足block的整数倍
	// eg3: 块大小为8，src为3，那么需要补充的为8-3%8=5个字节，也就是需要补充5个0x05
	paddingCnt := blockSize - len(src)%blockSize
	if paddingCnt == blockSize {
		return src
	}
	// 使用bytes.Repeat生成需要补充的paddingCnt个paddingCnt
	padtext := bytes.Repeat([]byte{byte(paddingCnt)}, paddingCnt)
	// 将src和padtext拼接起来返回
	return append(src, padtext...)
}

// PKCS7UnPadding 对密码进行PKCS7解填充
// Unpadding其实就是将填充进行逆向操作，根据数据的最后一个字节判断填充了多少个字节
// 针对特殊的情况需要进行判断，首先填充只存在填和不填的情况，且填充的长度是 0 <= paddingCnt < blockSize
// 因此padding的数量数一定不可能大于src的，也不可能等于src的长度（这种就全是padding，没有数据了），只能小于src的长度
func PKCS7UnPadding(src []byte) []byte {
	if len(src) == 0 {
		return src
	}
	// 获取最后一个字节，因为在填充的时候，最后一个字节的值就是填充了多少个字节
	paddingCnt := int(src[len(src)-1])
	if paddingCnt >= len(src) {
		return src
	}
	// 真实的数据部分就是去掉paddingCnt个字节后的内容
	realData := src[:len(src)-paddingCnt]
	return realData
}
