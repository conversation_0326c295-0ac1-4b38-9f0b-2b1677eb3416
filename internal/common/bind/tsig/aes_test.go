package tsig

import (
	"bytes"
	"testing"
)

func TestPKCS7Padding(t *testing.T) {
	type testCase struct {
		name      string
		src       []byte
		blockSize int
		expected  []byte
	}

	testCases := []testCase{
		{name: "case1", src: []byte{1, 2, 3, 4}, blockSize: 8, expected: []byte{1, 2, 3, 4, 4, 4, 4, 4}},
		{name: "case2", src: []byte{1, 2, 3, 4, 5, 6, 7, 8}, blockSize: 8, expected: []byte{1, 2, 3, 4, 5, 6, 7, 8}},
		{name: "case3", src: []byte{1, 2, 3}, blockSize: 2, expected: []byte{1, 2, 3, 1}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := PKCS7Padding(tc.src, tc.blockSize)
			if !bytes.Equal(result, tc.expected) {
				t.<PERSON>("PKCS7Padding(%v, %d) = %v; expected %v", tc.src, tc.blockSize, result, tc.expected)
			}
		})
	}
}

func TestPKCS7UnPadding(t *testing.T) {
	type testCase struct {
		name      string
		src       []byte
		expected  []byte
	}

	testCases := []testCase{
		{name: "case1", src: []byte{1, 2, 3, 4, 4, 4, 4, 4}, expected: []byte{1, 2, 3, 4}},
		{name: "case2", src: []byte{1, 2, 3, 4, 5, 6, 7, 8}, expected: []byte{1, 2, 3, 4, 5, 6, 7, 8}},
		{name: "case3", src: []byte{1, 2, 3, 1}, expected: []byte{1, 2, 3}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := PKCS7UnPadding(tc.src)
			if !bytes.Equal(result, tc.expected) {
				t.Errorf("PKCS7UnPadding(%v) = %v; expected %v", tc.src, result, tc.expected)
			}
		})
	}
}

func TestEncryptAndDecrypt(t *testing.T) {
	type testCase struct {
		name      string
		data      []byte
	}

	testSecret := []byte("fakesecretpaddin")

	testCases := []testCase{
		{name: "case1", data: []byte("1234567890")},
		{name: "case2", data: []byte("thisisateststr@#$%#ing")},
		{name: "case3", data: []byte("akkkkkyehNlDS2Z+voRpFx3D3h6FTs=")},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 加密
			ciphertext, err := encrypt(testSecret, tc.data)
			if err != nil {
				t.Error(err)
			}
			// 解密
			plaintext, err := decrypt(testSecret, ciphertext)
			if err != nil {
				t.Error(err)
			}
			// 验证解密结果
			if !bytes.Equal(plaintext, tc.data) {
				t.Errorf("decrypt(%v, %v) = %v; expected %v", testSecret, ciphertext, plaintext, tc.data)
			}
		})
	}
}
