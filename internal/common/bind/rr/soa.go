package rr

import (
	"strconv"
	"time"

	"ks-knoc-server/pkg/regex_pattern"

	"github.com/miekg/dns"
)

// NewSOARecord 构造一个新的soa记录，返回soa的content
// 主要在创建zone的时候, 初始化soa记录使用。
func NewSOARecord(zone string) string {
	// 为了避免冲突，我就直接以今天作为序列号，由于是新创建zone，因此默认从01开始
	now := time.Now().Format("20060102")
	nowSerial, _ := strconv.Atoi(now + "01")
	serialNumber := uint32(nowSerial)

	// SOA记录比较特殊, 会包含多个属性字段
	soa := dns.SOA{
		Hdr: dns.RR_Header{
			Name:   zone,
			Rrtype: dns.TypeSOA,
			Class:  dns.ClassINET,
			Ttl:    3600,
		},
		Mbox:    "wangzhichao03.kuaishou.com", // SOA对应的邮件服务器地址，如**************************, 也可以写邮件组
		Ns:      "ns1",                        // 默认的primary dns我们永远认为是ns1
		Serial:  serialNumber,                 // 序列号
		Refresh: 10800,                        // 刷新时间, Slave服务器多少时间更新数据, 以秒为单位, 默认值: 3个小时
		Retry:   900,                          // 若Slave服务器更新数据失败多久后进行尝试, 以秒为单位，配置文件中可以写1D, 15M, 1W, 2H, 默认15分钟
		Expire:  604800,                       // 若Slave服务器无法从Master上更新数据，原有数据的有效期是多久, 我这里也以秒为单位, 默认一周
		Minttl:  3600,                         // 若资源记录没有设定TTL，则以此TTL为准, 这里统一以秒为单位, 默认值: 1个小时
	}
	return regex_pattern.RemoveExtraSpaces(soa.String())
}
