package bpm

type TaskType string

func (t TaskType) String() string {
	return string(t)
}

const (
	TaskTypeRaidConfig  TaskType = "raid_config"
	TaskTypeBmcConfig   TaskType = "bmc_config"
	TaskTypeOsInstall   TaskType = "os_install"
	TaskTypeExporter    TaskType = "install_exporter"
	TaskTypeNetworkBond TaskType = "network_bond"
	TaskTypeAgent       TaskType = "agent"
	TaskTypeHids        TaskType = "install_hids"
	TaskTypeReboot      TaskType = "reboot"
)

// 任务名称常量
const (
	TaskNameRaidConfig  = "raid_config"      // RAID配置
	TaskNameBmcConfig   = "bmc_config"       // BMC配置
	TaskNameNetworkBond = "network_bond"     // 网卡绑定
	TaskNameOsInstall   = "os_install"       // 系统安装
	TaskNameExporter    = "install_exporter" // 监控Exporter安装
	TaskNameAgent       = "install_agent"    // Agent安装
	TaskNameHids        = "install_hids"     // HIDS安装
	TaskNameReboot      = "reboot"           // 重启
)

var TaskNameMap = map[TaskType]string{
	TaskTypeRaidConfig:  "RAID配置",
	TaskTypeBmcConfig:   "BMC配置",
	TaskTypeNetworkBond: "网卡绑定",
	TaskTypeOsInstall:   "系统安装",
	TaskTypeExporter:    "监控Exporter安装",
	TaskTypeAgent:       "Agent安装",
	TaskTypeHids:        "HIDS安装",
	TaskTypeReboot:      "重启",
}

var TaskSequence = []TaskType{
	TaskTypeBmcConfig,
	TaskTypeRaidConfig,
	TaskTypeOsInstall,
	TaskTypeAgent,
	TaskTypeExporter,
	TaskTypeHids,
	TaskTypeNetworkBond,
}

const (
	// 任务状态
	TaskStatusPending   uint8 = 1 // 等待执行
	TaskStatusRunning   uint8 = 2 // 执行中
	TaskStatusCompleted uint8 = 3 // 已完成
	TaskStatusFailed    uint8 = 4 // 失败
	TaskStatusSkipped   uint8 = 5 // 跳过
)

var TaskStatusMap = map[uint8]string{
	TaskStatusPending:   "等待执行",
	TaskStatusRunning:   "执行中",
	TaskStatusCompleted: "已完成",
	TaskStatusFailed:    "失败",
	TaskStatusSkipped:   "跳过",
}

// 状态常量定义
const (
	// 安装状态
	InstallStatusPending    uint8 = 1 // 等待中
	InstallStatusProcessing uint8 = 2 // 执行中
	InstallStatusCompleted  uint8 = 3 // 已完成
	InstallStatusFailed     uint8 = 4 // 失败
	InstallStatusCancelled  uint8 = 5 // 已取消
)

var InstallStatusMap = map[uint8]string{
	InstallStatusPending:    "等待中",
	InstallStatusProcessing: "执行中",
	InstallStatusCompleted:  "已完成",
	InstallStatusFailed:     "失败",
	InstallStatusCancelled:  "已取消",
}
