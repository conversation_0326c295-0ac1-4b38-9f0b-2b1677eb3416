package bpm

// FieldTobeSupplement 需要补充的字段信息
type FieldTobeSupplement struct {
	FieldName        string `json:"field_name"`         // 需要补充的字段的标识
	FieldDisplayName string `json:"field_display_name"` // 需要补充字段的可读的名称
	FieldType        string `json:"field_type"`         // 需要补充的字段的类型
	Required         bool   `json:"required"`           // 是否必填
	FieldValue       any    `json:"field_value"`        // 字段的值
	Attributes       any    `json:"attributes"`         // 字段的属性
	FieldSeq         int64  `json:"field_seq"`          // 字段的顺序
	ReadOnly         bool   `json:"read_only"`          // 是否只读
}

// FieldTobeSupplementList 需要补充的字段列表
type FieldTobeSupplementList []*FieldTobeSupplement

func (f FieldTobeSupplementList) Len() int {
	return len(f)
}

func (f FieldTobeSupplementList) Swap(i, j int) {
	f[i], f[j] = f[j], f[i]
}

// Less 按照字段顺序排序
func (f FieldTobeSupplementList) Less(i, j int) bool {
	return f[i].FieldSeq < f[j].FieldSeq
}
