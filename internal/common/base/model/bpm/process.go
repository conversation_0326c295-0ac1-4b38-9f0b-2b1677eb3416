package bpm

import (
	"encoding/json"
	"strings"

	"ks-knoc-server/internal/common/base/api/bpm"
	"ks-knoc-server/pkg/mapdata"
)

// OrderAbstract 工单
type OrderAbstract struct {
	ID                 int64           `gorm:"primaryKey" mapstructure:"id"`                                    // Order自增ID
	Title              string          `gorm:"column:title" mapstructure:"title"`                               // 工单标题
	Abstract           string          `gorm:"column:abstract" mapstructure:"abstract"`                         // 工单摘要
	ProcessKey         string          `gorm:"column:process_key" mapstructure:"process_key"`                   // 流程Key
	ProcessName        string          `gorm:"column:process_name" mapstructure:"process_name"`                 // 流程名称
	ProcessState       ProcessState    `gorm:"column:process_state" mapstructure:"process_state"`               // 流程状态, 审批中，审批结束，终止，拒绝
	ProcessCategory    ProcessCategory `gorm:"column:process_category" mapstructure:"process_category"`         // 流程分类，目前有两种，专业流程和快流程
	BusinessID         string          `gorm:"column:business_id" mapstructure:"business_id"`                   // 业务工单ID
	InitiatorID        string          `gorm:"column:initiator_id" mapstructure:"initiator_id"`                 // 发起人的ID
	InitiatorName      string          `gorm:"column:initiator_name" mapstructure:"initiator_name"`             // 发起人的名字
	InitiatorUserName  string          `gorm:"column:initiator_username" mapstructure:"initiator_username"`     // 发起人的邮箱前缀
	InitiatorOrg       string          `gorm:"column:initiator_org" mapstructure:"initiator_org"`               // 发起人的部门Code, 比如D7882
	InitiatorOrgName   string          `gorm:"column:initiator_org_name" mapstructure:"initiator_org_name"`     // 发起人部门名称
	ApprovalUser       string          `gorm:"column:approval_user" mapstructure:"approval_user"`               // 当前审批人
	ApprovalList       string          `gorm:"column:approval_list" mapstructure:"approval_list"`               // 审批人列表，此处记录曾经参与过审批的人的列表
	TaskID             string          `gorm:"column:task_id" mapstructure:"task_id"`                           // 当前任务节点ID
	TaskKey            string          `gorm:"column:task_key" mapstructure:"task_key"`                         // 当前任务节点标识
	TaskName           string          `gorm:"column:task_name" mapstructure:"task_name"`                       // 当前任务节点名称
	Description        string          `gorm:"column:description" mapstructure:"description"`                   // 工单描述
	ActivityID         string          `gorm:"column:activity_id" mapstructure:"activity_id"`                   // 外部任务标识
	CreateTime         int64           `gorm:"column:create_time" mapstructure:"create_time"`                   // 创建时间
	UpdateTime         int64           `gorm:"column:update_time" mapstructure:"update_time"`                   // 更新时间
	DesireDeliveryTime string          `gorm:"column:desire_delivery_time" mapstructure:"desire_delivery_time"` // 期望交付时间
	Office             string          `gorm:"column:office" mapstructure:"office"`                             // 操作职场
	OfficeID           string          `gorm:"column:office_id" mapstructure:"office_id"`                       // 操作职场ID
	Url                string          `gorm:"column:url" mapstructure:"url"`                                   // 工单详情页URL, 仅快流程需要跳转
	ProcessVars        []byte          `gorm:"column:process_vars" mapstructure:"process_vars"`                 // 流程变量
}

// UpdateApprovalUserList 更新审批人列表, userList 是新的审批人列表
func (od *OrderAbstract) UpdateApprovalUserList(userList []string) []string {
	// 获取旧的审批人的列表, 使用map去重取并集
	approveUserList := make(mapdata.MapData)
	if od.ApprovalList != "" {
		for _, user := range strings.Split(od.ApprovalList, ",") {
			approveUserList.Set(user, struct{}{})
		}
	}
	// 追加上新的审批人，使用map去重
	for _, user := range userList {
		if _, exist := approveUserList.Get(user); !exist {
			approveUserList.Set(user, struct{}{})
		}
	}
	// 拼接最新的审批人列表信息
	od.ApprovalList = strings.Join(approveUserList.Keys(), ",")
	return approveUserList.Keys()
}

// OrderDetail 工单详情表, 记录工单的详细数据
type OrderDetail struct {
	ID           int64               `gorm:"primaryKey"`           // 自增ID
	Title        string              `gorm:"column:title"`         // 工单标题
	OpType       bpm.DCOperationType `gorm:"column:op_type"`       // 操作类型
	BusinessID   string              `gorm:"column:business_id"`   // 业务工单ID
	BusinessData []byte              `gorm:"column:business_data"` // 工单数据
	CreateTime   int64               `gorm:"column:create_time"`   // 创建时间
	UpdateTime   int64               `gorm:"column:update_time"`   // 更新时间
}

// ParseBusinessData 解析工单数据
func (od *OrderDetail) ParseBusinessData() ([]map[string]any, error) {
	// 如果工单数据为空，则返回空数组
	if len(od.BusinessData) == 0 {
		return []map[string]any{}, nil
	}

	var content []map[string]any
	if err := json.Unmarshal(od.BusinessData, &content); err != nil {
		return nil, err
	}
	return content, nil
}

// ProcessDetail 流程信息表
type ProcessDetail struct {
	ID              int64           `gorm:"primaryKey"`              // Process自增ID
	ProcessKey      string          `gorm:"column:process_key"`      // 流程Key
	ProcessName     string          `gorm:"column:process_name"`     // 流程名称
	ProcessCategory ProcessCategory `gorm:"column:process_category"` // 流程分类，目前有两种，专业流程和快流程
	FormCode        string          `gorm:"column:form_code"`        // 表单Code
}

type ProcessNodeType string

const (
	StartEvent  ProcessNodeType = "startEvent"  // 开始
	Application ProcessNodeType = "application" // 发起节点
	EndEvent    ProcessNodeType = "endEvent"    // 结束
	Task        ProcessNodeType = "task"        // 任务节点（这里主要指的是调用第三方的任务）
	GateWay     ProcessNodeType = "gateway"     // 网关，包含排他网关，并行网关，包容网关
	AuditEvent  ProcessNodeType = "auditEvent"  // 审批节点
)

// ProcessNodeDetail 流程节点信息定义表，这里默认先只针对专业流程
type ProcessNodeDetail struct {
	ID          int64           `gorm:"primaryKey"`
	ProcessKey  string          `gorm:"column:process_key"` // 节点所属的流程标识
	NodeID      string          `gorm:"column:node_id"`     // 对应的审批节点的ID
	NodeName    string          `gorm:"column:node_name"`   // 对应的节点名称
	NodeType    ProcessNodeType `gorm:"column:node_type"`   // 对应的节点类型
	RequireData bool            `gorm:"require_data"`       // 是否需要补充数据
}

// ProcessNode 审批节点信息表
type ProcessNode struct {
	ID          int64           `gorm:"primaryKey"`
	ProcessKey  string          `gorm:"column:process_key"`
	NodeID      string          `gorm:"column:node_id"`
	NodeName    string          `gorm:"column:node_name"`
	NodeType    ProcessNodeType `gorm:"column:node_type"`
	RequireData bool            `gorm:"require_data"`
	RejectTo    string          `gorm:"column:reject_to"` // 驳回后跳转的节点ID
}

type FieldType int

const (
	FieldString  FieldType = 1
	FieldNumber  FieldType = 2
	FieldBoolean FieldType = 3
	FieldDate    FieldType = 4
	FieldSelect  FieldType = 5
)

// FieldTypeMap 字段类型映射
var FieldTypeMap = map[FieldType]string{
	FieldString:  "input",
	FieldNumber:  "inputNumber",
	FieldBoolean: "boolean",
	FieldDate:    "datePicker",
	FieldSelect:  "select",
}

// ProcessNodeFieldDefinition 节点字段信息表，
type ProcessNodeFieldDefinition struct {
	ID               int64     `gorm:"primaryKey"`
	NodeID           string    `gorm:"column:node_id"`            // 节点ID，数据库自增ID
	FieldName        string    `gorm:"column:field_name"`         // 字段标识
	FieldDisplayName string    `gorm:"column:field_display_name"` // 字段展示名称
	FieldType        FieldType `gorm:"column:field_type"`         // 字段类型
	Required         bool      `gorm:"column:required"`           // 是否必填
	FieldInfo        []byte    `gorm:"column:field_info"`         // 字段信息，比如字段类型，字段长度，字段描述等
	FieldRule        string    `gorm:"column:field_rule"`         // 字段正则规则
	ServerOnly       bool      `gorm:"column:server_only"`        // 是否仅服务器使用
	FieldSeq         int64     `gorm:"column:field_seq"`          // 字段顺序
}

type SelectOption struct {
	Label string `json:"label"`
	Value any    `json:"value"`
}

type ProcessNodeFieldInfoSelect struct {
	RequestURL string            `json:"request_url"` // 请求URL
	Params     []string          `json:"params"`      // 请求参数, 适用于需要异步请求的情况，如果AsyncRequest为false，则该字段为空
	Method     string            `json:"method"`      // 请求方法，目前先简单设计，默认只支持GET
	Options    []SelectOption    `json:"options"`     // 下拉框选项, 适用于不需要异步请求的情况，如果AsyncRequest为true，则需要通过RequestURL获取下拉框选项
	RelyField  map[string]string `json:"rely_field"`  // 依赖字段, 适用于需要根据依赖字段的值来获取下拉框选项的情况
}

type AgileFieldMetaType int

const (
	AgileFieldTypeTitle AgileFieldMetaType = 1 + iota
	AgileFieldTypeInitiatorUsername
	AgileFieldTypeInitiatorName
	AgileFieldTypeInitiatorOrgName
	AgileFieldTypeAttachment
	AgileFieldTypeContent
)

// AgileFieldMeta 快流程字段元数据表
type AgileFieldMeta struct {
	ID              int64              `gorm:"primaryKey"`
	ProcessKey      string             `gorm:"column:process_key"`
	FormElementID   string             `gorm:"column:form_element_id"`
	FormElementName string             `gorm:"column:form_element_name"`
	FormElementType AgileFieldMetaType `gorm:"column:form_element_type"`
}
