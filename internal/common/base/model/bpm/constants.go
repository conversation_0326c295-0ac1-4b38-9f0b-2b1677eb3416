package bpm

type EventType string

const (
	ProcessStart       EventType = "PROCESS_START"        // 流程启动
	ProcessEnd         EventType = "PROCESS_END"          // 流程结束
	TaskCreate         EventType = "TASK_CREATE"          // 任务创建，有新的节点生成
	TaskComplete       EventType = "TASK_COMPLETE"        // 任务完成，有可能是通过或者拒绝的状态
	ProcessCancelled   EventType = "PROCESS_CANCELLED"    // 流程取消，重新发起会删除原有流程实例
	ProcessRollBack    EventType = "PROCESS_ROLL_BACK"    // 流程撤回
	TaskConsult        EventType = "TASK_CONSULT"         // 任务征询
	SignShift          EventType = "SIGN_SHIFT"           // 转签
	TaskFrontAddSign   EventType = "TASK_FRONT_ADD_SIGN"  // 前加签
	TaskAfterAddSign   EventType = "TASK_AFTER_ADD_SIGN"  // 后加签
	TransferTaskAction EventType = "TRANSFER_TASK_ACTION" // 代理回收
	SequenceFlowTaken  EventType = "SEQUENCE_FLOW_TAKEN"  // 连线、网关通过, 业务可以忽略
	SignalEntityCreate EventType = "SIGNAL_ENTITY_CREATE" // 等待节点创建
	ActivitySignaled   EventType = "ACTIVITY_SIGNALED"    // 等待节点通过
	NotifyAction       EventType = "NOTIFY_ACTION"        // 知会
)

var EventTypeMap = map[EventType]string{
	ProcessStart:       "流程启动",
	ProcessEnd:         "流程结束",
	TaskCreate:         "任务创建",
	TaskComplete:       "任务完成",
	ProcessCancelled:   "流程取消",
	ProcessRollBack:    "流程撤回",
	TaskConsult:        "任务征询",
	SignShift:          "转签",
	TaskFrontAddSign:   "前加签",
	TaskAfterAddSign:   "后加签",
	TransferTaskAction: "代理回收",
	SequenceFlowTaken:  "连线通过",
	SignalEntityCreate: "等待节点创建",
	ActivitySignaled:   "等待节点通过",
	NotifyAction:       "知会",
}

type ProcessState string

func (p ProcessState) String() string {
	return string(p)
}

const (
	Submit      ProcessState = "Submit"      // 提交中
	Audit       ProcessState = "Audit"       // 审批中
	End         ProcessState = "End"         // 审批结束
	Rejected    ProcessState = "Rejected"    // 拒绝
	Termination ProcessState = "Termination" // 终止
)

var ProcessStateNameMap = map[ProcessState]string{
	Submit:      "提交中",
	Audit:       "审批中",
	End:         "审批通过",
	Rejected:    "拒绝",
	Termination: "终止",
}

type ProcessOperation string

const (
	Passed  ProcessOperation = "Passed"  // 通过
	Refuse  ProcessOperation = "Refuse"  // 拒绝
	Consult ProcessOperation = "Consult" // 征询
)

type ProcessCategory string

const (
	AgileProcessDesigner ProcessCategory = "agile_process_designer" // 快流程
	ProProcessDesigner   ProcessCategory = "pro_process_designer"   // 专业流程
)

var ProcessCategoryNameMap = map[ProcessCategory]string{
	AgileProcessDesigner: "快流程",
	ProProcessDesigner:   "专业流程",
}
