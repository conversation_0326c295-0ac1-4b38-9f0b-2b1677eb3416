package bpm

import "encoding/json"

type InstallConfig struct {
	Raid struct {
		Enable bool `json:"enable"`
		Info   struct {
			SystemRaidLevel string `json:"system_raid_level" mapstructure:"system_raid_level"`
			DataRaidLevel   string `json:"data_raid_level" mapstructure:"data_raid_level"`
			HotSpare        bool   `json:"hot_spare" mapstructure:"hot_spare"`
			HotSpareCount   int    `json:"hot_spare_count" mapstructure:"hot_spare_count"`
		} `json:"info" mapstructure:"info"`
	} `json:"raid"`
	BMC struct {
		BMCIP   string `json:"bmc_ip" mapstructure:"bmc_ip"`
		BMCMask string `json:"bmc_mask" mapstructure:"bmc_mask"`
		BMCGW   string `json:"bmc_gw" mapstructure:"bmc_gw"`
	} `json:"bmc" mapstructure:"bmc"`
	SysFamily struct {
		OsName   string `json:"os_name" mapstructure:"os_name"`
		Hostname string `json:"hostname" mapstructure:"hostname"`
	} `json:"os" mapstructure:"os"`
	Network struct {
		BondMode   int      `json:"bond_mode" mapstructure:"bond_mode"`
		SystemDNS  []string `json:"system_dns" mapstructure:"system_dns"`
		SystemGW   string   `json:"system_gw" mapstructure:"system_gw"`
		SystemIP   string   `json:"system_ip" mapstructure:"system_ip"`
		SystemMask string   `json:"system_mask" mapstructure:"system_mask"`
	} `json:"network" mapstructure:"network"`
}

// OsInstall 操作系统安装表
type OsInstall struct {
	ID            int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	BusinessID    string          `json:"business_id" gorm:"column:business_id"`
	SN            string          `json:"sn" gorm:"column:sn"`
	Creator       string          `json:"creator" gorm:"column:creator"`
	Config        json.RawMessage `json:"config" gorm:"column:config"`
	InstallStatus uint8           `json:"install_status" gorm:"column:install_status;default:1"`
	CreatedAt     int64           `json:"created_at" gorm:"column:created_at"`
	UpdatedAt     int64           `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 指定表名
func (OsInstall) TableName() string {
	return "os_install"
}

// InstallTasks 安装任务详情表
type InstallTask struct {
	ID          int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	OsInstallID int64  `json:"os_install_id" gorm:"column:os_install_id"`
	TaskName    string `json:"task_name" gorm:"column:task_name"`
	TaskStatus  uint8  `json:"task_status" gorm:"column:task_status;default:1"`
	Message     string `json:"message" gorm:"column:message"`
	StartTime   int64  `json:"start_time" gorm:"column:start_time;default:0"`
	EndTime     int64  `json:"end_time" gorm:"column:end_time;default:0"`
	Duration    int32  `json:"duration" gorm:"column:duration;default:0"`
	CreatedAt   int64  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   int64  `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 指定表名
func (InstallTask) TableName() string {
	return "install_tasks"
}
