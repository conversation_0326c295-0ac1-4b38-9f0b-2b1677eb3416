package bpm

// ExecutionStatusCode 执行状态码枚举
type ExecutionStatus string

func (code ExecutionStatus) String() string {
	return string(code)
}

const (
	ProcessApplicationStatus ExecutionStatus = "application" // 发起申请
	ProcessAuditStatus       ExecutionStatus = "audit"       // 审批中
	ProcessPassedStatus      ExecutionStatus = "passed"      // 审批通过
	ProcessRefusedStatus     ExecutionStatus = "refused"     // 审批拒绝
	ProcessRollbackStatus    ExecutionStatus = "rollback"    // 撤回操作
	ProcessTerminatedStatus  ExecutionStatus = "terminated"  // 终止申请
	ProcessEndStatus         ExecutionStatus = "end"         // 流程结束
)

// ExecutionStatusMap 状态码到中文描述的映射
var ExecutionStatusMap = map[ExecutionStatus]string{
	ProcessApplicationStatus: "发起申请",
	ProcessAuditStatus:       "审批中",
	ProcessPassedStatus:      "审批通过",
	ProcessRefusedStatus:     "审批拒绝",
	ProcessRollbackStatus:    "撤回操作",
	ProcessTerminatedStatus:  "终止",
	ProcessEndStatus:         "流程结束",
}

// GetStatusCodeByText 根据中文状态文本获取状态码
func GetStatusCodeByText(statusText string) ExecutionStatus {
	for code, text := range ExecutionStatusMap {
		if text == statusText {
			return code
		}
	}
	return ProcessAuditStatus // 默认返回审批中
}

// IsProcessStart 判断是否为流程开始节点
func (code ExecutionStatus) IsProcessStart() bool {
	return code == ProcessApplicationStatus
}

// IsProcessEnd 判断是否为流程结束节点
func (code ExecutionStatus) IsProcessEnd() bool {
	return code == ProcessEndStatus
}

// IsPending 判断是否为待审批状态
func (code ExecutionStatus) IsPending() bool {
	return code == ProcessAuditStatus
}

// IsFinished 判断是否为已完成状态（通过、拒绝、终止等）
func (code ExecutionStatus) IsFinished() bool {
	return code == ProcessPassedStatus || code == ProcessRefusedStatus ||
		code == ProcessTerminatedStatus || code == ProcessEndStatus
}

// GetI18nText 获取国际化文本
func (code ExecutionStatus) GetI18nText() string {
	return ExecutionStatusMap[code]
}

// ProcessExecutionLog 流程执行日志表
type ProcessExecutionLog struct {
	ID           int64           `json:"id" gorm:"primaryKey"`                      // 自增ID
	BusinessID   string          `json:"business_id" gorm:"column:business_id"`     // 业务ID
	ProcessKey   string          `json:"process_key" gorm:"column:process_key"`     // 流程Key
	NodeID       string          `json:"node_id" gorm:"column:node_id"`             // 节点ID
	NodeName     string          `json:"node_name" gorm:"column:node_name"`         // 节点名称
	NodeType     ProcessNodeType `json:"node_type" gorm:"column:node_type"`         // 节点类型
	Comments     string          `json:"comments" gorm:"column:comments"`           // 评论, 审批意见
	Executor     string          `json:"executor" gorm:"column:executor"`           // 执行人
	StartTime    int64           `json:"start_time" gorm:"column:start_time"`       // 开始时间
	EndTime      int64           `json:"end_time" gorm:"column:end_time"`           // 结束时间
	Duration     int64           `json:"duration" gorm:"column:duration"`           // 执行时长，单位：秒
	Status       string          `json:"status" gorm:"column:status"`               // 状态, 比如：审批中，审批通过，审批拒绝，审批终止
	Sequence     int             `json:"sequence" gorm:"column:sequence"`           // 执行顺序
	CreatedAt    int64           `json:"created_at" gorm:"column:created_at"`       // 创建时间
	UpdatedAt    int64           `json:"updated_at" gorm:"column:updated_at"`       // 更新时间
	TaskID       string          `json:"task_id" gorm:"column:task_id"`             // 任务ID
	ApprovalList string          `json:"approval_list" gorm:"column:approval_list"` // 审批人列表
	EventID      string          `json:"event_id" gorm:"column:event_id"`           // 唯一事件ID，用于幂等性控制
}

// TableName 指定表名
func (ProcessExecutionLog) TableName() string {
	return "process_execution_log"
}
