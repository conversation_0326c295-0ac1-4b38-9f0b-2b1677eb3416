package bpm

import (
	"ks-knoc-server/pkg/mapdata"
)

// Process 流程消息体
type Process struct {
	ID                    string           `json:"id"`
	EventType             EventType        `json:"eventType"`             // 事件类型
	ProcessState          ProcessState     `json:"processState"`          // 流程状态
	ProcessInstanceID     string           `json:"processInstanceId"`     // 流程实例ID
	ProcessName           string           `json:"processName"`           // 流程名称
	BusinessID            string           `json:"businessId"`            // 工单ID
	ProcessKey            string           `json:"processKey"`            // 流程Key
	InitiatorID           string           `json:"initiatorId"`           // 发起人ID
	InitiatorUsername     string           `json:"initiatorUsername"`     // 发起人邮箱前缀
	InitiatorName         string           `json:"initiatorName"`         // 发起人中文名
	InitiatorOrg          string           `json:"initiatorOrg"`          // 发起人部门代码
	InitiatorTime         string           `json:"initiatorTime"`         // 发起时间
	ApplyExplain          string           `json:"applyExplain"`          // 流程标题
	SysCode               string           `json:"sysCode"`               // 流程所属系统code
	FormType              string           `json:"formType"`              // 表单类型
	LastTaskIsApplication bool             `json:"lastTaskIsApplication"` // 上一个认为是申请节点
	ActiveProfile         string           `json:"activeProfile"`         // 当前环境
	TaskDefinitionKey     string           `json:"taskDefinitionKey"`     // Task任务节点标识
	TaskID                string           `json:"taskId"`                // 当前任务节点ID
	TaskName              string           `json:"taskName"`              // 当前任务节点名称
	Assiginee             string           `json:"assiginee"`             // 当前审批人ID
	Assignees             string           `json:"assignees"`             // 当前审批人的ID, 逗号隔开
	AssigneeInUsername    []string         `json:"assigneeInUsername"`    // 审批人邮箱前缀列表
	ApproveType           string           `json:"approveType"`           // 审批类型，默认是MANUAL即手动
	CurrentTimeStamp      int64            `json:"currentTimestamp"`      // 当前时间戳，用户调整消息顺序
	Operation             ProcessOperation `json:"operation"`             // 操作, 拒绝, 通过, 征询
	Operator              string           `json:"operator"`              // 操作人ID
	OldAssiginee          string           `json:"oldAssiginee"`          // 旧的审批人ID
	Comments              string           `json:"comments"`              // 评论或备注, 或者是征询的内容
	Commments             string           `json:"commments"`             // 评论或备注, 或者是征询的内容，这里是bpm的bug，转签是commments，其他是comments
	ActivitiID            string           `json:"activitiId"`            // 回调任务ID，主要用在接收任务环节
	ExecutionID           string           `json:"executionId"`           // 回调执行ID
	SignalData            map[string]any   `json:"signalData"`            // 信号数据
	SignalName            string           `json:"signalName"`
	SourceTaskKey         string           `json:"sourceTaskKey"` // 源任务节点标识
	TargetTaskKey         string           `json:"targetTaskKey"` // 目标任务节点标识
	UserId                string           `json:"userId"`        // 用户ID
	ProcessEnded          bool             `json:"processEnded"`
	ProcessDefinitionKey  string           `json:"processDefinitionKey"` // 流程定义Key
	FlowType              string           `json:"flowType"`             // 流程类型，比如：专业流程，通用流程
	Variables             mapdata.MapData  `json:"variables"`
	Cause                 string           `json:"cause"` // 作废原因
}

// SignalReceived 信号消息体，当接收到消息类型的事件的时候，本地去调用某个事件然后回调bpm，告知节点通过
// BPM其实不关心你通过还是不通过，即使任务执行失败了，流程也结束了，但是这个时候，你自己想办法处理。
type SignalReceived struct {
	BusinessId string `json:"businessId"`
	SignalName string `json:"signalName"`
}
