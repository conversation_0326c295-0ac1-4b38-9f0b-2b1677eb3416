package v1

// CollectorPortMapping Prometheus采集器与端口映射，目前先写死，这是目前最快的一个办法
var CollectorPortMapping = map[string]int{
	"node_exporter":          9100,
	"nginx_exporter":         9113,
	"bind_exporter":          9119,
	"elasticsearch_exporter": 9114,
	"blackbox_exporter":      9115,
	"ping_exporter":          8085,
	"process_exporter":       9256,
	"redis-exporter":         9121,
	"snmp_exporter":          9116,
	"storcli_exporter":       9356,
	"unbound_exporter":       9167,
	"wmi_exporter":           9182,
	"x509_exporter":          9793,
}

type HTTPSDObject struct {
	Targets []string          `json:"targets"`
	Labels  map[string]string `json:"labels"`
}

type TargetLabelMap struct {
	DataID string            `bson:"_id"`
	Labels map[string]string `bson:"labels"`
}
