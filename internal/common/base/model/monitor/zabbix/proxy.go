package zabbix

type ZabbixProxyStatus int

const (
	ZabbixProxyStatusActive   ZabbixProxyStatus = 5
	ZabbixProxyStatusInactive ZabbixProxyStatus = 6
)

type ZabbixProxy struct {
	ProxyID      string            `json:"proxyid" gorm:"column:proxyid"`
	Host         string            `json:"host" gorm:"column:host"`
	Status       ZabbixProxyStatus `json:"status" gorm:"column:status"`
	Description  string            `json:"description" gorm:"column:description"`
	ProxyAddress string            `json:"proxy_address" gorm:"column:proxy_ipaddress"`
}
