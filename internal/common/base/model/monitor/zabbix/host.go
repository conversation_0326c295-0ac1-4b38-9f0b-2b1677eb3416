package zabbix

type InventoryModel int

const (
	InventoryModelManual    InventoryModel = 0
	InventoryModelAutomatic InventoryModel = 1
)

type SnmpAvailableStatus int

const (
	SnmpAvailableUnknown SnmpAvailableStatus = 0
	SnmpAvailable        SnmpAvailableStatus = 1
	SnmpUnavailable      SnmpAvailableStatus = 2
)

type ZabbixHostStatus int

const (
	ZabbixHostMonitored   ZabbixHostStatus = 0 // 监控中 default
	ZabbixHostUnmonitored ZabbixHostStatus = 1 // 未监控
)

type ZabbixInterfaceDetails struct {
	Version        string `json:"version"`        // 必填项: SNMP版本，1表示v1，2表示v2c，3表示v3，
	Bulk           string `json:"bulk"`           // 是否使用snmp的bulk模式，0表示不使用，1表示使用，1是默认值，也就是默认是启用的
	Community      string `json:"community"`      // 团体名，snmp的v1和v2的必填项
	SecurityName   string `json:"securityname"`   // snmpv3专用
	SecurityLevel  string `json:"securitylevel"`  // 安全级别，snmp的v3的必填项，0表示noAuthNoPriv[不认证不加密],默认为0，1表示authNoPriv[认证不加密]，2表示authPriv[认证加密]
	AuthPassPhrase string `json:"authpassphrase"` // 认证密码，snmp的v3的必填项
	PrivPassPhrase string `json:"privpassphrase"` // 加密密码，snmp的v3的必填项
	AuthProtocol   string `json:"authprotocol"`   // 认证协议，snmp的v3的必填项，0表示MD5，1表示SHA，默认为0
	PrivProtocol   string `json:"privprotocol"`   // 加密协议，snmp的v3的必填项，0表示DES，1表示AES，默认为0
	ContextName    string `json:"contextname"`    // 上下文名称，snmp的v3的必填项
}

type ZabbixInterface struct {
	InterfaceID string                 `json:"interfaceid" mapstructure:"interfaceid"`
	HostID      string                 `json:"hostid" mapstructure:"hostid"`
	Main        string                 `json:"main" mapstructure:"main"`       // 0表示非默认接口，1表示默认接口
	Type        string                 `json:"type" mapstructure:"type"`       // 接口类型，其实是监控类型，0表示agent，1表示snmp，2表示IPMI，3表示JMX
	Useip       string                 `json:"useip" mapstructure:"useip"`     // 是否通过IP建立链接，0表示使用DNS，1表示使用IP
	Ip          string                 `json:"ip" mapstructure:"ip"`           // 如果useip为1，则表示IP
	Port        string                 `json:"port" mapstructure:"port"`       // ip端口
	DNS         string                 `json:"dns" mapstructure:"dns"`         // 如果useip为0，则表示DNS
	Details     ZabbixInterfaceDetails `json:"details" mapstructure:"details"` // 接口详情，一般主要是SNMP会使用
}

type ZabbixHost struct {
	HostID         string            `json:"hostid,omitempty" mapstructure:"hostid"`
	Host           string            `json:"host,omitempty" mapstructure:"host"`
	Description    string            `json:"description,omitempty" mapstructure:"description"`
	InventoryModel InventoryModel    `json:"inventory_model,omitempty" mapstructure:"inventory_model"`
	Name           string            `json:"name,omitempty" mapstructure:"name"`
	ProxyHostID    string            `json:"proxy_hostid,omitempty" mapstructure:"proxy_hostid"`
	SnmpAvailable  string            `json:"snmp_available,omitempty" mapstructure:"snmp_available"`
	SnmpError      string            `json:"snmp_error,omitempty" mapstructure:"snmp_error"`
	Status         string            `json:"status,omitempty" mapstructure:"status"`
	SN             string            `json:"sn,omitempty" mapstructure:"sn"`
	Interfaces     []ZabbixInterface `json:"interfaces,omitempty" mapstructure:"interfaces"`
}

func (h *ZabbixHost) IP() string {
	if len(h.Interfaces) > 0 {
		intObj := h.Interfaces[0]
		if intObj.Useip == "1" {
			return intObj.Ip
		}
	}
	return ""
}

func (h *ZabbixHost) GetName() string {
	return h.Host
}

// NewSnmpHostMap 创建zabbix主机map
func (h *ZabbixHost) NewSnmpHostMap(groupID, proxyID string, templateIDs []string) map[string]interface{} {
	hostMap := make(map[string]interface{})
	hostMap["host"] = h.Host
	hostMap["proxy_hostid"] = proxyID
	hostMap["interfaces"] = []map[string]any{
		{
			"type":  1, // 1表示snmp
			"main":  1, // 1标识默认接口
			"useip": 1, // 1表示使用IP
			"ip":    h.IP(),
			"dns":   "",
			"port":  "161",
			"details": map[string]any{
				"version":   "2",                 // 目前默认的snmp版本，2表示v2c
				"bulk":      "1",                 // 是否使用snmp的bulk模式，0表示不使用，1表示使用，1是默认值，也就是默认是启用的
				"community": "{$SNMP_COMMUNITY}", // 目前默认的community的宏名称，宏名称用的是一个全局的宏
			},
		},
	}

	hostMap["groups"] = []map[string]any{
		{
			"groupid": groupID,
		},
	}

	// 设备可能存在多个模板，所以需要遍历模板ID，然后添加到hostMap中
	templates := make([]map[string]string, 0)
	for _, templateID := range templateIDs {
		templates = append(templates, map[string]string{
			"templateid": templateID,
		})
	}
	hostMap["templates"] = templates
	hostMap["inventory_mode"] = 0
	return hostMap
}
