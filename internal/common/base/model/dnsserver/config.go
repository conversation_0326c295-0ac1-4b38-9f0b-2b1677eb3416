package dnsserver

// DnsConfig 配置结构体
type DnsConfig struct {
	ID         int64             `json:"id"`          // 配置ID
	ViewID     int64             `json:"view_id"`     // 视图ID
	ZoneID     int64             `json:"zone_id"`     // 区域ID
	ZoneName   string            `json:"zone_name"`   // 区域名称
	Type       string            `json:"type"`        // 配置类型
	Content    string            `json:"content"`     // 配置内容
	Settings   map[string]string `json:"settings"`    // 配置设置
	CreateTime int64             `json:"create_time"` // 创建时间
	UpdateTime int64             `json:"update_time"` // 更新时间
}

// ZoneFileResponse 区域文件响应
type ZoneFileResponse struct {
	Content     string `json:"content"`      // 文件内容
	ZoneName    string `json:"zone_name"`    // 区域名称
	ViewID      int64  `json:"view_id"`      // 视图ID
	ContentType string `json:"content_type"` // 内容类型
	FileName    string `json:"file_name"`    // 文件名
}
