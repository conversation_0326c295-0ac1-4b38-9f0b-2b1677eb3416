package dnsserver

type Office struct {
	ID          int64  `json:"id"`          // 数据中心的ID
	Name        string `json:"name"`        // 数据中心的名称
	Code        string `json:"code"`        // 数据中心的编码
	OfficeType  string `json:"office_type"` // 数据中心的类型
	Description string `json:"description"` // 数据中心的描述
	Status      string `json:"status"`      // 数据中心的状态
	CreateTime  int64  `json:"create_time"` // 数据中心的创建时间
	UpdateTime  int64  `json:"update_time"` // 数据中心的更新时间
}

type OfficeStatus string

const (
	OfficeStatusActive   OfficeStatus = "active"
	OfficeStatusInactive OfficeStatus = "inactive"
)
