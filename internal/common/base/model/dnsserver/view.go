package dnsserver

import (
	"encoding/json"
)

// View 视图
type View struct {
	ID          int64  `json:"id" gorm:"column:id"`                   // 视图ID
	Code        string `json:"code" gorm:"column:code"`               // 视图编码
	Name        string `json:"name" gorm:"column:name"`               // 视图名称
	Description string `json:"description" gorm:"column:description"` // 视图描述

	ACLs          json.RawMessage `json:"acls" gorm:"column:acls"`                     // view对应的ACL
	SecretControl bool            `json:"secret_control" gorm:"column:secret_control"` // 是否开启密钥控制
	ViewKeyAlgo   string          `json:"view_key_algo" gorm:"column:view_key_algo"`   // view对应的key算法
	ViewKey       string          `json:"view_key" gorm:"column:view_key"`             // view对应的key
	ViewKeyMD5    string          `json:"view_key_md5" gorm:"column:view_key_md5"`     // view对应的key的md5

	ParentID  int64  `json:"parent_id" gorm:"column:parent_id"`   // 父视图ID
	Level     int    `json:"level" gorm:"column:level"`           // 视图级别
	LevelType string `json:"level_type" gorm:"column:level_type"` // 视图级别类型

	CreateTime int64 `json:"create_time" gorm:"column:create_time"` // 创建时间
	UpdateTime int64 `json:"update_time" gorm:"column:update_time"` // 更新时间
	Deleted    bool  `json:"deleted" gorm:"column:deleted"`         // 是否删除
}

type ViewLevelType string

func (v ViewLevelType) String() string {
	return string(v)
}

type ViewList []*View

func NewViewList() ViewList {
	return make(ViewList, 0)
}

func (v ViewList) Len() int {
	return len(v)
}

func (v ViewList) Less(i, j int) bool {
	return v[i].Level < v[j].Level
}

func (v ViewList) Swap(i, j int) {
	v[i], v[j] = v[j], v[i]
}

const (
	// ViewLevelTypeDefault 默认线路
	ViewLevelTypeDefault ViewLevelType = "default"
	// ViewLevelTypeBiz 业务线路, 对应的是研发，客服，审核，测试，演练
	ViewLevelTypeBiz ViewLevelType = "biz"
	// ViewLevelTypeOffice 办公线路, 对应的是正常，对标IDC的DC
	ViewLevelTypeOffice ViewLevelType = "office"
	// ViewLevelTypeInvalid 无效的视图级别
	ViewLevelTypeInvalid ViewLevelType = "invalid"
)

var ViewLevelTypeMap = map[ViewLevelType]string{
	ViewLevelTypeDefault: "default",
	ViewLevelTypeBiz:     "biz",
	ViewLevelTypeOffice:  "office",
	ViewLevelTypeInvalid: "invalid",
}

var ViewLevelDescribeMap = map[ViewLevelType]string{
	ViewLevelTypeDefault: "默认视图",
	ViewLevelTypeBiz:     "业务视图",
	ViewLevelTypeOffice:  "办公视图",
	ViewLevelTypeInvalid: "无效视图",
}

func (v ViewLevelType) GetViewLevelDescribe() string {
	return ViewLevelDescribeMap[v]
}

// GetViewLevel 获取视图的层级, 默认视图为0，业务视图为1，办公视图为2
// 层级主要为了指定优先级，优先级越高，越先被匹配，也就是说办公视图会覆盖业务视图的配置
func (v ViewLevelType) GetViewLevel() int {
	switch v {
	case ViewLevelTypeDefault:
		return 0
	case ViewLevelTypeBiz:
		return 1
	case ViewLevelTypeOffice:
		return 2
	}
	return -1
}

func ToViewLevelType(vt string) ViewLevelType {
	switch vt {
	case "default":
		return ViewLevelTypeDefault
	case "biz":
		return ViewLevelTypeBiz
	case "office":
		return ViewLevelTypeOffice
	}
	return ViewLevelTypeInvalid
}

// BizType 业务类型, 主要是ViewLevelBiz的类型
type BizType string

func (b BizType) String() string {
	return string(b)
}

const (
	BizTypeDev   BizType = "dev"   // 研发
	BizTypeCS    BizType = "cs"    // 客服
	BizTypeAudit BizType = "audit" // 审核
	BizTypeVPN   BizType = "vpn"   // VPN区域
	BizTypeTest  BizType = "test"  // 测试环境
	BizTypeDrill BizType = "drill" // 演练环境
)

var BizTypeNameMapping = map[BizType]string{
	BizTypeDev:   "研发",
	BizTypeCS:    "客服",
	BizTypeAudit: "审核",
	BizTypeVPN:   "VPN区域",
	BizTypeTest:  "测试环境",
	BizTypeDrill: "演练环境",
}

var BizNameMapping = map[string]BizType{
	"研发":    BizTypeDev,
	"客服":    BizTypeCS,
	"审核":    BizTypeAudit,
	"VPN区域": BizTypeVPN,
	"测试环境":  BizTypeTest,
	"演练环境":  BizTypeDrill,
}

func GetBizType(name string) (BizType, bool) {
	bizType, ok := BizNameMapping[name]
	if !ok {
		return "", false
	}
	return bizType, true
}
