package dnsserver

import (
	"ks-knoc-server/pkg/json"
)

const (
	MessageOK = "ok"
)

// TaskStatus 任务状态
type TaskStatus string

func (t TaskStatus) String() string {
	return string(t)
}

const (
	TaskStatusCreated  TaskStatus = "created"  // 创建
	TaskStatusRunning  TaskStatus = "running"  // 执行中
	TaskStatusSuccess  TaskStatus = "success"  // 成功
	TaskStatusFailed   TaskStatus = "failed"   // 失败
	TaskStatusCanceled TaskStatus = "canceled" // 取消
)

// TaskDetailStatus 子任务状态
type TaskDetailStatus string

func (t TaskDetailStatus) String() string {
	return string(t)
}

const (
	TaskDetailStatusInit    TaskDetailStatus = "init"    // 初始化
	TaskDetailStatusPending TaskDetailStatus = "pending" // 等待执行
	TaskDetailStatusRunning TaskDetailStatus = "running" // 执行中
	TaskDetailStatusSuccess TaskDetailStatus = "success" // 成功
	TaskDetailStatusFailed  TaskDetailStatus = "failed"  // 失败
	TaskDetailStatusSkipped TaskDetailStatus = "skipped" // 跳过（比如服务器不可用）
	TaskDetailStatusTimeout TaskDetailStatus = "timeout" // 超时
)

// TaskType 任务类型
type TaskType string

const (
	TaskTypeAdd    TaskType = "add"    // 添加记录
	TaskTypeUpdate TaskType = "update" // 更新记录
	TaskTypeDelete TaskType = "delete" // 删除记录
)

// RecordInfo 表示要变更的DNS记录信息
// 新增操作时：old为空，new为新值
// 删除操作时：old为原值，new为空
// 更新操作时：old为原值，new为新值
type RecordInfo struct {
	Action     string `json:"action"`                // 操作类型
	Name       string `json:"name"`                  // 主机记录
	TTL        int    `json:"ttl,omitempty"`         // TTL值
	Type       string `json:"type"`                  // 记录类型
	OldValue   string `json:"old_value,omitempty"`   // 原记录值，更新操作时使用
	NewValue   string `json:"new_value"`             // 新记录值
	MXPriority int    `json:"mx_priority,omitempty"` // MX优先级
	Enabled    bool   `json:"enabled,omitempty"`     // 是否启用
}

// TaskTargetInfo 任务目标信息
type TaskTargetInfo struct {
	ZoneName string       `json:"zone_name,omitempty"` // 区域名称
	Records  []RecordInfo `json:"records"`
	Servers  []*Server    `json:"servers"`
}

// Task 任务
// 任务指的是一次更新的操作，那么这个操作比如更新default视图下的某个解析
// 那么实际这个任务可能是操作多个服务器，此时多个操作会被记录到task_details中
// 一次task操作任务可能包含add，delete，update三种操作；
type Task struct {
	ID     int64      `json:"id" gorm:"primaryKey"`        // 任务ID
	Status TaskStatus `json:"status" gorm:"column:status"` // 任务状态

	TargetInfo  json.JSON `json:"target_info" gorm:"column:target_info"`   // 目标信息
	Operator    string    `json:"operator" gorm:"column:operator"`         // 操作人
	ExecuteTime int64     `json:"execute_time" gorm:"column:execute_time"` // 执行时间

	TotalDetails int `json:"total_details" gorm:"column:total_details"` // 总子任务数
	SuccessCount int `json:"success_count" gorm:"column:success_count"` // 成功子任务数
	FailedCount  int `json:"failed_count" gorm:"column:failed_count"`   // 失败子任务数

	// 计划执行时间，先设计，暂不使用，预计用于后面定时任务
	ScheduledAt int64 `json:"scheduled_at" gorm:"column:scheduled_at"`

	CreateTime int64 `json:"create_time" gorm:"column:create_time"` // 任务创建时间
	UpdateTime int64 `json:"update_time" gorm:"column:update_time"` // 任务更新时间
}

// TaskDetails 任务详情
type TaskDetails struct {
	ID     int64            `json:"id" gorm:"primaryKey"`          // 任务详情ID
	TaskID int64            `json:"task_id" gorm:"column:task_id"` // 当前子任务属于哪个task
	OpType string           `json:"op_type" gorm:"column:op_type"` // 操作类型
	Status TaskDetailStatus `json:"status" gorm:"column:status"`   // 子任务状态

	ServerName       string    `json:"server_name" gorm:"column:server_name"`             // 服务器名称
	ServerIP         string    `json:"server_ip" gorm:"column:server_ip"`                 // 服务器IP
	ZoneName         string    `json:"zone_name" gorm:"column:zone_name"`                 // 区域名称
	ViewName         string    `json:"view_name" gorm:"column:view_name"`                 // 视图名称
	ReqMsg           json.JSON `json:"req_msg" gorm:"column:req_msg"`                     // 请求消息
	ResMsg           string    `json:"res_msg" gorm:"column:res_msg"`                     // 响应消息, 可能是成功可能是是失败的错误日志
	ReqTs            int64     `json:"req_ts" gorm:"column:req_ts"`                       // 请求时间戳
	ResTs            int64     `json:"res_ts" gorm:"column:res_ts"`                       // 响应时间戳
	OperationTimeout int64     `json:"operation_timeout" gorm:"column:operation_timeout"` // 操作超时时间

	CreateAt int64 `json:"create_at" gorm:"column:create_at"` // 创建时间
	UpdateAt int64 `json:"update_at" gorm:"column:update_at"` // 更新时间
}
