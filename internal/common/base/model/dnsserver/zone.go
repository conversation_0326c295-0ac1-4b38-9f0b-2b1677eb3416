package dnsserver

import "slices"

type Zone struct {
	ID          int64  `json:"id" gorm:"column:id"`
	Name        string `json:"name" gorm:"column:name"`
	ZoneType    string `json:"zone_type" gorm:"column:zone_type"`
	Description string `json:"description" gorm:"column:description"`
	CreateTime  int64  `json:"create_time" gorm:"column:create_time"`
	UpdateTime  int64  `json:"update_time" gorm:"column:update_time"`
}

type ZoneType string

func (z ZoneType) String() string {
	return string(z)
}

func GetZoneTypes() []ZoneType {
	return []ZoneType{
		ZoneTypeDomain,
		ZoneTypeBlackList,
		ZoneTypeForward,
		ZoneTypeHost,
		ZoneTypeRPZ,
	}
}

const (
	ZoneTypeDomain    ZoneType = "domain"
	ZoneTypeBlackList ZoneType = "blacklist"
	ZoneTypeForward   ZoneType = "forward"
	ZoneTypeHost      ZoneType = "host"
	ZoneTypeRPZ       ZoneType = "rpz"
	ZoneTypeInvalid   ZoneType = ""
)

func (z ZoneType) Equal(zt string) bool {
	return z == ZoneType(zt)
}

func (z ZoneType) IsBlackZone() bool {
	return z == ZoneTypeBlackList
}

func (z ZoneType) IsValid() bool {
	return z != ZoneTypeInvalid
}

func (z ZoneType) IsRPZZone() bool {
	return z == ZoneTypeRPZ
}

func (z ZoneType) IsForwardZone() bool {
	return z == ZoneTypeForward
}

func (z ZoneType) IsHostZone() bool {
	return z == ZoneTypeHost
}

func (z ZoneType) ShowName() string {
	switch z {
	case ZoneTypeDomain:
		return "普通区域"
	case ZoneTypeBlackList:
		return "黑名单区域"
	case ZoneTypeForward:
		return "转发区域"
	case ZoneTypeHost:
		return "主机区域"
	case ZoneTypeRPZ:
		return "RPZ区域"
	case ZoneTypeInvalid:
		return "无效区域"
	}
	return ""
}

func (z ZoneType) ShowDescription() string {
	switch z {
	case ZoneTypeDomain:
		return "标准的权威区域，用于解析业务用户自己的域名"
	case ZoneTypeBlackList:
		return "黑名单封禁区域，用于风险域名以及钓鱼网站"
	case ZoneTypeForward:
		return "转发区域，用于转发请求到其他DNS服务器，多见于海外污染的域名"
	case ZoneTypeHost:
		return "主机区域，用于解析主机记录"
	case ZoneTypeRPZ:
		return "RPZ区域，用于解析RPZ记录，由于办公网目前大多不是权威域，因此该类型是目前最常用的区域类型"
	case ZoneTypeInvalid:
		return "无效区域"
	}
	return ""
}

func ToZoneType(zt string) ZoneType {
	switch zt {
	case "domain":
		return ZoneTypeDomain
	case "blacklist":
		return ZoneTypeBlackList
	case "forward":
		return ZoneTypeForward
	case "host":
		return ZoneTypeHost
	case "rpz":
		return ZoneTypeRPZ
	}
	return ZoneTypeInvalid
}

type ResponsePolicy string

func (rp ResponsePolicy) String() string {
	return string(rp)
}

const (
	ResponsePolicyPassthru ResponsePolicy = "rpz-passthru." // 透传
	ResponsePolicyDrop     ResponsePolicy = "rpz-drop."     // 丢弃, 不返回任何响应
	ResponsePolicyNXDomain ResponsePolicy = "."             // 返回nxdoamin
	ResponsePolicyTCPOnly  ResponsePolicy = "rpz-tcp-only." // 强制解析器使用tcp进行请求
	ResponsePolicyNoData   ResponsePolicy = "*."            // 针对严格匹配的返回空的响应，针对子域名返回nxdomain
)

func RpzPolicies() []ResponsePolicy {
	return []ResponsePolicy{
		ResponsePolicyPassthru,
		ResponsePolicyDrop,
		ResponsePolicyNXDomain,
		ResponsePolicyTCPOnly,
		ResponsePolicyNoData,
	}
}

func ValidRPZ(rp ResponsePolicy) bool {
	return slices.Contains(RpzPolicies(), rp)
}
