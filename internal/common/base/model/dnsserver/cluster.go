package dnsserver

import "ks-knoc-server/pkg/json"

type Cluster struct {
	ID             int64     `json:"id" gorm:"column:id"`                             // 集群的ID
	Name           string    `json:"name" gorm:"column:name"`                         // 集群的名称
	Description    string    `json:"description" gorm:"column:description"`           // 集群的描述
	Status         string    `json:"status" gorm:"column:status"`                     // 集群的状态
	Forwarders     json.JSON `json:"forwarders" gorm:"column:forwarders"`             // 集群的转发器
	ForVPN         bool      `json:"for_vpn" gorm:"column:for_vpn"`                   // 集群的VPN
	ForTest        bool      `json:"for_test" gorm:"column:for_test"`                 // 集群是否用于测试
	ForDrill       bool      `json:"for_drill" gorm:"column:for_drill"`               // 集群是否用于演练
	HasLoadBalance bool      `json:"has_load_balance" gorm:"column:has_load_balance"` // 集群是否具有负载均衡
	LoadBalanceIPs json.JSON `json:"load_balance_ips" gorm:"column:load_balance_ips"` // 集群的负载均衡IP
	SecretControl  bool      `json:"secret_control" gorm:"column:secret_control"`     // 集群是否具有密钥控制
	CreateTime     int64     `json:"create_time" gorm:"column:create_time"`           // 集群的创建时间
	UpdateTime     int64     `json:"update_time" gorm:"column:update_time"`           // 集群的更新时间
	Deleted        bool      `json:"deleted" gorm:"column:deleted"`                   // 集群是否被删除

	*ClusterRndcKeyInfo `gorm:"embedded"`
}

type ClusterStatus string

func (c ClusterStatus) String() string {
	return string(c)
}

const (
	ClusterStatusActive   ClusterStatus = "active"
	ClusterStatusInactive ClusterStatus = "inactive"
)

type ClusterType string

func (c ClusterType) String() string {
	return string(c)
}

const (
	ClusterTypeAccess    ClusterType = "access"
	ClusterTypeRecursive ClusterType = "recursive"
	ClusterTypeForwarder ClusterType = "forwarder"
	ClusterTypeTest      ClusterType = "test"
	ClusterTypeDrill     ClusterType = "drill"
)

var ClusterTypeMapping = map[ClusterType]string{
	ClusterTypeAccess:    "接入集群",
	ClusterTypeRecursive: "递归集群",
	ClusterTypeForwarder: "转发集群",
	ClusterTypeTest:      "测试集群",
	ClusterTypeDrill:     "演练集群",
}

// ClusterRndcKeyInfo 集群的RndcKey信息
// 一个集群通用一套RndcKey，用于管理集群的DNS Server
type ClusterRndcKeyInfo struct {
	RndcKeyAlgo string `json:"rndc_key_algo" gorm:"column:rndc_key_algo"` // RndcKey的算法
	RndcKey     string `json:"rndc_key" gorm:"column:rndc_key"`           // RndcKey的密钥
	RndcKeyMd5  string `json:"rndc_key_md5" gorm:"column:rndc_key_md5"`   // RndcKey的MD5值
}
