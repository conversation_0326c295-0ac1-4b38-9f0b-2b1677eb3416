package dnsserver

import (
	"strings"

	"github.com/miekg/dns"
)

const (
	// DefaultTTL 默认TTL, 单位: 秒, 即1小时
	DefaultTTL = 3600
	// FqdnEnd FQDN结束符, .表示根域名，虽然平常可以省略，但是这里为了规范，还是加上
	FqdnEnd = "."
)

type RecordType string

const (
	RecordTypeA       RecordType = "A"
	RecordTypeCNAME   RecordType = "CNAME"
	RecordTypeMX      RecordType = "MX"
	RecordTypeSOA     RecordType = "SOA"
	RecordTypeTXT     RecordType = "TXT"
	RecordTypeSRV     RecordType = "SRV"
	RecordTypeInvalid RecordType = ""
)

func (r RecordType) String() string {
	return string(r)
}

func (r RecordType) Equal(rt RecordType) bool {
	return r == rt
}

func (r RecordType) IsValid() bool {
	return r != RecordTypeInvalid
}

func ToRecordType(rt string) RecordType {
	switch strings.ToUpper(rt) {
	case "A":
		return RecordTypeA
	case "CNAME":
		return RecordTypeCNAME
	case "MX":
		return RecordTypeMX
	case "SOA":
		return RecordTypeSOA
	case "TXT":
		return RecordTypeTXT
	case "SRV":
		return RecordTypeSRV
	default:
		return RecordTypeInvalid
	}
}

func ToDNSRecordType(rt string) dns.Type {
	switch strings.ToUpper(rt) {
	case "A":
		return dns.Type(dns.TypeA)
	case "CNAME":
		return dns.Type(dns.TypeCNAME)
	case "MX":
		return dns.Type(dns.TypeMX)
	case "SOA":
		return dns.Type(dns.TypeSOA)
	case "TXT":
		return dns.Type(dns.TypeTXT)
	case "SRV":
		return dns.Type(dns.TypeSRV)
	default:
		return dns.Type(dns.TypeNone)
	}
}

// ToDNSType 将RecordType转换为dns.Type, dns.Type是uint16类型的别名
func ToDNSType(rt string) uint16 {
	switch strings.ToUpper(rt) {
	case "A":
		return dns.TypeA
	case "CNAME":
		return dns.TypeCNAME
	case "MX":
		return dns.TypeMX
	case "SOA":
		return dns.TypeSOA
	case "TXT":
		return dns.TypeTXT
	case "SRV":
		return dns.TypeSRV
	default:
		return dns.TypeNone
	}
}

// DNSRecord 域名记录
type DNSRecord struct {
	ID          int64  `json:"id" gorm:"column:id"`
	Name        string `json:"name" gorm:"column:name"`
	RType       string `json:"r_type" gorm:"column:r_type"`
	Description string `json:"description" gorm:"column:description"`
	TTL         int    `json:"ttl" gorm:"column:ttl"`
	Value       string `json:"value" gorm:"column:value"`             // 记录内容
	IsWildCard  bool   `json:"is_wildcard" gorm:"column:is_wildcard"` // 是否是泛解析

	ZoneID int64 `json:"zone_id" gorm:"column:zone_id"` // 区域ID
	ViewID int64 `json:"view_id" gorm:"column:view_id"` // 视图ID

	CreateTime int64  `json:"create_time" gorm:"column:create_time"` // 创建时间
	UpdateTime int64  `json:"update_time" gorm:"column:update_time"` // 更新时间
	Creator    string `json:"creator" gorm:"column:creator"`         // 创建人，一般是sre
	Owner      string `json:"owner" gorm:"column:owner"`             // 负责人

	Enabled bool `json:"enabled" gorm:"column:enabled"` // 是否启用

	MXRecordInfo  *MXRecordInfo  `json:"mx_record_info" gorm:"embedded"`  // MX记录信息
	SrvRecordInfo *SrvRecordInfo `json:"srv_record_info" gorm:"embedded"` // SRV记录信息
}

type MXRecordInfo struct {
	MXPriority int `json:"mx_priority" gorm:"column:mx_priority;default:0"` // 优先级
}

type SrvRecordInfo struct {
	SrvPriority int `json:"srv_priority" gorm:"column:srv_priority;default:0"` // 优先级
	SrvWeight   int `json:"srv_weight" gorm:"column:srv_weight;default:0"`     // 权重
	SrvPort     int `json:"srv_port" gorm:"column:srv_port;default:0"`         // 端口
}

// RecordList 记录列表
type RecordList []DNSRecord

// Len 获取记录列表长度
func (l RecordList) Len() int {
	return len(l)
}

// Swap 交换记录列表中的两个记录
func (l RecordList) Swap(i, j int) {
	l[i], l[j] = l[j], l[i]
}

// Less 比较两个记录的创建时间
func (l RecordList) Less(i, j int) bool {
	return l[i].CreateTime < l[j].CreateTime
}

// DomainNode 表示DNS域名树中的一个节点
type DomainNode struct {
	// 节点标签，例如 "www" 或 "*"
	Label string

	// 子节点映射，键为标签，值为子节点指针
	Children map[string]*DomainNode

	// 是否为泛解析节点
	// 当节点本身是泛解析记录（如 *.example.com）时为 true
	IsWildCard bool

	// 节点本身对应的记录（如果有）
	// 例如：对于节点 example.com，这里存储的是 example.com 本身的记录
	// 这个字段用于快速访问节点本身的记录，避免遍历 Records 切片
	NodeRecord *DNSRecord

	// 节点记录列表
	Records []*DNSRecord
}

// NewDomainNode 创建一个新的域名节点
func NewDomainNode(label string) *DomainNode {
	return &DomainNode{
		Label:    label,
		Children: make(map[string]*DomainNode),
		Records:  make([]*DNSRecord, 0),
	}
}

type RpzTree struct {
	Root *DomainNode
}

// NewRpzRoot 创建一个新的RPZ树根节点
func NewRpzRoot() *RpzTree {
	return &RpzTree{
		Root: NewDomainNode(""),
	}
}

// ViewImpact 子视图受影响信息
type ViewImpact struct {
	ViewID    int64    `json:"view_id"`    // 视图 ID
	ViewName  string   `json:"view_name"`  // 视图名称
	ViewLevel string   `json:"view_level"` // 视图层级
	OldValue  []string `json:"old_value"`  // 变更前的值
	NewValue  []string `json:"new_value"`  // 变更后的值
	IsChanged bool     `json:"is_changed"` // 是否发生变化
}

type RecordChangeAction string

func (r RecordChangeAction) String() string {
	return string(r)
}

const (
	RecordChangeCreate RecordChangeAction = "create"
	RecordChangeUpdate RecordChangeAction = "update"
	RecordChangeDelete RecordChangeAction = "delete"
)

// RecordChange 变更前后对比结果
type RecordChange struct {
	RecordID  int64  `json:"record_id"`  // 记录 ID
	Name      string `json:"name"`       // 记录名称
	Type      string `json:"type"`       // 记录类型
	OldValue  string `json:"old_value"`  // 变更前的值
	NewValue  string `json:"new_value"`  // 变更后的值
	TTL       int    `json:"ttl"`        // TTL值
	IsChanged bool   `json:"is_changed"` // 是否发生变化
	Action    string `json:"action"`     // 操作类型
	ViewID    int64  `json:"view_id"`    // 记录所属的视图ID
}

type RecordChanges struct {
	ViewID        int64          `json:"view_id"`                // 视图 ID
	ViewName      string         `json:"view_name"`              // 视图名称
	ViewLevel     string         `json:"view_level"`             // 视图层级
	ViewOldValues []string       `json:"view_old_values"`        // 当前视图变更前的值
	ViewNewValues []string       `json:"view_new_values"`        // 当前视图变更后的值
	Changes       []RecordChange `json:"changes,omitempty"`      // 变更记录（包含系统计算的所有变更）
	TaskChanges   []RecordChange `json:"task_changes,omitempty"` // 用户的真实意图变更（仅包含用户直接操作）
	Impacts       []*ViewImpact  `json:"impacts,omitempty"`      // 对其他视图的影响
}

type RecordValueMap map[int64]string

func NewRecordValueMap() RecordValueMap {
	return RecordValueMap{}
}

func (r RecordValueMap) Set(key int64, value string) {
	r[key] = value
}

func (r RecordValueMap) Get(key int64) string {
	return r[key]
}

func (r RecordValueMap) Delete(key int64) {
	delete(r, key)
}

// ValuesList 获取所有值的列表
func (r RecordValueMap) ValuesList() []string {
	values := make([]string, 0, len(r))
	for _, v := range r {
		values = append(values, v)
	}
	return values
}
