package dnsserver

import (
	"errors"
)

/*
	DNS Serve
	Server结构体主要对实际Bind DNS Server的抽象
*/

type Server struct {
	ID int64 `json:"id"`
	// 对应的集群的ID
	ClusterID          int64 `json:"cluster_id"`
	ServerInfo         `gorm:"embedded"`
	ServerSoftWareInfo `gorm:"embedded"`
	ServerCheckInfo    `gorm:"embedded"`
	ServerConfInfo     `gorm:"embedded"`

	CreateTime int64 `json:"create_time"`
	UpdateTime int64 `json:"update_time"`
}

type ServerRole string

func (s ServerRole) String() string {
	return string(s)
}

const (
	ServerRoleMaster    ServerRole = "master"    // 主服务器
	ServerRoleSlave     ServerRole = "slave"     // 从服务器
	ServerRoleRecursion ServerRole = "recursion" // 递归服务器
	ServerRoleUnknown   ServerRole = "unknown"   // 未知
)

func ToServerRole(role string) ServerRole {
	switch role {
	case "master":
		return ServerRoleMaster
	case "slave":
		return ServerRoleSlave
	case "recursion":
		return ServerRoleRecursion
	default:
		return ServerRoleUnknown
	}
}

type ServerStatus string

func (s ServerStatus) String() string {
	return string(s)
}

const (
	ServerStatusRunning ServerStatus = "running" // 运行中
	ServerStatusStopped ServerStatus = "stopped" // 停止
	ServerStatusUnknown ServerStatus = "unknown" // 未知
)

type ServerInfo struct {
	Name   string       `json:"name"`   // 服务器的主机名
	Role   ServerRole   `json:"role"`   // 服务器角色
	Status ServerStatus `json:"status"` // 服务器状态
	IP     string       `json:"ip"`     // 服务器IP
	Port   int          `json:"port"`   // 服务器端口
}

// CanSendNSUpdateToServer 判断是否可以发送NSUPDATE请求
// 只有Running状况下才可以发送NsUpdate请求，并且必须是Master节点，且Server是启动的状态
func (s *ServerInfo) CanSendNSUpdateToServer() bool {
	return s.Status == ServerStatusRunning && s.Role == ServerRoleMaster
}

type ServerSoftWareInfo struct {
	Version string `json:"version"` // 软件版本
	DbPath  string `json:"db_path"` // 软件数据库路径
}

func (s *ServerSoftWareInfo) GetDbPath() string {
	return s.DbPath
}

func (s *ServerSoftWareInfo) GetVersion() string {
	return s.Version
}

func (s *ServerSoftWareInfo) Validate() error {
	if s.Version == "" {
		return errors.New("version is required")
	}
	if s.DbPath == "" {
		return errors.New("db_path is required")
	}
	return nil
}

type ServerCheckInfo struct {
	Checked       bool  `json:"checked"`         // 是否检查
	LastAliveTime int64 `json:"last_alive_time"` // 最后活跃时间
}

// ServerConfInfo 服务器配置信息
// 当前这个Server所指向的线路的版本ID
type ServerConfInfo struct {
	ViewID int64 `json:"view_id"` // 线路版本ID
}
