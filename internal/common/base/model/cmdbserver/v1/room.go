package v1

import (
	"fmt"

	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
)

const (
	EleTypeRack     = "rack"     // 机柜
	EleTypeObstacle = "obstacle" // 障碍物
	EleTypeEmpty    = "empty"    // 空地
)

type RackInfo struct {
	EleType     string `json:"ele_type"`     // 元素类型
	RackID      string `json:"rack_id"`      // 机柜ID
	RackName    string `json:"rack_name"`    // 机柜名称
	Height      int    `json:"height"`       // 机柜高度
	Power       int    `json:"power"`        // 机柜电量
	VacancyRate int    `json:"vacancy_rate"` // 机柜空闲率
}

func NewRackInfo() *RackInfo {
	return &RackInfo{}
}

func (r *RackInfo) GetRackType() string {
	return r.EleType
}

// RackLayout 机柜布局
type RackLayout struct {
	RoomID string
	x, y   int
	Boxes  [][]RackInfo
}

func NewRackLayout(x, y int) *RackLayout {
	// 初始化机柜布局，一个二位数组，都填上空位置
	boxes := make([][]RackInfo, 0)
	for i := 0; i < x; i++ {
		boxes = append(boxes, make([]RackInfo, 0))
		for j := 0; j < y; j++ {
			boxes[i] = append(boxes[i], RackInfo{
				EleType: EleTypeEmpty,
			})
		}
	}
	return &RackLayout{
		x:     x,
		y:     y,
		Boxes: boxes,
	}
}

func (r *RackLayout) GetX() int {
	return r.x
}

func (r *RackLayout) GetY() int {
	return r.y
}

// Show 展示机柜布局
func (r *RackLayout) Show() {
	for i := 0; i < r.x; i++ {
		for j := 0; j < r.y; j++ {
			if r.Boxes[i][j].EleType == EleTypeEmpty {
				fmt.Printf("[空]  ")
			} else {
				fmt.Printf(r.Boxes[i][j].RackName + "  ")
			}
		}
		// 换行
		fmt.Println()
	}

	fmt.Println("############################################")
}

func (r *RackLayout) GetBoxes() [][]RackInfo {
	return r.Boxes
}

func (r *RackLayout) GetBox(x, y int) *RackInfo {
	// 数组不可以越界，因此x和y都不可以超过初始化时候的x和y
	if x > r.x || y > r.y {
		return nil
	}

	// 同理，x和y都不可以小于0
	if x < 0 || y < 0 {
		return nil
	}

	return &r.Boxes[x][y]
}

func (r *RackLayout) SetBox(x, y int, rackInfo RackInfo) error {
	// 数组不可以越界，因此x和y都不可以超过初始化时候的x和y
	if x > r.x || y > r.y {
		return errno.ErrRoomRackPosition.Add("机柜位置错误")
	}

	// 同理，x和y都不可以小于0
	if x < 0 || y < 0 {
		return errno.ErrRoomRackPosition.Add("机柜位置错误")
	}

	current := r.GetBox(x, y)
	if current.EleType == EleTypeEmpty {
		r.Boxes[x][y] = rackInfo

		// debug模式下会打印机柜布局
		if gin.Mode() == gin.DebugMode {
			fmt.Println("===== Current Position x: ", x, "y: ", y, "=====")
			r.Show()
		}

		return nil
	}

	return errno.ErrRoomRackPosition.Add("机柜位置已被占用")
}

// RoomProperty 机房属性
type RoomProperty struct {
	RackTotal   int `json:"rack_total"`   // 机柜总数
	DeviceTotal int `json:"device_total"` // 设备总数
	VacancyRate int `json:"vacancy_rate"` // 空闲率
	Power       int `json:"power"`        // 电量
}

func NewRoomProperty() RoomProperty {
	return RoomProperty{}
}

type Room struct {
	RoomID       string       `json:"room_id"` // 机房ID，对应IDC模型的dataID
	RoomProperty RoomProperty `json:"property"`
	Layout       RackLayout   `json:"layout"`
}

func NewRoom(roomID string, x, y int) *Room {
	return &Room{
		RoomID:       roomID,
		RoomProperty: NewRoomProperty(),
		Layout:       *NewRackLayout(x, y),
	}
}

type RoomResponse struct {
	Room         ModelData    `json:"data"`
	RoomProperty RoomProperty `json:"property"`
	Layout       RackLayout   `json:"layout"`
}

func NewRoomResponse() *RoomResponse {
	return &RoomResponse{}
}

type BizViewListResponse struct {
}
