package v1

import (
	"encoding/json"
	"fmt"
	"strings"

	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/clock"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/mozillazg/go-pinyin"
	"github.com/qiniu/qmgo/field"
)

type Data interface {
	GetID() string
	Name() string
	GetData() mapdata.MapData
}

// InputType 输入类型，主要记录的是数据是通过什么方式输入的
type InputType int64

const (
	ManualInput InputType = iota
	ImportInput
	AutoImport
)

type ModelData struct {
	ID                 string              `bson:"_id" json:"id"`                                                                         // ID 唯一标识
	ModelCode          string              `json:"model_code" bson:"model_code" binding:"required" mapstructure:"model_code"`             // ModelCode 要添加的数据的所属模型是什么
	Data               mapdata.MapData     `json:"data" bson:"data" binding:"required" mapstructure:"data"`                               // Data 要添加的数据是什么
	IdentifyName       string              `json:"identify_name" bson:"identify_name" binding:"required" mapstructure:"identify_name"`    // IdentifyName 唯一标识的名称，用来唯一标识这个数据的字段的key
	IdentifyValue      string              `json:"identify_value" bson:"identify_value" binding:"required" mapstructure:"identify_value"` // IdentifyValue 唯一标识的值，用来唯一标识这个数据的字段的value
	InPutType          InputType           `json:"input_type" bson:"input_type" mapstructure:"input_type"`                                // InPutType 录入类型
	CreateAt           int64               `bson:"create_at" json:"create_at" mapstructure:"create_at"`                                   // CreateAt 创建时间
	UpdateAt           int64               `bson:"update_at" json:"update_at" mapstructure:"update_at"`                                   // UpdateAt 更新时间
	ParentID           string              `json:"parent_id" bson:"parent_id" mapstructure:"parent_id"`                                   // ParentID 父模型的实例
	ParentDesc         string              `json:"parent_desc" bson:"parent_desc" mapstructure:"parent_desc"`                             // ParentDesc 父模型的实例描述
	AssociateInstances []AssociateInstance `json:"associate_instances" bson:"associate_instances" mapstructure:"associate_instances"`     // AssociateInstances 关联的实例
	Active             bool                `json:"active" bson:"active" mapstructure:"active"`                                            // Active 是否激活
	LabelIDList        []string            `json:"label_id_list" bson:"label_id_list" mapstructure:"label_id_list"`                       // 数据绑定标签的ID列表
	MetaData           mapdata.MapData     `json:"meta_data" bson:"meta_data" mapstructure:"meta_data"`                                   // MetaData 元数据
}

// NewModelData 新建一个模型数据
func NewModelData() *ModelData {
	return &ModelData{
		Data:               make(mapdata.MapData),
		MetaData:           make(mapdata.MapData),
		AssociateInstances: make([]AssociateInstance, 0),
		LabelIDList:        make([]string, 0),
	}
}

func (m *ModelData) Copy() (*ModelData, error) {
	jsonData, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}

	var copied ModelData
	if err := json.Unmarshal(jsonData, &copied); err != nil {
		return nil, err
	}

	return &copied, nil
}

func (m *ModelData) GetID() string {
	return m.ID
}

func (m *ModelData) GetParentID() string {
	return m.ParentID
}

func (m *ModelData) GetParentDesc() string {
	return m.ParentDesc
}

// Name 返回数据的名称
func (m *ModelData) Name() string {
	key := fmt.Sprintf("%s_name", m.ModelCode)
	if name, ok := m.Data[key]; ok {
		return utils.ToString(name)
	}
	return ""
}

// InitTime 设置初始化时间
func (m *ModelData) InitTime() *ModelData {
	currentTime := clock.NowInNano()
	m.CreateAt = currentTime
	m.UpdateAt = currentTime
	return m
}

// CustomFields 自定义字段，主要是设置ID
func (m *ModelData) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

// SetUpdateTime 设置更新时间
func (m *ModelData) SetUpdateTime() *ModelData {
	m.UpdateAt = clock.NowInNano()
	return m
}

// GetDataInfo 获取数据的信息，将数据内容拼接为一个长字符串，主要是用来打印日志
// TODO: 需要优化一下，这里没有判断哪些字段是存在的，有可能有的字段已经删除了，但是数据没有删除
// 这块优化一下数据。
func (m *ModelData) GetDataInfo(withKey bool) string {
	info := ""

	// 不要直接拼接数据，得看一下对应的字段还在不在
	identifyField := fmt.Sprintf("%s_code", m.ModelCode)
	counter := 0
	for k, v := range m.Data {
		counter++
		if k == identifyField {
			continue
		}
		if utils.ToString(v) == "" {
			continue
		}
		if counter == len(m.Data) {
			if withKey {
				info += fmt.Sprintf("%s:%v", k, v)
			} else {
				info += fmt.Sprintf("%v", v)
			}
			break
		} else {
			if withKey {
				info += fmt.Sprintf("%s:%v, ", k, v)
			} else {
				info += fmt.Sprintf("%v, ", v)
			}
		}
	}
	return info
}

// ToString 将数据的值转换为字符串
func (m *ModelData) ToString() *ModelData {
	for dataK, dataV := range m.Data {
		// 有可能包含空格，把两侧的空格给去掉
		dataString := strings.TrimSpace(utils.ToString(dataV))
		m.Data[dataK] = dataString
	}
	return m
}

// AutoGenerateDataCode 自动生成数据的code
// 资源的唯一标识（自动生成）：「模型唯一标识」+该资源的「名称」+时间戳(若名称为中文则转成拼音)
func (m *ModelData) AutoGenerateDataCode() (string, error) {
	pyStr := ""
	pyObj := pinyin.NewArgs()
	// 默认会丢弃非中文的字符，因此这里可以设置一个FallBack函数，将非中文的字符转换为字符串并返回，而不是忽略
	pyObj.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)}
	}
	var ok bool
	if _, ok = m.Data["name"]; ok {
		if nameStr, ok := m.Data["name"].(string); ok {
			for _, p := range pinyin.Pinyin(nameStr, pyObj) {
				pyStr += p[0]
			}
			return fmt.Sprintf("%s_%s_%d", m.ModelCode, pyStr, clock.NowInNano()), nil
		}
	}
	return "", errno.ErrDataInvalid.Addf("name字段不存在或者不是字符串类型")
}

func (m *ModelData) GetAssociateInstance(dataID string) *AssociateInstance {
	for _, instance := range m.AssociateInstances {
		if instance.InstanceID == dataID {
			return &instance
		}
	}
	return nil
}

func (m *ModelData) GetData() mapdata.MapData {
	return m.Data
}

func (m *ModelData) Info() string {
	infoString := ""
	for k, v := range m.Data {
		infoString += fmt.Sprintf("%s: %v; ", k, v)
	}
	return infoString
}

// UpdateModelDataStoreRequest 更新模型数据请求
type UpdateModelDataStoreRequest struct {
	Id           string // 要更新的数据的唯一标识
	ModelData    *ModelData
	UpdateParent bool
	ParentID     string
	ParentDesc   string
}
