package v1

type NodeType string

const (
	NodeTypeCity      NodeType = "city"         // 城市
	NodeTypeOffice    NodeType = "office"       // 职场
	NodeTypeIDC       NodeType = "idc"          // 机房
	NodeTypeRack      NodeType = "rack"         // 机柜
	NodeTypeStoreroom NodeType = "storeroom"    // 库房
	NodeTypeRoot      NodeType = "root"         // 根节点
	NodeTypeApp       NodeType = "app"          // 应用
	NodeTypeBiz       NodeType = "biz"          // 业务
	NodeTypeService   NodeType = "service"      // 服务
	NodeUnattributed  NodeType = "unattributed" // 服务
)

type TreeNode struct {
	Title    string       `json:"title"`
	Key      string       `json:"key"`
	NodeType NodeType     `json:"type"`
	Children TreeNodeList `json:"children"`
}

type TreeNodeList []*TreeNode

func (t TreeNodeList) Len() int {
	return len(t)
}

func (t TreeNodeList) Swap(i, j int) {
	t[i], t[j] = t[j], t[i]
}

func (t TreeNodeList) Less(i, j int) bool {
	return t[i].Title < t[j].Title
}

func NewTreeNode() *TreeNode {
	return &TreeNode{Children: make([]*TreeNode, 0)}
}

type HostTreeResponse struct {
	Data []*TreeNode `json:"data"`
}

func NewHostTreeResponse() *HostTreeResponse {
	return &HostTreeResponse{Data: make([]*TreeNode, 0)}
}
