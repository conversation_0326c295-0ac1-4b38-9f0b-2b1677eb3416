package v1

// AuditRecordResponse 审计记录数据的响应
type AuditRecordResponse struct {
	Total       int64   `json:"total"`
	Pages       int64   `json:"pages"`
	PageSize    int64   `json:"page_size"`
	CurrentPage int64   `json:"current_page"`
	Data        []Audit `json:"data"`
}

type Audit struct {
	ID           int    `json:"id"`
	UniqueId     string `json:"unique_id" bson:"unique_id"`
	ModelCode    string `bson:"model_code" bson:"model_code"`
	ResourceType string `json:"resource_type" bson:"resource_type"`
	Action       string `json:"action"`
	Operator     string `json:"operator"`
	Operation    string `json:"operation"`
	Ts           int    `json:"ts"`
}
