package v1

import (
	"ks-knoc-server/internal/common/validate"

	"go.uber.org/zap"
)

type ModelAttrType string

func (m ModelAttrType) String() string {
	return string(m)
}

const (
	StringType   ModelAttrType = "string"
	IntType      ModelAttrType = "int"
	FloatType    ModelAttrType = "float"
	DateType     ModelAttrType = "date"
	DateTimeType ModelAttrType = "datetime"
	TextAreaType ModelAttrType = "textarea"
	BoolType     ModelAttrType = "bool"
	SelectType   ModelAttrType = "select"
	ArrayType    ModelAttrType = "array"
	JsonType     ModelAttrType = "json"
)

var (
	v *validate.ValidateOptions
)

// ModelAttribute 模型属性
type ModelAttribute interface {
	GetName() string            // 属性名称, 由GeneralModelObj定义
	GetCode() string            // 属性唯一标识, 由GeneralModelObj定义
	GetModelCode() string       // 属性所属模型的唯一标识
	GetTypeName() ModelAttrType // 属性类型
	GetAttrGroupCode() string   // 属性所属分组的唯一标识
	CheckIsNull() bool          // 是否可以为空
	CheckIsDisplay() bool       // 是否可展示
	CheckIsUnique() bool        // 是否唯一
	CheckIsEditAble() bool      // 是否可以编辑
	CheckIsSystemCreated() bool // 是否是系统创建的
	CheckIsBuiltIn() bool       // 是否是内置的, 由GeneralModelObj定义
	Validate() error            // 验证属性的合法性
}

// NewModelAttribute 根据属性类型创建属性
func NewModelAttribute(attrType string) ModelAttribute {
	switch attrType {
	case "string":
		return NewStringAttribute()
	case "array":
		return NewArrayAttribute()
	case "json":
		return NewJsonAttribute()
	case "int":
		return NewIntAttribute()
	case "float":
		return NewFloatAttribute()
	case "date":
		return NewDateAttribute()
	case "datetime":
		return NewDateTimeAttribute()
	case "textarea":
		return NewTextAreaAttribute()
	case "bool":
		return NewBoolAttribute()
	case "select":
		return NewSelectAttribute()
	default:
		zap.L().Error("Invalid attrType", zap.String("attrType", attrType))
		return nil
	}
}
