package v1

type OneHost struct {
	ModelData   `json:",inline"`
	<PERSON><PERSON><PERSON><PERSON><PERSON> bool         `json:"has_children"`
	Children    []*ModelData `json:"children"`
}

func NewOneHost() *OneHost {
	return &OneHost{
		Children: make([]*ModelData, 0),
	}
}

type HostViewListResponse struct {
	Total       int64         `json:"total"`
	Pages       int64         `json:"pages"`
	PageSize    int64         `json:"page_size"`
	CurrentPage int64         `json:"current_page"`
	Models      []SimpleModel `json:"models"`
	Data        []*OneHost    `json:"data"`
}

func NewHostViewListResponse() *HostViewListResponse {
	return &HostViewListResponse{
		Total:       0,
		Pages:       1,
		PageSize:    10,
		CurrentPage: 1,
		Models:      make([]SimpleModel, 0),
		Data:        make([]*OneHost, 0),
	}
}
