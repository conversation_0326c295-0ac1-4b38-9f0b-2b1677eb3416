package v1

import (
	"fmt"
	"regexp"
	"time"

	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/clock"

	"github.com/qiniu/qmgo/field"
)

// GeneralModelObj 通用模型类
type GeneralModelObj struct {
	ID       string `bson:"_id" json:"id" mapstructure:"_id"`
	Code     string `bson:"code,omitempty" json:"code" binding:"required,CheckCode,MaxLength512" mapstructure:"code"`
	CreateAt int64  `bson:"create_at,omitempty" json:"create_at" mapstructure:"create_at"`
	UpdateAt int64  `bson:"update_at,omitempty" json:"update_at" mapstructure:"update_at"`
	Name     string `bson:"name" json:"name" binding:"required,MaxLength512" mapstructure:"name"`
	Builtin  bool   `bson:"builtin" json:"builtin" mapstructure:"builtin"` // Builtin 是否是内置的，内置的不允许删除
}

// GetName 获取名称
func (g *GeneralModelObj) GetName() string {
	return g.Name
}

// GetCode 获取唯一标识
func (g *GeneralModelObj) GetCode() string {
	return g.Code
}

// CheckIsBuiltIn 检查是否是内置的
func (g *GeneralModelObj) CheckIsBuiltIn() bool {
	return g.Builtin
}

// Validate 验证数据的合法性
func (g *GeneralModelObj) Validate() error {
	var errMsg string

	// Code的规则，首字母必须是字母，后面可以是字母、数字、下划线
	regexPattern := "^[a-zA-Z][a-zA-Z0-9_]*$"
	re := regexp.MustCompile(regexPattern)
	if !re.MatchString(g.Code) {
		errMsg = fmt.Sprintf("唯一标识不合规，要求必须以英文字母开头，后面可以是英文字母、数字、下划线的组合，当前值为：%s", g.Code)
		return errno.ErrParameterInvalid.Add(errMsg)
	}

	// 判断字符串长度
	if len(g.Code) > 512 || len(g.Name) > 512 {
		return errno.ErrParameterInvalid.Add("超过最大字符限制，长度不得超过512")
	}
	return nil
}

// SetTime 设置时间，首次创建的时候需要设置，同时更新创建时间和更新时间
func (g *GeneralModelObj) SetTime() *GeneralModelObj {
	g.CreateAt = time.Now().UnixNano()
	g.UpdateAt = time.Now().UnixNano()
	return g
}

// SetUpdateAt 设置更新时间
func (g *GeneralModelObj) SetUpdateAt() *GeneralModelObj {
	g.UpdateAt = clock.NowInNano()
	return g
}

// SetGeneralDefaultVal 设置通用的默认值
func (g *GeneralModelObj) SetGeneralDefaultVal() *GeneralModelObj {
	g.SetTime()
	return g
}

// IsBuiltIn 是否是内置的，内置的不允许删除
func (g *GeneralModelObj) IsBuiltIn() bool {
	return g.Builtin
}

// CustomFields 自定义字段，主要用于映射doc中的_id字段
func (g *GeneralModelObj) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

func NewGeneralModelObj(name, code string, builtin bool) GeneralModelObj {
	return GeneralModelObj{
		Name:    name,
		Code:    code,
		Builtin: builtin,
	}
}

// InstanceFilter 唯一标识过滤器
type InstanceFilter struct {
	Code string `bson:"code" json:"code" binding:"required"`
	Name string `bson:"name" json:"name"`
}

// IDFilter ID过滤器
type IDFilter struct {
	ID string `bson:"_id" json:"id" binding:"required"`
}
