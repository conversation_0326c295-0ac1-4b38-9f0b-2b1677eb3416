package v1

// ModelAttrRule 通用的Model规则
type ModelAttrRule struct {
    RuleName string `json:"rule_name" bson:"rule_name" mapstructure:"rule_name"`
    RuleRe   string `json:"rule_re" bson:"rule_re" mapstructure:"rule_re"`
}

type AttributeRules string

const (
    AttributeRulesIPv4                                  = AttributeRules(`^((25[0-5]|2[0-4]\d|[1]{1}\d{1}\d{1}|[1-9]{1}\d{1}|\d{1})(\.|$)){4}$`)
    AttributeRulesPhone                                 = AttributeRules(`^0?(13|14|15|18|17)[0-9]{9}$`)
    AttributeRulesEmail                                 = AttributeRules(`^\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}$`)
    AttributeRulesLowercase                             = AttributeRules(`^[a-z]*$`)
    AttributeRulesUppercase                             = AttributeRules(`^[A-Z]*$`)
    AttributeRulesEnglishAndNumber                      = AttributeRules(`^[a-zA-Z0-9]*$`)
    AttributeRulesEnglishAndNumberAndUnderlineAndHyphen = AttributeRules(`^[\\.a-zA-Z0-9_-]*$`)
    AttributeRulesChinese                               = AttributeRules(`^[\u4e00-\u9fa5]{0,}$`)
    AttributeRulesPositiveInteger                       = AttributeRules(`^[1-9]\d*$`)
    AttributeRulesNegativeInteger                       = AttributeRules(`^-[1-9]\d*$`)
    AttributeRulesInteger                               = AttributeRules(`^[+-]?\d+$`)
    AttributeRulesPositiveFloat                         = AttributeRules(`^[+]?[0-9]*\\.{1}?[0-9]+$`)
    AttributeRulesNegativeFloat                         = AttributeRules(`^-[+]?[0-9]*\\.{1}?[0-9]+$`)
)

var BuiltInRegexMapping = map[AttributeRules]string{
    AttributeRulesIPv4:                                  "IPv4",
    AttributeRulesPhone:                                 "手机号",
    AttributeRulesEmail:                                 "邮箱",
    AttributeRulesLowercase:                             "小写字母",
    AttributeRulesUppercase:                             "大写字母",
    AttributeRulesEnglishAndNumber:                      "英文和数字",
    AttributeRulesEnglishAndNumberAndUnderlineAndHyphen: "英文和数字，下划线，中横线，英文小数点",
    AttributeRulesChinese:                               "中文",
    AttributeRulesPositiveInteger:                       "正整数",
    AttributeRulesNegativeInteger:                       "负整数",
    AttributeRulesInteger:                               "整数",
    AttributeRulesPositiveFloat:                         "正浮点数",
    AttributeRulesNegativeFloat:                         "负浮点数",
}

func GetRegexName(regexPattern string) string {
    name, exist := BuiltInRegexMapping[AttributeRules(regexPattern)]
    if exist {
        return name
    }
    // 用户自定义的正则规则
    return ""
}
