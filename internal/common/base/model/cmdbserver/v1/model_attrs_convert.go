package v1

import (
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"

	"github.com/mitchellh/mapstructure"
	"go.uber.org/zap"
)

// CommonModelAttribute 通用的模型属性
type CommonModelAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         map[string]interface{} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

type CommonModelAttributeList []CommonModelAttribute

func (c CommonModelAttributeList) Len() int {
	return len(c)
}

func (c CommonModelAttributeList) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}

func (c CommonModelAttributeList) Less(i, j int) bool {
	return c[i].CreateAt < c[j].CreateAt
}

func (c *CommonModelAttribute) Type() string {
	return c.TypeName
}

func (c *CommonModelAttribute) AttrName() string {
	return c.Name
}

func (c *CommonModelAttribute) AttrCode() string {
	return c.Code
}

func (c *CommonModelAttribute) ToStringField() (*StringAttribute, error) {
	str := NewStringAttribute()
	if c.TypeName == "string" {
		err := mapstructure.Decode(c, str)
		if err != nil {
			zap.L().Error("ToStringField", zap.Error(err))
			return nil, err
		}
		return str, nil
	}
	return nil, errno.ErrModelAttrNotTypeString.Add("属性不是字符串类型")
}

func (c *CommonModelAttribute) ToIntField() (*IntAttribute, error) {
	intAttr := NewIntAttribute()
	if c.TypeName == "int" {
		err := mapstructure.Decode(c, intAttr)
		if err != nil {
			zap.L().Error("ToIntField", zap.Error(err))
			return nil, err
		}
		return intAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeInt.Add("属性不是整型")
}

func (c *CommonModelAttribute) ToFloatField() (*FloatAttribute, error) {
	floatAttr := NewFloatAttribute()
	if c.TypeName == "float" {
		err := mapstructure.Decode(c, floatAttr)
		if err != nil {
			zap.L().Error("ToFloatField", zap.Error(err))
			return nil, err
		}
		return floatAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeFloat.Add("属性不是浮点型")
}

func (c *CommonModelAttribute) ToBoolField() (*BoolAttribute, error) {
	boolAttr := NewBoolAttribute()
	if c.TypeName == "bool" {
		err := mapstructure.Decode(c, boolAttr)
		if err != nil {
			zap.L().Error("ToBoolField", zap.Error(err))
			return nil, err
		}
		return boolAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeBool.Add("属性不是布尔型")
}

func (c *CommonModelAttribute) ToDateField() (*DateAttribute, error) {
	dateAttr := NewDateAttribute()
	if c.TypeName == "date" {
		err := mapstructure.Decode(c, dateAttr)
		if err != nil {
			zap.L().Error("ToDateField", zap.Error(err))
			return nil, err
		}
		return dateAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeDate.Add("属性不是日期型")
}

func (c *CommonModelAttribute) ToDateTimeField() (*DateTimeAttribute, error) {
	dateTimeAttr := NewDateTimeAttribute()
	if c.TypeName == "datetime" {
		err := mapstructure.Decode(c, dateTimeAttr)
		if err != nil {
			zap.L().Error("ToDateTimeField", zap.Error(err))
			return nil, err
		}
		return dateTimeAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeDateTime.Add("属性不是日期时间型")
}

func (c *CommonModelAttribute) ToTextAreaField() (*TextareaAttribute, error) {
	textAreaAttr := NewTextAreaAttribute()
	if c.TypeName == "textarea" {
		err := mapstructure.Decode(c, textAreaAttr)
		if err != nil {
			zap.L().Error("ToTextAreaField", zap.Error(err))
			return nil, err
		}
		return textAreaAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeTextArea.Add("属性不是文本域型")
}

func (c *CommonModelAttribute) ToSelectField() (*SelectAttribute, error) {
	selectAttr := NewSelectAttribute()
	if c.TypeName == "select" {
		err := mapstructure.Decode(c, selectAttr)
		if err != nil {
			zap.L().Error("ToSelectField", zap.Error(err))
			return nil, err
		}
		return selectAttr, nil
	}
	return nil, errno.ErrModelAttrNotTypeSelect.Add("属性不是下拉框型")
}

// ToRelationField 将关系字段转换为关系对象
func (c *CommonModelAttribute) ToRelationField() (RelationField, error) {
	if c.TypeName == "relationship" {
		switch utils.ToInt(c.Attrs["rel_type"]) {
		case int(Subordinate):
			sub := NewSubordinationRelationshipAttribute()
			err := mapstructure.Decode(c, sub)
			if err != nil {
				return nil, err
			}
			return sub, nil
		case int(Associate):
			ass := NewAssociationRelationshipAttribute()
			err := mapstructure.Decode(c, ass)
			if err != nil {
				return nil, err
			}
			return ass, nil
		default:
			return nil, errno.ErrModelAttrNotTypeRelationship.Add("模型属性既不是从属关系也不是关联关系类型")
		}
	}

	return nil, errno.ErrModelAttrNotTypeRelationship.Add("模型属性不是关系类型")
}
