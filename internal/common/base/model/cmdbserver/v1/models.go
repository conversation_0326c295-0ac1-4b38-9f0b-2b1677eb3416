package v1

// Model 模型
type Model struct {
	// GeneralModelObj bson inline 可以让GeneralModelObj的字段直接嵌入到Model中，这一点和json不太一样
	// Json默认是直接嵌入的，bson默认是不嵌入的，json如果需要嵌入，就需要手动指定Json的tag。
	GeneralModelObj `bson:",inline"`
	ModelGroup      string   `bson:"model_group" json:"model_group" binding:"required"`
	Icon            string   `bson:"icon" json:"icon"`
	ParentCode      string   `bson:"parent_code" json:"parent_code"`
	ChildrenCode    []string `bson:"children_code" json:"children_code"`
	Description     string   `bson:"description" json:"description"`
	Total           int64    `bson:"-" json:"total"`
}

// SimpleModel 简单模型，仅包含code和name
type SimpleModel struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

type ModelList []Model

func (ml ModelList) Len() int {
	return len(ml)
}

func (ml ModelList) Less(i, j int) bool {
	return ml[i].CreateAt < ml[j].CreateAt
}

func (ml ModelList) Swap(i, j int) {
	ml[i], ml[j] = ml[j], ml[i]
}

// ModelIcon 模型图标
type ModelIcon struct {
	ID   int    `json:"id" gorm:"id" mapstructure:"id"`
	URL  string `json:"url" gorm:"url" mapstructure:"url"`
	Name string `json:"name" gorm:"name" mapstructure:"name"`
}
