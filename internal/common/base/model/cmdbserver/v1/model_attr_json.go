package v1

import (
	"ks-knoc-server/internal/common/errno"
)

// JsonAttribute Json类型的专有属性，主要用户存储json类型的数据
type JsonAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (j *JsonAttribute) GetTypeName() ModelAttrType {
	return JsonType
}

func (j *JsonAttribute) CheckRule() error {
	if j.Attrs.ModelAttrRule.RuleRe == "" {
		return errno.ErrParameterRequired.Add("正则表达式不能为空")
	}
	return nil
}

func (j *JsonAttribute) Validate() error {
	if err := j.BaseAttribute.Validate(); err != nil {
		return err
	}
	return nil
}

func NewJsonAttribute() *JsonAttribute {
	j := &JsonAttribute{}
	// 由于JsonAttribute还未完全上线，因此先默认不做展示
	j.BaseAttribute.IsDisplay = false
	return j
}
