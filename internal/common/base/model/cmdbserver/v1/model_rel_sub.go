package v1

import (
	"github.com/qiniu/qmgo/field"
)

// ModelSubordinateRelationRequest 创建模型关联关系请求
type ModelSubordinateRelationRequest struct {
	ModelCode string `json:"model_code" bson:"model_code" mapstructure:"model_code" binding:"required" validate:"required"`
	Name      string `bson:"name" json:"name" binding:"required,max=512" mapstructure:"name"`
	RelTo     string `json:"rel_to" bson:"rel_to" mapstructure:"rel_to" validate:"required" binding:"required"`
	RelDesc   string `json:"rel_desc" bson:"rel_desc" mapstructure:"rel_desc" binding:"required,max=512"`
	BuiltIn   bool   `json:"builtin" bson:"builtin" mapstructure:"builtin"`
}

func NewModelSubordinateRelationRequest() *ModelSubordinateRelationRequest {
	return &ModelSubordinateRelationRequest{}
}

// SubordinationRelationshipAttribute 从属关系属性，mongodb中的保存形式
type SubordinationRelationshipAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		RelType     int    `json:"rel_type" bson:"rel_type" mapstructure:"rel_type" validate:"required"`
		RelFrom     string `json:"rel_from" bson:"rel_from" mapstructure:"rel_from" validate:"required"`
		RelFromName string `json:"rel_from_name" bson:"rel_from_name" mapstructure:"rel_from_name"`
		RelTo       string `json:"rel_to" bson:"rel_to" mapstructure:"rel_to" validate:"required"`
		RelToDesc   string `json:"rel_to_desc" bson:"rel_to_desc" mapstructure:"rel_to_desc"`
		RelToName   string `json:"rel_to_name" bson:"rel_to_name" mapstructure:"rel_to_name"`
	} `json:"attrs" bson:"attrs"`
}

// NewSubordinationRelationshipAttribute 创建从属关系属性
func NewSubordinationRelationshipAttribute() *SubordinationRelationshipAttribute {
	return &SubordinationRelationshipAttribute{}
}

func (s *SubordinationRelationshipAttribute) Init() {
	// 初始化一些值
	s.IsUnique = false
	s.IsEditAble = true
	s.IsNull = true
	s.SystemCreated = false
	s.IsDisplay = true
	s.SetGeneralDefaultVal()
}

// RelType 获取关系类型
func (s *SubordinationRelationshipAttribute) RelType() RelationType {
	switch RelationType(s.Attrs.RelType) {
	case Subordinate:
		return Subordinate
	case Associate:
		return Associate
	default:
		return Unknown
	}
}

func (s *SubordinationRelationshipAttribute) RemoteName() string {
	return s.Attrs.RelToName
}

func (s *SubordinationRelationshipAttribute) LocalName() string {
	return s.Attrs.RelFromName
}

func (s *SubordinationRelationshipAttribute) RelDesc() string {
	return s.Attrs.RelToDesc
}

func (s *SubordinationRelationshipAttribute) Remote() string {
	return s.Attrs.RelTo
}

func (s *SubordinationRelationshipAttribute) Local() string {
	return s.Attrs.RelFrom
}

func (s *SubordinationRelationshipAttribute) GetID() string {
	return s.ID
}

func (s *SubordinationRelationshipAttribute) LocalModelName() string {
	return s.Attrs.RelFromName
}

func (s *SubordinationRelationshipAttribute) RemoteModelName() string {
	return s.Attrs.RelToName
}

func (s *SubordinationRelationshipAttribute) RelName() string {
	return s.Name
}

func (s *SubordinationRelationshipAttribute) Self() interface{} {
	return s
}

func (s *SubordinationRelationshipAttribute) LocalFieldCode() string {
	return s.Code
}

// DataTreePath 数据树路径 主要针对的是从属关系，因为从属关系构建出来其实是一棵树
type DataTreePath struct {
	ParentID       string `json:"parent_id" bson:"parent_id" mapstructure:"parent_id"`
	ChildID        string `json:"child_id" bson:"child_id" mapstructure:"child_id"`
	ChildModelCode string `json:"child_model_code" bson:"child_model_code" mapstructure:"child_model_code"`
	ParentName     string `json:"parent_name" bson:"parent_name" mapstructure:"parent_name"`
	ChildName      string `json:"child_name" bson:"child_name" mapstructure:"child_name"`
	Depth          int    `json:"depth" bson:"depth" mapstructure:"depth"`
}

func (d *DataTreePath) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

func NewDataTreePath() *DataTreePath {
	return &DataTreePath{}
}

type DataTreePathList []*DataTreePath

func (d DataTreePathList) Len() int {
	return len(d)
}

func (d DataTreePathList) Descendants() []string {
	ids := make([]string, 0)
	for _, v := range d {
		ids = append(ids, v.ChildID)
	}
	return ids
}
