package v1

type RelationType int

const (
	Subordinate RelationType = iota + 1
	Associate
	Unknown
)

type ModelRelation interface {
	GetRelationType() RelationType
	Self() interface{}
}

// ModelRelationResponse 模型关系response响应
type ModelRelationResponse struct {
	Subordinate []struct {
		SubordinationRelationshipAttribute `json:",inline"`
		IsParent                           bool `json:"is_parent"`
		IsRack                             bool `json:"is_rack"`
	} `json:"subordinate"`
	Associate []struct {
		AssociationRelationshipAttribute `json:",inline"`
		RelMode                          int `json:"rel_mode"`
	} `json:"association"`
}

func NewModelRelationResponse() *ModelRelationResponse {
	return &ModelRelationResponse{
		Subordinate: make([]struct {
			SubordinationRelationshipAttribute `json:",inline"`
			IsParent                           bool `json:"is_parent"`
			IsRack                             bool `json:"is_rack"`
		}, 0),
		Associate: make([]struct {
			AssociationRelationshipAttribute `json:",inline"`
			RelMode                          int `json:"rel_mode"`
		}, 0),
	}
}

type UpdateModelRelationRequest struct {
	RelID   string `json:"rel_id" binding:"required"`
	RelDesc string `json:"rel_desc" binding:"required"`
}

type Edge struct {
	ID            string `json:"id"`
	Code          string `json:"code"`
	Name          string `json:"name"`
	Builtin       bool   `json:"builtin"`
	ModelCode     string `json:"model_code"`
	SystemCreated bool   `json:"system_created"`
	RelType       int    `json:"rel_type"`
	RelCode1      string `json:"rel_code1"`
	RelCode2      string `json:"rel_code2"`
	RelDesc       string `json:"rel_desc"`
}

type ModelRelationTopo struct {
	Nodes []Model `json:"nodes"`
	Edges []Edge  `json:"edges"`
}

func NewModelRelationTopo() *ModelRelationTopo {
	return &ModelRelationTopo{
		Nodes: make([]Model, 0),
		Edges: make([]Edge, 0),
	}
}

type RelatedData struct {
	Relation struct {
		RelName            string `json:"rel_name"`
		RelDesc            string `json:"rel_desc"`          // 数据关系描述
		RelationShipDesc   string `json:"relationship_desc"` // 模型关系描述
		RelType            int    `json:"rel_type"`
		RelID              string `json:"rel_id"`
		RelSourceModelName string `json:"rel_source_model_name"`
		RelTargetModelName string `json:"rel_target_model_name"`
	} `json:"relation"`
	Source struct {
		SourceInstanceID   string `json:"source_instance_id"`
		SourceInstanceName string `json:"source_instance_name"`
		SourceModelCode    string `json:"source_model_code"`
		CurrentData        bool   `json:"current_data"`
	} `json:"source"`
	Target struct {
		TargetInstanceID   string `json:"target_instance_id"`
		TargetInstanceName string `json:"target_instance_name"`
		TargetModelCode    string `json:"target_model_code"`
		CurrentData        bool   `json:"current_data"`
	} `json:"target"`
	IsRack bool   `json:"is_rack"` // 关联对端的数据是否和机柜建立了从属关系，即机柜是父模型
	StartU int    `json:"start_u"` // 关联对端的数据在机柜上的起始U位
	RackID string `json:"rack_id"` // 关联对端的数据所在的机柜ID
	DelID  string `json:"del_id"`  // 这个id的作用是用来删除关系使用的
}

func NewRelatedData() *RelatedData {
	return &RelatedData{}
}

type RelatedDataList []RelatedData

func (r RelatedDataList) Less(i, j int) bool {
	return r[i].Relation.RelName < r[j].Relation.RelName
}

func (r RelatedDataList) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

func (r RelatedDataList) Len() int {
	return len(r)
}

func NewRelatedDataList() RelatedDataList {
	return make([]RelatedData, 0)
}

type RelatedDataResponse struct {
	Total       int64           `json:"total"`
	Pages       int64           `json:"pages"`
	PageSize    int64           `json:"page_size"`
	CurrentPage int64           `json:"current_page"`
	Data        RelatedDataList `json:"data"`
}

func NewRelatedDataResponse() *RelatedDataResponse {
	return &RelatedDataResponse{
		Data: NewRelatedDataList(),
	}
}

type RelationField interface {
	GetID() string           // 获取关系属性的ID
	RelType() RelationType   // 关系类型
	Local() string           // 本端模型Code
	Remote() string          // 对端模型Code
	LocalName() string       // 本端模型名称
	RemoteName() string      // 对端模型名称
	RelDesc() string         // 关系描述
	LocalModelName() string  // 所属的模型的名称
	RemoteModelName() string // 对端模型的名称
	RelName() string         // 关系名称
	LocalFieldCode() string  // 本端字段
}
