package v1

import (
	"fmt"
	"regexp"
	"strconv"

	"ks-knoc-server/internal/common/errno"
)

// ArrayAttribute 数组类型的专有属性
type ArrayAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (a *ArrayAttribute) GetTypeName() ModelAttrType {
	return ArrayType
}

func (a *ArrayAttribute) CheckRule() error {
	if a.Attrs.ModelAttrRule.RuleRe == "" {
		return errno.ErrParameterRequired.Add("正则表达式不能为空")
	}
	return nil
}

func (a *ArrayAttribute) CheckIsDisplay() bool {
	return a.BaseAttribute.IsDisplay
}

func (a *ArrayAttribute) Validate() error {
	if err := a.BaseAttribute.Validate(); err != nil {
		return err
	}
	// 说明没传递或者设置为了null
	if (a.Attrs.ModelAttrRule == ModelAttrRule{}) {
		return nil
	}
	regexPattern := a.Attrs.ModelAttrRule.RuleRe
	if regexPattern != "" {
		// 当正则表达式规则不为空的时候，那么UserHint就不可以为空
		if a.Attrs.UserHint == "" {
			return errno.ErrParameterInvalid.Add("当rule_re不为空时，用户提示不能为空")
		}
		// 当正则表达式规则不为空的时候，那么Sample就不可以为空
		if a.Attrs.Sample == "" {
			return errno.ErrParameterInvalid.Add("当rule_re不为空时，示例不能为空")
		}

		pattern := ""
		escapedPattern, err := strconv.Unquote(`"` + regexPattern + `"`)

		if err != nil {
			pattern = regexPattern
		} else {
			pattern = escapedPattern
		}

		re := regexp.MustCompile(pattern)
		if !re.MatchString(a.Attrs.Sample) {
			return errno.ErrParameterInvalid.Add(fmt.Sprintf("示例%s不符合正则表达式规则%s", a.Attrs.Sample, regexPattern))
		}
	}
	return nil
}

func NewArrayAttribute() *ArrayAttribute {
	return &ArrayAttribute{}
}
