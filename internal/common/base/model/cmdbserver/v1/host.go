package v1

import (
	"errors"
	"fmt"

	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"
)

// OnRackRequest 上架的请求参数
type OnRackRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
	RackID   string `json:"rack_id" binding:"required"`
	StartU   int    `json:"start_u" binding:"CheckStartU"`
	Height   int    `json:"height"`
}

// OffRackRequest 下架的请求参数
type OffRackRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
}

type Dev ModelData

func ToDev(m ModelData) (Dev, error) {
	if m.ModelCode == "" {
		return Dev{}, fmt.Errorf("model code is empty")
	}
	// return Dev{ModelData: m, DeviceType: DeviceTypeStandard}, nil
	return Dev(m), nil
}

// StartU 设备的起始U位
func (d Dev) StartU() int {
	modelCode := d.ModelCode

	startU, exist := d.Data[fmt.Sprintf("%s_start_u", modelCode)]
	if !exist {
		return 0
	}

	return utils.ToInt(startU)
}

// Height 设备的高度
func (d Dev) Height() int {
	modelCode := d.ModelCode

	height, exist := d.Data[fmt.Sprintf("%s_height", modelCode)]
	if !exist {
		return 0
	}

	return utils.ToInt(height)
}

func (d Dev) DataID() string {
	return d.ID
}

func (d Dev) DeviceType() DT {
	return DeviceTypeStandard
}

// EndU 设备的结束U位，左闭右开区间
func (d Dev) EndU() int {
	// 说明设备没有配置起始U位，或者设备没有配置高度
	if d.StartU() == 0 || d.Height() == 0 {
		return 0
	}
	return d.StartU() + d.Height() - 1
}

// Range 设备的U位范围，闭区间，针对合法的U位，第二个参数返回true。否则返回false
func (d Dev) Range() ([2]int, bool) {
	if d.StartU() == 0 || d.EndU() == 0 {
		return [2]int{0, 0}, false
	}
	return [2]int{d.StartU(), d.EndU()}, true
}

// Standard 设备是否是标准设备，标准的设备占用单独的U位，非标准的设备，单独的U位可能会放多台设备，默认所有设备都是标准设备，即返回true
func (d Dev) Standard() bool {
	standardFieldName := fmt.Sprintf("%s_standard", d.ModelCode)
	standard, exist := d.Data[standardFieldName]
	if exist {
		if standard == "true" || standard == nil || standard == "" {
			return true
		}
		return false
	}
	return true
}

// UID 返回设备UID的状态，当然只有物理服务器一般才会有UID状态
// TODO: 目前还没有编写UID的逻辑，所以这块只要是服务器类型的设备就返回UIDOff，其他的都返回UIDNil
func (d Dev) UID() UID {
	if d.ModelCode == "server" {
		return UIDOff
	} else {
		return UIDNil
	}
}

func (d Dev) IP() string {
	fieldName := fmt.Sprintf("%s_internal_ip", d.ModelCode)
	ip, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(ip)
	}
	return ""
}

func (d Dev) SN() string {
	fieldName := fmt.Sprintf("%s_sn", d.ModelCode)
	sn, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(sn)
	}
	return ""
}

func (d Dev) Name() string {
	fieldName := fmt.Sprintf("%s_name", d.ModelCode)
	name, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(name)
	}
	return ""
}

// Brand 品牌
func (d Dev) Brand() string {
	fieldName := fmt.Sprintf("%s_brand", d.ModelCode)
	brand, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(brand)
	}
	return ""
}

// Model 型号
func (d Dev) Model() string {
	fieldName := fmt.Sprintf("%s_model", d.ModelCode)
	model, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(model)
	}
	return ""
}

func (d Dev) Icon() string {
	fieldName := fmt.Sprintf("%s_icon", d.ModelCode)
	icon, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(icon)
	}
	return ""
}

func (d Dev) Owner() string {
	fieldName := fmt.Sprintf("%s_owner", d.ModelCode)
	owner, exist := d.Data[fieldName]
	if exist {
		return utils.ToString(owner)
	}
	return ""
}

func (d Dev) GetProperties(modelCode string) mapdata.MapData {
	mp := make(mapdata.MapData)
	switch modelCode {
	case "server":
		// UID灯的逻辑，放到后面再说吧
		mp.Set("uid", false)
	}
	return mp
}

type UID string

func (u UID) String() string {
	return string(u)
}

const (
	UIDOn  UID = "on"
	UIDOff UID = "off"
	UIDNil UID = ""
)

type PlaceHolderType int

const (
	ODF        PlaceHolderType = iota + 1 // ODF
	Tray                                  // 托盘
	PatchPanel                            // 配线架
)

var PlaceHolderMapping map[PlaceHolderType]string = map[PlaceHolderType]string{
	ODF:        "ODF",
	Tray:       "托盘",
	PatchPanel: "配线架",
}

type PlaceHolder struct {
	Start        int             `json:"start_u" bson:"start_u"`
	DeviceHeight int             `json:"height" bson:"height"`
	Name         string          `json:"name" bson:"name"`
	Comment      string          `json:"comment" bson:"comment"`
	PType        PlaceHolderType `json:"p_type" bson:"p_type"`
	DT           `json:"device_type" bson:"device_type"`
}

func (p PlaceHolder) DataID() string {
	return ""
}

func (p PlaceHolder) DeviceType() DT {
	return DeviceTypeDCInfra
}

func (p PlaceHolder) StartU() int {
	return p.Start
}

func (p PlaceHolder) Height() int {
	return p.DeviceHeight
}

func (p PlaceHolder) EndU() int {
	return p.Start + p.DeviceHeight - 1
}

func (p PlaceHolder) Range() ([2]int, bool) {
	if p.Start == 0 || p.EndU() == 0 {
		return [2]int{0, 0}, false
	}
	return [2]int{p.Start, p.EndU()}, true
}

func NewPlaceHolder(start, height int) (*PlaceHolder, error) {
	if start == 0 || height == 0 {
		return nil, errors.New("起始U位和高度不能为0")
	}
	return &PlaceHolder{Start: start, DeviceHeight: height, DT: DeviceTypeDCInfra}, nil
}
