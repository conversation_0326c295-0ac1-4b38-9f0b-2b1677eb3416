package v1

import (
	"strings"

	"ks-knoc-server/pkg/mapdata"
)

type SelectOption struct {
	ID   string `json:"id" bson:"id" mapstructure:"id,required"`
	Name string `json:"name" bson:"name" mapstructure:"name,required"`
}

// Equal 判断两个SelectOption是否相等，只要ID和Name都相等，就认为是相等的
func (s SelectOption) Equal(t SelectOption) bool {
	if s.ID == t.ID && s.Name == t.Name {
		return true
	}
	return false
}

type SelectOptions []SelectOption

func NewSelectOptions() SelectOptions {
	return make([]SelectOption, 0)
}

// Range 遍历SelectOptions, 对每一个SelectOption执行f
func (ss SelectOptions) Range(f func(so SelectOption)) {
	for _, s := range ss {
		f(s)
	}
}

func NewSelectOption(optID, optName string) *SelectOption {
	return &SelectOption{
		ID:   optID,
		Name: optName,
	}
}

// SelectDataTree 选择类型的树形数据结构
type SelectDataTree struct {
	Children []SelectOption `json:"children" bson:"children" mapstructure:"children"` // 子级数据
	ID       string         `json:"id" bson:"id" mapstructure:"id"`                   // 父级数据的ID
	Name     string         `json:"name" bson:"name" mapstructure:"name"`             // 父级数据的内容
}

func NewSelectDataTree() *SelectDataTree {
	return &SelectDataTree{Children: make([]SelectOption, 0)}
}

// SelectChainData 枚举继承数据的结构
type SelectChainData struct {
	DataTree       []SelectDataTree  `json:"data_tree" bson:"data_tree" mapstructure:"data_tree"`
	ParentCode     string            `json:"parent_code" bson:"parent_code" mapstructure:"parent_code"`
	reverseMapping map[string]string // key是option的id，value是parent option的name
}

// SelectAttribute 选择类型的专有属性
type SelectAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string          `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Inherit       bool            `json:"inherit" bson:"inherit" mapstructure:"inherit"` // 是否继承
		Depth         int             `json:"depth" bson:"depth" mapstructure:"depth"`       // 继承的深度
		Options       SelectOptions   `json:"opts" bson:"opts" mapstructure:"opts,required"`
		ChainData     SelectChainData `json:"chain_data" bson:"chain_data" mapstructure:"chain_data,omitempty"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (s *SelectAttribute) GetParentOptionValue(optID string) string {
	if s.Attrs.ChainData.reverseMapping == nil {
		s.Attrs.ChainData.reverseMapping = make(map[string]string)
		for _, tree := range s.Attrs.ChainData.DataTree {
			for _, subOpt := range tree.Children {
				s.Attrs.ChainData.reverseMapping[subOpt.ID] = tree.Name
			}
		}
	}

	if parentName, ok := s.Attrs.ChainData.reverseMapping[optID]; ok {
		return parentName
	}

	return ""
}

func (s *SelectAttribute) GetTypeName() ModelAttrType {
	return SelectType
}

// OptionValueConflict 判断枚举字段的值是否有重复的
func (s *SelectAttribute) OptionValueConflict() (bool, string) {
	var (
		conflict bool
		values   = make(map[string]int)
	)

	for _, opt := range s.Attrs.Options {
		optName := strings.TrimSpace(opt.Name)
		values[optName]++
	}

	var conflictOpts []string
	for k, v := range values {
		if v > 1 {
			conflict = true
			conflictOpts = append(conflictOpts, k)
		}
	}

	return conflict, strings.Join(conflictOpts, ",")
}

// Compare 接收一个SelectChainData类型的参数，用来比较两个SelectChainData的差异
// 返回target相较于s，删除的options，修改的options，新增的options
func (s *SelectAttribute) Compare(target SelectAttribute) (deleteOpts, editOpts, addOpts SelectOptions) {

	var (
		oldMapSet = make(mapdata.MapData) // 旧的枚举字段的options
		newMapSet = make(mapdata.MapData) // 新的枚举字段的options
	)

	for _, o := range s.Attrs.Options {
		oldMapSet[o.ID] = o
	}

	for _, n := range target.Attrs.Options {
		newMapSet[n.ID] = n
	}

	// old - new，得到删除的options
	deleteIDs := oldMapSet.Difference(newMapSet)
	for _, id := range deleteIDs {
		deleteOpts = append(deleteOpts, oldMapSet[id].(SelectOption))
	}

	// new - old，得到新增的options
	addIDs := newMapSet.Difference(oldMapSet)
	for _, id := range addIDs {
		addOpts = append(addOpts, newMapSet[id].(SelectOption))
	}

	// 取交集，看看交集里面有哪些option被修改了
	intersection := oldMapSet.Intersection(newMapSet)
	for _, id := range intersection {
		newOpt := newMapSet[id].(SelectOption)
		oldOpt := oldMapSet[id].(SelectOption)

		if !newOpt.Equal(oldOpt) {
			editOpts = append(editOpts, newOpt)
		}
	}

	return
}

func (s *SelectAttribute) Validate() error {
	if err := s.BaseAttribute.Validate(); err != nil {
		return err
	}
	return nil
}

func NewSelectAttribute() *SelectAttribute {
	return &SelectAttribute{}
}
