package v1

import (
	"regexp"

	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/mapdata"

	"github.com/qiniu/qmgo/field"
	"go.uber.org/zap"
)

const (
	LabelKeyColName   = "label_key"
	LabelValueColName = "label_value"
)

var (
	// LabelNameRE 允许大小写字母，下划线，数字，且不能以数字开头
	LabelNameRE = regexp.MustCompile("^[a-zA-Z_][a-zA-Z0-9_]*$")
	// LabelValueRE 中英文，数字，中横线，下划线，长度限定不超过64个字符
	LabelValueRE = regexp.MustCompile("^[a-zA-Z0-9\u4e00-\u9fa5\\-_]{1,64}$")
)

func CheckLabelValue(value string) bool {
	return LabelValueRE.MatchString(value)
}

type LabelKey struct {
	ID       string `bson:"_id" json:"id"`
	Name     string `json:"name" bson:"name" mapstructure:"name"`
	Describe string `json:"describe" bson:"describe" mapstructure:"describe"`
	CreateAt int64  `bson:"create_at" json:"create_at" mapstructure:"create_at"`
	UpdateAt int64  `bson:"update_at" json:"update_at" mapstructure:"update_at"`
}

func (lk *LabelKey) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

type LabelValue struct {
	ID       string `bson:"_id" json:"id"`
	KeyID    string `json:"key_id" bson:"key_id" mapstructure:"key_id"`
	Value    string `json:"value" bson:"value" mapstructure:"value"`
	Describe string `json:"describe" bson:"describe" mapstructure:"describe"`
	CreateAt int64  `bson:"create_at" json:"create_at" mapstructure:"create_at"`
	UpdateAt int64  `bson:"update_at" json:"update_at" mapstructure:"update_at"`
}

func (lv *LabelValue) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

type CreateLabelRequest struct {
	LabelKey    string   `json:"label_key" binding:"required"`
	LabelValues []string `json:"label_values" binding:"required,min=1"`
	Describe    string   `json:"describe"`
}

func (r *CreateLabelRequest) Validate() error {
	if !LabelNameRE.MatchString(r.LabelKey) {
		return errno.ErrParameterInvalid.Add(r.LabelKey + " 不符合命名规范")
	}

	for _, v := range r.LabelValues {
		// 首先查一下标签值是否合法，如果不合法，则直接跳过
		if !CheckLabelValue(v) {
			zap.L().Error("对应的label标签不合法", zap.String("label_value", v))
			return errno.ErrParameterInvalid.Add("label值 " + v + " 不符合标签值的规则")
		}
	}

	return nil
}

type LabelKeyValuePair struct {
	LabelKeyID  string `json:"key_id"`
	LabelKey    string `json:"key"`
	LabelValues []struct {
		KeyID      string `json:"key_id"`
		LabelValue string `json:"label_value"`
		ValueID    string `json:"value_id"`
	} `json:"value_list"`
	Describe string `json:"describe"`
}

type GetLabelsResponse struct {
	Total       int64               `json:"total"`
	Pages       int64               `json:"pages"`
	PageSize    int64               `json:"page_size"`
	CurrentPage int64               `json:"current_page"`
	Data        []LabelKeyValuePair `json:"labels"`
}

type UpdateLabelKeyRequest struct {
	Name     string `json:"name" binding:"required"`
	Describe string `json:"describe"`
}

type UpdateLabelValueRequest struct {
	Name string `json:"name" binding:"required"`
}

// LabelBindingRequest 标签绑定请求, 将标签绑定到数据上
type LabelBindingRequest struct {
	DataList  []string `json:"data_list" binding:"required"`
	LabelList []string `json:"label_list" binding:"required"`
}

type LabelUnBindingRequest struct {
	DataID   string   `json:"data_id"`
	LabelIDs []string `json:"label_id"`
}

type SearchLabeledDataRequest struct {
	ModelCode []string `json:"model_code"`
	LabelID   []string `json:"label_id" binding:"required"`
}

type DataWithLabelInfo struct {
	ID                 string              `json:"id" mapstructure:"id" bson:"_id"`
	ModelCode          string              `json:"model_code" binding:"required" mapstructure:"model_code" bson:"model_code"`
	Data               mapdata.MapData     `json:"data" binding:"required" mapstructure:"data" bson:"data"`
	IdentifyName       string              `json:"identify_name" binding:"required" mapstructure:"identify_name" bson:"identify_name"`
	IdentifyValue      string              `json:"identify_value" binding:"required" mapstructure:"identify_value" bson:"identify_value"`
	InPutType          InputType           `json:"input_type" mapstructure:"input_type" bson:"input_type"`
	CreateAt           int64               `json:"create_at" mapstructure:"create_at" bson:"create_at"`
	UpdateAt           int64               `json:"update_at" mapstructure:"update_at" bson:"update_at"`
	ParentID           string              `json:"parent_id" mapstructure:"parent_id" bson:"parent_id"`
	ParentDesc         string              `json:"parent_desc" mapstructure:"parent_desc" bson:"parent_desc"`
	AssociateInstances []AssociateInstance `json:"associate_instances" mapstructure:"associate_instances" bson:"associate_instances"`
	Active             bool                `json:"active" mapstructure:"active" bson:"active"`
	Labels             []struct {
		KeyID     string `json:"key_id" bson:"key_id"`
		KeyName   string `json:"key_name" bson:"key_name"`
		ValueID   string `json:"value_id" bson:"value_id"`
		ValueName string `json:"value_name" bson:"value_name"`
	} `json:"labels" bson:"labels"`
}

type SearchAggregatedDataWithLabelResponse struct {
	Total    int64               `json:"total" bson:"total"`
	DataList []DataWithLabelInfo `json:"data_list" bson:"data"`
}

type SearchLabeledModelInfo struct {
	ModelCode string `json:"model_code"`
	ModelName string `json:"model_name"`
	DataCount int    `json:"data_count"`
}

type SearchLabeledDataResponse struct {
	Total            int64                    `json:"total"`
	Pages            int64                    `json:"pages"`
	PageSize         int64                    `json:"page_size"`
	CurrentPage      int64                    `json:"current_page"`
	Data             []DataWithLabelInfo      `json:"data_list"`          // 数据列表
	SearchModelCode  string                   `json:"search_model_code"`  // 当前在查询的模型code
	MatchedModelCode []SearchLabeledModelInfo `json:"matched_model_code"` // 匹配到的模型列表
}
