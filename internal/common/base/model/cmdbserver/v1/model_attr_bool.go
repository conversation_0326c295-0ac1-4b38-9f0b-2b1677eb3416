package v1

// BoolAttribute 布尔类型的专有属性
type BoolAttribute struct {
	BaseAttribute `bson:",inline" mapstructure:",squash"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (b *BoolAttribute) CheckSampleValidate() error { return nil }

func (b *BoolAttribute) GetTypeName() ModelAttrType {
	return BoolType
}

func NewBoolAttribute() *BoolAttribute {
	return &BoolAttribute{}
}
