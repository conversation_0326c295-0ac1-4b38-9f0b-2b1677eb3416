package v1

import (
	"fmt"
	"regexp"

	"ks-knoc-server/internal/common/errno"
)

// IntAttribute 整型类型的专有属性
type IntAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Unit          string `json:"unit" bson:"unit" mapstructure:"unit"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func NewIntAttribute() *IntAttribute {
	return &IntAttribute{}
}

func (i *IntAttribute) GetTypeName() ModelAttrType {
	return IntType
}

// Validate 检查样例是否符合正则表达式规则 TODO: 代码重复，需要优化
func (i *IntAttribute) Validate() error {
	if err := i.BaseAttribute.Validate(); err != nil {
		return err
	}
	// 说明没传递或者设置为了null
	if (i.Attrs.ModelAttrRule == ModelAttrRule{}) {
		return nil
	}
	regexPattern := i.Attrs.ModelAttrRule.RuleRe
	if regexPattern != "" {
		// 当正则表达式规则不为空的时候，那么UserHint就不可以为空
		if i.Attrs.UserHint == "" {
			return errno.ErrParameterRequired.Add("当rule_re不为空时，用户提示不能为空")
		}
		// 当正则表达式规则不为空的时候，那么Sample就不可以为空
		if i.Attrs.Sample == "" {
			return errno.ErrParameterRequired.Add("当rule_re不为空时，示例不能为空")
		}
		re := regexp.MustCompile(regexPattern)
		if re.MatchString(i.Attrs.Sample) {
			return nil
		} else {
			return errno.InternalServerError.Add(fmt.Sprintf("示例不符合正则表达式规则：%s", regexPattern))
		}
	}
	return nil
}

// FloatAttribute 浮点型类型的专有属性
type FloatAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Unit          string `json:"unit" bson:"unit" mapstructure:"unit"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (f *FloatAttribute) GetTypeName() ModelAttrType {
	return FloatType
}

// Validate 检查样例是否符合正则表达式规则 TODO: 代码重复，需要优化
func (f *FloatAttribute) Validate() error {
	if err := f.BaseAttribute.Validate(); err != nil {
		return err
	}
	// 说明没传递或者设置为了null
	if (f.Attrs.ModelAttrRule == ModelAttrRule{}) {
		return nil
	}
	regexPattern := f.Attrs.ModelAttrRule.RuleRe
	if regexPattern != "" {
		// 当正则表达式规则不为空的时候，那么UserHint就不可以为空
		if f.Attrs.UserHint == "" {
			return errno.ErrParameterRequired.Add("当rule_re不为空时，用户提示不能为空")
		}
		// 当正则表达式规则不为空的时候，那么Sample就不可以为空
		if f.Attrs.Sample == "" {
			return errno.ErrParameterRequired.Add("当rule_re不为空时，示例能为空")
		}
		re := regexp.MustCompile(regexPattern)
		if re.MatchString(f.Attrs.Sample) {
			return nil
		} else {
			return errno.ErrParameterInvalid.Add(fmt.Sprintf("示例不符合正则表达式规则：%s", regexPattern))
		}
	}
	return nil
}

func NewFloatAttribute() *FloatAttribute {
	return &FloatAttribute{}
}
