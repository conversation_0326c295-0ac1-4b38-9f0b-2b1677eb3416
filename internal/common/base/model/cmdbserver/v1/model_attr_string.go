package v1

import (
	"fmt"
	"regexp"
	"strconv"

	"ks-knoc-server/internal/common/errno"
)

// StringAttribute 字符串类型的专有属性
type StringAttribute struct {
	// 要带上bson:",inline"，否则ID字段无法被自动的初始化
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func NewStringAttribute() *StringAttribute {
	return &StringAttribute{}
}

func (s *StringAttribute) GetTypeName() ModelAttrType {
	return StringType
}

func (s *StringAttribute) CheckRule() error {
	if s.Attrs.ModelAttrRule.RuleRe == "" {
		return errno.ErrParameterRequired.Add("正则表达式不能为空")
	}
	return nil
}

func (s *StringAttribute) Validate() error {
	if err := s.BaseAttribute.Validate(); err != nil {
		return err
	}
	// 说明没传递或者设置为了null
	if (s.Attrs.ModelAttrRule == ModelAttrRule{}) {
		return nil
	}
	regexPattern := s.Attrs.ModelAttrRule.RuleRe
	if regexPattern != "" {
		// 当正则表达式规则不为空的时候，那么UserHint就不可以为空
		if s.Attrs.UserHint == "" {
			return errno.ErrParameterInvalid.Add("当rule_re不为空时，用户提示不能为空")
		}
		// 当正则表达式规则不为空的时候，那么Sample就不可以为空
		if s.Attrs.Sample == "" {
			return errno.ErrParameterInvalid.Add("当rule_re不为空时，示例不能为空")
		}

		pattern := ""
		escapedPattern, err := strconv.Unquote(`"` + regexPattern + `"`)

		if err != nil {
			pattern = regexPattern
		} else {
			pattern = escapedPattern
		}

		re := regexp.MustCompile(pattern)
		if !re.MatchString(s.Attrs.Sample) {
			return errno.ErrParameterInvalid.Add(fmt.Sprintf("示例%s不符合正则表达式规则%s", s.Attrs.Sample, regexPattern))
		}
	}
	return nil
}

// TextareaAttribute 多行文本类型的专有属性
type TextareaAttribute struct {
	BaseAttribute `bson:",inline" mapstructure:",squash"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func NewTextAreaAttribute() *TextareaAttribute {
	return &TextareaAttribute{}
}

func (t *TextareaAttribute) Name() string {
	return t.BaseAttribute.Name
}

func (t *TextareaAttribute) Code() string {
	return t.BaseAttribute.Code
}

func (t *TextareaAttribute) GetTypeName() ModelAttrType {
	return TextAreaType
}

func (t *TextareaAttribute) Validate() error {
	if err := t.BaseAttribute.Validate(); err != nil {
		return errno.InternalServerError.Add(err.Error())
	}
	if (t.Attrs.ModelAttrRule == ModelAttrRule{}) {
		return nil
	}
	regexPattern := t.Attrs.ModelAttrRule.RuleRe
	if regexPattern != "" {
		// 当正则表达式规则不为空的时候，那么UserHint就不可以为空
		if t.Attrs.UserHint == "" {
			return errno.InternalServerError.Add("当rule_re不为空时，用户提示不能为空")
		}
		// 当正则表达式规则不为空的时候，那么Sample就不可以为空
		if t.Attrs.Sample == "" {
			return errno.InternalServerError.Add("当rule_re不为空时，示例不能为空")
		}
		var pattern string
		escapedPattern, err := strconv.Unquote(`"` + regexPattern + `"`)
		if err != nil {
			pattern = regexPattern
		} else {
			pattern = escapedPattern
		}
		re := regexp.MustCompile(pattern)
		if re.MatchString(t.Attrs.Sample) {
			return nil
		} else {
			return errno.ErrParameterInvalid.Add(fmt.Sprintf("示例不符合正则表达式规则：%s", regexPattern))
		}
	}
	return nil
}
