package v1

import (
	"fmt"

	"ks-knoc-server/pkg/mapdata"

	"github.com/mitchellh/mapstructure"
)

// RelInstance 关系实例
type RelInstance struct {
	ModelData `json:",.inline"`
	InUse     bool `json:"in_use"`
}

// ModelDataResponse 模型数据的响应
type ModelDataResponse struct {
	Total       int64  `json:"total"`
	Pages       int64  `json:"pages"`
	PageSize    int64  `json:"page_size"`
	CurrentPage int64  `json:"current_page"`
	Data        []Data `json:"data"`
}

func (m *ModelDataResponse) BuildResponse(total, pageSize, currentPage int64, data []Data) {
	pages := (total / pageSize) + 1
	m.Total = total
	m.Pages = pages
	m.PageSize = pageSize
	m.CurrentPage = currentPage
	m.Data = data
}

type ModelDataResponseWithLabelInfo struct {
	Total       int64               `json:"total"`
	Pages       int64               `json:"pages"`
	PageSize    int64               `json:"page_size"`
	CurrentPage int64               `json:"current_page"`
	Data        []DataWithLabelInfo `json:"data"`
}

func (m *ModelDataResponseWithLabelInfo) BuildResponse(total, pageSize, currentPage int64, data []DataWithLabelInfo) {
	pages := (total / pageSize) + 1
	m.Total = total
	m.Pages = pages
	m.PageSize = pageSize
	m.CurrentPage = currentPage
	m.Data = data
}

func NewModelDataResponse() *ModelDataResponse {
	return &ModelDataResponse{
		Data: make([]Data, 0),
	}
}

// ModelDataWithRelInfo 携带关系信息的模型数据
type ModelDataWithRelInfo struct {
	ModelData `json:",.inline"`
	RelName   string `json:"rel_name"`
	RelDesc   string `json:"rel_desc"`
	RelID     string `json:"rel_id"`
	IsRack    bool   `json:"is_rack"`
	StartU    int    `json:"start_u"`
	RackID    string `json:"rack_id"`
	DelID     string `json:"del_id"` // 这个id的作用是用来删除关系使用的
}

type ModelDataResponseWithRelInfo struct {
	Total       int64                  `json:"total"`
	Pages       int64                  `json:"pages"`
	PageSize    int64                  `json:"page_size"`
	CurrentPage int64                  `json:"current_page"`
	AttrList    []CommonModelAttribute `json:"attr_list"`
	Data        []ModelDataWithRelInfo `json:"data"`
}

func NewModelDataResponseWithRelInfo() *ModelDataResponseWithRelInfo {
	return &ModelDataResponseWithRelInfo{
		Data: make([]ModelDataWithRelInfo, 0),
	}
}

type ModelDataResponseWithRelInfoV2 struct {
	Total       int64                  `json:"total"`
	Pages       int64                  `json:"pages"`
	PageSize    int64                  `json:"page_size"`
	CurrentPage int64                  `json:"current_page"`
	AttrList    []CommonModelAttribute `json:"attr_list"`
	Data        []struct {
		ModelData `json:",.inline"`
		RelName   string `json:"rel_name"`
		RelDesc   string `json:"rel_desc"`
		RelType   int    `json:"rel_type"`
		IsParent  bool   `json:"is_parent"`
		// 这个id的作用是用来删除关系使用的
		DelID string `json:"del_id"`
	} `json:"data"`
}

// ModelDataResponseWithOutPagination 不带分页的模型数据的响应
type ModelDataResponseWithOutPagination struct {
	Total int64       `json:"total"`
	Data  []ModelData `json:"data"`
}

type ModelRelationDataResponse struct {
	ModelAttrs struct {
		Code     string `json:"code"`
		Name     string `json:"name"`
		Sort     int32  `json:"sort"`
		TypeName string `json:"type_name"`
	} `json:"model_attrs"`
	Data ModelDataResponse `json:"data"`
}

type ModelDataCount struct {
	Code  string `bson:"_id"`
	Count int64  `bson:"count"`
}

// SearchData 搜索的数据，该结构体主要用于构造数据然后存储到ES中
type SearchData struct {
	ID        string                 `mapstructure:"id" json:"id"`
	ModelCode string                 `mapstructure:"model_code" json:"model_code"`
	Data      map[string]interface{} `mapstructure:"data" json:"data"`
	InPutType InputType              `mapstructure:"input_type" json:"input_type"`
	CreateAt  int64                  `mapstructure:"create_at" json:"create_at"`
	UpdateAt  int64                  `mapstructure:"update_at" json:"update_at"`
	Labels    mapdata.MapData        `mapstructure:"labels" json:"labels"`
	Meta      mapdata.MapData        `mapstructure:"meta" json:"meta"`
	MetaData  mapdata.MapData        `json:"meta_data" mapstructure:"meta_data"` // MetaData 元数据
}

func (s *SearchData) InjectData(data *ModelData) error {
	if err := mapstructure.Decode(data, s); err != nil {
		return err
	}

	d := s.Data
	dataCode := fmt.Sprintf("%s_code", data.ModelCode)
	// 这里无需判断key是否存在，删除一个不存在的key也不会引发panic
	delete(d, dataCode)

	// 删除掉以后再重新赋值给是s.Data
	s.Data = d

	return nil
}

func NewSearchData() *SearchData {
	return &SearchData{
		Labels: make(mapdata.MapData),
		Meta:   make(mapdata.MapData),
	}
}
