// Package v1 关联关系模型
package v1

import (
	"ks-knoc-server/internal/common/errno"

	"github.com/qiniu/qmgo/field"
)

type RelMode int

const (
	RelModeUnknown    RelMode = 0
	RelModeOneToOne   RelMode = 1
	RelModeOneToMany  RelMode = 2
	RelModeManyToMany RelMode = 3
)

func ToRelMode(mode int) RelMode {
	switch mode {
	case 1:
		return RelModeOneToOne
	case 2:
		return RelModeOneToMany
	case 3:
		return RelModeManyToMany
	default:
		return RelModeUnknown
	}
}

// ModelAssociateRelationRequest 关联关系类型的专有属性请求
type ModelAssociateRelationRequest struct {
	// 关系名称，这个名称目前两端会生成一样的
	Name string `bson:"name" json:"name" binding:"required,max=512" mapstructure:"name"`
	// RelModel1Code 关联关系字段1的模型Code
	RelModel1Code string `json:"rel_model1_code" bson:"rel_model1_code" mapstructure:"rel_model1_code" binding:"required"`
	// RelModel2Code 关联关系字段2的模型Code
	RelModel2Code string `json:"rel_model2_code" bson:"rel_model2_code" mapstructure:"rel_model2_code" binding:"required"`
	// RelDesc 关联关系的描述
	RelDesc string `json:"rel_desc" bson:"rel_desc" mapstructure:"rel_desc" binding:"required"`
	// Builtin 是否是内置的关系，内置的关系不允许删除
	BuiltIn bool `json:"builtin" bson:"builtin" mapstructure:"builtin"`
	// RelMode 关联关系模式，1表示一对一，2表示一对多，3表示多对多
	RelMode int `json:"rel_mode" bson:"rel_mode" mapstructure:"rel_mode"`
}

func (r *ModelAssociateRelationRequest) Validate() error {
	relMode := ToRelMode(r.RelMode)
	if relMode == RelModeUnknown {
		return errno.ErrParameterInvalid.Add("关联关系模式不合法")
	}
	return nil
}

// AssociateRelations 关联关系，这个是要保存在model_relation这个collection中的
type AssociateRelations struct {
	ID string `json:"id" bson:"_id" mapstructure:"id"`
	// RelType 关联关系类型的类型，关联关系中，拓展用，目前RelType目前固定值为2，
	RelType       int    `json:"rel_type" bson:"rel_type" mapstructure:"rel_type"`
	RelField1Code string `json:"rel_field1_code" bson:"rel_field1_code" mapstructure:"rel_field1_code"`
	RelField2Code string `json:"rel_field2_code" bson:"rel_field2_code" mapstructure:"rel_field2_code"`
	RelModel1Code string `json:"rel_model1_code" bson:"rel_model1_code" mapstructure:"rel_model1_code"`
	RelModel2Code string `json:"rel_model2_code" bson:"rel_model2_code" mapstructure:"rel_model2_code"`
	RelModel1Name string `json:"rel_model1_name" bson:"rel_model1_name" mapstructure:"rel_model1_name"`
	RelModel2Name string `json:"rel_model2_name" bson:"rel_model2_name" mapstructure:"rel_model2_name"`
	Builtin       bool   `json:"builtin" bson:"builtin" mapstructure:"builtin"`
	RelMode       int    `json:"rel_mode" bson:"rel_mode" mapstructure:"rel_mode"`
}

// IsOneEnd 判断模型是否是单端关系
func (a *AssociateRelations) IsOneEnd(modelCode string) bool {
	relMode := ToRelMode(a.RelMode)

	if relMode == RelModeOneToOne {
		return true
	} else if relMode == RelModeOneToMany {
		return modelCode == a.RelModel1Code
	} 
	return false
}

// IsManyEnd 判断模型是否是多端关系
func (a *AssociateRelations) IsManyEnd(modelCode string) bool {
	relMode := ToRelMode(a.RelMode)
	if relMode == RelModeOneToOne {
		return false
	} else if relMode == RelModeOneToMany {
		return modelCode == a.RelModel2Code
	}
	return true
}

func (a *AssociateRelations) GetSelf(modelCode string) (code, name, fieldCode string) {
	if modelCode == a.RelModel1Code {
		return a.RelModel1Code, a.RelModel1Name, a.RelField1Code
	}
	return a.RelModel2Code, a.RelModel2Name, a.RelField2Code
}

func (a *AssociateRelations) GetOpposite(modelCode string) (code, name, fieldCode string) {
	if modelCode == a.RelModel1Code {
		return a.RelModel2Code, a.RelModel2Name, a.RelField2Code
	}
	return a.RelModel1Code, a.RelModel1Name, a.RelField1Code
}

func (a *AssociateRelations) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

func NewAssociateRelations() *AssociateRelations {
	return &AssociateRelations{RelType: 2}
}

// AssociationRelationshipAttribute 关联关系类型的专有属性，作为字段存储在mongodb数据库中
type AssociationRelationshipAttribute struct {
	BaseAttribute `bson:",inline" mapstructure:",squash"`
	Attrs         struct {
		// RelType 关联关系类型的类型，关联关系中，RelType固定为2，1表示主从关系
		RelType int `json:"rel_type" bson:"rel_type" mapstructure:"rel_type"`
		// RelFieldCode 关联关系字段的Code，注意字段是对端的
		RelFieldCode string `json:"rel_field_code" bson:"rel_field_code" mapstructure:"rel_field_code"`
		// RelModelCode 关联关系字段的模型Code，注意模型是对端的，即在A和B的关联关系中，A的RelModelCode是B的Code
		RelModelCode string `json:"rel_model_code" bson:"rel_model_code" mapstructure:"rel_model_code"`
		// Describe 关联关系字段的描述
		RelDesc string `json:"rel_desc" bson:"rel_desc" mapstructure:"rel_desc" binding:"required,max=512"`
		// RelModelName 关联关系字段的模型名称，注意模型是对端的
		RelModelName string `json:"rel_model_name" bson:"rel_model_name" mapstructure:"rel_model_name"`
		// RelLocalModelCode 关联关系字段的本地模型Code
		RelLocalModelCode string `json:"rel_local_model_code" bson:"rel_local_model_code" mapstructure:"rel_local_model_code"`
		// RelLocalModelName 关联关系字段的本地模型名称
		RelLocalModelName string `json:"rel_local_model_name" bson:"rel_local_model_name" mapstructure:"rel_local_model_name"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (a *AssociationRelationshipAttribute) Init() {
	// 初始化一些值
	a.IsUnique = false
	a.IsEditAble = true
	a.IsNull = true
	a.SystemCreated = false
	a.IsDisplay = true
	a.SetGeneralDefaultVal()
}

func (a *AssociationRelationshipAttribute) RelType() RelationType {
	switch RelationType(a.Attrs.RelType) {
	case Subordinate:
		return Subordinate
	case Associate:
		return Associate
	default:
		return Unknown
	}
}

func (a *AssociationRelationshipAttribute) LocalModelName() string {
	return a.Attrs.RelLocalModelName
}

func (a *AssociationRelationshipAttribute) RemoteModelName() string {
	return a.Attrs.RelModelName
}

func (a *AssociationRelationshipAttribute) GetID() string {
	return a.ID
}

func (a *AssociationRelationshipAttribute) RemoteName() string {
	return a.Attrs.RelModelName
}

func (a *AssociationRelationshipAttribute) LocalName() string {
	return a.Attrs.RelLocalModelName
}

func (a *AssociationRelationshipAttribute) Remote() string {
	return a.Attrs.RelModelCode
}

func (a *AssociationRelationshipAttribute) Local() string {
	return a.Attrs.RelLocalModelCode
}

func (a *AssociationRelationshipAttribute) RelDesc() string {
	return a.Attrs.RelDesc
}

func (a *AssociationRelationshipAttribute) Self() interface{} {
	return a
}

func (a *AssociationRelationshipAttribute) RelName() string {
	return a.Name
}

func (a *AssociationRelationshipAttribute) LocalFieldCode() string {
	return a.Code
}

func NewAssociationRelationshipAttribute() *AssociationRelationshipAttribute {
	return &AssociationRelationshipAttribute{}
}

// AssociationRelation 关联关系类型字段源目字段模板
type AssociationRelation struct {
	// Name 关联关系字段的名称
	Name string `json:"name" bson:"name" mapstructure:"name"`
	// Code 关联关系字段的Code
	Code string `json:"code" bson:"code" mapstructure:"code"`
	// Describe 关系字段描述
	Describe string `json:"describe" bson:"describe" mapstructure:"describe"`
}

// AssociateInstance 关联关系的实例
type AssociateInstance struct {
	// RelationID 关联的关系的id，对应model_relation这个collection
	RelationID string `json:"rel_id" bson:"rel_id"`
	// InstanceID target实例对象的ID
	InstanceID string `json:"instance_id" bson:"instance_id"`
	// RelationDesc 本条关联关系的描述
	RelationDesc string `json:"relation_desc" bson:"relation_desc"`
}
