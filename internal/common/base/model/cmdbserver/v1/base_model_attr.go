package v1

import (
	"github.com/qiniu/qmgo/field"
)

// BaseAttribute 属性的基本信息，每一个字段都应该几倍一下的类型
type BaseAttribute struct {
	GeneralModelObj `bson:",inline" mapstructure:",squash"`
	// IsNull 属性是否可以为空
	IsNull bool `json:"is_null" bson:"is_null" mapstructure:"is_null"`
	// IsUnique 属性是否唯一
	IsUnique bool `json:"is_unique" bson:"is_unique" mapstructure:"is_unique"`
	// IsEditable 属性是否可以编辑
	IsEditAble bool `json:"is_editable" bson:"is_editable" mapstructure:"is_editable"`
	// IsDisplay 属性是否可以展示
	IsDisplay bool `json:"is_display" bson:"is_display" mapstructure:"is_display"`
	// ModelCode 属性所属的模型
	ModelCode string `json:"model_code" bson:"model_code" mapstructure:"model_code" binding:"required" validate:"required"`
	// AttrGroup 属性所属的分组
	AttrGroup string `json:"attr_group" bson:"attr_group" mapstructure:"attr_group" binding:"required" validate:"required"`
	// SystemCreated 属性是否是系统创建的
	SystemCreated bool `json:"system_created" bson:"system_created" mapstructure:"system_created"`
	// Describe 属性的描述
	Describe string `json:"describe" bson:"describe" mapstructure:"describe"`
	// TypeName 属性的类型，属性的类型可以是
	// string, int, float, date, select, relationship, password, attachment,textarea
	TypeName string `json:"type_name" bson:"type_name"  mapstructure:"type_name" binding:"required"`
	// Universal 是否是通用类型字段
	Universal bool `json:"universal" bson:"universal" mapstructure:"universal"`
}

func (b *BaseAttribute) GetModelCode() string {
	return b.ModelCode
}

func (b *BaseAttribute) GetName() string {
	return b.Name
}

func (b *BaseAttribute) GetCode() string {
	return b.Code
}

func (b *BaseAttribute) GetAttrGroupCode() string {
	return b.AttrGroup
}

func (b *BaseAttribute) CheckIsNull() bool {
	return b.IsNull
}

func (b *BaseAttribute) CheckIsUnique() bool {
	return b.IsUnique
}

func (b *BaseAttribute) CheckIsEditAble() bool {
	return b.IsEditAble
}

func (b *BaseAttribute) CheckIsDisplay() bool {
	return b.IsDisplay
}

func (b *BaseAttribute) CheckIsSystemCreated() bool {
	return b.SystemCreated
}

func (b *BaseAttribute) CheckIsBuiltIn() bool {
	return b.Builtin
}

func (b *BaseAttribute) SetDefault(attr map[string]any) {
	b.GeneralModelObj.SetGeneralDefaultVal()
	// 设置isNull的默认值，默认我们允许字段可以为空
	isNull, ok := attr["is_null"]
	// 如果说用户压根没传这个字段，那么就默认设置为true，否则设置为用户想要设置的值
	if !ok {
		b.IsNull = true
	} else {
		// 这里说明用户故意传递了null值，我们默认的处理策略就是传递null值和不传一样，所以保持原样
		if isNull != nil {
			b.IsNull = isNull.(bool)
		}
	}

	// 我们默认也是允许用户进行编辑操作的。
	isEditAble, ok := attr["is_editable"]
	if !ok {
		b.IsEditAble = true
	} else {
		if isEditAble != nil {
			b.IsEditAble = isEditAble.(bool)
		}
	}
	universal, ok := attr["universal"]
	if ok {
		b.Universal = universal.(bool)
	} else {
		b.Universal = false
	}

	// 默认display也是可以展示的, 目前平台还未支持设置字段是否可隐藏，因此默认都是可以展示的
	b.IsDisplay = true
	// IsDisplay, ok := attr["is_display"]
	// if !ok {
	// 	b.IsDisplay = true
	// } else {
	// 	if IsDisplay != nil {
	// 		b.IsDisplay = IsDisplay.(bool)
	// 	}
	// }
}

func (b *BaseAttribute) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

// IsRelation 判断当前的属性是否是一个关系属性
func (b *BaseAttribute) IsRelation() bool {
	return b.TypeName == "relationship"
}

var ModelAttributeTypeMapping = map[string]string{
	"string":   "字符串",
	"int":      "整数",
	"float":    "浮点数",
	"date":     "日期",
	"select":   "枚举",
	"bool":     "布尔",
	"json":     "JSON",
	"array":    "数组",
	"textarea": "文本域",
	"time":     "时间",
}
