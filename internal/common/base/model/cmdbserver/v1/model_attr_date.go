package v1

// DateAttribute 日期类型的专有属性
type DateAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (d *DateAttribute) GetTypeName() ModelAttrType {
	return DateType
}

func (d *DateAttribute) CheckSampleValidate() error { return nil }

func NewDateAttribute() *DateAttribute {
	return &DateAttribute{}
}

// DateTimeAttribute 日期时间类型的专有属性
type DateTimeAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

func (d *DateTimeAttribute) CheckSampleValidate() error { return nil }

func (d *DateTimeAttribute) GetTypeName() ModelAttrType {
	return DateTimeType
}

func NewDateTimeAttribute() *DateTimeAttribute {
	return &DateTimeAttribute{}
}
