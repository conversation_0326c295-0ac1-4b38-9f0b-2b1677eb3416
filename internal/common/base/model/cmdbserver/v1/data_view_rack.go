package v1

import (
	"ks-knoc-server/pkg/mapdata"
)

const (
	RackDeviceTypeOccupy = "occupy" // 正常占用
	RackDeviceEmpty      = "empty"  // 空闲
)

type RackViewResponse struct {
	Height    int       `json:"height"`
	RackUnits RackUints `json:"rack_units"`
}

func NewRackViewResponse() *RackViewResponse {
	return &RackViewResponse{RackUnits: make([]RackUnit, 0)}
}

type RackUnit struct {
	StartU       int             `json:"start_u"`
	EndU         int             `json:"end_u"`
	Height       int             `json:"height"`
	Usage        string          `json:"usage"`       // 占用情况，两种情况，占用，不占用
	DeviceType   DT              `json:"device_type"` // 放在机柜上的设备类型，因为不一定是设备，有可能是配线架，ODF
	ModelInfo    ModelInfo       `json:"model_info"`
	Device       DeviceInfo      `json:"device"`
	DeviceOrigin any             `json:"device_origin"`
	Properties   mapdata.MapData `json:"properties"`
}

func NewRackUnit() *RackUnit {
	return &RackUnit{DeviceOrigin: make(mapdata.MapData), Properties: make(mapdata.MapData)}
}

type RackUints []RackUnit

func (r RackUints) Len() int {
	return len(r)
}

func (r RackUints) Less(i, j int) bool {
	return r[i].StartU < r[j].StartU
}

func (r RackUints) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

type DeviceInfo struct {
	DeviceIP   string `json:"device_ip"`
	DeviceSn   string `json:"device_sn"`
	DeviceIcon string `json:"device_icon"`
	Name       string `json:"name"`
	Comment    string `json:"comment"`
}

type ModelInfo struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

type RackInstance struct {
	DeviceID  string `json:"device_id"`
	ModelName string `json:"model_name"`
	Name      string `json:"name"`
	SN        string `json:"sn"`
	StartU    int    `json:"start_u"`
	UID       UID    `json:"uid"`
	IP        string `json:"ip"`
	Height    int    `json:"height"`
	CreateAt  int64  `json:"create_at"`
	UpdateAt  int64  `json:"update_at"`
}

type RackViewInstances []RackInstance

func (r RackViewInstances) Len() int {
	return len(r)
}

func (r RackViewInstances) Less(i, j int) bool {
	return r[i].CreateAt < r[j].CreateAt
}

func (r RackViewInstances) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

type RackViewInstanceResponse struct {
	Total       int64             `json:"total"`
	Pages       int64             `json:"pages"`
	PageSize    int64             `json:"page_size"`
	CurrentPage int64             `json:"current_page"`
	Items       RackViewInstances `json:"items"`
}

func NewRackViewInstanceResponse() *RackViewInstanceResponse {
	return &RackViewInstanceResponse{Items: make([]RackInstance, 0)}
}
