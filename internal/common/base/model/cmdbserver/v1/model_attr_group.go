package v1

import (
	"fmt"
	"time"

	"ks-knoc-server/pkg/clock"

	pinyin "github.com/mozillazg/go-pinyin"
	"github.com/qiniu/qmgo/field"
)

type ModelAttrGroup struct {
	ID            string `bson:"_id" json:"id" mapstructure:"_id"`                                // ID 唯一标识
	Code          string `bson:"code" json:"code" mapstructure:"code"`                            // Code 唯一标识
	CreateAt      int64  `bson:"create_at,omitempty" json:"create_at" mapstructure:"create_at"`   // 创建时间
	UpdateAt      int64  `bson:"update_at,omitempty" json:"update_at" mapstructure:"update_at"`   // 更新时间
	Name          string `bson:"name" json:"name" binding:"required,max=512" mapstructure:"name"` // 名称
	Builtin       bool   `bson:"builtin" json:"builtin" mapstructure:"builtin"`                   // 是否是内置
	SystemCreated bool   `json:"system_created" bson:"system_created"`                            // 是否是系统创建的
	ModelCode     string `json:"model_code" bson:"model_code" binding:"required"`                 // 模型code
	Relation      bool   `json:"relation" bson:"relation"`                                        // 是否是关联属性组
}

// SetTime 设置时间，首次创建的时候需要设置，同时更新创建时间和更新时间
func (m *ModelAttrGroup) SetTime() *ModelAttrGroup {
	m.CreateAt = clock.NowInNano()
	m.UpdateAt = clock.NowInNano()
	return m
}

// SetUpdateAt 设置更新时间
func (m *ModelAttrGroup) SetUpdateAt() *ModelAttrGroup {
	m.UpdateAt = clock.NowInNano()
	return m
}

// SetGeneralDefaultVal 设置通用的默认值
func (m *ModelAttrGroup) SetGeneralDefaultVal() *ModelAttrGroup {
	m.SetTime()
	return m
}

// IsBuiltIn 是否是内置的，内置的不允许删除
func (m *ModelAttrGroup) IsBuiltIn() bool {
	return m.Builtin
}

// IsSystemCreated 是否是系统创建的
func (m *ModelAttrGroup) IsSystemCreated() bool {
	return m.SystemCreated
}

// AutoSetCode 自动生成code，格式为：模型code_拼音_时间戳
func (m *ModelAttrGroup) AutoSetCode(name string) string {
	pyStr := ""
	pyObj := pinyin.NewArgs()
	// 默认会丢弃非中文的字符，因此这里可以设置一个FallBack函数，将非中文的字符转换为字符串并返回，而不是忽略
	pyObj.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)}
	}
	for _, p := range pinyin.Pinyin(name, pyObj) {
		pyStr += p[0]
	}
	return fmt.Sprintf("%s_%s_%d", m.ModelCode, pyStr, time.Now().Unix())
}

func (m *ModelAttrGroup) CustomFields() field.CustomFieldsBuilder {
	return field.NewCustom().SetId("ID")
}

// GenerateDefaultModelAttrGroup 生成默认的模型属性分组
func GenerateDefaultModelAttrGroup(modelCode string) (
	basicModelAttrGroup, relationModelAttrGroup, systemModelAttrGroup *ModelAttrGroup) {
	// 创建基本分组
	basicModelAttrGroup = &ModelAttrGroup{
		Code:          fmt.Sprintf("%s_basic", modelCode),
		Name:          "基础分组",
		Builtin:       true,
		SystemCreated: true,
		ModelCode:     modelCode,
	}
	basicModelAttrGroup.SetGeneralDefaultVal()

	// 创建关系分组
	relationModelAttrGroup = &ModelAttrGroup{
		Code:          fmt.Sprintf("%s_relation", modelCode),
		Name:          "关系分组",
		Builtin:       true,
		SystemCreated: true,
		ModelCode:     modelCode,
		Relation:      true,
	}
	relationModelAttrGroup.SetGeneralDefaultVal()

	// 创建系统分组
	systemModelAttrGroup = &ModelAttrGroup{
		Code:          fmt.Sprintf("%s_system", modelCode),
		Name:          "系统分组",
		Builtin:       true,
		SystemCreated: true,
		ModelCode:     modelCode,
	}
	systemModelAttrGroup.SetGeneralDefaultVal()

	return
}
