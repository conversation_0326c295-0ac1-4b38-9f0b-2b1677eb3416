package v1

// DataRelRequest 数据从属关系创建的请求，单源多目标
type DataRelRequest struct {
	// SourceInstanceID 源实例ID
	SourceInstanceID string `json:"source_instance_id" mapstructure:"source_instance_id" binding:"required"`
	// TargetInstanceID 目标实例ID
	TargetInstanceID []string `json:"target_instance_id" mapstructure:"target_instance_id" binding:"required,min=1"`
	// RelDesc 关系描述
	RelDesc string `json:"rel_desc" mapstructure:"rel_desc" binding:"max=1000"`
	// RelID 关系字段的ID
	RelID string `json:"rel_id" mapstructure:"rel_id" binding:"required"`
}

// DataNTo1Request 数据从属关系创建的请求，多源单目标
type DataNTo1Request struct {
	// SourceInstanceID 源实例ID
	SourceInstanceID []string `json:"source_instance_id" mapstructure:"source_instance_id" binding:"required,min=1"`
	// TargetInstanceID 目标实例ID
	TargetInstanceID string `json:"target_instance_id" mapstructure:"target_instance_id" binding:"required"`
	// RelDesc 关系描述
	RelDesc string `json:"rel_desc" mapstructure:"rel_desc" binding:"max=1000"`
	// RelID 关系字段的ID
	RelID string `json:"rel_id" mapstructure:"rel_id" binding:"required"`
}

type DataMToNRequest struct {
	// SourceInstanceID 源实例ID
	SourceInstanceID []string `json:"source_instance_id" mapstructure:"source_instance_id" binding:"required,min=1"`
	// TargetInstanceID 目标实例ID
	TargetInstanceID []string `json:"target_instance_id" mapstructure:"target_instance_id" binding:"required,min=1"`
	// RelDesc 关系描述
	RelDesc string `json:"rel_desc" mapstructure:"rel_desc" binding:"max=1000"`
	// RelID 关系字段的ID
	RelID string `json:"rel_id" mapstructure:"rel_id" binding:"required"`
}

type Data1To1Request struct {
	// SourceInstanceID 源实例ID
	SourceInstanceID string `json:"source_instance_id" mapstructure:"source_instance_id" binding:"required"`
	// TargetInstanceID 目标实例ID
	TargetInstanceID string `json:"target_instance_id" mapstructure:"target_instance_id" binding:"required"`
	// RelDesc 关系描述
	RelDesc string `json:"rel_desc" mapstructure:"rel_desc" binding:"max=1000"`
	// RelID 关系字段的ID
	RelID string `json:"rel_id" mapstructure:"rel_id" binding:"required"`
}

type DataMoveReason struct {
	InstanceName string   `json:"instance_name"`
	FailedReason []string `json:"failed_reason"`
}

// ReasonString 返回字符串形式的错误信息，每一个错误信息以;分隔
func (d *DataMoveReason) ReasonString() (errString string) {
	for _, reason := range d.FailedReason {
		errString += reason + ";"
	}
	return
}

func NewDataMoveReason() *DataMoveReason {
	return &DataMoveReason{
		FailedReason: make([]string, 0),
	}
}

type DataMoveToResponse struct {
	Success int              `json:"success"`
	Failed  int              `json:"failed"`
	Result  []DataMoveReason `json:"result"`
}

func NewDataMoveToResponse() *DataMoveToResponse {
	return &DataMoveToResponse{
		Result: make([]DataMoveReason, 0),
	}
}

type AvailableRelInstances struct {
	RelAttrID string   `json:"rel_attr_id" binding:"required"`
	DataIDs   []string `json:"data_ids" binding:"required,min=1"`
	Field     string   `json:"field"`
	Keyword   string   `json:"keyword"`
}
