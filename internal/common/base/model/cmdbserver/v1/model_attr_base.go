package v1

import (
	"errors"
	"fmt"
	"reflect"

	"ks-knoc-server/internal/common/validate"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	"github.com/mitchellh/mapstructure"
)

// GenerateDefaultModelAttrs 生成默认的模型属性
// 主要用来生成基本属性中的唯一标识和名称
// TODO: 这样实现很麻烦，待优化
func GenerateDefaultModelAttrs(modelName string) (code, name *StringAttribute) {
	// 生成属性分组
	attrGroup := fmt.Sprintf("%s_basic", modelName)
	// 生成唯一标识
	code = &StringAttribute{
		BaseAttribute: BaseAttribute{
			GeneralModelObj: GeneralModelObj{
				Code:    fmt.Sprintf("%s_code", modelName),
				Name:    "唯一标识",
				Builtin: true,
			},
			IsNull:        false,
			IsUnique:      true,
			IsEditAble:    true,
			SystemCreated: true,
			IsDisplay:     true,
			ModelCode:     modelName,
			AttrGroup:     attrGroup,
			Describe:      "唯一标识",
			TypeName:      "string",
		},
		Attrs: struct {
			ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
			UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
			Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
		}{
			ModelAttrRule: ModelAttrRule{},
			UserHint:      "请输入唯一标识",
		},
	}
	code.SetGeneralDefaultVal()
	// 生成名称
	name = &StringAttribute{
		BaseAttribute: BaseAttribute{
			GeneralModelObj: GeneralModelObj{
				Code:    fmt.Sprintf("%s_name", modelName),
				Name:    "名称",
				Builtin: true,
			},
			IsNull:        false,
			IsUnique:      true,
			IsEditAble:    true,
			IsDisplay:     true,
			SystemCreated: true,
			ModelCode:     modelName,
			AttrGroup:     attrGroup,
			Describe:      "名称",
			TypeName:      "string",
		},
		Attrs: struct {
			ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
			UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
			Sample        string `json:"sample" bson:"sample" mapstructure:"sample"`
		}{
			ModelAttrRule: ModelAttrRule{},
			UserHint:      "请输入名称",
		},
	}
	name.SetGeneralDefaultVal()
	return
}

// UpdateAttrTmpl 更新属性的模板
type UpdateAttrTmpl struct {
	Field string
	Value interface{}
}

// ModelRules 模型规则
type ModelRules struct {
	ID       int    `json:"id" gorm:"id" mapstructure:"id"`
	RuleName string `json:"rule_name" gorm:"rule_name" mapstructure:"rule_name"`
	RuleRe   string `json:"rule_re" gorm:"rule_re" mapstructure:"rule_re"`
	RuleType string `json:"rule_type" gorm:"rule_type" mapstructure:"rule_type"`
}

// CheckAttrValidate 检查属性的合法性
func CheckAttrValidate(attr any) (err error) {
	attrValue := reflect.ValueOf(attr)
	ruleReValue := attrValue.FieldByName("Attrs").FieldByName("ModelAttrRule").FieldByName("RuleRe")
	attrType := attrValue.FieldByName("BaseAttribute").FieldByName("TypeName")

	var ruleRe string
	if ruleReValue.IsValid() {
		ruleRe = ruleReValue.String()
	}

	var attrTypeString string
	if attrType.IsValid() {
		attrTypeString = attrType.String()
	}

	// 动态的设置rule_re的值
	mav := validate.NewModelAttrValidate()
	mav.SetRuleRe(ruleRe)
	mav.SetTypeString(attrTypeString)
	v, err = validate.GetValidateOptions()
	if err != nil {
		return err
	}
	if err = binding.Validator.ValidateStruct(attr); err != nil {
		var errs validator.ValidationErrors
		if errors.As(err, &errs) {
			return v.TranslateValidationErrors(errs)
		}
		return
	}
	return
}

// ModelAttrValidate 根据类型进行属性的验证
func ModelAttrValidate(attrTypeName string, attr map[string]interface{}) (err error) {
	switch attrTypeName {
	case "string":
		m := StringAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = CheckAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	case "int":
		m := IntAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = CheckAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	case "float":
		m := FloatAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = CheckAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	case "date":
		m := DateAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = CheckAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	case "datetime":
		m := DateAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = CheckAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	case "select":
		m := SelectAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = CheckAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	case "json":
		// json类型的数据不需要做校验
		m := NewJsonAttribute()
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		m.SetTime()
		return
	default:
		return nil
	}
}

const ModelAttrESTmpl = `{
			"properties": {
				"data": {
					"properties": {
						"%s": {
							"type": "text",
							"fields": {
								"keyword": {
									"type": "keyword",
									"ignore_above": 512
								}
							}
						}
					}
				}
			}
		}`
