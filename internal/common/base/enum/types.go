// Package enum **/
/**
* @Author: chenhao29
* @Date: 2023/9/19 14:44
* @QQ: 1149558764
* @Email: i@umb.<NAME_EMAIL>
 */
package enum

const (
	Int      = "int"
	Float    = "float"
	Date     = "date"
	Bool     = "bool"
	Enum     = "enum"
	Time     = "datetime"
	Select   = "select"
	String   = "string"
	TextArea = "textarea"
)

var i18NTypeEnum = map[string]string{
	Int:      "整数",
	Float:    "浮点型",
	Select:   "枚举",
	Bool:     "布尔",
	Date:     "日期",
	Time:     "时间",
	String:   "字符串",
	TextArea: "文本域",
}

func GetI18NTypeName(typeName string) string {
	return i18NTypeEnum[typeName]
}
