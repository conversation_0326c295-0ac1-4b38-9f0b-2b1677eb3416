package dnsserver

type DnsServer struct {
	Hostname string `json:"hostname" binding:"required"`
	Role     string `json:"role" binding:"required"`
	IP       string `json:"ip" binding:"required"`
	Port     int    `json:"port"`
	OfficeId int32  `json:"office_id" binding:"required"`
}

// NSResponse 查询DNS服务器信息
type NSResponse struct {
	OfficeName string `json:"office_name"`
	CreateTime string `json:"create_time"`
	UpdateTime string `json:"update_time"`
}
