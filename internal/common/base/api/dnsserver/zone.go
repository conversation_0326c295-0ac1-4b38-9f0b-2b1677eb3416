package dnsserver

import (
	"errors"
	"regexp"
	"strings"
)

var (
	zoneNameRegex = regexp.MustCompile(`^[A-Za-z0-9]+([-.]?[A-Za-z0-9]+)*$`)
)

// type zoneType string

// func (z zoneType) String() string {
// 	return string(z)
// }

// const (
// 	ZoneTypeMaster         zoneType = "master"
// 	ZoneTypePrimary        zoneType = "primary"
// 	ZoneTypeSlave          zoneType = "slave"
// 	ZoneTypeSecondary      zoneType = "secondary"
// 	ZoneTypeStub           zoneType = "stub"
// 	ZoneTypeMirror         zoneType = "mirror"
// 	ZoneTypeHint           zoneType = "hint"
// 	ZoneTypeForward        zoneType = "forward"
// 	ZoneTypeStaticStub     zoneType = "static-stub"
// 	ZoneTypeRedirect       zoneType = "redirect"
// 	ZoneTypeDelegationOnly zoneType = "delegation-only"
// )

// var zoneTypes = []zoneType{
// 	ZoneTypeMaster,
// 	ZoneTypePrimary,
// 	ZoneTypeSlave,
// 	ZoneTypeSecondary,
// 	ZoneTypeStub,
// 	ZoneTypeMirror,
// 	ZoneTypeHint,
// 	ZoneTypeForward,
// 	ZoneTypeStaticStub,
// 	ZoneTypeRedirect,
// 	ZoneTypeDelegationOnly,
// }

// type DnsZone struct {
// 	Name       string   `json:"name" binding:"required"`
// 	ZoneType   zoneType `json:"zone_type" binding:"required"`
// 	OfficeId   int32    `json:"office_id" binding:"required"`
// 	Forwarders string   `json:"forwarders"`
// 	TSIGKey    string   `json:"tsig_key"`
// 	TSIGSecret string   `json:"tsig_secret"`
// 	Comment    string   `json:"comment"`
// 	Creator    string   `json:"creator" binding:"required"`
// }

// func (d *DnsZone) CheckZoneType() bool {
// 	return array.InArray(d.ZoneType, zoneTypes)
// }

// func CheckForwarders(fds string) (string, error) {
// 	// 如果设置了forwarder的话就需要校验
// 	forwarders := make([]string, 0)
// 	for _, forwarder := range strings.Split(fds, ",") {
// 		if !regex_pattern.CheckIP(forwarder) {
// 			return "", errors.New("forwarder IP地址不合法")
// 		}

// 		forwarders = append(forwarders, forwarder)
// 	}
// 	return strings.Join(forwarders, ","), nil
// }

type CreateZoneParams struct {
	Name        string `json:"name"`
	ZoneType    string `json:"zone_type"`
	Description string `json:"description"`
}

func (c *CreateZoneParams) Validate() error {
	trimmedName := strings.TrimSpace(c.Name)

	if trimmedName == "" {
		return errors.New("区域名称不能为空")
	}

	if len(trimmedName) > 255 {
		return errors.New("区域名称不能超过255个字符")
	}

	if !zoneNameRegex.MatchString(trimmedName) {
		return errors.New("区域名称只能包含字母、数字、下划线和连字符")
	}

	if c.ZoneType == "" {
		return errors.New("区域类型不能为空")
	}

	return nil
}

// type DeleteZoneRequest struct {
// 	Name     string `json:"name" binding:"required"`
// 	OfficeID int32  `json:"office_id" binding:"required"`
// }

// type UpdateZoneRequest struct {
// 	Name       string `json:"name" binding:"required"`
// 	OfficeId   int32  `json:"office_id" binding:"required"`
// 	Forwarders string `json:"forwarders" binding:"required"`
// 	Comment    string `json:"comment"`
// }
