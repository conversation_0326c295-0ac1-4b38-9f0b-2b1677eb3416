package dnsserver

import (
	"errors"
)

// TaskListRequest 查询任务列表请求
type TaskListRequest struct {
	Status   string `json:"status"`    // 任务状态
	Operator string `json:"operator"`  // 操作人
	Page     int    `json:"page"`      // 页码
	PageSize int    `json:"page_size"` // 每页大小
}

func (t *TaskListRequest) Validate() error {
	if t.Page <= 0 {
		t.Page = 1
	}

	if t.PageSize <= 0 {
		t.PageSize = 10
	}

	return nil
}

// RetryTaskDetailRequest 重试TaskDetail请求
type RetryTaskDetailRequest struct {
	DetailID int64 `json:"detail_id"` // TaskDetail ID
}

func (r *RetryTaskDetailRequest) Validate() error {
	if r.DetailID <= 0 {
		return errors.New("detail_id不能为空或小于等于0")
	}

	return nil
}
