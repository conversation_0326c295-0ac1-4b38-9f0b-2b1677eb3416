package dnsserver

import (
	"errors"
	"ks-knoc-server/pkg/utils"
)

// CreateServerRequest 创建服务器请求
type CreateServerRequest struct {
	ClusterID int64  `json:"cluster_id"` // 集群ID
	ViewID    int64  `json:"view_id"`    // 线路版本ID
	Name      string `json:"name"`       // 服务器主机名
	Role      string `json:"role"`       // 服务器角色
	Status    string `json:"status"`     // 服务器状态
	IP        string `json:"ip"`         // 服务器IP
	Port      int    `json:"port"`       // 服务器端口
	Version   string `json:"version"`    // 软件版本
	DbPath    string `json:"db_path"`    // 数据库路径
}

func (c *CreateServerRequest) Validate() error {
	if c.ClusterID <= 0 {
		return errors.New("cluster_id is required")
	}

	if c.ViewID <= 0 {
		return errors.New("view_id is required")
	}

	if c.Name == "" {
		return errors.New("name is required")
	}

	if c.Role == "" {
		return errors.New("role is required")
	}

	if !utils.IsValidIPv4(c.IP) {
		return errors.New("ip must be a valid IPv4 address")
	}

	if c.Port != 53 {
		if c.Port <= 1024 || c.Port > 65535 {
			return errors.New("当port非默认53时，必须在1024-65535之间")
		}
	}

	if c.Version == "" {
		return errors.New("version is required")
	}

	if c.DbPath == "" {
		return errors.New("db_path is required")
	}

	return nil
}

// UpdateServerRequest 更新服务器请求
type UpdateServerRequest struct {
	ID        int64  `json:"id" binding:"required"`         // 服务器ID
	ClusterID int64  `json:"cluster_id" binding:"required"` // 集群ID
	Name      string `json:"name"`                          // 服务器主机名
	Role      string `json:"role"`                          // 服务器角色
	IP        string `json:"ip"`                            // 服务器IP
	Port      int    `json:"port"`                          // 服务器端口
	Version   string `json:"version"`                       // 软件版本
	DbPath    string `json:"db_path"`                       // 数据库路径
	ViewID    int64  `json:"view_id"`                       // 线路版本ID
}

// QueryServerRequest 查询服务器请求
type QueryServerRequest struct {
	ID        int64  `json:"id"`         // 服务器ID
	ClusterID int64  `json:"cluster_id"` // 集群ID
	ViewID    int64  `json:"view_id"`    // 线路版本ID
	Name      string `json:"name"`       // 服务器主机名
	IP        string `json:"ip"`         // 服务器IP
	Role      string `json:"role"`       // 服务器角色
	Status    string `json:"status"`     // 服务器状态
	Page      int    `json:"page"`       // 页码
	PageSize  int    `json:"page_size"`  // 每页大小
	OrderBy   string `json:"order_by"`   // 排序字段
	Order     string `json:"order"`      // 排序方式：asc/desc
}

// ServerResponse 服务器响应
type ServerResponse struct {
	ID            int64  `json:"id"`              // 服务器ID
	ClusterID     int64  `json:"cluster_id"`      // 集群ID
	Name          string `json:"name"`            // 服务器主机名
	Role          string `json:"role"`            // 服务器角色
	Status        string `json:"status"`          // 服务器状态
	IP            string `json:"ip"`              // 服务器IP
	Port          int    `json:"port"`            // 服务器端口
	Version       string `json:"version"`         // 软件版本
	DbPath        string `json:"db_path"`         // 数据库路径
	ViewID        int64  `json:"view_id"`         // 线路版本ID
	Checked       bool   `json:"checked"`         // 是否检查
	LastAliveTime int64  `json:"last_alive_time"` // 最后活跃时间
	CreateTime    int64  `json:"create_time"`     // 创建时间
	UpdateTime    int64  `json:"update_time"`     // 更新时间
}

// ServerListResponse 服务器列表响应
type ServerListResponse struct {
	Total   int64            `json:"total"`   // 总数
	Servers []ServerResponse `json:"servers"` // 服务器列表
}
