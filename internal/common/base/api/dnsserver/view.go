package dnsserver

import (
	"errors"
	"strings"

	"ks-knoc-server/internal/common/bind/tsig"
	"ks-knoc-server/pkg/utils"

	"go.uber.org/zap"
)

type UpdateViewParams struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`

	ACLs          []string `json:"acls"`
	SecretControl bool     `json:"secret_control"`
	ViewKeyAlgo   string   `json:"view_key_algo"`
}

func (c *UpdateViewParams) Validate() error {
	if c.ID <= 0 {
		return errors.New("视图ID不能为空")
	}

	if len(c.Name) > 255 {
		return errors.New("视图名称不能超过255个字符")
	}

	if c.SecretControl {
		if c.ViewKeyAlgo == "" {
			return errors.New("SecretControl为true时，View key算法不能为空")
		}

		viewKeyAlgo := tsig.ToTSIGKeyAlgo(c.ViewKeyAlgo)
		if !viewKeyAlgo.IsValid() {
			return errors.New("view key算法不合法")
		}
	}

	return nil
}

// CreateViewParams 创建视图的参数
type CreateViewParams struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`

	ACLs          []string `json:"acls"`
	SecretControl bool     `json:"secret_control"`
	ViewKeyAlgo   string   `json:"view_key_algo"`

	ParentID  int64  `json:"parent_id"`  // 父视图ID
	LevelType string `json:"level_type"` // 视图级别类型

}

// Validate 验证创建视图的参数
func (c *CreateViewParams) Validate() error {
	// View视图需要挂在某个视图下
	if c.ParentID < 0 {
		return errors.New("不合法的父级视图ID")
	}

	if c.Code == "" {
		return errors.New("视图标识不能为空")
	}

	if c.Name == "" {
		return errors.New("视图名称不能为空")
	}

	if len(c.Name) > 255 {
		return errors.New("视图名称不能超过255个字符")
	}

	if c.LevelType == "" {
		return errors.New("视图级别类型不能为空")
	}

	// 构建一个acl的集合以及一个切片，目的是为了校验acl的合法性以及去重
	aclMap := make(map[string]struct{})
	newAcls := make([]string, 0)
	for _, acl := range c.ACLs {
		// acl如果是一个空的字符串，说明acl不合法
		if acl == "" {
			continue
		}
		// acl已经在aclMap中添加过
		if _, ok := aclMap[strings.TrimSpace(acl)]; ok {
			return errors.New("地址列表不能重复")
		}
		// 看一看这个acl是不是一个合法的子网
		if !utils.IsValidNetwork(acl) {
			return errors.New("地址列表中的" + acl + "不是一个合法的子网")
		}

		aclMap[strings.TrimSpace(acl)] = struct{}{}
		newAcls = append(newAcls, strings.TrimSpace(acl))
	}
	if len(newAcls) == 0 {
		return errors.New("地址列表不能为空")
	}
	c.ACLs = newAcls

	// 当前要求必须开启secret控制
	if !c.SecretControl {
		return errors.New("必须开启secret控制, 请检查secret_control字段为true")
	}

	// 如果view key算法为空，则使用默认的hmac-sha256
	if c.ViewKeyAlgo == "" {
		zap.L().Warn("ViewKeyAlgo is empty, use default hmac-sha256")
		c.ViewKeyAlgo = "hmac-sha256"
	}

	// 校验view key算法是否合法
	viewKeyAlgo := tsig.ToTSIGKeyAlgo(c.ViewKeyAlgo)
	if !viewKeyAlgo.IsValid() {
		return errors.New("view key算法不合法")
	}

	return nil
}

type ViewTree struct {
	ID        int64       `json:"id"`
	Code      string      `json:"code"`
	Name      string      `json:"name"`
	ParentID  int64       `json:"parent_id"`
	Level     int         `json:"level"`
	LevelType string      `json:"level_type"`
	Children  []*ViewTree `json:"children"`
}
