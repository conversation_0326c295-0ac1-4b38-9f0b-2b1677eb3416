package dnsserver

import (
	"errors"
	"regexp"
	"strings"

	"ks-knoc-server/internal/common/bind/tsig"

	"go.uber.org/zap"
)

const (
	clusterNameRegex = "^[a-zA-Z0-9_-]+$"
)

// UpdateClusterParam 更新集群的参数
type UpdateClusterParam struct {
	ID              int64    `json:"id"`                // 集群的ID
	Name            string   `json:"name"`              // 集群的名称
	Description     string   `json:"description"`       // 集群的描述
	Status          string   `json:"status"`            // 集群的状态
	Forwarders      []string `json:"forwarders"`        // 集群的转发器
	ForVPN          bool     `json:"for_vpn"`           // 集群是否是用于VPN的
	ForTest         bool     `json:"for_test"`          // 集群是否是用于测试的
	ForDrill        bool     `json:"for_drill"`         // 集群是否是用于演练的
	HasLoadBalance  bool     `json:"has_load_balance"`  // 集群是否具有负载均衡
	LoadBanlanceIPs []string `json:"load_banlance_ips"` // 集群的负载均衡IP
}

func (c *UpdateClusterParam) Validate() error {
	if c.ID <= 0 {
		return errors.New("集群ID不能为空")
	}
	return nil
}

type ClusterParam struct {
	Name            string   `json:"name"`              // 集群的名称
	Description     string   `json:"description"`       // 集群的描述
	Status          string   `json:"status"`            // 集群的状态
	Forwarders      []string `json:"forwarders"`        // 集群的转发器
	ForVPN          bool     `json:"for_vpn"`           // 集群是否是用于VPN的
	ForTest         bool     `json:"for_test"`          // 集群是否是用于测试的
	ForDrill        bool     `json:"for_drill"`         // 集群是否是用于演练的
	ClusterType     string   `json:"cluster_type"`      // 集群的类型
	HasLoadBalance  bool     `json:"has_load_balance"`  // 集群是否具有负载均衡
	LoadBanlanceIPs []string `json:"load_banlance_ips"` // 集群的负载均衡IP
	RndcKeyArgo     string   `json:"rndc_key_argo"`     // 集群的RNDC密钥算法
	SecretControl   bool     `json:"secret_control"`    // 集群是否具有密钥控制
}

func (c *ClusterParam) Validate() error {
	if strings.TrimSpace(c.Name) == "" {
		return errors.New("集群名称不能为空")
	}

	if matched, err := regexp.MatchString(clusterNameRegex, c.Name); !matched || err != nil {
		return errors.New("集群名称不合法, 仅支持数字、字母、下划线、中划线")
	}

	// 集群如果开启了密钥控制，则需要指定RNDC密钥算法
	if c.SecretControl {
		zap.L().Debug("集群开启了密钥控制, 需要指定RNDC密钥算法")

		if strings.TrimSpace(c.RndcKeyArgo) == "" {
			return errors.New("RNDC密钥算法不能为空")
		}

		tsigArgo := tsig.ToTSIGKeyAlgo(c.RndcKeyArgo)
		if !tsigArgo.IsValid() {
			return errors.New("RNDC密钥算法不合法")
		}
	}

	if len(c.Forwarders) == 0 {
		c.Forwarders = make([]string, 0)
	}

	// 如果集群具有负载均衡，则需要指定负载均衡IP
	if c.HasLoadBalance {
		if len(c.LoadBanlanceIPs) == 0 {
			return errors.New("负载均衡IP不能为空")
		}
	} else {
		c.LoadBanlanceIPs = make([]string, 0)
	}

	return nil
}
