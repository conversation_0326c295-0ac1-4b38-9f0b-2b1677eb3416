package dnsserver

// KeyData 存储单个key的信息
type KeyData struct {
	KeyName   string
	Algorithm string
	Secret    string
}

// NamedConfKeysData 存储named.conf.keys文件的所有数据
type NamedConfKeysData struct {
	GeneratedAt string
	RndcKey     *KeyData
	ViewKeys    []*KeyData
}

// SlaveServer 从服务器信息
type SlaveServer struct {
	IP   string
	Port int
}

// ResponsePolicy 响应策略配置
type ResponsePolicy struct {
	Zones []string
}

// ViewConfig 单个view的配置
type ViewConfig struct {
	ViewName       string
	KeyName        string
	ACLName        string // 引用的ACL名称，替代直接的地址列表
	AllowQuery     []string
	Recursion      string // "yes" 或 "no"
	ResponsePolicy *ResponsePolicy
	Forwarders     []string
}

// MasterServer 主服务器信息
type MasterServer struct {
	IP   string
	Port int
}

// NamedConfViewsData 存储named.conf.views文件的所有数据
type NamedConfViewsData struct {
	GeneratedAt   string
	ServerRole    string // "master" 或 "slave"
	SlaveServers  []*SlaveServer
	MasterServers []*MasterServer // 主服务器列表，用于slave配置中排除master IP
	Views         []*ViewConfig
	DefaultView   *ViewConfig // 默认view的特殊配置
}

// ZoneConfigData 单个zone的配置信息
type ZoneConfigData struct {
	Name string
	Role string // "master" 或 "slave"
}

// MasterServerData 主服务器信息
// ServerData 通用服务器数据结构，用于在DNS配置中描述服务器信息
type ServerData struct {
	IP          string
	IPv6IP      string
	IPv4Enabled bool
	IPv6Enabled bool
	ClusterID   int64 // 关联的cluster ID
}

// NamedViewZonesData 存储named.{view_name}.zones文件的所有数据
type NamedViewZonesData struct {
	GeneratedAt       string
	ViewCode          string
	ViewName          string
	ViewKeyName       string
	ServerRole        string // "master" 或 "slave"
	Zones             []*ZoneConfigData
	BlacklistZones    []*ZoneConfigData
	HasBlacklistZones bool
	MasterServers     []*ServerData
	SlaveServers      []*SlaveServer // 从服务器列表，用于also-notify配置
}

// ACLData 单个ACL的配置信息
type ACLData struct {
	ACLName          string   `json:"acl_name"`
	AddressMatchList []string `json:"address_match_list"`
	ViewNames        []string `json:"view_names"` // 关联的view名称，用于统计
}

// NamedConfACLsData 存储named.conf.acls文件的所有数据
type NamedConfACLsData struct {
	GeneratedAt string     `json:"generated_at"`
	ACLs        []*ACLData `json:"acls"`
	DefaultACL  *ACLData   `json:"default_acl"` // 默认ACL配置
}

// ConfigFile 表示单个配置文件
type ConfigFile struct {
	Name    string
	Content []byte
}

// ConfigZipResponse 配置文件ZIP压缩包响应
type ConfigZipResponse struct {
	FileName    string
	ContentType string
	Data        []byte
}

type ZoneConfig struct {
	TTL          int
	SOA          SOAConfig
	NSRecords    []string
	NSServers    map[string]string
	ARecords     map[string][]ARecord
	CNAMERecords []CNAMERecord
}

// SOAConfig SOA记录配置
type SOAConfig struct {
	PrimaryNS   string
	AdminEmail  string
	Serial      string
	Refresh     string
	Retry       string
	Expire      string
	NegativeTTL string
}

// ARecord A记录数据
type ARecord struct {
	Domain string
	IP     string
}

// CNAMERecord CNAME记录数据
type CNAMERecord struct {
	Domain string
	Target string
}
