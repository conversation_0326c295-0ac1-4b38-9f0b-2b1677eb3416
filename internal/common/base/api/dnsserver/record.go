package dnsserver

import "errors"

type CreateBlacklistDnsRecordRequest struct {
	Name        string `json:"name"`        // 解析记录名称
	RType       string `json:"r_type"`      // 解析记录类型
	TTL         int    `json:"ttl"`         // 解析记录TTL
	Description string `json:"description"` // 解析记录描述
	Value       string `json:"value"`       // 解析记录值

	ZoneID int64 `json:"zone_id"` // 区域ID
	ViewID int64 `json:"view_id"` // 视图ID

	Creator string `json:"creator"` // 创建人，一般是sre或者是业务的发起人
	Owner   string `json:"owner"`   // 负责人或实际的归属人
}

func (c *CreateBlacklistDnsRecordRequest) Validate() error {
	if c.Name == "" {
		return errors.New("解析主机名不能为空")
	}

	if c.RType == "" {
		return errors.New("解析记录类型不能为空")
	}

	if c.Value == "" {
		return errors.New("解析记录值不能为空")
	}

	if c.ZoneID == 0 {
		return errors.New("区域ID不能为空")
	}

	if c.ViewID == 0 {
		return errors.New("视图ID不合法")
	}

	if c.Creator == "" {
		return errors.New("创建人不能为空")
	}

	if c.Owner == "" {
		return errors.New("负责人不能为空")
	}

	if c.TTL < 0 {
		return errors.New("TTL不能小于0")
	}

	return nil
}

type UpdateDnsRecord struct {
	RecordID int64  `json:"record_id" binding:"required"`
	Creator  string `json:"creator"`
	Owner    string `json:"owner"`
	TTL      int32  `json:"ttl"`
	RType    string `json:"r_type"`
	Value    string `json:"value"`
}

type UpdateDnsRecordRequest struct {
	DryRun          *bool             `json:"dry_run"`
	ZoneID          int64             `json:"zone_id"`
	ViewID          int64             `json:"view_id"`
	Name            string            `json:"name"`
	Description     string            `json:"description"`
	Records         []UpdateDnsRecord `json:"records"`
	DeleteRecordIds []int64           `json:"delete_record_ids"`
}

func (u *UpdateDnsRecordRequest) Validate() error {
	if u.DryRun == nil {
		trueValue := true
		u.DryRun = &trueValue
	}

	// 如果长度为空的话，则直接return，不需要判断
	if len(u.Records) == 0 {
		return nil
	}

	if u.ZoneID <= 0 {
		return errors.New("区域ID不能为空")
	}

	if u.ViewID <= 0 {
		return errors.New("视图ID不能为空")
	}

	for _, v := range u.Records {
		if v.Owner == "" {
			return errors.New("负责人不能为空")
		}

		if v.Creator == "" {
			return errors.New("创建人不能为空")
		}

		if v.TTL <= 0 {
			return errors.New("TTL不能小于0")
		}

		if v.Value == "" {
			return errors.New("解析记录值不能为空")
		}

		if v.RType == "" {
			return errors.New("解析记录类型不能为空")
		}
	}

	return nil
}

func NewUpdateDnsRecordRequest() *UpdateDnsRecordRequest {
	return &UpdateDnsRecordRequest{
		Records: make([]UpdateDnsRecord, 0),
	}
}

type ImportDnsRecordRequest struct {
	ZoneName string `json:"zone_name" binding:"required"`
	OfficeID int32  `json:"office_id" binding:"required"`
	View     string `json:"view"`
	Content  string `json:"content" binding:"required"`
	Comment  string `json:"comment"`
	Creator  string `json:"creator" binding:"required"`
}

// QueryDnsRecordRequest 查询DNS记录请求
type QueryDnsRecordRequest struct {
	ZoneID   int64   `json:"zone_id"`
	ViewIDs  []int64 `json:"view_ids"`
	Name     string  `json:"name"`
	RType    string  `json:"r_type"`
	Value    string  `json:"value"`
	Creator  string  `json:"creator"`
	Owner    string  `json:"owner"`
	Page     int64   `json:"page"`
	PageSize int64   `json:"page_size"`

	// 是否分组
	Group   bool   `json:"group"`
	GroupBy string `json:"group_by"`

	// OrderBy 排序
	OrderBy string `json:"order_by"`
	Order   string `json:"order"`

	// SelectFields 查询字段
	SelectFields []string `json:"select_fields"`

	// 是否模糊匹配, 即调用Like查询
	Like bool `json:"like"`
}

func (q *QueryDnsRecordRequest) Validate() error {
	if q.ZoneID == 0 {
		return errors.New("区域ID不能为空")
	}

	if q.Page <= 0 {
		q.Page = 1
	}

	if q.PageSize <= 0 {
		q.PageSize = 10
	}

	return nil
}

type QueryRecordInViewsRequest struct {
	ZoneID   int64  `json:"zone_id"`
	ViewID   int64  `json:"view_id"`
	Name     string `json:"name"`
	WildCard bool   `json:"wild_card"`
	Page     int64  `json:"page"`
	PageSize int64  `json:"page_size"`
}

func (q *QueryRecordInViewsRequest) Validate() error {
	if q.ZoneID == 0 {
		return errors.New("区域ID不能为空")
	}

	if q.Name == "" {
		return errors.New("名称不能为空")
	}

	if q.Page <= 0 {
		q.Page = 1
	}

	if q.PageSize <= 0 {
		q.PageSize = 10
	}

	return nil
}
