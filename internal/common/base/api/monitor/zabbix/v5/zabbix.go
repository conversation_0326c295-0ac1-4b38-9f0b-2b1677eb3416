package zabbix

import (
	"fmt"

	"github.com/spf13/viper"
)

// BaseZabbixRequest 基础请求结构体
type BaseZabbixRequest struct {
	JsonRPC string `json:"jsonrpc"`
	Method  string `json:"method"`
	ID      int    `json:"id"`
	Auth    string `json:"auth"`
	Params  any    `json:"params"`
}

func NewBaseZabbixRequest(method string, params any) *BaseZabbixRequest {
	return &BaseZabbixRequest{
		JsonRPC: "2.0",
		Method:  method,
		ID:      1,
		Auth:    viper.GetString("zabbix.auth"),
		Params:  params,
	}
}

func CreateZabbixHost(hostName string, hostGroupID string) {
	req := NewBaseZabbixRequest("host.create", nil)
	fmt.Println(req)
}
