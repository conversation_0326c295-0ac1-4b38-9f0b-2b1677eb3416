package zabbix

import "ks-knoc-server/pkg/mapdata"

type HostMethod string

const (
	HostMethodCreate HostMethod = "host.create"
	HostMethodDelete HostMethod = "host.delete"
	HostMethodUpdate HostMethod = "host.update"
	HostMethodGet    HostMethod = "host.get"
)

type HostInterfaceType int

const (
	// 使用ZabbixAgent
	HostInterfaceTypeAgent HostInterfaceType = 1
	// 使用SNMP，网络设备常用
	HostInterfaceTypeSNMP HostInterfaceType = 2
	// 使用IPMI
	HostInterfaceTypeIPMI HostInterfaceType = 3
	// 使用JMX
	HostInterfaceTypeJMX HostInterfaceType = 4
)

type HostInterfaceUseIP int

const (
	HostInterfaceUseDNS       HostInterfaceUseIP = 0
	HostInterfaceUseIPAddress HostInterfaceUseIP = 1
)

// HostInterface 主机接口
type HostInterface struct {
	InterfaceID string             `json:"interfaceid"` // 接口ID
	HostID      string             `json:"hostid"`      // 接口所属主机的ID
	Type        HostInterfaceType  `json:"type"`        // 接口类型
	Main        int                `json:"main"`        // 是否为默认接口，0表示不是，1表示是
	UseIP       HostInterfaceUseIP `json:"useip"`       // 是否通过IP建立链接
	IP          string             `json:"ip"`          // 接口使用的IP地址，如果使用DNS建联，这里也可以为空
	DNS         string             `json:"dns"`         // 接口使用的dns名称，当建联方式是IP的时候，这个是可以忽略的
	Port        string             `json:"port"`        // 接口使用的端口号，可以包含宏
}

type HostGroupFlag int

const (
	HostGroupFlagPlain = 0
	HostGroupDiscoverd = 4
)

type HostGroup struct {
	GroupID  string        `json:"groupid"`
	Name     string        `json:"name"`
	Flags    HostGroupFlag `json:"flags"`
	Internal int           `json:"internal"` // 0表示非内部的，1表示内部的，内部组不能被删除
}

// HostMacro 主机宏
type HostMacro struct {
	Macro       string `json:"macro"`
	Value       string `json:"value"`
	Description string `json:"description"`
}

// HostTag 主机标签
type HostTag struct {
	Tag   string `json:"tag"`
	Value string `json:"value"`
}

// HostTemplate 主机模板
type HostTemplate struct {
	TemplateID  string `json:"templateid"`
	Name        string `json:"name"`
	Host        string `json:"host"`
	Description string `json:"description"`
}

type HostCreateParam struct {
	Host          string          `json:"host"`
	Interfaces    []HostInterface `json:"interfaces"`
	Groups        []HostGroup     `json:"groups"`
	Tags          []HostTag       `json:"tags"`
	Templates     []HostTemplate  `json:"templates"`
	Macros        []HostMacro     `json:"macros"`
	InventoryMode int             `json:"inventory_mode"`
	Inventory     mapdata.MapData `json:"inventory"`
}

func NewHostCreateParam(hostName string, hostGroupID string) *HostCreateParam {
	host := &HostCreateParam{
		Host: hostName,
		Interfaces: []HostInterface{
			{
				Type: HostInterfaceTypeAgent,
				Main: 1,
				UseIP: HostInterfaceUseIPAddress,
				IP:   "127.0.0.1",
				Port: "10050",
			},
		},
	}
	return host
}
