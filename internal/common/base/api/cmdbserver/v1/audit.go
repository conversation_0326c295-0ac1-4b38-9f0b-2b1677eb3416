package v1

type AuditRecordRequest struct {
	StartTime     int64    `json:"startTime"`
	EndTime       int64    `json:"endTime"`
	Keyword       string   `json:"keyword"`
	UniqueId      string   `json:"unique_id"`
	ModelCode     string   `json:"model_code" bson:"model_code"`
	ResourceTypes []string `json:"resourceTypes" binding:"required"`
}

type AuditRecordRequestByID struct {
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
	Keyword   string `json:"keyword"`
	UniqueId  string `json:"unique_id" binding:"required"`
}
