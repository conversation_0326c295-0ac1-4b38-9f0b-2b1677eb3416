package v1

import (
	"errors"
	"reflect"

	"ks-knoc-server/pkg/clock"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	"github.com/mitchellh/mapstructure"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/internal/common/validate"
)

var (
	v *validate.ValidateOptions
)

type NestedModelAttr struct {
	ID            string                    `bson:"_id" json:"id" mapstructure:"_id"`
	Code          string                    `bson:"code" json:"code" mapstructure:"code"`
	Name          string                    `bson:"name" json:"name" mapstructure:"name"`
	Total         int                       `bson:"total" json:"total" mapstructure:"total"`
	BuiltIn       bool                      `bson:"builtin" json:"builtin" mapstructure:"builtin"`
	SystemCreated bool                      `bson:"system_created" json:"system_created" mapstructure:"system_created"`
	Children      []v1.CommonModelAttribute `bson:"children" json:"children"`
}

type GeneralModelObj struct {
	ID       string `bson:"_id" json:"id" mapstructure:"_id"`
	Code     string `bson:"code" json:"code" binding:"required" mapstructure:"code,required" validate:"required"`
	CreateAt int64  `bson:"create_at,omitempty" json:"create_at" mapstructure:"create_at"`
	UpdateAt int64  `bson:"update_at,omitempty" json:"update_at" mapstructure:"update_at"`
	Name     string `bson:"name" json:"name" binding:"required" mapstructure:"name"`
	Builtin  *bool  `bson:"builtin" json:"builtin" binding:"required" mapstructure:"builtin"`
}

// SetUpdateAt 设置更新时间
func (g *GeneralModelObj) SetUpdateAt() *GeneralModelObj {
	g.UpdateAt = clock.NowInNano()
	return g
}

type BaseAttribute struct {
	GeneralModelObj `bson:",inline" mapstructure:",squash"`
	IsNull          *bool  `json:"is_null" bson:"is_null" mapstructure:"is_null" binding:"required"`
	IsUnique        *bool  `json:"is_unique" bson:"is_unique" mapstructure:"is_unique" binding:"required"`
	IsEditAble      *bool  `json:"is_editable" bson:"is_editable" mapstructure:"is_editable" binding:"required"`
	ModelCode       string `json:"model_code" bson:"model_code" mapstructure:"model_code" binding:"required"`
	AttrGroup       string `json:"attr_group" bson:"attr_group" mapstructure:"attr_group" binding:"required"`
	SystemCreated   bool   `json:"system_created" bson:"system_created" mapstructure:"system_created"`
	Describe        string `json:"describe" bson:"describe" mapstructure:"describe" binding:"-"`
	TypeName        string `json:"type_name" bson:"type_name"  mapstructure:"type_name" binding:"required"`
}

type ModelAttrRule struct {
	RuleRe string `json:"rule_re" bson:"rule_re" mapstructure:"rule_re" binding:"-"`
}

type StringAttribute struct {
	// 要带上bson:",inline"，否则ID字段无法被自动的初始化
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"CheckRule"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample" binding:"CheckRule,CheckRuleRe"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// IntAttribute 整型类型的专有属性
type IntAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"CheckRule"`
		Unit          string `json:"unit" bson:"unit" mapstructure:"unit"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample" binding:"CheckRule,CheckRuleRe"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// FloatAttribute 浮点型类型的专有属性
type FloatAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"CheckRule"`
		Unit          string `json:"unit" bson:"unit" mapstructure:"unit"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample" binding:"CheckRuleRe"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// DateAttribute 日期类型的专有属性
type DateAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"-"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// DateTimeAttribute 日期时间类型的专有属性
type DateTimeAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"-"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// TextareaAttribute 多行文本类型的专有属性
type TextareaAttribute struct {
	BaseAttribute `bson:",inline" mapstructure:",squash"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"CheckRule"`
		Sample        string `json:"sample" bson:"sample" mapstructure:"sample" binding:"CheckRuleRe"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// BoolAttribute 布尔类型的专有属性
type BoolAttribute struct {
	BaseAttribute `bson:",inline" mapstructure:",squash"`
	Attrs         struct {
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint" binding:"-"`
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// SelectAttribute 选择类型的专有属性
type SelectAttribute struct {
	BaseAttribute `mapstructure:",squash" bson:",inline"`
	Attrs         struct {
		ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
		UserHint      string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
		Inherit       bool   `json:"inherit" bson:"inherit" mapstructure:"inherit"`              // 是否继承
		Depth         int    `json:"depth" bson:"depth" mapstructure:"depth" binding:"required"` // 继承的深度
		Options       []struct {
			Name string `json:"name" bson:"name" mapstructure:"name" binding:"required"`
			ID   string `json:"id" bson:"id" mapstructure:"id" binding:"required"`
		} `json:"opts" bson:"opts" mapstructure:"opts"`
		ChainData SelectChainData `json:"chain_data" bson:"chain_data" mapstructure:"chain_data,omitempty" binding:"-"`
	} `json:"attrs" bson:"attrs" mapstructure:"attrs"`
}

// SelectChainData 枚举继承数据的结构
type SelectChainData struct {
	DataTree []struct {
		Children []struct {
			ID   string `json:"id" bson:"id" mapstructure:"id" binding:"required"`       // 子数据的ID
			Name string `json:"name" bson:"name" mapstructure:"name" binding:"required"` // 子数据的内容
		} `json:"children" bson:"children" mapstructure:"children"` // 子级数据
		ID   string `json:"id" bson:"id" mapstructure:"id" binding:"required"`       // 父级数据的ID
		Name string `json:"name" bson:"name" mapstructure:"name" binding:"required"` // 父级数据的内容
	} `json:"data_tree" bson:"data_tree" mapstructure:"data_tree"`
	ParentCode string `json:"parent_code" bson:"parent_code" mapstructure:"parent_code" binding:"-"`
}

// CheckAttrValidate 检查属性的合法性
func checkAttrValidate(attr interface{}) (err error) {
	attrValue := reflect.ValueOf(attr)
	ruleReValue := attrValue.FieldByName("Attrs").FieldByName("ModelAttrRule").FieldByName("RuleRe")
	attrType := attrValue.FieldByName("BaseAttribute").FieldByName("TypeName")

	var attrTypeString string
	if attrType.IsValid() {
		attrTypeString = attrType.String()
	}

	var ruleRe string
	if ruleReValue.IsValid() {
		ruleRe = ruleReValue.String()
	}
	// 动态的设置rule_re的值
	mav := validate.NewModelAttrValidate()
	mav.SetRuleRe(ruleRe)
	mav.SetTypeString(attrTypeString)
	v, err = validate.GetValidateOptions()
	if err != nil {
		return err
	}
	if err = binding.Validator.ValidateStruct(attr); err != nil {
		var errs validator.ValidationErrors
		if errors.As(err, &errs) {
			return v.TranslateValidationErrors(errs)
		}
		return
	}
	return
}

func checkAttrBinding(attr any) error {
	vo, err := validate.GetValidateOptions()
	if err != nil {
		return err
	}

	if err = binding.Validator.ValidateStruct(attr); err != nil {
		var errs validator.ValidationErrors
		if errors.As(err, &errs) {
			return vo.TranslateValidationErrors(errs)
		}
		return err
	}
	return nil
}

// ModelAttrValidate 根据类型进行属性的验证
func ModelAttrValidate(attr map[string]interface{}) (err error) {
	// 首先检测一下是否传递了type_name参数，如果没有传递直接返回报错即可
	attrType, ok := attr["type_name"]
	if !ok {
		return errno.ErrParameterRequired.Add("type_name为必传参数，请传入type_name")
	}
	// 检测type_name是否为string类型，如果不是string类型，直接返回报错即可
	attrTypeName, ok := attrType.(string)
	if !ok {
		return errno.ErrParameterRequired.Add("type_name应传入string类型的参数，参数校验失败")
	}
	if attrTypeName == "" {
		return errno.ErrParameterRequired.Add("type_name不可以为空")
	}
	switch attrTypeName {
	case "string":
		m := StringAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = checkAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetUpdateAt()
		return
	case "int":
		m := IntAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = checkAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetUpdateAt()
		return
	case "float":
		m := FloatAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = checkAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetUpdateAt()
		return
	case "date":
		m := DateAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = checkAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetUpdateAt()
		return
	case "datetime":
		m := DateAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = checkAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetUpdateAt()
		return
	case "select":
		m := SelectAttribute{}
		err = mapstructure.Decode(attr, &m)
		if err != nil {
			return err
		}
		err = checkAttrValidate(m)
		if err != nil {
			return err
		}
		m.SetUpdateAt()
		return
	default:
		return nil
	}
}
