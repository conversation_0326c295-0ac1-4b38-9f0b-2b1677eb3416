package v1

type OfflineRequest struct {
	InstanceIDs []string `json:"instance_ids" binding:"required"`
	Reason      string   `json:"reason" binding:"required"`
}

type OfflineFailedItem struct {
	InstanceID string `json:"instance_id"`
	Reason     string `json:"reason"`
}

type OfflineResponse struct {
	SuccessCount int64               `json:"success_count"`
	FailedCount  int64               `json:"failed_count"`
	FailedItems  []OfflineFailedItem `json:"failed_items"`
}

func NewOfflineResponse() *OfflineResponse {
	return &OfflineResponse{
		SuccessCount: 0,
		FailedCount:  0,
		FailedItems:  make([]OfflineFailedItem, 0),
	}
}
