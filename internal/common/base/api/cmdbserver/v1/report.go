package v1

// Payload represents the main payload structure.
type Payload struct {
	DeviceID     string            `json:"device_id"`
	AgentID      string            `json:"agent_id"`
	AgentVersion string            `json:"agent_version"`
	IsVM         bool              `json:"is_vm"`
	Labels       map[string]string `json:"labels"` // 标签
	SystemInfo   SystemInfo        `json:"system_info"`
	NetworkInfo  NetworkInfo       `json:"network_info"`
	ProcessInfo  ProcessInfo       `json:"process_info"`
	KVMInfo      KVMInfo           `json:"kvm_info"`
	Hardware     Hardware          `json:"hardware"`
	DiskInfo     DiskInfo          `json:"disk_info"`
	MemoryInfo   MemoryInfo        `json:"memory_info"`
	OpticalInfo  OpticalInfo       `json:"optical_info"`
	Timestamp    int64             `json:"timestamp"`
	Status       string            `json:"status"`
	Messages     []string          `json:"messages"`
}

func (p *Payload) Validate() error {
	return nil
}

// SystemInfo represents the system information.
type SystemInfo struct {
	OS            string `json:"os"`
	OSFamily      string `json:"os_family"`
	OSVersion     string `json:"os_version"`
	KernelVersion string `json:"kernel_version"`
	Hostname      string `json:"hostname"`
	CPUCores      int    `json:"cpu_cores"`
	SN            string `json:"sn"`
	BareMetal     bool   `json:"bare_metal"`
}

// NetworkInfo represents the network information.
type NetworkInfo struct {
	Interfaces []Interface `json:"interfaces"`
}

// Interface represents a network interface.
type Interface struct {
	Name        string   `json:"name"`
	IPAddresses []string `json:"ip_addresses"`
	MACAddress  string   `json:"mac_address"`
}

// ProcessInfo represents the process information.
type ProcessInfo struct {
	Processes map[string]Process `json:"Processes"`
}

// Process represents a single process.
type Process struct {
	Name   string `json:"name"`
	Status bool   `json:"status"`
	PID    string `json:"pid"`
}

// KVMInfo represents the KVM information.
type KVMInfo struct {
	IsKVMHost         bool             `json:"is_kvm_host"`
	KVMModuleLoaded   bool             `json:"kvm_module_loaded"`
	VirtualMachines   []VirtualMachine `json:"virtual_machines"`
	LibvirtRunning    bool             `json:"libvirt_running"`
	LibvirtVersion    string           `json:"libvirt_version"`
	QEMUVersion       string           `json:"qemu_version"`
	QEMUProcessCount  int              `json:"qemu_process_count"`
	HasVMDefinitions  bool             `json:"has_vm_definitions"`
	CPUVirtualization bool             `json:"cpu_virtualization"`
}

// VirtualMachine represents a virtual machine.
type VirtualMachine struct {
	Name         string   `json:"name"`
	State        string   `json:"state"`
	AutoStart    bool     `json:"auto_start"`
	AutoStartStr string   `json:"auto_start_str"`
	MACAddress   []string `json:"mac_address"`
}

// Hardware represents the hardware information.
type Hardware struct {
	Hardware struct {
		Components []Component `json:"components"`
	} `json:"hardware"`
}

// Component represents a hardware component.
type Component struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Vendor      string `json:"vendor"`
	Model       string `json:"model"`
	SerialNum   string `json:"serial_num"`
	Version     string `json:"version"`
	Description string `json:"description"`
}

// DiskInfo represents the disk information.
type DiskInfo struct {
	Disks []Disk `json:"disks"`
}

// Disk represents a single disk.
type Disk struct {
	BasicPath  string     `json:"basic_path"`
	BasicInfo  BasicInfo  `json:"basic_info"`
	DetailInfo DetailInfo `json:"detail_info"`
}

// BasicInfo represents the basic information of a disk.
type BasicInfo struct {
	State string `json:"State"`
	Model string `json:"Model"`
	Size  string `json:"Size"`
	Intf  string `json:"Intf"`
	Med   string `json:"Med"`
}

// DetailInfo represents the detailed information of a disk.
type DetailInfo struct {
	SN string `json:"SN"`
}

// MemoryInfo represents the memory information.
type MemoryInfo struct {
	Memory struct {
		TotalSize string         `json:"total_size"`
		Modules   []MemoryModule `json:"modules"`
	} `json:"memory"`
}

// MemoryModule represents a memory module.
type MemoryModule struct {
	Size      string `json:"size"`
	Type      string `json:"type"`
	Speed     string `json:"speed"`
	Slot      string `json:"slot"`
	Vendor    string `json:"vendor"`
	SerialNum string `json:"serial_num"`
	PartNum   string `json:"part_num"`
}

// OpticalInfo represents the optical information.
type OpticalInfo struct {
	Optical struct {
		Modules []OpticalModule `json:"modules"`
	} `json:"optical"`
}

// OpticalModule represents an optical module.
type OpticalModule struct {
	Interface    string `json:"interface"`
	Type         string `json:"type"`
	Vendor       string `json:"vendor"`
	PartNumber   string `json:"part_number"`
	SerialNumber string `json:"serial_number"`
	Wavelength   string `json:"wavelength"`
	Status       string `json:"status"`
}
