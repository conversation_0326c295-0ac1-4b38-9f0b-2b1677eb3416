package v1

import (
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
)

type PlaceHolder struct {
	PType   v1.PlaceHolderType `json:"p_type" binding:"required"`  // 占位设备类型
	StartU  int                `json:"start_u" binding:"required"` // 占位设备起始U位
	Height  int                `json:"height" binding:"required"`  // 占位设备高度
	Name    string             `json:"name" binding:"required"`    // 占位设备名称
	Comment string             `json:"comment"`                    // 占位设备备注
	RackID  string             `json:"rack_id" binding:"required"` // 机柜ID
}

// TypeValid 占位类型是否有效
func (p *PlaceHolder) TypeValid() bool {
	switch p.PType {
	// 目前就先支持这几种类型
	case v1.ODF, v1.Tray, v1.PatchPanel:
		return true
	default:
		return false
	}
}

func NewPlaceHolder() *PlaceHolder {
	return &PlaceHolder{}
}
