package v1

import (
	"errors"
	"fmt"
)

var (
	ErrDeviceNotFound = errors.New("设备不存在")
)

type OfficeListResponse struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

type DeviceSNList struct {
	SN []string `json:"sn" binding:"required"`
}

type DevicePositionResponse struct {
	Area         string `json:"area"`
	Office       string `json:"office"`
	IDC          string `json:"room"`
	Rack         string `json:"rack"`
	DeviceType   string `json:"device_type"`
	DeviceSN     string `json:"device_sn"`
	StartU       int    `json:"start_u"`
	DeviceHeight int    `json:"device_height"`
}

type DeviceInfoResponse struct {
	DeviceSN string `json:"device_sn"`
	Status   string `json:"status"`
	Error    struct {
		Code    int `json:"code"`
		Message string `json:"message"`
	} `json:"error"`
	MetaData struct {
		Name       string `json:"name"`
		DeviceType string `json:"device_type"`
		DeviceSN   string `json:"device_sn"`
		Brand      string `json:"brand"`
		Model      string `json:"model"`
		Office     string `json:"office"`
		IDC        string `json:"idc"`
		Rack       string `json:"rack"`
		Owner      string `json:"owner"`
		Position   int    `json:"position"`
	} `json:"metadata"`
}

func (d DevicePositionResponse) Position() string {
	return fmt.Sprintf("%d-%d", d.StartU, d.StartU+d.DeviceHeight-1)
}

func (d DevicePositionResponse) RackInfo() string {
	return fmt.Sprintf("机柜:%s; 设备类型: %s; U位信息: %s", d.Rack, d.DeviceType, d.Position())
}

func (d DevicePositionResponse) IDCInfo() string {
	return fmt.Sprintf("机房:%s; 机柜:%s; 设备类型: %s; U位信息: %s", d.Area, d.Rack, d.DeviceType, d.Position())
}

func (d DevicePositionResponse) OfficeInfo() string {
	return fmt.Sprintf("区域:%s; 职场:%s; 机房:%s; 机柜:%s; 设备类型: %s; U位信息: %s", d.Area, d.Office, d.IDC, d.Rack, d.DeviceType, d.Position())
}

func (d DevicePositionResponse) AreaInfo() string {
	return fmt.Sprintf("区域:%s; 职场:%s; 机房:%s; 机柜:%s; 设备类型: %s; U位信息: %s", d.Area, d.Office, d.IDC, d.Rack, d.DeviceType, d.Position())
}

type OfficeInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type IdcInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
