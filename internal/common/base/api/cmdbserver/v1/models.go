package v1

import (
	"ks-knoc-server/internal/common/base/model/cmdbserver/v1"
)

type DownloadModelReq struct {
	IDS        []string `json:"ids" binding:"required"`
	ExcludeIDs []string `json:"exclude_ids"`
	ModelCode  string   `json:"code" binding:"required"`
	Mode       string   `json:"mode"`
}

// ModelWithNested 带有嵌套的模型，即模型分组套模型的结构
type ModelWithNested struct {
	ID       string       `bson:"_id" json:"id" mapstructure:"_id"`
	Code     string       `bson:"code" json:"code" mapstructure:"code"`
	Name     string       `bson:"name" json:"name" mapstructure:"name"`
	Total    int          `bson:"total" json:"total" mapstructure:"total"`
	BuiltIn  bool         `bson:"builtin" json:"builtin" mapstructure:"builtin"`
	Children v1.ModelList `bson:"children" json:"children"`
	CreateAt int64        `bson:"create_at" json:"create_at" mapstructure:"create_at"`
}

type PaginatedModelResponseWithNested struct {
	Total       int64             `json:"total"`
	Pages       int64             `json:"pages"`
	PageSize    int64             `json:"page_size"`
	CurrentPage int64             `json:"current_page"`
	Data        []ModelWithNested `json:"data"`
}

// PaginatedModelResponse 带分页的模型列表
type PaginatedModelResponse struct {
	Total       int64      `json:"total"`
	Pages       int64      `json:"pages"`
	PageSize    int64      `json:"page_size"`
	CurrentPage int64      `json:"current_page"`
	Data        []v1.Model `json:"data"`
}
