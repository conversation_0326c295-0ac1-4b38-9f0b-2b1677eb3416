package v1

import (
	"strings"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/pkg/mapdata"
)

// DataResponse 模型数据响应
type DataResponse struct {
	v1.ModelData
	Info  string `json:"info"`
	Title string `json:"title"`
}

// SearchResultDataBlock 搜索结果的数据部分
type SearchResultDataBlock struct {
	Total       int64          `json:"total"`
	Pages       int64          `json:"pages"`
	PageSize    int64          `json:"page_size"`
	CurrentPage int64          `json:"current_page"`
	Data        []DataResponse `json:"data"`
}

type NestedModelList []ModelWithNested

// Len is the number of elements in the collection.
func (m NestedModelList) Len() int { return len(m) }

// Swap swaps the elements with indexes i and j.
func (m NestedModelList) Swap(i, j int) {
	m[i], m[j] = m[j], m[i]
}

// Less is the comparison function.
func (m NestedModelList) Less(i, j int) bool {
	return m[i].CreateAt < m[j].CreateAt
}

// FuzzySearchResponse 模糊搜索的响应
type FuzzySearchResponse struct {
	ModelGroups  NestedModelList       `json:"model_groups"`
	Data         SearchResultDataBlock `json:"data"`
	SearchFilter mapdata.MapData       `json:"search_filter"`
}

type GlobalSearchResponse struct {
	ModelGroups  NestedModelList       `json:"model_groups"`
	Data         SearchResultDataBlock `json:"data"`
	SearchFilter mapdata.MapData       `json:"search_filter"`
}

type GlobalBatchSearchResponse struct {
	GlobalSearchResponse
	NotMatched []string `json:"not_matched"`
}

func NewGlobalBatchSearchResponse() *GlobalBatchSearchResponse {
	return &GlobalBatchSearchResponse{
		NotMatched: make([]string, 0),
	}
}

// FuzzySearchRequest 模糊搜索的请求
type FuzzySearchRequest struct {
	Keyword        string   `json:"keyword"`
	ModelCode      []string `json:"model_code"`
	ModelGroupCode string   `json:"model_group_code"`
}

func (f *FuzzySearchRequest) Format() *FuzzySearchRequest {
	f.Keyword = strings.TrimSpace(f.Keyword)
	return f
}

func NewFuzzySearchRequest() *FuzzySearchRequest {
	return &FuzzySearchRequest{}
}

// GlobalSearchFuzzyRequest 全局搜索的模糊搜索请求
type GlobalSearchFuzzyRequest struct {
	ModelGroupCode string   `json:"model_group_code"`           // 模型分组code
	ModelCode      []string `json:"model_code"`                 // 模型code
	Keyword        string   `json:"keyword" binding:"required"` // 搜索关键词
}

// GlobalSearchBatchRequest 全局搜索的批量搜索请求
type GlobalSearchBatchRequest struct {
	SearchField    string   `json:"search_field" binding:"required"`
	ModelGroupCode string   `json:"model_group_code"`
	ModelCode      []string `json:"model_code"`
	Keywords       []string `json:"keywords" binding:"required"`
}

// CombinedField 组合搜索的组合字段
type CombinedField struct {
	FieldName  string   `json:"field_name" binding:"required"`
	FieldValue []string `json:"field_value" binding:"required"`
}

// GlobalSearchCombineRequest 全局搜索的组合搜索请求
type GlobalSearchCombineRequest struct {
	CombineFields  []CombinedField `json:"combine_fields" binding:"required"`
	ModelGroupCode string          `json:"model_group_code"`
	ModelCode      []string        `json:"model_code"`
}

func NewGlobalSearchCombineRequest() *GlobalSearchCombineRequest {
	return &GlobalSearchCombineRequest{
		CombineFields: make([]CombinedField, 0),
	}
}

