package bpm

// OrderInfoResponse 主要用于BPM底座展示数据详情
type OrderInfoResponse struct {
	InitiatorUserName string            `json:"initiator_username"`
	InitiatorName     string            `json:"initiator_name"`
	Department        string            `json:"department"`
	OpType            string            `json:"op_type"`
	FieldMapping      map[string]string `json:"field_mapping"`
	Note              string            `json:"note"` // 备注字段
	OrderContent      any               `json:"order_content"`
}

func NewOrderInfoResponse() *OrderInfoResponse {
	return &OrderInfoResponse{
		OrderContent: make([]any, 0),
	}
}

// OrderAbstractResponse 工单摘要
type OrderAbstractResponse struct {
	Title             string `json:"title"`
	InitiatorUserName string `json:"initiator_username"`
	Abstract          string `json:"abstract"`
	Department        string `json:"department"`
	ProcessState      string `json:"process_state"`
	CreateTime        int64  `json:"create_time"`
	BusinessID        string `json:"business_id"`
	ProcessKey        string `json:"process_key"`
	ProcessName       string `json:"process_name"`
	ProcessType       string `json:"process_type"`
	URL               string `json:"url"`
	DetailPageType    string `json:"detail_page_type"` // 详情页类型，用于前端路由判断
}

type DeviceInfoResponse struct {
	Data                map[string]any          `json:"data"`
	FieldTobeSupplement FieldTobeSupplementList `json:"field_tobe_supplement"`
}

type FieldTobeSupplementList []*FieldTobeSupplement

func (f FieldTobeSupplementList) Len() int {
	return len(f)
}

func (f FieldTobeSupplementList) Swap(i, j int) {
	f[i], f[j] = f[j], f[i]
}

// Less 按照FieldSeq排序
func (f FieldTobeSupplementList) Less(i, j int) bool {
	return f[i].FieldSeq < f[j].FieldSeq
}

func NewDeviceInfoResponse() *DeviceInfoResponse {
	return &DeviceInfoResponse{
		Data:                make(map[string]any),
		FieldTobeSupplement: make([]*FieldTobeSupplement, 0),
	}
}

// OrderDetailResponse 工单详情
type OrderDetailResponse struct {
	BusinessID        string `json:"business_id"`
	OpType            string `json:"op_type"`
	Initiator         string `json:"initiator"`
	InitiatorUserName string `json:"initiator_username"`
	Department        string `json:"department"`
	Title             string `json:"title"`
	ProcessKey        string `json:"process_key"`
	ProcessState      string `json:"process_state"`
	CreateTime        int64  `json:"create_time"`
	Note              string `json:"note"`
	OrderContent      any    `json:"order_content"`
}

func NewOrderDetailResponse() *OrderDetailResponse {
	return &OrderDetailResponse{}
}

type OrderAuditRecord struct {
	TaskID          string `json:"task_id"`
	ApproveTime     string `json:"approveTime"`
	Comments        string `json:"comments"`
	Operation       string `json:"operation"`
	OperationName   string `json:"operationName"`
	OperationResult string `json:"operationResult"`
	TaskName        string `json:"taskName"`
	UserName        string `json:"userName"`
}

type OrderAuditRecordResponse struct {
	BusinessID       string             `json:"business_id"`
	ProcessKey       string             `json:"process_key"`
	ProcessName      string             `json:"process_name"`
	Title            string             `json:"title"`
	ProcessState     string             `json:"process_state"`
	ProcessStateName string             `json:"process_state_name"`
	Data             []OrderAuditRecord `json:"data"`
}

func NewOrderAuditRecordResponse() *OrderAuditRecordResponse {
	return &OrderAuditRecordResponse{
		Data: make([]OrderAuditRecord, 0),
	}
}

// FieldTobeSupplement 需要补充的字段信息
type FieldTobeSupplement struct {
	FieldName        string `json:"field_name"`
	FieldDisplayName string `json:"field_display_name"`
	FieldType        string `json:"field_type"`
	Required         bool   `json:"required"`
	FieldValue       any    `json:"field_value"`
	Attributes       any    `json:"attributes"`
	FieldSeq         int64  `json:"field_seq"`
}

// ProcessNodeFieldTypeString 字符串字段信息
type ProcessNodeFieldTypeString struct {
	MaxLength int `json:"max_length"` // 最大长度
}

// ProcessNodeFieldTypeNumber 数字字段信息
type ProcessNodeFieldTypeNumber struct {
	MinValue int `json:"min_value"` // 最小值
	MaxValue int `json:"max_value"` // 最大值
}

// SelectOption 下拉框选项
type SelectOption struct {
	Label string `json:"label"`
	Value any    `json:"value"`
}

// ProcessNodeFieldTypeSelect 下拉框字段信息
type ProcessNodeFieldTypeSelect struct {
	RequestURL string            `json:"request_url"` // 请求URL
	Params     map[string]any    `json:"params"`      // 请求参数, 适用于需要异步请求的情况，如果AsyncRequest为false，则该字段为空
	Method     string            `json:"method"`      // 请求方法，目前先简单设计，默认只支持GET
	Options    []SelectOption    `json:"options"`     // 下拉框选项, 适用于不需要异步请求的情况，如果AsyncRequest为true，则需要通过RequestURL获取下拉框选项
	RelyField  map[string]string `json:"rely_field"`  // 依赖字段, 适用于需要根据依赖字段的值来获取下拉框选项的情况
}

func NewProcessNodeFieldTypeSelect() *ProcessNodeFieldTypeSelect {
	return &ProcessNodeFieldTypeSelect{
		Params:  make(map[string]any),
		Options: make([]SelectOption, 0),
	}
}

// OrderListResponse 工单列表响应
type OrderListResponse struct {
	Total       int64                    `json:"total"`
	Pages       int64                    `json:"pages"`
	PageSize    int64                    `json:"page_size"`
	CurrentPage int64                    `json:"current_page"`
	Data        []*OrderAbstractResponse `json:"data"`
}

func NewOrderListResponse() *OrderListResponse {
	return &OrderListResponse{
		Data: make([]*OrderAbstractResponse, 0),
	}
}
