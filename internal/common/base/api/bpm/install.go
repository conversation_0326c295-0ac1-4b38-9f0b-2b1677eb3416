package bpm

import "errors"

// DCReInstallDevice 重新安装设备
type DCReInstallDevice struct {
	DeviceSN     string       `json:"device_sn" binding:"required" mapstructure:"device_sn"`         // 设备SN
	ServiceInfo  string       `json:"service_info" binding:"required" mapstructure:"service_info"`   // 服务说明信息
	SystemFamily SystemFamily `json:"system_family" binding:"required" mapstructure:"system_family"` // 操作系统
	RaidGroup    RaidGroup    `json:"raid_group" mapstructure:"raid_group"`                          // 选填: raid卡组
	IPAddress    string       `json:"ip_address" mapstructure:"ip_address"`                          // 选填: 内网IP地址
	IPNetMask    string       `json:"ip_netmask" mapstructure:"ip_netmask"`                          // 选填: 内网IP掩码
	IPGateway    string       `json:"ip_gateway" mapstructure:"ip_gateway"`                          // 选填: 内网网关
	IPDNS        []string     `json:"ip_dns" mapstructure:"ip_dns"`                                  // 选填: 内网DNS
	BMCIPAddress string       `json:"bmc_ip_address" mapstructure:"bmc_ip_address"`                  // 选填: BMC IP地址
	BMCNetMask   string       `json:"bmc_netmask" mapstructure:"bmc_netmask"`                        // 选填: BMC 子网掩码
	BMCGateway   string       `json:"bmc_gateway" mapstructure:"bmc_gateway"`                        // 选填: BMC 网关
	Confirm      bool         `json:"confirm" mapstructure:"confirm"`                                // 选填: 操作前确认
}

func (d *DCReInstallDevice) Validate() error {
	if d.DeviceSN == "" {
		return errors.New("设备序列号不能为空")
	}

	return nil
}

var DcReInstallDeviceFieldName = map[string]string{
	"device_sn":      "设备序列号",
	"service_info":   "服务说明",
	"system_family":  "操作系统",
	"raid_group":     "raid卡组",
	"ip_address":     "内网IP地址",
	"ip_netmask":     "内网IP掩码",
	"ip_gateway":     "内网网关",
	"ip_dns":         "内网DNS",
	"bmc_ip_address": "BMC IP地址",
	"bmc_netmask":    "BMC 子网掩码",
	"bmc_gateway":    "BMC 网关",
}

// NewDCReInstallRequest 重新安装设备
func NewDCReInstallRequest() []*DCReInstallDevice {
	return make([]*DCReInstallDevice, 0)
}
