package bpm

type DCOperationType string

func (d DCOperationType) String() string {
	return string(d)
}

const (
	OpTypePrefix string          = "it_dc_type"
	OnRack       DCOperationType = "on_rack"   // 设备上架
	OffRack      DCOperationType = "off_rack"  // 设备下架
	ReInstall    DCOperationType = "reinstall" // 设备重装
	Operation    DCOperationType = "operation" // 设备操作
	Cabling      DCOperationType = "cabling"   // 综合布线
)

var OpTypeNameMap = map[DCOperationType]string{
	OnRack:    "上架",
	OffRack:   "下架",
	ReInstall: "重装",
	Operation: "操作",
	Cabling:   "布线",
}

type SystemFamily string

const (
	WindowsServer2019 SystemFamily = "WindowsServer2019"
	Centos7           SystemFamily = "Centos7"
	KwaiOS            SystemFamily = "KwaiOS"
)

// RaidGroup 磁盘组
type RaidGroup string

const (
	Raid1            RaidGroup = "raid1"
	Raid5            RaidGroup = "raid5"
	Raid5AndHotSpare RaidGroup = "raid5_hot_spare"
)

// RaidGroupTranslateMap RAID组类型翻译映射
var RaidGroupTranslateMap = map[RaidGroup]string{
	Raid1:            "RAID1",
	Raid5:            "RAID5",
	Raid5AndHotSpare: "RAID5+1热备盘",
}

// Translate 获取RAID组类型的中文翻译
func (r RaidGroup) Translate() string {
	if translation, exists := RaidGroupTranslateMap[r]; exists {
		return translation
	}
	return string(r)
}

// DetailPageType 详情页类型
type DetailPageType string

const (
	// DetailPageTypeDefault 默认详情页
	DetailPageTypeDCDefault DetailPageType = "it_dc_default"
	// DetailPageTypeReinstall 重装定制详情页
	DetailPageTypeDCReinstall DetailPageType = "it_dc_reinstall"
)
