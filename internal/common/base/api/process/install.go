package process

import "errors"

type TaskCallBackRequest struct {
	TaskID     int64  `json:"task_id"`
	TaskStatus uint8  `json:"task_status"`
	Message    string `json:"message"`
	StartTime  int64  `json:"start_time"`
	EndTime    int64  `json:"end_time"`
}

func (r *TaskCallBackRequest) Validate() error {
	if r.TaskID <= 0 {
		return errors.New("task_id不能为空")
	}

	if r.TaskStatus <= 0 {
		return errors.New("task_status不能为空")
	}

	if r.TaskStatus == 4 {
		if r.Message == "" {
			return errors.New("task_status为失败时，message不能为空")
		}
	}

	return nil
}
