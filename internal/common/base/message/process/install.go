package process

import (
	"sort"

	share "ks-knoc-server/internal/common/base/share/bpm"
	"ks-knoc-server/pkg/mapdata"
)

type ConfigRaid struct {
	Enabled bool `json:"enabled" mapstructure:"enabled"`
	Info    struct {
		SystemRaidLevel string `json:"system_raid_level" mapstructure:"system_raid_level"`
		DataRaidLevel   string `json:"data_raid_level" mapstructure:"data_raid_level"`
		HotSpare        bool   `json:"hot_spare" mapstructure:"hot_spare"`
		HotSpareCount   int    `json:"hot_spare_count" mapstructure:"hot_spare_count"`
	} `json:"info" mapstructure:"info"`
}

type ConfigBmc struct {
	BMCIP       string `json:"bmc_ip" mapstructure:"bmc_ip"`
	BMCMask     string `json:"bmc_mask" mapstructure:"bmc_mask"`
	BMCGateway  string `json:"bmc_gateway" mapstructure:"bmc_gateway"`
	BmcUsername string `json:"bmc_user" mapstructure:"bmc_user"`
	BmcPassword string `json:"bmc_pass" mapstructure:"bmc_pass"`
}

type ConfigOS struct {
	OSName   string `json:"os_name" mapstructure:"os_name"`
	Hostname string `json:"hostname" mapstructure:"hostname"`
}

type ConfigNetwork struct {
	BondMode   int      `json:"bond_mode" mapstructure:"bond_mode"`
	SystemIP   string   `json:"system_ip" mapstructure:"system_ip"`
	SystemMask string   `json:"system_mask" mapstructure:"system_mask"`
	SystemDNS  []string `json:"system_dns" mapstructure:"system_dns"`
	SystemGW   string   `json:"system_gw" mapstructure:"system_gw"`
}

type InstallConfig struct {
	ConfigRaid    ConfigRaid    `json:"raid" mapstructure:"raid"`
	ConfigBmc     ConfigBmc     `json:"bmc" mapstructure:"bmc"`
	ConfigOS      ConfigOS      `json:"os" mapstructure:"os"`
	ConfigNetwork ConfigNetwork `json:"network" mapstructure:"network"`
}

// InstallTask 安装任务
type InstallTask struct {
	TaskID   int64  `json:"task_id"`
	TaskName string `json:"task_name"`
}

type InstallInfo struct {
	ServerSN      string        `json:"server_sn"`
	Message       string        `json:"message"`
	BusinessID    string        `json:"business_id"`
	InstallConfig InstallConfig `json:"config"`
	Tasks         []InstallTask `json:"tasks"`
	Creator       string        `json:"creator"`
	CreatedAt     int64         `json:"created_at"`
	UpdatedAt     int64         `json:"updated_at"`
}

type DeviceInfo struct {
	DeviceSN     string                        `json:"device_sn"`
	DeviceType   string                        `json:"device_type"`
	DeviceBrand  string                        `json:"device_brand"`
	DeviceModel  string                        `json:"device_model"`
	DeviceName   string                        `json:"device_name"`
	OfficeName   string                        `json:"office_name"`
	IdcName      string                        `json:"idc_name"`
	RackName     string                        `json:"rack_name"`
	Position     string                        `json:"position"`
	Owner        string                        `json:"owner"`
	DeviceStatus string                        `json:"device_status"`
	Fields       share.FieldTobeSupplementList `json:"fields"`
}

// NewDeviceInfo 创建设备信息以及要补充的字段的信息
func NewDeviceInfo() *DeviceInfo {
	return &DeviceInfo{
		Fields: make(share.FieldTobeSupplementList, 0),
	}
}

type QueryInstallTaskDetail struct {
	TaskID      int64  `json:"task_id"`
	TaskName    string `json:"task_name"`
	TaskMessage string `json:"task_message"`
	TaskStatus  string `json:"task_status"`
	CreatedAt   int64  `json:"created_at"`
	UpdatedAt   int64  `json:"updated_at"`
	Duration    int64  `json:"duration"`
}

type QueryInstallTaskList []QueryInstallTaskDetail

// SortByTaskSeq 按照任务序列排序
func (qt *QueryInstallTaskList) SortByTaskSeq() {
	// key为任务名称，value为任务在TaskSequence中的索引位置
	taskIndexMap := make(map[string]int, len(share.TaskSequence))
	for idx, task := range share.TaskSequence {
		taskIndexMap[task.String()] = idx
	}

	sort.Slice(*qt, func(i, j int) bool {
		// 获取任务i和j的索引
		iIndex, iExists := taskIndexMap[(*qt)[i].TaskName]
		jIndex, jExists := taskIndexMap[(*qt)[j].TaskName]

		// 处理不在序列中的任务
		if !iExists {
			return false // i不在序列中，排在后面
		}
		if !jExists {
			return true // j不在序列中，排在后面
		}

		// 按照索引顺序排序
		return iIndex < jIndex
	})

}

// QueryInstallDeviceInfo 查询安装设备详情
type QueryInstallDeviceInfo struct {
	DeviceInfo
	Number int                      `json:"number"` // 设备编号
	Tasks  []QueryInstallTaskDetail `json:"tasks"`  // 任务列表
}

func NewQueryInstallDeviceInfo() *QueryInstallDeviceInfo {
	return &QueryInstallDeviceInfo{
		Tasks: make([]QueryInstallTaskDetail, 0),
	}
}

type QueryInstallDeviceList []*QueryInstallDeviceInfo

var InstallFieldMeta = mapdata.MapData{
	"device_sn":    "设备SN",
	"device_type":  "设备类型",
	"device_brand": "设备品牌",
	"device_model": "设备型号",
	"device_name":  "设备名称",
	"office_name":  "设备职场",
	"idc_name":     "设备机房",
	"rack_name":    "设备机柜",
	"position":     "设备位置",
	"owner":        "设备负责人",
}

// QueryInstallDetail 查询安装详情
// 该详情涵盖了工单的设备列表，Tasks任务列表
type QueryInstallDetail struct {
	Title             string                 `json:"title"`              // 工单标题
	ProcessState      string                 `json:"process_state"`      // 流程状态
	ProcessKey        string                 `json:"process_key"`        // 流程key
	BusinessID        string                 `json:"business_id"`        // 工单id
	Initiator         string                 `json:"initiator"`          // 申请人
	InitiatorUserName string                 `json:"initiator_username"` // 申请人username
	FieldMeta         mapdata.MapData        `json:"field_meta"`         // 字段元数据
	Devices           QueryInstallDeviceList `json:"devices"`            // 设备列表
}

// NewQueryInstallDetail 创建一个查询安装详情
func NewQueryInstallDetail() *QueryInstallDetail {
	return &QueryInstallDetail{
		Devices:   make(QueryInstallDeviceList, 0),
		FieldMeta: InstallFieldMeta,
	}
}
