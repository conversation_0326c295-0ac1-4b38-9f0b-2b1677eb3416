package process

type OrderBaseMessage struct {
	BusinessID         string   `json:"business_id"`
	Initiator          string   `json:"initiator"`
	Description        string   `json:"description"`
	OperationType      string   `json:"op_type"`
	CreatedAt          int64    `json:"created_at"`
	Title              string   `json:"title"`
	DesireDeliveryTime string   `json:"desire_delivery_time"`
	ProcessState       string   `json:"process_state"`
	Approvers          []string `json:"approvers"`
	TaskKey            string   `json:"task_key"`
	DetailPageType     string   `json:"detail_page_type"`
}

type OrderAuditLogMessage struct {
	ID         int64    `json:"id"`
	BusinessID string   `json:"business_id"`
	Executor   string   `json:"executor"`
	NodeName   string   `json:"node_name"`
	Status     string   `json:"status"`
	StatusCode string   `json:"status_code"`
	Comments   string   `json:"comments"`
	CreatedAt  int64    `json:"created_at"`
	UpdatedAt  int64    `json:"updated_at"`
	Approvers  []string `json:"approvers"`
}

type OrderAuditLogMessages []OrderAuditLogMessage

func (m OrderAuditLogMessages) Len() int {
	return len(m)
}

func (m OrderAuditLogMessages) Swap(i, j int) {
	m[i], m[j] = m[j], m[i]
}

func (m OrderAuditLogMessages) Less(i, j int) bool {
	return m[i].CreatedAt < m[j].CreatedAt
}
