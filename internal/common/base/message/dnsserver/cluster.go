package dnsserver

type QueryClusterMessage struct {
	ID             int64    `json:"id"`
	Name           string   `json:"name"`
	Status         string   `json:"status"`
	Forwarders     []string `json:"forwarders"`
	ForVPN         bool     `json:"for_vpn"`
	ForTest        bool     `json:"for_test"`
	ForDrill       bool     `json:"for_drill"`
	HasLoadBalance bool     `json:"has_load_balance"`
	LoadBalanceIPs []string `json:"load_balance_ips"`
	CreateTime     int64    `json:"create_time"`
	UpdateTime     int64    `json:"update_time"`
	Description    string   `json:"description"`
}
