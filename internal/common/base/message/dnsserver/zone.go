package dnsserver

type QueryZoneMessage struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	ZoneType    string `json:"zone_type"`
	Description string `json:"description"`
	CreateTime  int64  `json:"create_time"`
	UpdateTime  int64  `json:"update_time"`
	RecordCount int64  `json:"record_count"`
}

type QueryZoneTypeInfo struct {
	ZoneTypeCode string `json:"code"`
	ZoneTypeName string `json:"name"`
}
