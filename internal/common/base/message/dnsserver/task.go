package dnsserver

type TaskDetailResponse struct {
	ID         int64  `json:"id"`
	OpType     string `json:"op_type"`
	Status     string `json:"status"`
	ServerName string `json:"server_name"`
	ServerIP   string `json:"server_ip"`
	ZoneName   string `json:"zone_name"`
	ViewName   string `json:"view_name"`
	Record     struct {
		Name       string `json:"name"`
		RecordType string `json:"type"`
		Action     string `json:"action"`
		Value      string `json:"value"`
		TTL        int    `json:"ttl"`
		OldValue   string `json:"old_value,omitempty"`
		NewValue   string `json:"new_value"`
		MXPriority int    `json:"mx_priority,omitempty"`
		Enabled    bool   `json:"enabled,omitempty"`
	}
	Message    string `json:"message"`
	Duration   int64  `json:"duration"`
	CreateTime int64  `json:"create_time"`
	UpdateTime int64  `json:"update_time"`
}

type TaskRecordResponse struct {
	ID         int64  `json:"id"`                    // 记录ID
	Name       string `json:"name"`                  // 主机记录
	Action     string `json:"action"`                // 操作类型
	TTL        int    `json:"ttl"`                   // TTL值
	OldValue   string `json:"old_value,omitempty"`   // 原记录值，更新操作时使用
	NewValue   string `json:"new_value"`             // 新记录值
	MXPriority int    `json:"mx_priority,omitempty"` // MX优先级
	Enabled    bool   `json:"enabled,omitempty"`     // 是否启用
}

type TaskServerResponse struct {
	ID     int64  `json:"id"`
	IP     string `json:"ip"`
	Name   string `json:"name"`
	Role   string `json:"role"`
	Status string `json:"status"`
}

type TaskResponse struct {
	ID           int64                 `json:"id"`
	Status       string                `json:"status"`
	TaskRecords  []*TaskRecordResponse `json:"task_records"`
	TaskServers  []*TaskServerResponse `json:"task_servers"`
	Operator     string                `json:"operator"`
	ExecuteTime  int64                 `json:"execute_time"`
	TotalDetails int                   `json:"total_details"`
	CreateTime   int64                 `json:"create_time"` // 创建时间
	UpdateTime   int64                 `json:"update_time"` // 更新时间
	TaskDetails  []*TaskDetailResponse `json:"task_details"`
}

func NewTaskResponse() *TaskResponse {
	return &TaskResponse{
		TaskRecords: make([]*TaskRecordResponse, 0),
		TaskServers: make([]*TaskServerResponse, 0),
		TaskDetails: make([]*TaskDetailResponse, 0),
	}
}

type TaskListResponse struct {
	Total    int64           `json:"total"`
	Page     int64           `json:"page"`
	PageSize int64           `json:"page_size"`
	TaskList []*TaskResponse `json:"task_list"`
}

func NewTaskListResponse() *TaskListResponse {
	return &TaskListResponse{
		TaskList: make([]*TaskResponse, 0),
	}
}
