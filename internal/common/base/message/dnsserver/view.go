package dnsserver

type QueryViewListMessage struct {
	ID            int64    `json:"id"`
	Code          string   `json:"code"`
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	ACLs          []string `json:"acls" gorm:"column:acls"`                     // view对应的ACL
	SecretControl bool     `json:"secret_control" gorm:"column:secret_control"` // 是否开启密钥控制
	ViewKeyAlgo   string   `json:"view_key_algo" gorm:"column:view_key_algo"`   // view对应的key算法
	ParentID      int64    `json:"parent_id"`
	Level         int      `json:"level"`
	LevelType     string   `json:"level_type"`
	CreateTime    int64    `json:"create_time"`
	UpdateTime    int64    `json:"update_time"`
	Deleted       bool     `json:"deleted"`
}

type QueryViewDetailMessage struct {
	ID            int64    `json:"id"`
	Code          string   `json:"code"`
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	ACLs          []string `json:"acls" gorm:"column:acls"`                     // view对应的ACL
	SecretControl bool     `json:"secret_control" gorm:"column:secret_control"` // 是否开启密钥控制
	ViewKeyAlgo   string   `json:"view_key_algo" gorm:"column:view_key_algo"`   // view对应的key算法
	ParentID      int64    `json:"parent_id"`
	Level         int      `json:"level"`
	LevelType     string   `json:"level_type"`
	CreateTime    int64    `json:"create_time"`
	UpdateTime    int64    `json:"update_time"`
	Deleted       bool     `json:"deleted"`
}