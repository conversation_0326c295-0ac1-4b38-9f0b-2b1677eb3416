package dnsserver

type QueryRecordListMessage struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Fqdn        string `json:"fqdn"`
	Description string `json:"description"`
	Creator     string `json:"creator"`
	ViewID      int64  `json:"view_id"`
	ViewName    string `json:"view_name"`
	Value       string `json:"value,omitempty"`
	CreatedAt   int64  `json:"created_at"`
	UpdatedAt   int64  `json:"updated_at"`
}

type QueryRecordListResponse struct {
	Total    int64                     `json:"total"`
	Page     int64                     `json:"page"`
	PageSize int64                     `json:"page_size"`
	Items    []*QueryRecordListMessage `json:"items"`
}

func NewQueryRecordListResponse() *QueryRecordListResponse {
	return &QueryRecordListResponse{Items: make([]*QueryRecordListMessage, 0)}
}

type QueryRecordListInViewRecordBaseInfo struct {
	RecordID  int64  `json:"record_id"`
	Value     string `json:"value"`
	Creator   string `json:"creator"`
	Owner     string `json:"owner"`
	CreatedAt int64  `json:"created_at"`
	UpdatedAt int64  `json:"updated_at"`
}

type QueryRecordListInViewMessage struct {
	Name        string                                `json:"name"`
	Fqdn        string                                `json:"fqdn"`
	Description string                                `json:"description"`
	ViewID      int64                                 `json:"view_id"`
	ViewName    string                                `json:"view_name"`
	RType       string                                `json:"r_type"`
	Value       []QueryRecordListInViewRecordBaseInfo `json:"value,omitempty"`
}

type QueryRecordListInViewResponse struct {
	Total    int64                           `json:"total"`
	Page     int64                           `json:"page"`
	PageSize int64                           `json:"page_size"`
	Items    []*QueryRecordListInViewMessage `json:"items"`
}

func NewQueryRecordListInViewResponse() *QueryRecordListInViewResponse {
	return &QueryRecordListInViewResponse{Items: make([]*QueryRecordListInViewMessage, 0)}
}
