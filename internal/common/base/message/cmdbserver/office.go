package message

type QueryOfficeMessage struct {
	ID          string `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	OfficeType  string `json:"office_type"`
	Address     string `json:"address"`
	HostByIDC   bool   `json:"host_by_idc"`
	Status      string `json:"status"`
	CreateTime  string `json:"create_time"`
	UpdateTime  string `json:"update_time"`
}

type QueryOfficeListMessage []QueryOfficeMessage

func (q *QueryOfficeListMessage) Add(office *QueryOfficeMessage) {
	*q = append(*q, *office)
}

func NewQueryOfficeListMessage() QueryOfficeListMessage {
	return make(QueryOfficeListMessage, 0)
}