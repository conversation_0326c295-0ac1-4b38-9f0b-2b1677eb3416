package zabbix

import (
	"encoding/json"
	"errors"
	"log"
	"net/http"

	zbx "ks-knoc-server/internal/common/base/model/monitor/zabbix"
	"ks-knoc-server/internal/common/rest"

	"github.com/spf13/viper"
)

var (
	ErrHostNotFound = errors.New("未找到对应的主机")
	ErrHostMultiple = errors.New("找到多个主机")
)

func NewZabbixRestClient() *rest.Request {
	// Zabbix 的 API 是基于 JSON-RPC 2.0 的，所以请求结构体相对固定
	// 所以请求结构体相对固定，不一样的大多是Params
	// 注意请求头，Content-Type 是 application/json，或者 application/json-rpc
	return rest.NewClient(
		viper.GetString("zabbix.host"), "/zabbix/api_jsonrpc.php").
		Post().
		WithScheme(rest.SchemeHTTP).
		WithHeaders(http.Header{"Content-Type": []string{"application/json"}})
}

// ZabbixCommonRequest 结构体相对固定，不一样的大多是Params
type ZabbixCommonRequest struct {
	Jsonrpc string `json:"jsonrpc"`
	Method  string `json:"method"`
	Id      int    `json:"id"`
	Auth    any    `json:"auth"`
	Params  any    `json:"params"`
}

// NewZabbixCommonRequest 创建一个ZabbixCommonRequest
func NewZabbixCommonRequest(method string, auth, params any) *ZabbixCommonRequest {
	return &ZabbixCommonRequest{
		Jsonrpc: "2.0",
		Method:  method,
		Id:      1,
		Auth:    auth,
		Params:  params,
	}
}

// ZabbixResponse 是zabbix的通用响应结构体
type ZabbixResponse struct {
	Jsonrpc string `json:"jsonrpc"`
	Result  any    `json:"result"`
	Id      int    `json:"id"`
}

// HostGet 获取zabbix主机
func HostGet(token string, params map[string]any) (*ZabbixResponse, error) {
	reqBody := NewZabbixCommonRequest("host.get", token, params)
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return nil, err
	}
	return &resp, nil
}

// HostCreate 创建zabbix主机
// 创建一台主机需要什么？针对网络设备，主要需要：template， groups，host（名称），interfaces（接口， with macros）
// inventory_mode 0 表示手动, 使用默认值0
func HostCreate(token string, params map[string]any) (*ZabbixResponse, error) {
	reqBody := NewZabbixCommonRequest("host.create", token, params)
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return nil, err
	}
	return &resp, nil
}

// HostGroupGet 获取zabbix主机组
func HostGroupGet(token string, params map[string]any) (*ZabbixResponse, error) {
	reqBody := NewZabbixCommonRequest("hostgroup.get", token, params)
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return nil, err
	}
	return &resp, nil
}

// ProxyGet 获取zabbix代理
func ProxyGet(token string, params map[string]any) (*ZabbixResponse, error) {
	reqBody := NewZabbixCommonRequest("proxy.get", token, params)
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return nil, err
	}
	return &resp, nil
}

// GetZabbixHostByName 获取zabbix主机, 通过名称获取，由于目前主机名称不可以重复，所以返回结果是唯一的
func GetZabbixHostByName(token, hostname string) (*zbx.ZabbixHost, error) {
	reqBody := NewZabbixCommonRequest("host.get", token, map[string]any{
		"selectInterfaces": "extend",
		"filter": map[string]string{"host": hostname},
	})
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return nil, err
	}
	result, ok := resp.Result.([]any)
	if !ok {
		return nil, errors.New("断言response result失败")
	}
	if len(result) == 0 {
		return nil, ErrHostNotFound
	}
	if len(result) > 1 {
		return nil, ErrHostMultiple
	}
	hostBytes, err := json.Marshal(result[0])
	if err != nil {
		return nil, err
	}
	log.Println(string(hostBytes))
	host := zbx.ZabbixHost{}
	if err := json.Unmarshal(hostBytes, &host); err != nil {
		return nil, err
	}
	return &host, nil
}

// GetZabbixGroupByName 获取zabbix主机组, 通过名称获取，由于目前主机组名称不可以重复，所以返回结果是唯一的
func GetZabbixGroupByName(token, groupName string) (zbx.ZabbixHostGroup, error) {
	reqBody := NewZabbixCommonRequest("hostgroup.get", token, map[string]any{
		"output": "extend",
		"filter": map[string]string{"name": groupName},
	})
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return zbx.ZabbixHostGroup{}, err
	}
	result, ok := resp.Result.([]any)
	if !ok {
		return zbx.ZabbixHostGroup{}, errors.New("断言response result失败")
	}
	if len(result) == 0 {
		return zbx.ZabbixHostGroup{}, errors.New("未找到对应的主机组")
	}
	groupBytes, err := json.Marshal(result[0])
	if err != nil {
		return zbx.ZabbixHostGroup{}, err
	}
	group := zbx.ZabbixHostGroup{}
	if err := json.Unmarshal(groupBytes, &group); err != nil {
		return zbx.ZabbixHostGroup{}, err
	}
	return group, nil
}

func GetZabbixTemplateByName(token, templateName string) (zbx.ZabbixTemplate, error) {
	reqBody := NewZabbixCommonRequest("template.get", token, map[string]any{
		"output": "extend",
		"filter": map[string][]string{
			"name": {templateName},
		},
	})
	client := NewZabbixRestClient()
	var resp ZabbixResponse
	if err := client.Body(reqBody).
		WithScheme(rest.SchemeHTTP).Do().Into(&resp); err != nil {
		return zbx.ZabbixTemplate{}, err
	}
	result, ok := resp.Result.([]any)
	if !ok {
		return zbx.ZabbixTemplate{}, errors.New("断言response result失败")
	}
	if len(result) == 0 {
		return zbx.ZabbixTemplate{}, errors.New("未找到对应的模板")
	}
	templateBytes, err := json.Marshal(result[0])
	if err != nil {
		return zbx.ZabbixTemplate{}, err
	}
	template := zbx.ZabbixTemplate{}
	if err := json.Unmarshal(templateBytes, &template); err != nil {
		return zbx.ZabbixTemplate{}, err
	}
	return template, nil
}
