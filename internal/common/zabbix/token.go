package zabbix

import (
	"errors"
	"ks-knoc-server/pkg/utils"
	"log"

	"github.com/spf13/viper"
)

// GetZabbixToken 获取zabbix的token
// Zabbix版本：5.0
// 5.0版本目前仅仅支持账号密码登录的方式获取token
// API Token从6.0才开始引入，因此目前进支持账号密码方式
func GetZabbixToken() (token string, err error) {
	// 向zabbix请求token
	reqBody := map[string]any{
		"jsonrpc": "2.0",
		"method":  "user.login",
		"id":      1,
		"auth":    nil,
		"params": map[string]string{
			"user":     viper.GetString("zabbix.user"),
			"password": viper.GetString("zabbix.password"),
		},
	}
	// 创建rest客户端
	client := NewZabbixRestClient()
	// 发送请求
	var resp ZabbixResponse
	if err := client.Body(reqBody).Do().Into(&resp); err != nil {
		log.Fatalf("获取Zabbix Token失败, err: %v", err)
		return "", err
	}
	if resp.Result == nil {
		return "", errors.New("获取Zabbix Token失败")
	}
	// 将token赋值给全局变量
	token = utils.ToString(resp.Result)
	// 判断是否正常获取到token
	if token == "" {
		return "", errors.New("获取Zabbix Token失败")
	}
	return token, nil
}
