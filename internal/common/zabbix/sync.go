package zabbix

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type AsynqClient struct {
	c *asynq.Client
}

func InitAsynqClient() *AsynqClient {
	redisAddr := viper.GetString("redis.addr")
	if redisAddr == "" {
		zap.L().Error("redis.addr is empty")
		return nil
	}

	zap.L().Debug(fmt.Sprintf("init asynq redis, addr %s, db %d", redisAddr, viper.GetInt("redis.taskdb")))
	c := asynq.NewClient(
		asynq.RedisClientOpt{
			Addr:     viper.GetString("redis.addr"),
			DB:       viper.GetInt("redis.taskdb"),
			Password: viper.GetString("redis.password"),
		},
	)
	return &AsynqClient{c: c}
}

const (
	SyncTaskType = "zabbix_sync"
)

// SyncTask zabbix同步任务
type SyncTask struct {
	Hostname string `json:"hostname"`
	IP       string `json:"ip"`
	Action   string `json:"action"`
}

func NewZabbixSyncTask(hostname, ip, action string) (*asynq.Task, error) {
	// 创建一个zabbix同步任务, 参数接收bytes，如果不是字符串这种，需要进行json序列化转换成bytes
	payload, err := json.Marshal(SyncTask{Hostname: hostname, IP: ip, Action: action})
	if err != nil {
		return nil, err
	}
	task := asynq.NewTask(SyncTaskType, payload)
	return task, nil
}

func (a *AsynqClient) SubmitZabbixSyncTask(hostname, ip, action string) error {
	task, err := NewZabbixSyncTask(hostname, ip, action)
	if err != nil {
		return err
	}
	if _, err = a.c.Enqueue(task, asynq.Retention(720*time.Hour)); err != nil {
		zap.L().Error("Failed to enqueue zabbix sync task", zap.Error(err))
		return err
	}
	return nil
}
