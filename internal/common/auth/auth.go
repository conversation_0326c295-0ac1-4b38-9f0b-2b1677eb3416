package auth

import (
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func GetUsername(c *gin.Context) string {
	var currentUser string

	// 获取请求头
	amc := c.Request.Header.Get("SVC-AMC")
	if amc == "true" {
		u, exist := c.Get("username")
		if !exist {
			zap.L().Warn("从上下文获取username失败")
			return ""
		}
		currentUser = utils.ToString(u)
	} else {
		// 通过jwt认证的
		currentUser = utils.ToString(c.Keys["user"])
	}

	if currentUser == "" {
		zap.L().Warn("从上下文获取username成功, 但是Username为空")
		return ""
	}
	return currentUser
}
