package infra

import (
	"fmt"
	"log"
	"os"
	"time"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

const (
	SlowLogName = "knoc_process_slow"
)

type KDBOptions struct {
	BizDef                    string
	Cluster                   string
	TraceLog                  bool
	IgnoreRecordNotFoundError bool
}

func NewKDBOptions() *KDBOptions {
	return &KDBOptions{
		BizDef:                    viper.GetString("kdb.bizDef"),
		Cluster:                   viper.GetString("kdb.cluster"),
		TraceLog:                  viper.GetBool("kdb.traceLog"),
		IgnoreRecordNotFoundError: viper.GetBool("kdb.ignoreRecordNotFoundError"),
	}
}

func (k *KDBOptions) Init() (*kdb.Korm, error) {
	db, err := kdb.Open(kdb.Option{
		BizDef:                    k.BizDef,
		Cluster:                   k.Cluster,
		WithTraceLog:              k.<PERSON>,
		IgnoreRecordNotFoundError: k.IgnoreRecordNotFoundError,
	})
	zap.L().Debug("KdbCluterInfo", zap.String("biz", k.BizDef), zap.String("cluster", k.Cluster))
	if err != nil {
		zap.L().Fatal(err.Error())
		return nil, err
	}
	return db, nil
}

func (k *KDBOptions) setNewLogger(gConfig *gorm.Config) *gorm.Config {
	path := fmt.Sprintf("%s/%s/%s.log",
		viper.GetString("kdb.slowLogPath"),
		SlowLogName,
		time.Now().Format("2006-01-02"),
	)

	file, _ := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_APPEND, os.ModePerm)

	newLogger := logger.New(
		log.New(file, "\r\n", log.LstdFlags), logger.Config{
			SlowThreshold:             time.Millisecond * 300,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		})
	gConfig.Logger = newLogger
	return gConfig
}
