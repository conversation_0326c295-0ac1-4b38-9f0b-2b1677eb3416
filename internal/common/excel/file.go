// Package excel **/
/**
* @Author: chenhao29
* @Date: 2023/9/21 11:43
* @QQ: 1149558764
* @Email: i@umb.<NAME_EMAIL>
 */
package excel

import (
	"ks-knoc-server/pkg/utils"

	"github.com/xuri/excelize/v2"
)

// GetInitTemplateFile 获取初始化模板文件，它的功能是表一些固定的表头还有字段渲染进去，注意渲染的只是样式，没有填充数据
// sheetName 表名称
// tableSize 表格大小
func GetInitTemplateFile(sheetName string, tableSize int) (*excelize.File, error) {
	// 创建一个新的Excel文件
	file := excelize.NewFile()
	index, err := file.NewSheet(sheetName)
	if err != nil {
		return nil, err
	}

	// 初始化sheet名称
	file.SetActiveSheet(index)

	// 当NewSheet执行成功后，默认会创建一个sheet1，因此需要删除默认的sheet1，因为并不需要它
	if err = file.DeleteSheet("sheet1"); err != nil {
		return nil, err
	}

	// 设置初始化的表头
	if err := file.SetCellValue(sheetName, "A1", "字段名(请勿编辑)"); err != nil {
		return nil, err
	}
	if err := file.SetCellValue(sheetName, "A2", "字段类型(请勿编辑)"); err != nil {
		return nil, err
	}
	if err := file.SetCellValue(sheetName, "A3", "字段标识(请勿编辑)"); err != nil {
		return nil, err
	}
	if err := file.SetCellValue(sheetName, "A4", "示例(请勿编辑)"); err != nil {
		return nil, err
	}

	// 设置初始化的表的列宽, 接收起始列到结束列, 最后一个参数是宽度
	if err := file.SetColWidth(sheetName, "A", "A", 20); err != nil {
		return nil, err
	}

	// A列标题使用样式
	rowAStyleId, err := GetColATableStyle(file)
	if err := file.SetCellStyle(sheetName, "A1", "A4", rowAStyleId); err != nil {
		return nil, err
	}

	// 字段类型、字段标识使用样式
	tableStyleId, err := GetFieldTableColStyle(file)
	// 字段名使用样式
	fieldNameStyleId, err := GetFieldNameStyle(file)

	// 一共会有几列，这个取决于模型会有多少字段
	colNumber := 'B' + tableSize

	// 填充样式
	cell := utils.ConvertToColumnTitle(colNumber - 'A')

	// 填充B2到cell+"4", 假设说cell是I，那么就是B2到I4，其实是一个矩形区域
	if err := file.SetCellStyle(sheetName, "B2", cell+"4", tableStyleId); err != nil {
		return nil, err
	}
	// 这里填充的是一个横向区域从B1到cell+"1"
	if err := file.SetCellStyle(sheetName, "B1", cell+"1", fieldNameStyleId); err != nil {
		return nil, err
	}
	return file, err
}
