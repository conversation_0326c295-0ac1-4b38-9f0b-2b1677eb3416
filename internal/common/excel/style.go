// Package excel **/
/**
* @Author: chenhao29
* @Date: 2023/9/21 11:28
* @QQ: 1149558764
* @Email: i@umb.<NAME_EMAIL>
 */
package excel

import "github.com/xuri/excelize/v2"

var (
	// 列所用靠左对其且居中样式
	colLeftCenterAlignment = excelize.Alignment{
		Horizontal: "left",
		Vertical:   "center",
		WrapText:   true,
	}
	// 边框样式
	border = []excelize.Border{
		{Type: "top", Color: "D3D3D3", Style: 1},
		{Type: "bottom", Color: "D3D3D3", Style: 1},
		{Type: "left", Color: "D3D3D3", Style: 1},
		{Type: "right", Color: "D3D3D3", Style: 1},
	}
	// A列表标题单元格填充样式
	colATableFill = excelize.Fill{Type: "pattern", Color: []string{"#FFDEAD"}, Pattern: 1}
	// A列数据单元格背景填充样式
	colAIndexFill = excelize.Fill{Type: "pattern", Color: []string{"#FDF5E6"}, Pattern: 1}
	// 字段类型、字段标识单元格背景填充样式
	colAndRowFieldFill = excelize.Fill{Type: "pattern", Color: []string{"#B4EEB4"}, Pattern: 1}
	// 字段名行单元格填充样式
	rowFieldFill = excelize.Fill{Type: "pattern", Color: []string{"#66CDAA"}, Pattern: 1}
	// 标红文本样式
	fontRedStyle = excelize.Font{Bold: true, Color: "#FF0000", Size: 11}
)

// GetRichTextRedStyle 获取标红富文本样式
func GetRichTextRedStyle(text string) []excelize.RichTextRun {
	return []excelize.RichTextRun{
		{Text: text, Font: &fontRedStyle},
	}
}

// GetColAIndexStyle A列实例数据所用样式
func GetColAIndexStyle(file *excelize.File) (int, error) {
	colAIndexStyleId, err := file.NewStyle(&excelize.Style{
		Alignment: &colLeftCenterAlignment,
		Fill:      colAIndexFill,
		Border:    border,
	})
	return colAIndexStyleId, err
}

// GetFieldTableColStyle 字段类型、字段标识使用样式
func GetFieldTableColStyle(file *excelize.File) (int, error) {
	fieldStyleId, err := file.NewStyle(&excelize.Style{
		Fill:   colAndRowFieldFill,
		Border: border,
	})
	return fieldStyleId, err
}

// GetSampleColStyle 示例使用样式
func GetSampleColStyle(file *excelize.File) (int, error) {
	exampleStyleId, err := file.NewStyle(&excelize.Style{
		Alignment: &colLeftCenterAlignment,
		Fill:      colAndRowFieldFill,
		Border:    border,
	})
	return exampleStyleId, err
}

// GetFieldNameStyle 字段名行使用样式
func GetFieldNameStyle(file *excelize.File) (int, error) {
	fieldNameStyleId, err := file.NewStyle(&excelize.Style{
		Fill:   rowFieldFill,
		Border: border,
	})
	return fieldNameStyleId, err
}

// GetColATableStyle A列标题使用样式
func GetColATableStyle(file *excelize.File) (int, error) {
	rowAStyleId, err := file.NewStyle(&excelize.Style{
		Alignment: &colLeftCenterAlignment,
		Fill:      colATableFill,
		Border:    border,
	})
	return rowAStyleId, err
}

//
