package errno

import (
	"fmt"

	"ks-knoc-server/internal/common/i18n"
	"ks-knoc-server/internal/common/validate"

	"github.com/go-playground/validator/v10"
)

// Errno 错误的Code
type Errno struct {
	Code    int
	Message string
	*i18n.I18n
}

// Error ...
func (err Errno) Error() string {
	return err.Message
}

// Err 错误信息
type Err struct {
	Code    int
	Message string
	Err     string
	*i18n.I18n
}

// New 生成一个Err的指针
func New(errno *Errno, err error) *Err {
	return &Err{Code: errno.Code, Message: err.Error(), Err: err.<PERSON>rror(), I18n: errno.I18n}
}

// Add 追加Error Messages
func (err *Err) Add(message string) error {
	// 这里不可以直接用err.Message += " " + message，因为这里的err是一个指针
	// 然后如果错误重复触发的话，错误信息会越加越长
	err.Err = message
	return err
}

// Addf 按照指定格式添加错误信息
func (err *Err) Addf(format string, args ...interface{}) error {
	// 这里不可以直接用err.Message += " " + fmt.Sprintf(format, args...)，因为这里的err是一个指针
	// 然后如果错误重复触发的话，错误信息会越加越长
	err.Err = fmt.Sprintf(format, args...)
	return err
}

// Error 按照Err - code: xx, message: xx, error: xx的格式返回错误信息
func (err *Err) Error() string {
	return fmt.Sprintf("Err - code: %d, message: %s, error: %s", err.Code, err.Message, err.Err)
}

// DecodeErr ...
func DecodeErr(err error) (int, string, i18n.I18n) {
	if err == nil {
		return OK.Code, OK.Message, *OK.I18n
	}

	switch typed := err.(type) {
	case *Err:
		return typed.Code, typed.Err, *typed.I18n
	case *Errno:
		return typed.Code, typed.Message, *typed.I18n
	default:
	}

	return InternalServerError.Code, err.Error(), *InternalServerError.I18n
}

func GetErrMsg(err error) string {
	switch err.(type) {
	case validator.ValidationErrors:
		v, _ := validate.GetValidateOptions()
		return v.TranslateValidationErrors(err.(validator.ValidationErrors)).Error()
	case *Err:
		// 针对我们自定义的错误类型，直接返回错误信息
		return err.(*Err).Err
	default:
		// 针对其他类型的错误，直接返回错误信息
		return err.Error()
	}
}
