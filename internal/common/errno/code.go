package errno

import (
	"ks-knoc-server/internal/common/i18n"
)

var (
	ErrDataNotExists                  = &Err{Code: 20001, Message: "Data Not Exists.", I18n: i18n.ErrDataNotExists}
	ErrBuiltinObject                  = &Err{Code: 20002, Message: "Builtin Object, Can not Change.", I18n: i18n.ErrBuiltinObject}
	ErrDuplicateKey                   = &Err{Code: 20003, Message: "Duplicate Key.", I18n: i18n.ErrDuplicateKey}
	ErrDataDelete                     = &Err{Code: 20004, Message: "Data Delete Error.", I18n: i18n.ErrDataDelete}
	ErrModelCodeQuery                 = &Err{Code: 20005, Message: "No Model Code Query Error.", I18n: i18n.ErrModelCodeQuery}
	ErrDataCheck                      = &Err{Code: 20006, Message: "Data Check Error.", I18n: i18n.ErrDataCheck}
	ErrDataFieldUnEditable            = &Err{Code: 20007, Message: "Data Field UnEditable.", I18n: i18n.ErrDataFieldUnEditable}
	ErrExcelTemplateFile              = &Err{Code: 20008, Message: "Create Excel Template Failed.", I18n: i18n.ErrExcelTemplateError}
	ErrDataInvalid                    = &Err{Code: 20009, Message: "Data Invalid.", I18n: i18n.ErrDataInvalid}
	ErrInvalidPagination              = &Err{Code: 20010, Message: "Invalid Pagination.", I18n: i18n.ErrInvalidPagination}
	ErrModelAttrCreate                = &Err{Code: 20011, Message: "Model Attr Create Error.", I18n: i18n.ErrModelAttrCreate}
	ErrModelAttrUpdate                = &Err{Code: 20012, Message: "Model Attr Update Error.", I18n: i18n.ErrModelAttrUpdate}
	ErrModelAttrDelete                = &Err{Code: 20013, Message: "Model Attr Delete Error.", I18n: i18n.ErrModelAttrDelete}
	ErrModelIndexParseError           = &Err{Code: 20014, Message: "Model Data Parse Error.", I18n: i18n.ErrModelParseError}
	ErrModelDataDownloadError         = &Err{Code: 20015, Message: "Model Data Download Error.", I18n: i18n.ErrModelParseError}
	ErrModelNotBelongToModelGroup     = &Err{Code: 20016, Message: "Model Not Belong To Model Group.", I18n: i18n.ErrModelNotBelongToModelGroup}
	ErrFuzzySearchModelGroupCodeEmpty = &Err{Code: 20017, Message: "Fuzzy Search Model Group Code Empty.", I18n: i18n.ErrFuzzySearchModelGroupCodeEmpty}
	ErrModelNotFound                  = &Err{Code: 20018, Message: "Model Not Found.", I18n: i18n.ErrModelNotFound}
	ErrIdsEmpty                       = &Err{Code: 20018, Message: "Ids IsEmpty.", I18n: i18n.ErrIdsEmpty}
	ErrDecode                         = &Err{Code: 20019, Message: "Decode Error.", I18n: i18n.ErrDecode}
)

// 模型分组相关错误
var (
	// ErrUpdateModelGroup 更新模型属性分组错误
	ErrUpdateModelGroup = &Err{Code: 20500, Message: "Update Model Group Error.", I18n: i18n.ErrUpdateModelGroup}
)

// 模型相关错误
var (
	// ErrModelCodeNotExists 模型唯一标识不存在
	ErrModelCodeNotExists = &Err{Code: 21000, Message: "Model Code Not Exists.", I18n: i18n.ErrModelCodeNotExists}
)

// 模型属性分组相关错误
var (
	// ErrModelAttrGroupNameExist 模型属性分组名称已存在
	ErrModelAttrGroupNameExist     = &Err{Code: 21500, Message: "Model Attr Group Name Exists.", I18n: i18n.ErrModelAttrGroupNameExists}
	ErrModelAttrGroupNotExists     = &Err{Code: 21501, Message: "Model Attr Group Not Exists.", I18n: i18n.ErrModelAttrGroupNotExists}
	ErrModelAttrUniqueAtLeastOne   = &Err{Code: 21502, Message: "Model Attr Unique At Least One.", I18n: i18n.ErrModelAttrUniqueAtLeastOne}
	ErrModelAttrSelectInheritInUse = &Err{Code: 21503, Message: "Model Attr Select Inherit In Use.", I18n: i18n.ErrModelAttrSelectInheritInUse}
)

// 模型属性类型相关错误
var (
	// ErrModelAttrTypeInvalid 模型属性类型错误
	ErrModelAttrTypeInvalid         = &Err{Code: 22001, Message: "Model Type Invalid.", I18n: i18n.ErrModelAttrTypeInvalid}
	ErrModelDoNotHaveAttr           = &Err{Code: 22002, Message: "Model Do Not Have Attr.", I18n: i18n.ErrModelDoNotHaveAttr}
	ErrModelAttrNotExist            = &Err{Code: 22003, Message: "Model Attr Not Exist.", I18n: i18n.ErrModelAttrNotExist}
	ErrModelAttrNotTypeString       = &Err{Code: 22004, Message: "Model Attr Not Type String.", I18n: i18n.ErrModelAttrNotTypeString}
	ErrModelAttrNotTypeBool         = &Err{Code: 22005, Message: "Model Attr Not Type Bool.", I18n: i18n.ErrModelAttrNotTypeBool}
	ErrModelAttrNotTypeInt          = &Err{Code: 22006, Message: "Model Attr Not Type Int.", I18n: i18n.ErrModelAttrNotTypeInt}
	ErrModelAttrNotTypeFloat        = &Err{Code: 22007, Message: "Model Attr Not Type Float.", I18n: i18n.ErrModelAttrNotTypeFloat}
	ErrModelAttrNotTypeSelect       = &Err{Code: 22008, Message: "Model Attr Not Type Select.", I18n: i18n.ErrModelAttrNotTypeSelect}
	ErrModelAttrNotTypeTextArea     = &Err{Code: 22009, Message: "Model Attr Not Type TextArea.", I18n: i18n.ErrModelAttrNotTypeTextArea}
	ErrModelAttrNotTypeDate         = &Err{Code: 22010, Message: "Model Attr Not Type Date.", I18n: i18n.ErrModelAttrNotTypeDate}
	ErrModelAttrNotTypeDateTime     = &Err{Code: 22011, Message: "Model Attr Not Type DateTime.", I18n: i18n.ErrModelAttrNotTypeDateTime}
	ErrModelAttrNotTypeRelationship = &Err{Code: 22012, Message: "Model Attr Not Type Relationship.", I18n: i18n.ErrModelAttrNotTypeRelationship}
)

// 模型关系相关错误
var (
	// ErrModelSubordinateExists 模型从属关系已存在
	ErrModelSubordinateExists             = &Err{Code: 22500, Message: "Model Subordinate Exists.", I18n: i18n.ErrModelSubordinateExists}
	ErrModelSubordinateNotExists          = &Err{Code: 22501, Message: "Model Subordinate Not Exists.", I18n: i18n.ErrModelSubordinateNotExists}
	ErrSubordinateLoop                    = &Err{Code: 22502, Message: "Subordinate Loop.", I18n: i18n.ErrSubordinateLoop}
	ErrModelSubordinateInUse              = &Err{Code: 22503, Message: "Model Subordinate In Use.", I18n: i18n.ErrModelSubordinateInUse}
	ErrModelDataSubOrdinateRelationExists = &Err{Code: 22504, Message: "Model Data SubOrdinate Relation Exists.", I18n: i18n.ErrModelDataSubOrdinateRelationExists}
	ErrModelAssociateNameExist            = &Err{Code: 22505, Message: "Model Associate Name Exist.", I18n: i18n.ErrModelAssociateNameExist}
	ErrModelAssociateNotExists            = &Err{Code: 22506, Message: "Model Associate Not Exists.", I18n: i18n.ErrModelAssociateNotExists}
	ErrModelAssociateInUse                = &Err{Code: 22507, Message: "Model Associate In Use.", I18n: i18n.ErrModelAssociateInUse}
	ErrModelRelationType                  = &Err{Code: 22508, Message: "Model Relation Type Error.", I18n: i18n.ErrModelRelationType}
	ErrModelRelationNotExist              = &Err{Code: 22509, Message: "Model Relation Not Exist.", I18n: i18n.ErrModelRElationNotExist}
	ErrDataPathExists                     = &Err{Code: 22510, Message: "Data Path Exists.", I18n: i18n.ErrDataPathExists}
	ErrDataParentNotSet                   = &Err{Code: 22511, Message: "Data Parent Not Set.", I18n: i18n.ErrDataParentNotSet}
	ErrModelDataAssociateRelationExists   = &Err{Code: 22512, Message: "Model Data Associate Relation Exists.", I18n: i18n.ErrModelDataAssociateRelationExists}
	ErrModelRelationConflict              = &Err{Code: 22513, Message: "Model Relation Conflict.", I18n: i18n.ErrModelRelationConflict}
)

// 模型数据相关错误
var (
	// ErrModelDataMisMatch 模型数据与模型不匹配
	ErrModelDataMisMatch = &Err{Code: 23000, Message: "Model And Data MisMatch.", I18n: i18n.ErrModelDataMisMatch}
)

// ES相关错误
var (
	// ErrPutESMapping ES映射更新失败
	ErrPutESMapping = &Err{Code: 23501, Message: "Put ES Mapping Error.", I18n: i18n.ErrPutESMapping}
)

// 物理视图相关错误
var (
	// ErrRackPositionConflict 机柜位置冲突
	ErrRackPositionConflict = &Err{Code: 24001, Message: "Rack Position Conflict.", I18n: i18n.ErrRackPositionConflict}
	ErrDeviceHeightEmpty    = &Err{Code: 24002, Message: "Device Height Empty.", I18n: i18n.ErrDeviceHeightEmpty}
	ErrRackPositionError    = &Err{Code: 24003, Message: "Rack Position Error.", I18n: i18n.ErrRackPositionError}
	ErrRoomRackPosition     = &Err{Code: 24004, Message: "Room Rack Position Error.", I18n: i18n.ErrRoomRackPosition}
	ErrRackPositionNotSet   = &Err{Code: 24005, Message: "Rack Position Not Set.", I18n: i18n.ErrRackPositionNotSet}
	ErrRackPositionInvalid  = &Err{Code: 24006, Message: "Rack Position Invalid.", I18n: i18n.ErrRackPositionInvalid}
	ErrDeviceHeightInvalid  = &Err{Code: 24007, Message: "Device Height Invalid.", I18n: i18n.ErrDeviceHeightInvalid}
)

// 标签相关错误
var (
	// ErrLabelExists 标签已存在
	ErrLabelExists             = &Err{Code: 24500, Message: "Label Exists.", I18n: i18n.ErrLabelExists}
	ErrLabelBindingExceedLimit = &Err{Code: 24501, Message: "Label Binding Exceed Limit.", I18n: i18n.ErrLabelBindingExceedLimit}
)
