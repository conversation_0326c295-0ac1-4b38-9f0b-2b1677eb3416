package errno

import "ks-knoc-server/internal/common/i18n"

var (
	OK                   = &Err{Code: 0, Message: "OK", I18n: i18n.OK}
	InternalServerError  = &Err{Code: 10001, Message: "Internal server error.", I18n: i18n.InternalServerError}
	ErrBind              = &Err{Code: 10002, Message: "Error occurred while binding the request body to the struct.", I18n: i18n.ErrBind}
	ErrHeaderFormat      = &Err{Code: 10003, Message: "Authorization header format is wrong.", I18n: i18n.ErrHeaderFormat}
	ErrAuthorization     = &Err{Code: 10004, Message: "unrecognized Authorization header.", I18n: i18n.ErrAuthorization}
	ErrJsonMarshal       = &Err{Code: 10005, Message: "json marshal error.", I18n: i18n.ErrJsonMarshal}
	ErrJsonUnmarshal     = &Err{Code: 10006, Message: "json unmarshal error.", I18n: i18n.ErrJsonUnmarshal}
	ErrParameterRequired = &Err{Code: 10007, Message: "Parameter is required.", I18n: i18n.ErrParameterRequired}
	ErrParameterInvalid  = &Err{Code: 10008, Message: "Parameter is invalid.", I18n: i18n.ErrParameterInvalid}
)
