package requests

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"go.uber.org/zap"
)

type Request struct {
	URL         string
	Headers     map[string]string
	Params      map[string]string
	Body        interface{}
	Empowerment interface{}
}

type HTTPClient struct {
	req *http.Client
}

func NewTransPort() *http.Transport {
	transport := &http.Transport{
		MaxIdleConns:       1024,
		IdleConnTimeout:    120,
		DisableCompression: true,
		TLSClientConfig:    &tls.Config{InsecureSkipVerify: true},
	}

	return transport
}

func (r *HTTPClient) SplicingFullURL(protocol, host, uri string, port int) string {
	return fmt.Sprintf("%s://%s:%d%s", protocol, host, port, uri)
}

func (r *HTTPClient) addHeaders(req *http.Request, headers map[string]string) {
	if headers == nil {
		return
	}

	for k, v := range headers {
		req.Header.Add(k, v)
	}

	if host := req.Header.Get("Host"); host != "" {
		req.Host = host
		req.Header.Del("Host")
	}
}

func (r *HTTPClient) addParams(req *http.Request, params map[string]string) {
	if params == nil {
		return
	}

	q := req.URL.Query()
	for k, v := range params {
		q.Add(k, v)
	}

	req.URL.RawQuery = q.Encode()
}

func (r *HTTPClient) jsonConversion(body interface{}) (*bytes.Buffer, error) {
	if body == nil {
		return nil, nil
	}
	jsonStdout, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	return bytes.NewBuffer(jsonStdout), nil
}

func (r *HTTPClient) mixin(url, method string, headers, params map[string]string, body interface{}) (*http.Request, error) {
	b, _ := r.jsonConversion(body)
	var req *http.Request
	var err error

	if b == nil {
		req, err = http.NewRequest(method, url, nil)
	} else {
		req, err = http.NewRequest(method, url, b)
	}

	r.addHeaders(req, headers)
	r.addParams(req, params)
	return req, err
}

func (r *HTTPClient) do(obj *http.Request, dst interface{}) error {
	resp, err := r.req.Do(obj)
	if err != nil {
		return err
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	body, err := ioutil.ReadAll(resp.Body)
	zap.L().Debug("http response",
		zap.ByteString("body", body),
		zap.Int("status", resp.StatusCode),
		zap.String("url", obj.URL.String()),
		zap.String("method", obj.Method),
	)
	if err != nil {
		return err
	}
	return r.parser(body, dst)
}

// TODO: 解析json, 这里其实有一个问题，就是如果解析失败，返回的就是unmarshal error的报错，但是看不到实际的message
func (r *HTTPClient) parser(src []byte, dst interface{}) error {
	if err := json.Unmarshal(src, &dst); err != nil {
		return err
	}
	return nil
}

func (r *HTTPClient) GET(obj *Request) error {
	req, _ := r.mixin(obj.URL, "GET", obj.Headers, obj.Params, obj.Body)
	return r.do(req, obj.Empowerment)
}

func (r *HTTPClient) POST(obj *Request) error {
	req, _ := r.mixin(obj.URL, "POST", obj.Headers, obj.Params, obj.Body)
	return r.do(req, obj.Empowerment)
}

func (r *HTTPClient) DELETE(obj *Request) error {
	req, _ := r.mixin(obj.URL, "DELETE", obj.Headers, obj.Params, obj.Body)
	return r.do(req, obj.Empowerment)
}

func (r *HTTPClient) PUT(obj *Request) error {
	req, _ := r.mixin(obj.URL, "PUT", obj.Headers, obj.Params, obj.Body)
	return r.do(req, obj.Empowerment)
}

func NewHTTPClient() *HTTPClient {
	return &HTTPClient{
		req: &http.Client{
			Transport: NewTransPort(),
		},
	}
}
