package audit

import (
	"time"

	"ks-knoc-server/pkg/mapdata"
)

// EventType 事件类型
type EventType string

// 目前设计两种事件类型
const (
	// AuditLogEvent 表示数据的操作日志
	AuditLogEvent EventType = "audit"
	// OperationEvent 表示数据的操作事件, 与流程相关
	OperationEvent EventType = "operation"
	// ScriptEvent 表示由脚本触发的更新内容
	ScriptEvent EventType = "script"
	// MonitorEvent 表示监控事件
	MonitorEvent EventType = "monitor"
)

// CmdbEvent 定义CMDB事件通用接口
type CmdbEvent interface {
	Generate(evt EventType, resourceType ResourceType, actionType ActionType,
		operator, uniqueId string, src, dst mapdata.MapData) (*AuditLog, error)
	Save() error
}

type ResourceType string

func (r ResourceType) String() string {
	return string(r)
}

const (
	ModelRes                   ResourceType = "model"                     // 模型
	ModelAttributeRes          ResourceType = "model_attribute"           // 模型字段
	ModelGroupRes              ResourceType = "model_group"               // 模型分组
	ModelAttributesGroupRes    ResourceType = "model_attribute_group"     // 模型属性分组
	ResourceDetailRes          ResourceType = "resource_detail"           // 资源详情
	ModelRelationshipSubRes    ResourceType = "model_relationship_sub"    // 模型从属关系
	ModelRelationshipRelRes    ResourceType = "model_relationship_rel"    // 模型关联关系
	ResourceRelationshipSubRes ResourceType = "resource_relationship_sub" // 资源从属关系
	ResourceRelationshipRelRes ResourceType = "resource_relationship_rel" // 资源关联关系
)

var ResourceTypeMap = map[ResourceType]string{
	ModelRes:                   "模型",
	ModelAttributeRes:          "模型字段",
	ModelGroupRes:              "模型分组",
	ModelAttributesGroupRes:    "模型属性分组",
	ResourceDetailRes:          "资源详情",
	ModelRelationshipSubRes:    "模型从属关系",
	ModelRelationshipRelRes:    "模型关联关系",
	ResourceRelationshipSubRes: "资源从属关系",
	ResourceRelationshipRelRes: "资源关联关系",
}

// ActionType defines all the user's operation type
type ActionType string

func (a ActionType) String() string {
	return string(a)
}

const (
	ActionCreate ActionType = "create"
	ActionUpdate ActionType = "update"
	ActionDelete ActionType = "delete"
)

var actionTypeMap = map[ActionType]string{
	ActionCreate: "新增",
	ActionUpdate: "变更",
	ActionDelete: "删除",
}

type AuditLog struct {
	ID           int64        `json:"id" bson:"id"`                       // 审计日志唯一标识
	EventType    EventType    `json:"event_type" bson:"event_type"`       // 事件类型
	ResourceType ResourceType `json:"resource_type" bson:"resource_type"` // 资源类型
	Action       ActionType   `json:"action" bson:"action"`               // 动作，常见的动作也就是增删改
	Operator     string       `json:"operator" bson:"operator"`           // 操作人
	Operation    string       `bson:"operation"`                          // 操作内容
	UniqueId     string       `bson:"unique_id"`                          // 要操作的资源的id。
	ModelCode    string       `bson:"model_code" bson:"model_code"`       // 模型的Code
	Ts           int64        `bson:"ts"`                                 // 时间戳
}

// NewAuditLog 初始化审计日志
func NewAuditLog(evt EventType, resourceType ResourceType, actionType ActionType,
	operator, uniqueId string) *AuditLog {
	return &AuditLog{
		EventType:    evt,
		ResourceType: resourceType,
		Action:       actionType,
		Operator:     operator,
		Ts:           time.Now().Unix(),
		UniqueId:     uniqueId,
	}
}

func (a *AuditLog) GetEventType() string {
	return string(a.EventType)
}
