package validate

import (
	"errors"
	"reflect"
	"strings"
	"sync"

	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/zh"
	enTranslations "github.com/go-playground/validator/v10/translations/en"
	zhTranslations "github.com/go-playground/validator/v10/translations/zh"

	"github.com/gin-gonic/gin/binding"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
)

var (
	customValidator *ValidateOptions
	once            sync.Once
)

// ValidateOptions TODO
type ValidateOptions struct {
	ValidObj *validator.Validate
	ZhTrans  ut.Translator
	EnTrans  ut.Translator
}

// registerTranslation 为自定义字段，添加翻译功能
func registerTranslator(tag string, msg string) validator.RegisterTranslationsFunc {
	return func(trans ut.Translator) error {
		if err := trans.Add(tag, msg, false); err != nil {
			return err
		}
		return nil
	}
}

// translationFunc 自定义翻译函数
func translate(trans ut.Translator, fe validator.FieldError) string {
	t, err := trans.T(fe.Tag(), fe.Field())
	if err != nil {
		return fe.(error).Error()
	}
	return t
}

func (v *ValidateOptions) TranslateValidationErrors(ve validator.ValidationErrors) error {
	res := ""
	for _, err := range ve.Translate(v.ZhTrans) {
		res += err + "\n "
	}
	return errors.New(res)
}

// InitTranslator 初始化翻译器
func (v *ValidateOptions) InitTranslator() (err error) {
	var ok bool
	v.ValidObj, ok = binding.Validator.Engine().(*validator.Validate)
	if ok {
		v.ValidObj.RegisterTagNameFunc(func(fld reflect.StructField) string {
			name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			if name == "-" {
				return ""
			}
			return name
		})
		zhT := zh.New()              // 中文翻译器
		enT := en.New()              // 英文翻译器
		uni := ut.New(enT, zhT, enT) // 通用的翻译器

		v.ZhTrans, ok = uni.GetTranslator("zh")
		if !ok {
			return errors.New("uni.GetTranslator(zh) failed")
		}
		v.EnTrans, ok = uni.GetTranslator("en")
		if !ok {
			return errors.New("uni.GetTranslator(en) failed")
		}

		// 注册翻译器
		err = enTranslations.RegisterDefaultTranslations(v.ValidObj, v.EnTrans)
		if err != nil {
			return
		}
		err = zhTranslations.RegisterDefaultTranslations(v.ValidObj, v.ZhTrans)
		if err != nil {
			return
		}
	}
	return
}

func (v *ValidateOptions) CustomValidatorRegister(validatorList []CustomValidator) (err error) {
	for _, m := range validatorList {
		err = v.ValidObj.RegisterValidation(m.TagName, m.FuncName)
		if err != nil {
			return
		}
		// 第一个参数是tag, 第二个参数是trans ut.Translator,第三个参数是registerTranslationFunc,第四个参数是translationFunc
		err = v.ValidObj.RegisterTranslation(m.TagName, v.ZhTrans, registerTranslator(m.TagName, m.ZhMessage), translate)
		if err != nil {
			return
		}

		err = v.ValidObj.RegisterTranslation(m.TagName, v.EnTrans, registerTranslator(m.TagName, m.EnMessage), translate)
		if err != nil {
			return
		}
	}
	return
}

// GetValidateOptions 获取验证器的实例
func GetValidateOptions() (*ValidateOptions, error) {
	var err error
	once.Do(func() {
		customValidator = &ValidateOptions{}
		err = customValidator.InitTranslator()
	})
	if err != nil {
		return nil, err
	}
	return customValidator, nil
}
