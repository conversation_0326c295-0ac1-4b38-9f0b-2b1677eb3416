package validate

import (
	"regexp"
	"strconv"
	"sync"

	"github.com/go-playground/validator/v10"
)

var (
	modelAttrValidate     *ModelAttrValidate
	attrOnce              sync.Once
	commonCustomValidator *CommonCustomValidator
	commonCustomOnce      sync.Once
)

// CustomValidator 自定义校验器
type CustomValidator struct {
	TagName   string
	FuncName  validator.Func
	ZhMessage string
	EnMessage string
}

func NewCustomValidator(tagName string, funcName validator.Func, zhMessage string, enMessage string) CustomValidator {
	return CustomValidator{
		TagName:   tagName,
		FuncName:  funcName,
		ZhMessage: zhMessage,
		EnMessage: enMessage,
	}
}

type RegValidate interface {
	RegisterValidator() (err error)
}

// CommonCustomValidator 通用的自定义校验器
type CommonCustomValidator struct {
	vl []CustomValidator
}

func NewCommonCustomValidator() *CommonCustomValidator {
	commonCustomOnce.Do(func() {
		commonCustomValidator = &CommonCustomValidator{
			vl: make([]CustomValidator, 0),
		}
	})
	return commonCustomValidator
}

// RegisterValidator 注册自定义校验器
func (c *CommonCustomValidator) RegisterValidator() (err error) {
	v, err := GetValidateOptions()
	if err != nil {
		return
	}
	err = v.CustomValidatorRegister(c.vl)
	if err != nil {
		return err
	}
	return nil
}

// AddCustomValidator 添加自定义校验器
func (c *CommonCustomValidator) AddCustomValidator(v CustomValidator) {
	c.vl = append(c.vl, v)
}

type ModelAttrValidate struct {
	RuleRe     string
	TypeString string
}

func (m *ModelAttrValidate) GetRuleRe() string {
	return m.RuleRe
}

func (m *ModelAttrValidate) SetRuleRe(ruleRe string) {
	m.RuleRe = ruleRe
}

func (m *ModelAttrValidate) GetTypeString() string {
	return m.TypeString
}

func (m *ModelAttrValidate) SetTypeString(typeName string) {
	m.TypeString = typeName
}

// CheckRule 检查模型属性字段中的Rule规则是否合规，按照PRD规则，如果说正则不为空的话，那么用户提示和示例不可以为空
func (m *ModelAttrValidate) CheckRule(fl validator.FieldLevel) bool {
	ruleRe := m.GetRuleRe()
	if ruleRe != "" {
		if fl.Field().String() == "" {
			return false
		}
	}
	return true
}

// CheckRuleRe 检测用户传递的Sample是否匹配正则规则
func (m *ModelAttrValidate) CheckRuleRe(fl validator.FieldLevel) bool {
	// 如果说正则不为空的话，就必须校验sample是否合规
	ruleRe := m.GetRuleRe()

	if ruleRe != "" {
		var re *regexp.Regexp
		// Panic: regexp: Compile(`^[\u4e00-\u9fa5]{0,}$`): error parsing regexp: invalid escape sequence: `\u`
		if m.GetTypeString() == "string" || m.GetTypeString() == "textarea" {
			var (
				pattern string
				err     error
			)
			pattern, err = strconv.Unquote(`"` + ruleRe + `"`)
			// 解析失败，说明没有转义字符，会报错invalid syntax，需要兼容一下这个错误， err类型为*errors.errorString
			if err != nil {
				pattern = ruleRe
			}
			re = regexp.MustCompile(pattern)
		} else {
			re = regexp.MustCompile(ruleRe)
		}

		sample := fl.Field().String()
		return re.MatchString(sample)
	}

	return true
}

func (m *ModelAttrValidate) RegisterValidator() (err error) {
	vl := []CustomValidator{
		{
			TagName:   "CheckRule",
			FuncName:  m.CheckRule,
			ZhMessage: "当正则规则不为空时，{0} 也不可以为空",
			EnMessage: "When the regular rule is not empty, {0} cannot be empty",
		},
		{
			TagName:   "CheckRuleRe",
			FuncName:  m.CheckRuleRe,
			ZhMessage: "{0} 不符合正则规则",
			EnMessage: "{0} does not conform to the rules",
		},
	}
	v, err := GetValidateOptions()
	if err != nil {
		return
	}
	err = v.CustomValidatorRegister(vl)
	if err != nil {
		return err
	}
	return nil
}

func NewModelAttrValidate() *ModelAttrValidate {
	attrOnce.Do(func() {
		modelAttrValidate = &ModelAttrValidate{}
	})
	return modelAttrValidate
}

func InitValidator() (err error) {
	_, err = GetValidateOptions()
	if err != nil {
		return
	}

	// TODO: 这里是否有什么更好的统一加载的方式
	ccValidator := NewCommonCustomValidator()
	ccValidator.AddCustomValidator(NewCustomValidator(
		"CheckCode",
		CheckCode,
		"唯一标识不合规，要求必须以英文字母开头，后面可以是英文字母、数字、下划线的组合",
		"{0} does not conform to the rules of the unique identifier"))
	ccValidator.AddCustomValidator(NewCustomValidator(
		"CheckStartU",
		CheckStartU,
		"起始U位不合规，要求必须是0~42的数字",
		"The starting U is not compliant and must be a number from 0 to 42"))
	ccValidator.AddCustomValidator(NewCustomValidator(
		"MaxLength512",
		MaxLength512,
		"{0} 超过最大字符限制，长度不得超过512",
		"{0} Exceeding the maximum character limit, the length must not exceed 512"))

	regs := []RegValidate{
		NewModelAttrValidate(),
		NewCommonCustomValidator(),
	}
	for _, reg := range regs {
		err = reg.RegisterValidator()
		if err != nil {
			return
		}
	}
	return
}
