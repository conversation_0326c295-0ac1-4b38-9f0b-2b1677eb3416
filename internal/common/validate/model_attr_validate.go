package validate

import (
	"regexp"

	"github.com/go-playground/validator/v10"
)

// CheckCode 检测Code是否合规，合规的code必须是字母、数字、下划线组成，首字母必须是字母
func CheckCode(fl validator.FieldLevel) bool {
	regexPattern := "^[a-zA-Z][a-zA-Z0-9_]*$"
	re := regexp.MustCompile(regexPattern)
	codeString := fl.Field().String()
	if re.MatchString(codeString) {
		return true
	}
	return false
}

func MaxLength512(fl validator.FieldLevel) bool {
	codeString := fl.Field().String()
	if len(codeString) <= 512 {
		return true
	}
	return false
}
