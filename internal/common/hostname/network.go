package hostname

import (
	"errors"
	"fmt"
	"html/template"
	"os"
	"regexp"
	"strings"
)

const (
	COMPANY = "K"
)

type Region string

const (
	RegionBeiJing       Region = "BJ"
	RegionTianJin       Region = "TJ"
	RegionWuLan         Region = "WL"
	RegionLuoYang       Region = "LY"
	RegionSuiNing       Region = "SN"
	RegionWuXi          Region = "WX"
	RegionHangzhou      Region = "HZ"
	RegionGuangZhou     Region = "GZ"
	RegionShenZhen      Region = "SZ"
	RegionXiangXiJiShou Region = "XXJS"
	RegionWuHan         Region = "WH"
	RegionSuzhou        Region = "SUZ"
	RegionHaErBin       Region = "HRB"
	RegionChengDu       Region = "CD"
	RegionTongRen       Region = "TR"
	RegionChengMai      Region = "CM"
	RegionHuaiAn        Region = "HA"
	RegionShangHai      Region = "SH"
	RegionXiangGang     Region = "HK"
	RegionBrazil        Region = "BR"
	RegionIndonesia     Region = "IDN"
	RegionSingapore     Region = "SGP"
	RegionCS            Region = "CS"
	RegionDL            Region = "DL"
)

var RegionNameMapping = map[Region]string{
	RegionBeiJing:       "北京",
	RegionTianJin:       "天津",
	RegionWuLan:         "乌兰察布",
	RegionLuoYang:       "洛阳",
	RegionSuiNing:       "遂宁",
	RegionWuXi:          "无锡",
	RegionHangzhou:      "杭州",
	RegionGuangZhou:     "广州",
	RegionShenZhen:      "深圳",
	RegionXiangXiJiShou: "湘西",
	RegionWuHan:         "武汉",
	RegionSuzhou:        "苏州",
	RegionHaErBin:       "哈尔滨",
	RegionChengDu:       "成都",
	RegionTongRen:       "铜仁",
	RegionChengMai:      "海南",
	RegionHuaiAn:        "淮安",
	RegionShangHai:      "上海",
	RegionXiangGang:     "香港",
	RegionBrazil:        "巴西",
	RegionIndonesia:     "印尼",
	RegionSingapore:     "新加坡",
	RegionCS:            "长沙",
	RegionDL:            "大连",
}

type Building string

const (
	BuildingY1     Building = "Y1"
	BuildingY2     Building = "Y2"
	BuildingY3     Building = "Y3"
	BuildingY8     Building = "Y8"
	BuildingY9     Building = "Y9"
	BuildingY10    Building = "Y10"
	BuildingY11    Building = "Y11"
	BuildingY12    Building = "Y12"
	BuildingY13    Building = "Y13"
	BuildingT10    Building = "T10"
	BuildingZL     Building = "ZL"
	BuildingWDDL   Building = "WDDL"
	BuildingXM     Building = "XM"
	BuildingWLF2   Building = "WLF2"
	BuildingM7     Building = "M7"
	BuildingB15    Building = "B15"
	BuildingXCS    Building = "XCS"
	BuildingLM     Building = "LM"
	BuildingHLZX   Building = "HLZX"
	BuildingJH     Building = "JH"
	BuildingHL     Building = "HL"
	BuildingEFC    Building = "EFC"
	BuildingHJ     Building = "HJ"
	BuildingXY     Building = "XY"
	BuildingGDPY   Building = "GDPY"
	BuildingBDDS   Building = "BDDS"
	BuildingJRDS   Building = "JRDS"
	BuildingYL     Building = "YL"
	BuildingRC     Building = "RC"
	BuildingDJ     Building = "DJ"
	BuildingCYY    Building = "CYY"
	BuildingXDF    Building = "XDF"
	BuildingXC     Building = "XC"
	BuildingHSK    Building = "HSK"
	BuildingWS     Building = "WS"
	BuildingWK     Building = "WK"
	BuildingHZ     Building = "HZ"
	BuildingXL     Building = "XL"
	BuildingTKO    Building = "TKO"
	BuildingGJ     Building = "GJ"
	BuildingSP     Building = "SP"
	BuildingJKT    Building = "JKT"
	BuildingFLZX   Building = "FLZX"
	BuildingWework Building = "Wework"
	BuildingWXSD   Building = "WXSD"
	BuildingYJGJ   Building = "YJGJ"
)

var BuildingNameMapping = map[Building]string{
	BuildingY1:     "元中心",
	BuildingY2:     "元中心",
	BuildingY3:     "元中心",
	BuildingY8:     "元中心",
	BuildingY9:     "元中心",
	BuildingY10:    "元中心",
	BuildingY11:    "元中心",
	BuildingY12:    "元中心",
	BuildingY13:    "元中心",
	BuildingT10:    "万家灯火",
	BuildingZL:     "中黎科技园",
	BuildingWDDL:   "五栋大楼",
	BuildingXM:     "小米机房",
	BuildingWLF2:   "乌兰察布F2园区",
	BuildingM7:     "M7机房",
	BuildingB15:    "赛尔机房",
	BuildingXCS:    "新城市中心",
	BuildingLM:     "蕾茗职场",
	BuildingHLZX:   "互连众信职场",
	BuildingJH:     "金慧职场",
	BuildingHL:     "恒隆职场",
	BuildingEFC:    "EFC职场",
	BuildingHJ:     "火炬职场",
	BuildingXY:     "星耀职场",
	BuildingGDPY:   "广电平云职场",
	BuildingBDDS:   "百度国际大厦",
	BuildingJRDS:   "金融大厦",
	BuildingYL:     "印力",
	BuildingRC:     "融创智谷",
	BuildingDJ:     "电竞馆",
	BuildingCYY:    "产业园",
	BuildingXDF:    "新天地",
	BuildingXC:     "新川",
	BuildingHSK:    "海思科",
	BuildingWS:     "万山产业园",
	BuildingWK:     "沃克公园",
	BuildingHZ:     "洪泽",
	BuildingXL:     "星联",
	BuildingTKO:    "将军澳机房",
	BuildingGJ:     "国金中心",
	BuildingSP:     "圣保罗",
	BuildingJKT:    "雅加达",
	BuildingFLZX:   "富力中心",
	BuildingWework: "上海广场",
	BuildingWXSD:   "万象时代",
	BuildingYJGJ:   "裕景国际",
}

type Floor string

const (
	FloorB Floor = "B"
	FloorF Floor = "F"
)

var FloorNameMapping = map[Floor]string{
	FloorB: "地下",
	FloorF: "地上",
}

type PatchRoom string

const (
	PatchRoomA PatchRoom = "A"
	PatchRoomJ PatchRoom = "J"
	PatchRoomU PatchRoom = "U"
)

var PatchRoomNameMapping = map[PatchRoom]string{
	PatchRoomA: "同楼层接入机房",
	PatchRoomJ: "核心机房",
	PatchRoomU: "超核机房",
}

type AccessArea string

const (
	AccessAreaSU  AccessArea = "SU"
	AccessAreaIDC AccessArea = "IDC"
	AccessAreaNET AccessArea = "NET"
	AccessAreaNAT AccessArea = "NAT"
	AccessAreaBA  AccessArea = "BA"
	AccessAreaBR  AccessArea = "BR"
	AccessAreaOS  AccessArea = "OS"
	AccessAreaL   AccessArea = "L"
	AccessAreaW   AccessArea = "W"
	AccessAreaIOT AccessArea = "IOT"
	AccessAreaS   AccessArea = "S"
	AccessAreaNM  AccessArea = "NM"
	AccessAreaNA  AccessArea = "NA"
	AccessAreaWA  AccessArea = "WA"
	AccessAreaTP  AccessArea = "TP"
	AccessAreaVPN AccessArea = "VPN"
)

var AccessAreaNameMapping = map[AccessArea]string{
	AccessAreaSU:  "超级核心",
	AccessAreaIDC: "IDC互联",
	AccessAreaNET: "互联网",
	AccessAreaNAT: "对外服务",
	AccessAreaBA:  "京区",
	AccessAreaBR:  "京外",
	AccessAreaOS:  "海外",
	AccessAreaL:   "有线",
	AccessAreaW:   "无线",
	AccessAreaIOT: "IOT",
	AccessAreaS:   "服务区",
	AccessAreaNM:  "网络管理",
	AccessAreaNA:  "网络应用",
	AccessAreaWA:  "无线集中区域",
	AccessAreaTP:  "第三方区域",
	AccessAreaVPN: "VPN",
}

type AccessLevel string

const (
	AccessLevelCO AccessLevel = "CO" // Core Layer
	AccessLevelEX AccessLevel = "EX" // External Layer
	AccessLevelDI AccessLevel = "DI" // Distribution Layer
	AccessLevelAC AccessLevel = "AC" // Access Layer
)

var AccessLevelNameMapping = map[AccessLevel]string{
	AccessLevelCO: "核心层",
	AccessLevelEX: "边缘层",
	AccessLevelDI: "汇聚层",
	AccessLevelAC: "接入层",
}

type AccessType string

const (
	AccessTypeNS  AccessType = "NS"
	AccessTypeNR  AccessType = "NR"
	AccessTypePS  AccessType = "PS"
	AccessTypeTE  AccessType = "TE"
	AccessTypeWC  AccessType = "WC"
	AccessTypeIT  AccessType = "IT"
	AccessTypeXZ  AccessType = "XZ"
	AccessTypeAF  AccessType = "AF"
	AccessTypeAP  AccessType = "AP"
	AccessTypeSV  AccessType = "SV"
	AccessTypeIV  AccessType = "IV"
	AccessTypeFW  AccessType = "FW"
	AccessTypeIS  AccessType = "IS"
	AccessTypeBC  AccessType = "BC"
	AccessTypeILO AccessType = "ILO"
	AccessTypeLB  AccessType = "LB"
	AccessTypeSD  AccessType = "SD"
	AccessTypeTOR AccessType = "TOR"
)

var AccessTypeNameMapping = map[AccessType]string{
	AccessTypeNS:  "普通交换机",
	AccessTypeNR:  "路由器",
	AccessTypePS:  "POE交换机",
	AccessTypeTE:  "传输设备",
	AccessTypeWC:  "无线控制器",
	AccessTypeIT:  "IOTIT接入交换机",
	AccessTypeXZ:  "IOT行政接入交换机",
	AccessTypeAF:  "安防交换机",
	AccessTypeAP:  "无线AP",
	AccessTypeSV:  "SSLVPN设备",
	AccessTypeIV:  "IPSecVPN设备",
	AccessTypeFW:  "防火墙",
	AccessTypeIS:  "ISE认证",
	AccessTypeBC:  "行为管理",
	AccessTypeILO: "管理接入交换机",
	AccessTypeLB:  "负载均衡设备",
	AccessTypeSD:  "SDWAN设备",
	AccessTypeTOR: "TOR交换机",
}

type DeviceManufacturer string

const (
	ManufacturerCisco     DeviceManufacturer = "C"
	ManufacturerAruba     DeviceManufacturer = "A"
	ManufacturerHuawei    DeviceManufacturer = "W"
	ManufacturerH3C       DeviceManufacturer = "H"
	ManufacturerRuiJie    DeviceManufacturer = "R"
	ManufacturerPA        DeviceManufacturer = "P"
	ManufacturerFortinet  DeviceManufacturer = "F"
	ManufacturerHillStone DeviceManufacturer = "S"
	ManufacturerSangFor   DeviceManufacturer = "X"
	ManufacturerTopSec    DeviceManufacturer = "T"
	ManufacturerQiAnXin   DeviceManufacturer = "Q"
	ManufacturerA10       DeviceManufacturer = "L"
	ManufacturerVmware    DeviceManufacturer = "V"
	ManufacturerFiberHome DeviceManufacturer = "E"
	ManufacturerAcceLink  DeviceManufacturer = "G"
	ManufacturerKuaiShou  DeviceManufacturer = "K"
)

var DeviceCodeNameMapping = map[DeviceManufacturer]string{
	ManufacturerCisco:     "Cisco",
	ManufacturerAruba:     "Aruba",
	ManufacturerHuawei:    "华为",
	ManufacturerH3C:       "H3C",
	ManufacturerRuiJie:    "锐捷",
	ManufacturerPA:        "PA",
	ManufacturerFortinet:  "飞塔",
	ManufacturerHillStone: "山石",
	ManufacturerSangFor:   "深信服",
	ManufacturerTopSec:    "天融信",
	ManufacturerQiAnXin:   "奇安信",
	ManufacturerA10:       "A10",
	ManufacturerVmware:    "VMware",
	ManufacturerFiberHome: "烽火",
	ManufacturerAcceLink:  "光迅",
	ManufacturerKuaiShou:  "快手自研",
}

type Hostname struct {
	CompanyID    string
	Region       string
	Building     string
	Location     string
	Floor        string
	PatchRoom    string
	AccessArea   string
	AccessLevel  string
	AccessType   string
	Manufacturer string
	Model        string
	SerialNumber string
	Custom       string
	originName   string
}

func (h *Hostname) Name() string {
	return h.originName
}

type AP struct {
	Region       string
	Building     string
	Floor        string
	SerialNumber string
}

var patternSegment = []struct {
	name    string
	pattern string
}{
	{
		name:    "公司标识",
		pattern: `(?P<CompanyID>[K]{1})-`, // aaaa-, 目前公司标识恒为K-
	},
	{
		name:    "区域",
		pattern: `(?P<Region>[A-Z]+)[\._]?`, // bb. 比如BJ.
	},
	{
		name:    "楼宇",
		pattern: `(?P<Building>[a-zA-Z0-9]+)[\._]??(?P<Location>[0-9]{2})?-`, // cc.[dd]-, 比如Y13, 由于[dd]是可选项，包含dd的比如EFC.06
	},
	{
		name:    "楼层",
		pattern: `(?P<Floor>[BF]{1}[0-9]{1,3})(?P<PatchRoom>[A-Z]?)-`, // e[f]-, 比如F2U,标识地上2层超核机房
	},
	{
		name:    "接入区域",
		pattern: `(?P<AccessArea>[A-Z]+)[\._]?`, // g. 比如SU.
	},
	{
		name:    "接入层次",
		pattern: `(?P<AccessLevel>[A-Z]{2})[\._]?`, // h. 比如CO.
	},
	{
		name:    "接入类型",
		pattern: `(?P<AccessType>[A-Z]+)-`, // i- 比如NS-
	},
	{
		name:    "设备厂家",
		pattern: `(?P<Manufacturer>[A-Z]{1})(?P<Model>[A-Z0-9]+)-`, // jk- 比如H12580-
	},
	{
		name:    "设备序号+自定义位",
		pattern: `(?P<SerialNumber>[0-9]{1,3})(?:\-)?(?:-(?P<Custom>.*))?`, // l[-m]
	},
}

// DebugHostname 调试主机名, 返回主机名不合规的部分
func DebugHostname(hostname string) error {
	currentPattern := ""
	for _, segment := range patternSegment {
		currentPattern += segment.pattern
		fullPattern := `^` + currentPattern

		// 初始化正则表达式
		re, err := regexp.Compile(fullPattern)
		if err != nil {
			fmt.Println("构建正则表达式失败:", err)
			return err
		}

		if !re.MatchString(hostname) {
			hint := ""
			switch segment.name {
			case "公司标识":
				hint = "公司标识不合规, 恒为K-"
			case "区域":
				hint = "区域不合规"
			case "楼宇":
				hint = "楼宇不合规"
			case "楼层":
				hint = "楼层不合规"
			case "接入区域":
				hint = "接入区域不合规"
			case "接入层次":
				hint = "接入层次不合规"
			case "接入类型":
				hint = "接入类型不合规"
			case "设备厂家":
				hint = "设备厂家不合规"
			case "设备序号+自定义位":
				hint = "设备序号和自定义位不合规"
			}
			return fmt.Errorf("主机名解析失败 %s, 失败阶段: %s, 失败提示: %s", hostname, segment.name, hint)
		}
	}

	return nil
}

// IsValidHostname 检查主机名格式是否合法
// 参考文档：https://docs.corp.kuaishou.com/k/home/<USER>/fcAD_IU0PCVhXrCi8l4D3vxEG#section=vodka.rb2vtm8hy5ut
func IsValidHostname(hostname string) (*Hostname, error) {
	// 以分组的形式匹配, 参考值：K-BJ.Y13-F2U-BA.CO.NS-H12508-01
	pattern := `^` + // 主机名全称为: aaaa-bb.cc.[d]-e[f]-g.h.i-jk-l[-m]
		`(?P<CompanyID>[K]{1})-` + // aaaa-, 目前公司标识恒为K-
		`(?P<Region>[A-Z]+)[\._]?` + // bb. 比如BJ.
		`(?P<Building>[a-zA-Z0-9]+)[\._]??(?P<Location>[0-9]{2})?-` + // cc.[dd]-, 比如Y13, 由于[dd]是可选项，包含dd的比如EFC.06
		`(?P<Floor>[BF]{1}[0-9]{1,3})(?P<PatchRoom>[A-Z]?)-` + // e[f]-, 比如F2U,标识地上2层超核机房
		`(?P<AccessArea>[A-Z]+)[\._]?` + // g. 比如SU.
		`(?P<AccessLevel>[A-Z]{2})[\._]?` + // h. 比如CO.
		`(?P<AccessType>[A-Z]+)-` + // i- 比如NS-
		`(?P<Manufacturer>[A-Z]{1})(?P<Model>[A-Z0-9]+)-` + // jk- 比如H12580-
		`(?P<SerialNumber>[0-9]{1,3})(?:\-)?(?:-(?P<Custom>.*))?` + // l[-m]
		`$`
	// 初始化正则表达式
	re := regexp.MustCompile(pattern)
	// 匹配主机名
	match := re.FindStringSubmatch(hostname)
	// 查看匹配结果
	if match != nil {
		matchMap := make(map[string]string)
		for i, name := range re.SubexpNames() {
			if i == 0 {
				continue
			}
			matchMap[name] = match[i]
		}
		h := &Hostname{
			CompanyID:    matchMap["CompanyID"],
			Region:       matchMap["Region"],
			Building:     matchMap["Building"],
			Location:     matchMap["Location"],
			Floor:        matchMap["Floor"],
			PatchRoom:    matchMap["PatchRoom"],
			AccessArea:   matchMap["AccessArea"],
			AccessLevel:  matchMap["AccessLevel"],
			AccessType:   matchMap["AccessType"],
			Manufacturer: matchMap["Manufacturer"],
			Model:        matchMap["Model"],
			SerialNumber: matchMap["SerialNumber"],
			Custom:       matchMap["Custom"],
			originName:   hostname,
		}

		if err := h.Validate(); err != nil {
			return nil, err
		}

		return h, nil
	}

	// 看看是不是AP的设备, 参考值: BJ.Y13-F2-WAP-001
	apPattern := `(?P<Region>[A-Z]+)\.` + // 楼宇
		`(?P<Building>[a-zA-Z0-9]+)-` + // 楼号
		`(?P<Floor>[BF]{1}[0-9]{1,3})-` + // 楼层
		`WAP-` + // AP设备类型
		`(?P<SerialNumber>[0-9]{1,3})` // 设备序号
	apRe := regexp.MustCompile(apPattern)
	apMatch := apRe.FindStringSubmatch(hostname)
	if apMatch != nil {
		apMatchMap := make(map[string]string)
		for i, name := range apRe.SubexpNames() {
			if i == 0 {
				continue
			}
			apMatchMap[name] = apMatch[i]
		}
		h := &Hostname{
			Region:       apMatchMap["Region"],
			Building:     apMatchMap["Building"],
			Floor:        apMatchMap["Floor"],
			AccessType:   string(AccessTypeAP),
			SerialNumber: apMatchMap["SerialNumber"],
		}

		if err := h.Validate(); err != nil {
			return nil, err
		}

		return h, nil
	}

	return nil, errors.New("主机名解析失败")
}

func (h *Hostname) Show() {
	// 格式化输出
	showTmplate := `
公司标识: {{.CompanyID}}
地域名: {{.Region}}
楼宇缩写: {{.Building}}
位置标识: {{.Location}}
楼层位置: {{.Floor}}
配线间位置: {{.PatchRoom}}
接入区域: {{.AccessArea}}
接入层次: {{.AccessLevel}}
接入类型: {{.AccessType}}
设备厂家: {{.Manufacturer}}
设备型号: {{.Model}}
序号: {{.SerialNumber}}
自定义: {{.Custom}}
	`

	tmpl, err := template.New("show").Parse(showTmplate)
	if err != nil {
		fmt.Println("Error parsing template:", err)
		return
	}

	tmpl.Execute(os.Stdout, h)
}

// Validate 验证主机名是否合法
func (h *Hostname) Validate() error {
	if _, exist := RegionNameMapping[Region(h.Region)]; !exist {
		return errors.New("地域名错误, 请检查")
	}
	if _, exist := BuildingNameMapping[Building(h.Building)]; !exist {
		return errors.New("楼宇字段不存在，可能原因为主机名错误或楼宇信息未及时更新，请检查")
	}
	// 楼层字段验证
	floorParts := strings.Split(h.Floor, "")
	if _, exist := FloorNameMapping[Floor(floorParts[0])]; !exist {
		return errors.New("楼层字段错误, 请检查, 参考值: B, F, 实际值: " + h.Floor)
	}

	// 验证设备的序号
	if h.SerialNumber == "" {
		return errors.New("设备序号字段为空")
	}

	// 验证设备是否为AP，如果不是AP，则需要验证其他字段，否则可以直接结束了
	if h.AccessType != string(AccessTypeAP) {
		if h.CompanyID != COMPANY {
			return errors.New("公司标识错误, 目前恒定为K")
		}
		if h.PatchRoom != "" {
			if _, exist := PatchRoomNameMapping[PatchRoom(h.PatchRoom)]; !exist {
				return errors.New("配线间位置字段错误, 请检查, 参考值: A, J, U, 实际值: " + h.PatchRoom)
			}
		}
		if _, exist := AccessAreaNameMapping[AccessArea(h.AccessArea)]; !exist {
			return errors.New("接入区域字段错误, 请检查, 参考值: SU, IDC, NET, NAT, BA, BR, OS, L, W, IOT, S, NM, NA, WA, TP, VPN, 实际值: " + h.AccessArea)
		}
		if _, exist := AccessLevelNameMapping[AccessLevel(h.AccessLevel)]; !exist {
			return errors.New("接入层次字段错误, 请检查, 参考值: CO, EX, DI, AC, 实际值: " + h.AccessLevel)
		}
		if _, exist := AccessTypeNameMapping[AccessType(h.AccessType)]; !exist {
			return errors.New("接入类型字段错误, 请检查, 参考值: NS, NR, PS, TE, WC, IT, XZ, AF, AP, SV, IV, FW, IS, BC, ILO, LB, SD, TOR, 实际值: " + h.AccessType)
		}
		if _, exist := DeviceCodeNameMapping[DeviceManufacturer(h.Manufacturer)]; !exist {
			return errors.New("设备厂家字段错误, 请检查, 参考值: C, A, W, H, R, P, F, S, X, T, Q, L, V, E, G, K, 实际值: " + h.Manufacturer)
		}
		if h.Model == "" {
			return errors.New("设备型号字段为空")
		}
	}

	return nil
}

func (h *Hostname) GetCompanyID() string {
	return h.CompanyID
}

func (h *Hostname) GetRegion() string {
	return h.Region
}

func (h *Hostname) GetBuilding() string {
	return h.Building
}

func (h *Hostname) GetLocation() string {
	return h.Location
}

func (h *Hostname) GetFloor() string {
	return h.Floor
}

func (h *Hostname) GetPatchRoom() string {
	return h.PatchRoom
}

func (h *Hostname) GetAccessArea() string {
	return h.AccessArea
}

func (h *Hostname) GetAccessLevel() string {
	return h.AccessLevel
}

func (h *Hostname) GetAccessType() string {
	return h.AccessType
}

func (h *Hostname) GetManufacturer() string {
	return h.Manufacturer
}

func (h *Hostname) GetModel() string {
	return h.Model
}

func (h *Hostname) GetSerialNumber() string {
	return h.SerialNumber
}

func (h *Hostname) GetCustom() string {
	return h.Custom
}

// ConcatZabbixGroup 拼接zabbix主机组
func (h *Hostname) ConcatZabbixGroup() string {
	region, ok := RegionNameMapping[Region(h.Region)]
	if !ok {
		return ""
	}
	building, ok := BuildingNameMapping[Building(h.Building)]
	if !ok {
		return ""
	}

	if building == string(BuildingHSK) {
		// 当是海思科职场的时候，还要根据location来看到底是审核还是客服职场
		if h.Location == "06" {
			building += "客服职场"
		} else if h.Location == "07" {
			building += "审核职场"
		}
	}
	accessLevel, ok := AccessLevelNameMapping[AccessLevel(h.AccessLevel)]
	if !ok {
		return ""
	}
	groupName := fmt.Sprintf("%s-%s-%s", region, building, accessLevel)
	return groupName
}

type ZabbixTemplate struct {
	AccessType   string
	AccessLayer  string
	Manufacturer string
	Model        string
}

func (t *ZabbixTemplate) Name() string {
	return fmt.Sprintf("T_%s_%s_%s_%s", t.AccessType, t.Manufacturer, t.Model, t.AccessLayer)
}

// GetZabbixTemplate 获取zabbix模板
func (h *Hostname) GetZabbixTemplate() string {
	tpl := ZabbixTemplate{
		AccessType:   h.AccessType,
		AccessLayer:  h.AccessLevel,
		Manufacturer: h.Manufacturer,
		Model:        h.Model,
	}

	return tpl.Name()
}
