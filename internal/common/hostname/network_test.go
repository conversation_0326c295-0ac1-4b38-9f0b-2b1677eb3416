package hostname

import (
	"testing"
)

func TestIsValidHostname(t *testing.T) {
	tests := []struct {
		name     string
		hostname string
		wantErr  bool
	}{
		{
			name:     "Valid hostname 1",
			hostname: "K-BJ.Y13-F2U-SU.CO.NS-H12508-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 2",
			hostname: "K-BJ.Y13-F2U-BA.CO.NS-H12508-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 3",
			hostname: "K-BJ.Y13-F2J-L.AC.NS-H5130-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 4",
			hostname: "K-BJ.Y13-F2-L.DI.NS-H6520-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 5",
			hostname: "K-BJ.Y13-F2J-W.CO.WC-A7220-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 6",
			hostname: "K-BJ.Y13-F2-L.AC.NS-H5130-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 7",
			hostname: "K-BJ.Y13-F2-W.AC.PS-A2930-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 8",
			hostname: "K-BJ.Y13-F2-IOT.AC.IT-H5130-01",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 9",
			hostname: "BJ.Y13-F2-WAP-001",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 10",
			hostname: "K-BJ.XM.03-F201-S.AC.TOR-W6865-01-U0.D07D09.101.254",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 11",
			hostname: "K-BJ.XM.03-F201-S.AC.TOR-W6865-01-U0.D07D09.101.254",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 12",
			hostname: "K-WL.WLF2.01-F207-S.AC.TOR-H6850-01-U1.A17.32.254",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 13",
			hostname: "K-WL.WLF2.01-F207-S.AC.TOR-H6850-01-U2.A18.32.254",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 14",
			hostname: "K-BJ.M7-F303-S.AC.TOR-H6800-06-U1.A0204.137.62",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 15",
			hostname: "K-BJ.M7-F303-S.AC.TOR-H6800-06-U2.A0206.137.62",
			wantErr:  false,
		},
		{
			name:     "Valid hostname 16",
			hostname: "K-HZ_XY_07-F6J-NET_EX_FW-F2201-01",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Log(tt.hostname)
			h, err := IsValidHostname(tt.hostname)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsValidHostname() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			h.Show()
			// t.Log(h.ConcatZabbixGroup())
		})
	}
}