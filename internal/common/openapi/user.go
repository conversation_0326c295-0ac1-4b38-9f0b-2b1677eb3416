package openapi

import (
	"fmt"

	"ks-knoc-server/internal/common/requests"
)

type UserInfo struct {
	Name        string `json:"name"`                   // 人员姓名
	Username    string `json:"username"`               // 用户名
	Number      string `json:"number"`                 // 员工工号
	Email       string `json:"email"`                  // 主邮箱
	Photo       string `json:"photo"`                  // 人员头像
	DisplayName string `json:"display_name,omitempty"` // 部门全名称
	Department  struct {
		Code string `json:"code"` // 部门code
		Name string `json:"name"` // 部门名称
	} `json:"department"`
}

type GetUserInfoByIdResponse struct {
	BaseOpenAPIResponse
	UserInfo UserInfo `json:"result"`
}

func GetUserInfoByUserID(userID string) (*GetUserInfoByIdResponse, error) {
	var resp GetUserInfoByIdResponse
	req := &requests.Request{
		URL:         fmt.Sprintf("/mdata/v1/persons/%s/base", userID),
		Empowerment: &resp,
		Params:      map[string]string{"ifOutsourcing": "true"},
	}
	if err := NewOpenapi().Get(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return &resp, fmt.Errorf("GetUserInfoByUserID failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return &resp, nil
}

func FuzzySearchUser(params map[string]string) ([]UserInfo, error) {
	resp := make([]UserInfo, 0)
	// 只查询有效用户
	params["only_active"] = "true"
	req := &requests.Request{
		URL:         "/mdata/v1/persons/-/simple",
		Empowerment: &resp,
		Params:      params,
	}
	// 这个openapi的接口很坑，不返回code和message，直接就返回result结果，所以就不需要判断code了
	// 如果查不到，result就是一个空的Object
	if err := NewOpenapi().Get(req); err != nil {
		return nil, err
	}
	return resp, nil
}

type UserInfoWithStatus struct {
	Name        string `json:"name"`                   // 人员姓名
	Number      string `json:"number"`                 // 员工工号
	Username    string `json:"username"`               // 用户名
	Email       string `json:"email"`                  // 主邮箱
	Photo       string `json:"photo"`                  // 人员头像
	DisplayName string `json:"display_name,omitempty"` // 部门全名称
	Status      struct {
		Code string `json:"code"` // 状态编号
		Name string `json:"name"` // 状态名称
	} `json:"status"`
}

func GetAllUserInfo() ([]UserInfoWithStatus, error) {
	var resp []UserInfoWithStatus
	req := &requests.Request{
		URL:         "/mdata/v1/persons",
		Empowerment: &resp,
		Params: map[string]string{
			"only_active":   "false",
			"ifOutsourcing": "true",
			"ifSpecial":     "true",
		},
	}
	if err := NewOpenapi().Get(req); err != nil {
		return nil, err
	}
	return resp, nil
}
