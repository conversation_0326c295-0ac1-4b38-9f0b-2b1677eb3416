package openapi

import (
	"testing"

	"ks-knoc-server/config/knoc"
)

func TestGetUserInfo(t *testing.T) {
	if err := knoc.InitConfig(); err != nil {
		t.<PERSON>rror(err)
	}
	if result, err := GetUserInfoByUserID("67544"); err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	} else {
		t.Log(result)
	}
}

func TestFuzzSearchUser(t *testing.T) {
	if err := knoc.InitConfig(); err != nil {
		t.Error(err)
	}
	params := map[string]string{"name": "志超"}
	result, err := FuzzySearchUser(params)
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
		return
	}
	t.Log(result)
}
