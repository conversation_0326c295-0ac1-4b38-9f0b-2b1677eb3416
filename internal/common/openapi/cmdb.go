package openapi

import (
	"fmt"

	apiV1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	cmdbMessage "ks-knoc-server/internal/common/base/message/cmdbserver"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/requests"
	"ks-knoc-server/pkg/mapdata"
)

type CreateModelDataRequest struct {
	ModelCode     string          `json:"model_code" binding:"required"`
	IdentifyName  string          `json:"identify_name" binding:"required"`
	IdentifyValue string          `json:"identify_value" binding:"required"`
	InputType     int             `json:"input_type"`
	Data          mapdata.MapData `json:"data" binding:"required"`
	ParentID      string          `json:"parent_id"`
}

// CreateOrUpdateModelDataResponse 创建或更新CMDB数据响应
type CreateOrUpdateModelDataResponse struct {
	Code    int          `json:"code"`
	Message string       `json:"message"`
	Data    v1.ModelData `json:"data"`
	I18N    struct {
		ZhCN string `json:"zhCN"`
		EnUS string `json:"enUS"`
	} `json:"i18n"`
}

// CreateCMDBData 创建CMDB数据
func CreateCMDBData(data *CreateModelDataRequest) error {
	var resp CreateOrUpdateModelDataResponse
	req := &requests.Request{
		URL:         "/cmdb-openapi/api/v1/model_datas",
		Empowerment: &resp,
		Body:        data,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return err
	}
	if resp.Code != 0 {
		return fmt.Errorf("创建cmdb数据失败, code: %d, message: %s", resp.Code, resp.Message)
	}
	return nil
}

// UpdateCMDBDataRequest 更新CMDB数据请求
type UpdateCMDBDataRequest struct {
	ID            string          `json:"id" binding:"required"`
	ModelCode     string          `json:"model_code" binding:"required"`
	Data          mapdata.MapData `json:"data" binding:"required"`
	IdentifyName  string          `json:"identify_name" binding:"required"`
	IdentifyValue string          `json:"identify_value" binding:"required"`
	InputType     int             `json:"input_type"`
}

// UpdateCMDBData 更新CMDB数据
func UpdateCMDBData(data *UpdateCMDBDataRequest) error {
	var resp CreateOrUpdateModelDataResponse
	req := &requests.Request{
		URL:         "/cmdb-openapi/api/v1/datas",
		Empowerment: &resp,
		Body:        data,
	}
	if err := NewOpenapi().Update(req); err != nil {
		return err
	}
	if resp.Code != 0 {
		return fmt.Errorf("更新CMDB数据失败, code: %d, message: %s", resp.Code, resp.Message)
	}
	return nil
}

// GetDeviceBySNResponse 根据SN查询设备
type GetDeviceBySNResponse struct {
	Code    int                        `json:"code"`
	Message string                     `json:"message"`
	TraceID string                     `json:"trace_id"`
	Data    []apiV1.DeviceInfoResponse `json:"data"`
	I18N    struct {
		ZhCN string `json:"zhCN"`
		EnUS string `json:"enUS"`
	} `json:"i18n"`
}

// GetDeviceBySN 根据SN查询设备
func GetDeviceBySN(sn []string) ([]apiV1.DeviceInfoResponse, error) {
	var resp GetDeviceBySNResponse
	req := &requests.Request{
		URL:         "/cmdb-openapi/api/v1/device/infos",
		Empowerment: &resp,
		Body:        map[string]any{"sn": sn},
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return nil, fmt.Errorf("根据SN获取设备信息失败, code: %d, message: %s", resp.Code, resp.Message)
	}
	return resp.Data, nil
}

type GetCMDBModelDataByFilterResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TraceID string `json:"trace_id"`
	Data    struct {
		Total int            `json:"total"`
		Data  []v1.ModelData `json:"data"`
	} `json:"data"`
	I18N struct {
		ZhCN string `json:"zhCN"`
		EnUS string `json:"enUS"`
	} `json:"i18n"`
}

func GetCMDBModelDataByFilter(filter map[string]interface{}) ([]v1.ModelData, error) {
	var resp GetCMDBModelDataByFilterResponse
	req := &requests.Request{
		URL:         "/cmdb-openapi/api/v1/model_data_by_filter",
		Empowerment: &resp,
		Body:        filter,
	}
	if err := NewOpenapi().Post(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return nil, fmt.Errorf("get cmdb model data by filter failed, code: %d, message: %s", resp.Code, resp.Message)
	}
	return resp.Data.Data, nil
}

type BaseResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TraceID string `json:"trace_id"`
	I18N    struct {
		ZhCN string `json:"zhCN"`
		EnUS string `json:"enUS"`
	} `json:"i18n"`
}

type GetOfficesResponse struct {
	BaseResponse
	Data cmdbMessage.QueryOfficeListMessage `json:"data"`
}

func GetOffices() (*GetOfficesResponse, error) {
	resp := &GetOfficesResponse{}
	req := &requests.Request{
		URL:         "/cmdb-openapi/api/v1/offices",
		Empowerment: &resp,
	}
	if err := NewOpenapi().Get(req); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return nil, fmt.Errorf("获取职场列表失败, code: %d, message: %s", resp.Code, resp.Message)
	}
	return resp, nil
}
