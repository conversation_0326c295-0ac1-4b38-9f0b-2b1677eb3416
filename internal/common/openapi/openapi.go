package openapi

import (
	"fmt"

	"ks-knoc-server/internal/common/requests"

	"github.com/spf13/viper"
)

type GetTokenResponse struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Result  GetTokenContent `json:"result"`
}

type GetTokenContent struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	ExpireTime   int    `json:"expireTime"`
	AppId        int    `json:"appId"`
}

type Openapi struct {
	host      string
	accessKey string
	secretKey string
	http      *requests.HTTPClient
}

func (c *Openapi) joinURL(uri string) string {
	return fmt.Sprintf("%s%s", c.host, uri)
}

func (c *Openapi) getToken() (string, error) {
	var data GetTokenResponse

	headers := map[string]string{
		"appKey":    c.accessKey,
		"secretKey": c.secretKey,
	}

	req := &requests.Request{
		URL:         c.joinURL("/token/get"),
		Params:      headers,
		Empowerment: &data,
	}

	if err := c.http.GET(req); err != nil {
		return "", err
	}

	return data.Result.AccessToken, nil
}

func (c *Openapi) AuthenticationRequest(src *requests.Request) error {
	var token string
	var err error

	if token, err = c.getToken(); err != nil {
		return err
	}

	req := &requests.Request{
		URL:         c.joinURL(src.URL),
		Headers:     map[string]string{"Authorization": fmt.Sprintf("Bearer %s", token), "Content-Type": "application/json"},
		Params:      src.Params,
		Empowerment: src.Empowerment,
	}
	return c.http.GET(req)
}

func (c *Openapi) addToken(params *requests.Request) error {
	if params.Headers == nil {
		params.Headers = map[string]string{}
	}
	token, err := c.getToken()
	if err != nil {
		return err
	}
	params.Headers["Authorization"] = fmt.Sprintf("Bearer %s", token)
	params.Headers["Content-Type"] = "application/json"
	return nil
}

func (c *Openapi) Post(data *requests.Request) error {
	var err error
	if err = c.addToken(data); err != nil {
		return err
	}
	req := &requests.Request{
		URL:         c.joinURL(data.URL),
		Headers:     data.Headers,
		Body:        data.Body,
		Empowerment: data.Empowerment,
	}
	return c.http.POST(req)
}

func (c *Openapi) Get(params *requests.Request) error {
	err := c.addToken(params)
	if err != nil {
		return err
	}
	req := &requests.Request{
		URL:         c.joinURL(params.URL),
		Headers:     params.Headers,
		Params:      params.Params,
		Empowerment: params.Empowerment,
		Body:        params.Body,
	}
	return c.http.GET(req)
}

func (c *Openapi) Delete(params *requests.Request) error {
	err := c.addToken(params)
	if err != nil {
		return err
	}
	req := &requests.Request{
		URL:         c.joinURL(params.URL),
		Headers:     params.Headers,
		Params:      params.Params,
		Empowerment: params.Empowerment,
		Body:        params.Body,
	}
	return c.http.DELETE(req)
}

func (c *Openapi) Update(data *requests.Request) error {
	err := c.addToken(data)
	if err != nil {
		return err
	}
	req := &requests.Request{
		URL:         c.joinURL(data.URL),
		Headers:     data.Headers,
		Body:        data.Body,
		Empowerment: data.Empowerment,
	}
	return c.http.PUT(req)
}

func (c *Openapi) UpdateAppKey(accessKey, secretKey string) {
	c.accessKey = accessKey
	c.secretKey = secretKey
}

func NewOpenapi() *Openapi {
	return &Openapi{
		host:      viper.GetString("openapi.url"),
		accessKey: viper.GetString("openapi.ak"),
		secretKey: viper.GetString("openapi.sk"),
		http:      requests.NewHTTPClient(),
	}
}
