package logger

import (
	"flag"
	"os"
	"path"
	"strings"
	"time"

	"ks-knoc-server/pkg/utils"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"

	"ks-knoc-server/config"
)

var LogDir = flag.String("it_log_dir", "", "custom log path")

// LogOptions ...
type LogOptions struct {
	Path       string     `yaml:"path"`
	Level      string     `yaml:"level"`
	FilePrefix string     `yaml:"filePrefix"`
	FileFormat string     `yaml:"fileFormat"`
	OutFormat  string     `yaml:"outFormat"`
	LumberJack lumberJack `yaml:"lumberJack"`
}

type lumberJack struct {
	MaxSize    int  `yaml:"maxSize"`
	MaxBackups int  `yaml:"maxBackups"`
	MaxAge     int  `yaml:"maxAge"`
	Compress   bool `yaml:"compress"`
}

// NewLogOptions ...
func NewLogOptions() *LogOptions {
	logPath := ""
	if *LogDir != "" {
		logPath = *LogDir
	} else if viper.GetString("log.path") != "" {
		logPath = viper.GetString("log.path")
	} else {
		logPath = "/tmp"
	}
	return &LogOptions{
		Path:       logPath,
		FilePrefix: viper.GetString("log.filePrefix"),
		FileFormat: viper.GetString("log.fileFormat"),
		OutFormat:  viper.GetString("log.outFormat"),
	}
}

// Init Logger初始化
func (l *LogOptions) Init() (*zap.Logger, error) {
	ws := make([]zapcore.WriteSyncer, 0)
	if l.Path == "stdout" {
		ws = append(ws, zapcore.AddSync(os.Stdout))
	} else {
		// 如果要写本地，需要看一下对应的路径是否存在，不存在则创建
		if exist, _ := utils.DirExist(l.Path); !exist {
			if err := utils.CreateDir(l.Path); err != nil {
				return nil, err
			}
		}
		ws = append(ws, zapcore.AddSync(l.getLumberjackWriteSyncer()))
		ws = append(ws, zapcore.AddSync(os.Stdout))
	}

	// 设置Encoder
	var encoder zapcore.Encoder
	if l.OutFormat == config.OutJSON {
		encoder = zapcore.NewJSONEncoder(l.getEncoderConfig())
	} else {
		encoder = zapcore.NewConsoleEncoder(l.getEncoderConfig())
	}
	writeSyncer := zapcore.NewMultiWriteSyncer(ws...)
	zapCore := zapcore.NewCore(encoder, writeSyncer, l.getLevel())

	// AddCaller会显示文件名和行号
	logger := zap.New(zapCore, zap.AddCaller())
	zap.ReplaceGlobals(logger)

	return logger, nil
}

func (l *LogOptions) getLevel() zapcore.Level {
	levelMap := map[string]zapcore.Level{
		"debug":  zapcore.DebugLevel,
		"info":   zapcore.InfoLevel,
		"warn":   zapcore.WarnLevel,
		"error":  zapcore.ErrorLevel,
		"dpanic": zapcore.DPanicLevel,
		"panic":  zapcore.PanicLevel,
		"fatal":  zapcore.FatalLevel,
	}

	// 优先使用LogOptions结构体中的Level字段
	if l.Level != "" {
		if level, ok := levelMap[strings.ToLower(l.Level)]; ok {
			return level
		}
	}

	// 如果LogOptions中Level为空或无效，再从viper配置读取
	if level, ok := levelMap[viper.GetString("log.level")]; ok {
		return level
	}

	return zapcore.InfoLevel
}

// 添加自定义短路径编码器
func ShortCallerEncoder(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	// 只显示文件名和行号，不显示完整路径
	enc.AppendString(path.Base(caller.File) + ":" + utils.ToString(caller.Line))
}

func (l *LogOptions) getEncoderConfig() zapcore.EncoderConfig {
	zapConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "S",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     l.getEncodeTime,
		EncodeDuration: zapcore.StringDurationEncoder,
		// 使用自定义短路径编码器替代完整路径编码器
		EncodeCaller: ShortCallerEncoder,
	}
	return zapConfig
}

func (l *LogOptions) getEncodeTime(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString(t.Format("2006/01/02 - 15:04:05.000"))
}

func (l *LogOptions) getLumberjackWriteSyncer() zapcore.WriteSyncer {
	lumberjackLogger := &lumberjack.Logger{
		Filename:   l.getLogFile(),
		MaxSize:    l.LumberJack.MaxSize,
		MaxBackups: l.LumberJack.MaxBackups,
		MaxAge:     l.LumberJack.MaxAge,
		Compress:   l.LumberJack.Compress,
	}
	return zapcore.AddSync(lumberjackLogger)
}

func (l *LogOptions) getLogFile() string {
	fileFormat := time.Now().Format(l.FileFormat)
	fileName := strings.Join([]string{
		l.FilePrefix,
		fileFormat,
		"log"}, ".")
	return path.Join(l.Path, fileName)
}

// FileRename ...
func (l *LogOptions) FileRename(rename string) {
	l.FilePrefix = l.FilePrefix + "-" + rename
}
