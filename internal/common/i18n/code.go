package i18n

var (
	OK                                    = &I18n{ZhCN: "成功", EnUS: "OK"}
	InternalServerError                   = &I18n{ZhCN: "服务器内部错误", EnUS: "Internal server error."}
	ErrBind                               = &I18n{ZhCN: "请求参数错误", EnUS: "Error occurred while binding the request body to the struct."}
	ErrPutFile                            = &I18n{ZhCN: "上传文件错误", EnUS: "Failed to upload file."}
	ErrHeaderFormat                       = &I18n{ZhCN: "授权头部格式错误", EnUS: "Authorization header format is wrong."}
	ErrAuthorization                      = &I18n{ZhCN: "无法识别头部授权信息", EnUS: "unrecognized Authorization header."}
	ErrJsonMarshal                        = &I18n{ZhCN: "JSON序列化错误", EnUS: "json marshal error."}
	ErrJsonUnmarshal                      = &I18n{ZhCN: "JSON反序列化错误", EnUS: "json unmarshal error."}
	ErrParameterRequired                  = &I18n{ZhCN: "参数不能为空", EnUS: "Parameter is required."}
	ErrParameterInvalid                   = &I18n{ZhCN: "参数不合规", EnUS: "Parameter is invalid."}
	ErrDataNotExists                      = &I18n{ZhCN: "数据不存在", EnUS: "Data Not Exists."}
	ErrBuiltinObject                      = &I18n{ZhCN: "内置对象，无法修改", EnUS: "Builtin Object, Can not Change."}
	ErrDuplicateKey                       = &I18n{ZhCN: "存在重复的键值", EnUS: "Duplicate Key."}
	ErrDataDelete                         = &I18n{ZhCN: "数据删除失败", EnUS: "Data Delete Error."}
	ErrModelCodeQuery                     = &I18n{ZhCN: "缺少模型code", EnUS: "No Model Code Error."}
	ErrDataCheck                          = &I18n{ZhCN: "数据校验失败", EnUS: "Data Check Error."}
	ErrDataFieldUnEditable                = &I18n{ZhCN: "数据字段不可编辑", EnUS: "Data Field UnEditable."}
	ErrDataInvalid                        = &I18n{ZhCN: "数据不合规", EnUS: "Data Invalid."}
	ErrInvalidPagination                  = &I18n{ZhCN: "分页参数不合规", EnUS: "Invalid Pagination."}
	ErrExcelTemplateError                 = &I18n{ZhCN: "Excel生成失败", EnUS: "Excel Template Error."}
	ErrModelAttrCreate                    = &I18n{ZhCN: "模型属性创建失败", EnUS: "Model Attr Create Error."}
	ErrModelAttrUpdate                    = &I18n{ZhCN: "模型属性更新失败", EnUS: "Model Attr Update Error."}
	ErrModelAttrDelete                    = &I18n{ZhCN: "模型属性删除失败", EnUS: "Model Attr Delete Error."}
	ErrModelDataError                     = &I18n{ZhCN: "下载数据错误", EnUS: "Model Data Download Error."}
	ErrModelParseError                    = &I18n{ZhCN: "所选数据与模板结构不对应", EnUS: "Model Code Parse Error."}
	ErrModelNotBelongToModelGroup         = &I18n{ZhCN: "模型不属于模型组", EnUS: "Model Not Belong To Model Group."}
	ErrFuzzySearchModelGroupCodeEmpty     = &I18n{ZhCN: "当模型不为空时，模糊搜索模型组code不能为空", EnUS: "Fuzzy Search Model Group Code Empty."}
	ErrModelCodeNotExists                 = &I18n{ZhCN: "模型code不存在", EnUS: "Model Code Not Exists."}
	ErrUpdateModelGroup                   = &I18n{ZhCN: "更新模型组失败", EnUS: "Update Model Group Error."}
	ErrModelAttrGroupNameExists           = &I18n{ZhCN: "模型属性组名称已存在", EnUS: "Model Attr Group Name Exists."}
	ErrModelAttrGroupNotExists            = &I18n{ZhCN: "模型属性组不存在", EnUS: "Model Attr Group Not Exists."}
	ErrModelNotFound                      = &I18n{ZhCN: "模型不存在", EnUS: "Model Not Found."}
	ErrIdsEmpty                           = &I18n{ZhCN: "IDS为空", EnUS: "Ids empty."}
	ErrPutESMapping                       = &I18n{ZhCN: "ES映射更新失败", EnUS: "Put ES Mapping Error."}
	ErrModelAttrTypeInvalid               = &I18n{ZhCN: "模型类型不合规", EnUS: "Model Type Invalid."}
	ErrModelSubordinateExists             = &I18n{ZhCN: "模型从属关系已存在", EnUS: "Model Subordinate Exists."}
	ErrDecode                             = &I18n{ZhCN: "解码错误", EnUS: "Decode Error."}
	ErrSubordinateLoop                    = &I18n{ZhCN: "从属关系出现环路", EnUS: "Subordinate Loop."}
	ErrModelSubordinateNotExists          = &I18n{ZhCN: "从属关系不存在", EnUS: "Model Subordinate Not Exists."}
	ErrModelSubordinateInUse              = &I18n{ZhCN: "已有资源关系的模型关系不可删除", EnUS: "Model Subordinate In Use."}
	ErrModelDataSubOrdinateRelationExists = &I18n{ZhCN: "模型数据从属关系已存在", EnUS: "Model Data SubOrdinate Relation Exists."}
	ErrModelDataMisMatch                  = &I18n{ZhCN: "模型与数据不匹配", EnUS: "Model And Data MisMatch."}
	ErrModelAssociateNameExist            = &I18n{ZhCN: "模型关联关系名称已存在", EnUS: "Model Associate Name Exist."}
	ErrModelAssociateNotExists            = &I18n{ZhCN: "模型关联关系不存在", EnUS: "Model Associate Not Exists."}
	ErrModelAssociateInUse                = &I18n{ZhCN: "已有资源关系的模型关系不可删除", EnUS: "Model Associate In Use."}
	ErrModelRelationType                  = &I18n{ZhCN: "模型关系类型错误", EnUS: "Model Relation Type Error."}
	ErrModelRElationNotExist              = &I18n{ZhCN: "模型关系不存在", EnUS: "Model Relation Not Exist."}
	ErrRackPositionConflict               = &I18n{ZhCN: "机柜位置冲突", EnUS: "Rack Position Conflict."}
	ErrDeviceHeightEmpty                  = &I18n{ZhCN: "设备高度为空", EnUS: "Device Height Empty."}
	ErrModelDoNotHaveAttr                 = &I18n{ZhCN: "模型没有属性", EnUS: "Model Do Not Have Attribute."}
	ErrDataPathExists                     = &I18n{ZhCN: "数据路径已存在", EnUS: "Data Path Exists."}
	ErrDataParentNotSet                   = &I18n{ZhCN: "数据父级未设置", EnUS: "Data Parent Not Set."}
	ErrRackPositionError                  = &I18n{ZhCN: "机柜位置错误", EnUS: "Rack Position Error."}
	ErrModelAttrNotExist                  = &I18n{ZhCN: "模型属性不存在", EnUS: "Model Attribute Not Exist."}
	ErrModelAttrNotTypeString             = &I18n{ZhCN: "模型属性不是字符串类型", EnUS: "Model Attribute Not Type String."}
	ErrModelAttrNotTypeInt                = &I18n{ZhCN: "模型属性不是整型", EnUS: "Model Attribute Not Type Int."}
	ErrModelAttrNotTypeFloat              = &I18n{ZhCN: "模型属性不是浮点型", EnUS: "Model Attribute Not Type Float."}
	ErrModelAttrNotTypeBool               = &I18n{ZhCN: "模型属性不是布尔型", EnUS: "Model Attribute Not Type Bool."}
	ErrModelAttrNotTypeSelect             = &I18n{ZhCN: "模型属性不是枚举类型", EnUS: "Model Attribute Not Type Select."}
	ErrModelAttrNotTypeTextArea           = &I18n{ZhCN: "模型属性不是文本域类型", EnUS: "Model Attribute Not Type TextArea."}
	ErrModelAttrNotTypeDate               = &I18n{ZhCN: "模型属性不是日期类型", EnUS: "Model Attribute Not Type Date."}
	ErrModelAttrNotTypeDateTime           = &I18n{ZhCN: "模型属性不是日期时间类型", EnUS: "Model Attribute Not Type DateTime."}
	ErrModelAttrNotTypeRelationship       = &I18n{ZhCN: "模型属性不是关系类型", EnUS: "Model Attribute Not Type Relationship."}
	ErrRoomRackPosition                   = &I18n{ZhCN: "机房机柜位置错误", EnUS: "Room Rack Position Error."}
	ErrRackPositionNotSet                 = &I18n{ZhCN: "机柜位置未设置", EnUS: "Rack Position Not Set."}
	ErrRackPositionInvalid                = &I18n{ZhCN: "机柜位置不合规", EnUS: "Rack Position Invalid."}
	ErrModelDataAssociateRelationExists   = &I18n{ZhCN: "模型数据关联关系已存在", EnUS: "Model Data Associate Relation Exists."}
	ErrModelRelationConflict              = &I18n{ZhCN: "模型关系冲突", EnUS: "Model Relation Conflict."}
	ErrModelAttrUniqueAtLeastOne          = &I18n{ZhCN: "模型属性至少有一个唯一", EnUS: "Model Attribute Unique At Least One."}
	ErrModelAttrSelectInheritInUse        = &I18n{ZhCN: "模型属性下拉框继承关系已使用", EnUS: "Model Attribute Select Inherit In Use."}
	ErrDeviceHeightInvalid                = &I18n{ZhCN: "设备高度不合规", EnUS: "Device Height Invalid."}
	ErrLabelExists                        = &I18n{ZhCN: "标签已存在", EnUS: "Label Exists."}
	ErrLabelBindingExceedLimit            = &I18n{ZhCN: "标签绑定数量超过限制", EnUS: "Label Binding Exceed Limit."}
	ErrDCIMDeviceNotExists                = &I18n{ZhCN: "机房操作设备不存在", EnUS: "DCIM Device Not Exists."}
	ErrDCIMDeviceNotOnRack                = &I18n{ZhCN: "设备不在机柜上", EnUS: "DCIM Device Not On Rack."}
)
