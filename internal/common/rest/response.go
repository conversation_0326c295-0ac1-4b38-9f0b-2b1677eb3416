package rest

import (
	"encoding/json"
	"fmt"
	"net/http"

	"go.uber.org/zap"
)

// Response TODO
type Response struct {
	Body       []byte
	Err        error
	StatusCode int
	Status     string
	Header     http.Header
}

type BaseResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Into 解析Response body到obj
func (r *Response) Into(obj interface{}) error {
	if r.Err != nil {
		return r.Err
	}

	if len(r.Body) != 0 {
		err := json.Unmarshal(r.Body, obj)
		if nil != err {
			if r.StatusCode >= 300 {
				return fmt.Errorf("http request err: %s", string(r.Body))
			}
			zap.L().Error(fmt.Sprintf("invalid response body, unmarshal json failed, reply:%s, error:%s", r.Body, err.Error()))
			return fmt.Errorf("http response err: %v, raw data: %s", err, r.Body)
		}
	} else if r.StatusCode >= 300 {
		return fmt.Errorf("http request failed: %s", r.Status)
	}
	return nil
}
