package rest

import (
	"net/http"
	"strings"
)

// ClientInterface RestClient interface
type ClientInterface interface {
	Verb(verb VerbType) *Request
	Post() *Request
	Put() *Request
	Get() *Request
	Delete() *Request
	Patch() *Request
}

// HttpClient TODO
type HttpClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// Verb TODO
func (r *Client) Verb(verb VerbType) *Request {
	return &Request{
		parent:   r,
		verb:     verb,
		baseURL:  r.baseUrl,
		Endpoint: r.endpoint,
	}
}

// Client Rest client
type Client struct {
	endpoint string
	baseUrl  string
}

// Post TODO
func (r *Client) Post() *Request {
	return r.Verb(POST)
}

// Put TODO
func (r *Client) Put() *Request {
	return r.Verb(PUT)
}

// Get TODO
func (r *Client) Get() *Request {
	return r.Verb(GET)
}

// Delete TODO
func (r *Client) Delete() *Request {
	return r.Verb(DELETE)
}

// Patch TODO
func (r *Client) Patch() *Request {
	return r.Verb(PATCH)
}

// NewClient TODO
func NewClient(endpoint, baseUrl string) ClientInterface {
	if baseUrl != "/" {
		baseUrl = strings.Trim(baseUrl, "/")
		baseUrl = "/" + baseUrl
	}

	client := &Client{
		baseUrl:  baseUrl,
		endpoint: endpoint,
	}

	return client
}
