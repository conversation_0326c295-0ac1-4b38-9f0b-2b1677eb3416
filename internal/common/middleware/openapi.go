package middleware

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func CheckAuthorization(path, secret, method, date, baAuth string) bool {
	// 这几个配置都得传值，不能为空
	if secret == "" || path == "" || method == "" || baAuth == "" {
		zap.L().Error("secret or path or method or baAuth is empty")
		return false
	}

	// 指定时间格式，根据请求提交过来的时间来判断，如果请求时间差过大，说明请求存在问题，可能是被篡改的
	dateFormat := "2006-01-02T15:04:05.999"
	timezone, _ := time.LoadLocation("Asia/Shanghai")
	ts, err := time.ParseInLocation(dateFormat, date, timezone)
	if err != nil {
		zap.L().Error(err.Error())
		return false
	}
	if time.Now().UnixMilli()-ts.UnixMilli() > (time.Minute * 10).Milliseconds() {
		zap.L().Error("request time is expired")
		return false
	}

	// 构建SignString
	signString := method + " " + path + " " + date
	secretByte := []byte(secret)
	mac := hmac.New(sha1.New, secretByte)
	mac.Write([]byte(signString))
	secretSign := mac.Sum(nil)
	stringToSign := strings.TrimSpace(base64.StdEncoding.EncodeToString(secretSign))

	// 处理请求提交过来的BA值
	authCheckValue := strings.TrimSpace(strings.Replace(baAuth, "BA :", "", 1))
	return authCheckValue == stringToSign
}

// AuthOpenApiToken 鉴权OpenAPI认证信息
func AuthOpenApiToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		span, _ := apm.StartSpan(ctx, "AuthOpenApiToken", "GET")
		defer span.End()

		// 获取请求的一些问题
		path := c.Request.URL.Path
		secret := viper.GetString("openapi.secret")
		method := c.Request.Method
		date := c.Request.Header.Get("requestTime")
		auth := c.Request.Header.Get("OpenApiAuthorization")

		if !CheckAuthorization(path, secret, method, date, auth) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"message": "Unauthorized",
				"code":    401,
			})
			c.Abort()
		}
		c.Next()
	}
}
