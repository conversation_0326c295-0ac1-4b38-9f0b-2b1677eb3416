package middleware

import (
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CORSMiddleware(c *gin.Context) {
	zap.L().Debug("CORS Middleware Called, the request method is: " + c.Request.Method)

	allowedOrigins := []string{
		"http://127.0.0.1:8000",
		"http://127.0.0.1:5173",
		"http://localhost:5173",
		"http://localhost:8000",
		"http://127.0.0.1:8080",
		"http://localhost:8080",
		"http://nginx.orb.local",
		"https://bpm.test.gifshow.com",
		"https://bpm.corp.kuaishou.com",
		"https://it-dcim.test.gifshow.com",
		"https://it-dcim.corp.kuaishou.com",
	}

	origin := c.Request.Header.Get("Origin")
	log.Println("Request Origin Is:", origin)
	validOrigin := ""
	isAllowed := false

	for _, allowedOrigin := range allowedOrigins {
		if origin == allowedOrigin {
			validOrigin = allowedOrigin
			isAllowed = true
			break
		}
	}

	log.Println("Request Origin:", origin, "Valid Origin:", validOrigin, "Is Allowed:", isAllowed)

	if isAllowed {
		// 对于允许的域名，设置具体的Origin并允许凭据
		c.Header("Access-Control-Allow-Origin", validOrigin)
		c.Header("Access-Control-Allow-Credentials", "true")
	} else {
		// 对于不允许的域名，设置通配符但不允许凭据
		c.Header("Access-Control-Allow-Origin", "*")
		// 不设置 Access-Control-Allow-Credentials
	}

	allowHeaders := []string{
		"DNT",
		"X-Mx-ReqToken",
		"Keep-Alive",
		"User-Agent",
		"X-Requested-With",
		"If-Modified-Since",
		"Cache-Control",
		"Content-Type",
		"Content-Length",
		"X-CSRF-Token",
		"Token",
		"x-custom-header",
		"Authorization",
		"Origin",
		"Accept",
	}

	c.Header("Access-Control-Allow-Headers", strings.Join(allowHeaders, ", "))
	c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

	if c.Request.Method == "OPTIONS" {
		c.AbortWithStatus(http.StatusNoContent)
		return
	}

	c.Next()
}
