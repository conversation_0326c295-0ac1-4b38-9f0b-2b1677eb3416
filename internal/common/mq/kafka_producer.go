package mq

import (
	"github.com/IBM/sarama"
)

func KafkaProduce() (sarama.SyncProducer, error) {
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 2
	config.Producer.Return.Successes = true
	config.Producer.Partitioner = sarama.NewRandomPartitioner
	// config.ClientID = viper.GetString("Knockafka.ptopic")
	config.Metadata.Full = true
	config.Net.SASL.Enable = true
	config.Net.SASL.User = "knoc-task-queue"
	config.Net.SASL.Password = ""
	config.Net.SASL.Handshake = true
	config.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient { return &XDGSCRAMClient{HashGeneratorFcn: SHA256} }
	config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
	// a := strings.Split(viper.GetString("Knockafka.url"), ",")
	a := []string{"kafka1.xm.kwai-it:9092", "kafka2.xm.kwai-it:9092", "kafka3.xm.kwai-it:9092"}
	producer, err := sarama.NewSyncProducer(a, config)
	if err != nil {
		return nil, err
	}

	return producer, nil
}
