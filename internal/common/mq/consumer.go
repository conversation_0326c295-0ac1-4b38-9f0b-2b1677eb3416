package mq

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/IBM/sarama"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// ConsumerConfig Kafka消费者配置
type ConsumerConfig struct {
	Brokers    []string `json:"brokers"`
	Group      string   `json:"group"`
	Topics     []string `json:"topics"`
	User       string   `json:"user"`
	Password   string   `json:"password"`
	Assignor   string   `json:"assignor"`   // sticky, roundrobin, range
	FromOldest bool     `json:"fromOldest"` // 是否从最旧的消息开始消费
	SASLEnable bool     `json:"saslEnable"` // 是否启用SASL认证
}

func NewConsumerConfig() *ConsumerConfig {
	topic := viper.GetString("kafka.topic")
	return &ConsumerConfig{
		Brokers:    viper.GetStringSlice("kafka.brokers"),
		Group:      viper.GetString("kafka.group"),
		Topics:     []string{topic},
		User:       viper.GetString("kafka.user"),
		Password:   viper.GetString("kafka.password"),
		Assignor:   viper.GetString("kafka.assignor"),
		FromOldest: viper.GetBool("kafka.old"),
		SASLEnable: viper.GetBool("kafka.saslEnable"),
	}
}

// KafkaConsumer Kafka消费者实现
type KafkaConsumer struct {
	config  *ConsumerConfig
	handler MessageHandler
	client  sarama.ConsumerGroup
	ready   chan struct{}
	mu      sync.RWMutex
}

// MessageHandler 消息处理器接口
type MessageHandler interface {
	// Handle 处理单条消息
	Handle(ctx context.Context, message *sarama.ConsumerMessage) error
}

// Consumer Kafka消费者接口
type Consumer interface {
	// Start 启动消费者
	Start(ctx context.Context) error
	// Stop 停止消费者
	Stop() error
	// IsReady 检查消费者是否就绪
	IsReady() <-chan struct{}
}

// NewKafkaConsumer 创建Kafka消费者
func NewKafkaConsumer(config *ConsumerConfig, handler MessageHandler) (*KafkaConsumer, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}
	if handler == nil {
		return nil, fmt.Errorf("消息处理器不能为空")
	}

	cfg := sarama.NewConfig()

	// 设置Kafka版本
	cfg.Version = sarama.V2_4_1_0

	// 消费者配置
	cfg.Consumer.Fetch.Min = 1
	cfg.Consumer.Fetch.Default = 1024 * 1024
	cfg.Consumer.Retry.Backoff = 2 * time.Second
	cfg.Consumer.MaxWaitTime = 250 * time.Millisecond
	cfg.Consumer.MaxProcessingTime = 100 * time.Millisecond
	cfg.Consumer.Return.Errors = true

	// 设置分区策略
	switch config.Assignor {
	case "sticky":
		cfg.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{
			sarama.NewBalanceStrategySticky(),
		}
	case "roundrobin":
		cfg.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{
			sarama.NewBalanceStrategyRoundRobin(),
		}
	case "range":
		cfg.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{
			sarama.NewBalanceStrategyRange(),
		}
	default:
		cfg.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{
			sarama.NewBalanceStrategyRange(),
		}
	}

	// 设置消费起始位置
	if config.FromOldest {
		cfg.Consumer.Offsets.Initial = sarama.OffsetOldest
	} else {
		cfg.Consumer.Offsets.Initial = sarama.OffsetNewest
	}

	// 配置SASL认证
	if config.SASLEnable {
		if config.User == "" || config.Password == "" {
			return nil, fmt.Errorf("SASL认证配置不完整, 请确认传入了用户名和密码")
		}
		cfg.Net.SASL.Enable = true
		cfg.Net.SASL.Handshake = true
		cfg.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
		cfg.Net.SASL.User = config.User
		cfg.Net.SASL.Password = config.Password
		cfg.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient {
			return &XDGSCRAMClient{HashGeneratorFcn: SHA256}
		}
	}

	// 创建消费者组
	client, err := sarama.NewConsumerGroup(config.Brokers, config.Group, cfg)
	if err != nil {
		return nil, fmt.Errorf("创建消费者组失败: %w", err)
	}

	return &KafkaConsumer{
		config:  config,
		handler: handler,
		client:  client,
		ready:   make(chan struct{}),
	}, nil
}

// Start 启动消费者
func (c *KafkaConsumer) Start(ctx context.Context) error {
	zap.L().Info("启动Kafka消费者",
		zap.Strings("brokers", c.config.Brokers),
		zap.String("group", c.config.Group),
		zap.Strings("topics", c.config.Topics))

	go c.consume(ctx)

	// 等待消费者就绪
	select {
	case <-c.ready:
		zap.L().Info("Kafka消费者已就绪")
		return nil
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(30 * time.Second):
		return fmt.Errorf("等待消费者就绪超时")
	}
}

// consume 消费循环
func (c *KafkaConsumer) consume(ctx context.Context) {
	wg := &sync.WaitGroup{}
	wg.Add(1)

	go func() {
		defer wg.Done()
		for {
			select {
			case <-ctx.Done():
				zap.L().Info("消费者收到停止信号")
				return
			default:
				if err := c.client.Consume(ctx, c.config.Topics, c); err != nil {
					zap.L().Error("消费失败", zap.Error(err))
					// 短暂延迟后重试
					time.Sleep(time.Second)
					continue
				}
			}

			// 重新初始化ready channel以支持重连
			c.mu.Lock()
			c.ready = make(chan struct{})
			c.mu.Unlock()
		}
	}()

	// 处理消费者组错误
	go func() {
		for err := range c.client.Errors() {
			zap.L().Error("消费者组错误", zap.Error(err))
		}
	}()

	wg.Wait()
}

// Stop 停止消费者
func (c *KafkaConsumer) Stop() error {
	zap.L().Info("停止Kafka消费者")
	return c.client.Close()
}

// IsReady 检查消费者是否就绪
func (c *KafkaConsumer) IsReady() <-chan struct{} {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.ready
}

// Setup 实现sarama.ConsumerGroupHandler接口
func (c *KafkaConsumer) Setup(sarama.ConsumerGroupSession) error {
	c.mu.Lock()
	close(c.ready)
	c.mu.Unlock()
	return nil
}

// Cleanup 实现sarama.ConsumerGroupHandler接口
func (c *KafkaConsumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim 实现sarama.ConsumerGroupHandler接口
func (c *KafkaConsumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				zap.L().Info("消息通道已关闭")
				return nil
			}

			// 处理消息
			if err := c.handler.Handle(session.Context(), message); err != nil {
				zap.L().Error("处理消息失败",
					zap.Error(err),
					zap.String("topic", message.Topic),
					zap.Int32("partition", message.Partition),
					zap.Int64("offset", message.Offset))
				// 根据业务需求决定是否继续消费
				// 这里选择继续消费，避免阻塞整个消费者
				continue
			}

			// 标记消息已处理
			session.MarkMessage(message, "")

			zap.L().Debug("消息处理成功",
				zap.String("topic", message.Topic),
				zap.Int32("partition", message.Partition),
				zap.Int64("offset", message.Offset))

		case <-session.Context().Done():
			return nil
		}
	}
}

// ConsumerService 消费者服务，提供完整的生命周期管理
type ConsumerService struct {
	consumer Consumer
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// NewConsumerService 创建消费者服务
func NewConsumerService(consumer Consumer) *ConsumerService {
	ctx, cancel := context.WithCancel(context.Background())
	return &ConsumerService{
		consumer: consumer,
		ctx:      ctx,
		cancel:   cancel,
		wg:       sync.WaitGroup{}, // 明确初始化，虽然零值也可用
	}
}

// Start 启动消费者服务
func (s *ConsumerService) Start() error {
	if err := s.consumer.Start(s.ctx); err != nil {
		return fmt.Errorf("启动消费者失败: %w", err)
	}

	// 启动信号监听
	s.wg.Add(1)
	go s.handleSignals()

	return nil
}

// Stop 停止消费者服务
func (s *ConsumerService) Stop() error {
	if s.cancel != nil {
		s.cancel()
	}
	s.wg.Wait()
	return s.consumer.Stop()
}

// handleSignals 处理系统信号
func (s *ConsumerService) handleSignals() {
	defer s.wg.Done()

	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)

	sigusr1 := make(chan os.Signal, 1)
	signal.Notify(sigusr1, syscall.SIGUSR1)

	for {
		select {
		case <-s.ctx.Done():
			zap.L().Info("消费者服务停止")
			return
		case <-sigterm:
			zap.L().Info("收到终止信号，停止消费者服务")
			s.cancel()
			return
		case <-sigusr1:
			zap.L().Info("收到SIGUSR1信号，暂停/恢复功能暂未实现")
			// 可以在这里实现暂停/恢复逻辑
		}
	}
}
