package mq

import (
	"errors"
	"log"
	"os"
	"strings"
	"time"

	"github.com/IBM/sarama"
	"github.com/segmentio/kafka-go"
	"github.com/spf13/viper"
)

// 基于sarama第三方库开发的kafka client
func getKafkaReader(kafkaURL, topic, groupID string) *kafka.Reader {
	brokers := strings.Split(kafkaURL, ",")
	return kafka.NewReader(kafka.ReaderConfig{
		Brokers:        brokers,
		GroupID:        groupID,
		Topic:          topic,
		MinBytes:       10e3,             // 10KB
		MaxBytes:       10e6,             // 10MB
		CommitInterval: 10 * time.Second, // flushes commits to Kafka every second
	})
}

func Init() *kafka.Reader {
	// get kafka reader using environment variables.
	kafkaURL := viper.GetString("Knockafka.url")
	topic := viper.GetString("Knockafka.topic")
	groupID := os.Getenv("groupID")

	reader := getKafkaReader(kafkaURL, topic, groupID)

	return reader
}

type KafkaOptions struct {
	Brokers    []string
	SASLEnable bool
	User       string
	Password   string
	GroupID    string
}

func NewKafkaOptions() *KafkaOptions {
	return &KafkaOptions{
		Brokers:    viper.GetStringSlice("kafka.brokers"),
		SASLEnable: viper.GetBool("kafka.saslEnable"),
		User:       viper.GetString("kafka.user"),
		Password:   viper.GetString("kafka.password"),
		GroupID:    viper.GetString("kafka.group"),
	}
}

func (o *KafkaOptions) Init() (sarama.SyncProducer, error) {
	// 初始化kafka配置
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 2
	config.Producer.Return.Successes = true
	config.Producer.Partitioner = sarama.NewRandomPartitioner
	config.Metadata.Full = true

	// 如果开启了SASL认证，则需要设置SASL认证的配置
	if o.SASLEnable {
		log.Println("kafka开启SASL认证")
		config.Net.SASL.Enable = true

		// 如果用户名或密码为空，则返回错误
		if o.User == "" || o.Password == "" {
			log.Println("kafka用户名或密码为空, 请检查配置")
			return nil, errors.New("kafka用户名或密码为空, 请检查配置")
		}

		config.Net.SASL.User = o.User
		config.Net.SASL.Password = o.Password
		config.Net.SASL.Handshake = true
		config.Net.SASL.SCRAMClientGeneratorFunc = func() sarama.SCRAMClient { return &XDGSCRAMClient{HashGeneratorFcn: SHA256} }
		config.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
	}

	// 初始化kafka producer
	log.Println("kafka brokers", o.Brokers)
	p, err := sarama.NewSyncProducer(o.Brokers, config)
	if err != nil {
		log.Println("初始化kafka producer失败", err)
		return nil, err
	}

	return p, nil
}
