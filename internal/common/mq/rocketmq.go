package mq

import (
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func InitRocket() rocketmq.PushConsumer {
	c, err := rocketmq.NewPushConsumer(
		consumer.WithGroupName(viper.GetString("rocketmq.group")),
		consumer.WithNsResolver(primitive.NewPassthroughResolver([]string{viper.GetString("lvmi.rocketUrl")})),
		//consumer.WithConsumeFromWhere(consumer.ConsumeFromLastOffset),
		consumer.WithCredentials(primitive.Credentials{
			AccessKey: viper.GetString("rocketmq.accessKey"),
			SecretKey: viper.GetString("rocketmq.secretKey"),
		}),
		consumer.WithAutoCommit(true),
		consumer.WithConsumeFromWhere(consumer.ConsumeFromLastOffset),
		////consumer.WithConsumerOrder(true),
		consumer.WithConsumerModel(consumer.Clustering),
		consumer.WithStrategy(consumer.AllocateByAveragelyCircle),
	)

	if err != nil {
		zap.L().Error("init consumer error: " + err.Error())
	}
	zap.L().Info("rocketmq consumer init success ")
	return c
}
