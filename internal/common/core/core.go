package core

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.elastic.co/apm"
	"go.uber.org/zap"

	"ks-knoc-server/internal/common/errno"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`  // 这个messqge可以去掉
	TraceID string      `json:"trace_id"` // 添加traceID
	Data    interface{} `json:"data,omitempty"`
	I18n    interface{} `json:"i18n,omitempty"`
}

type Data struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

func SendResponse(c *gin.Context, err error, data interface{}) {
	status, message, i18n := errno.DecodeErr(err)

	traceID := apm.TransactionFromContext(c.Request.Context()).TraceContext().Trace.String()

	if status != 0 {
		message += " " + fmt.Sprintf("TraceID: %s", traceID)
	}

	c.<PERSON>(http.StatusOK, Response{
		Code:    status,
		Message: message,
		TraceID: traceID,
		Data:    data,
		I18n:    i18n,
	})
}

func SendBasicResponse(c *gin.Context, err error) {
	code, message, _ := errno.DecodeErr(err)

	c.JSON(http.StatusOK, Response{
		Code:    code,
		Message: message,
	})
}

// WriteExcel 封装返回文件流
func WriteExcel(ctx *gin.Context, fileName string, file *excelize.File) {
	ctx.Writer.Header().Add("Content-Type", "application/octet-stream")
	ctx.Writer.Header().Add("Content-disposition", "attachment;filename="+fileName)
	ctx.Writer.Header().Add("Content-Transfer-Encoding", "binary")
	if err := file.Write(ctx.Writer); err != nil {
		zap.L().Warn(err.Error())
	}
}
