package cache

import (
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/spf13/viper"
)

type RedisCache struct {
	c   *redis.Client
	ttl time.Duration
}

func NewRedisCache(c *redis.Client) *RedisCache {
	return &RedisCache{c: c, ttl: time.Duration(viper.GetInt64("cache.ttl") * int64(time.Second))}
}

func (r *RedisCache) Set(key string, data []byte) error {
	return r.c.Set(key, data, r.ttl).Err()
}

func (r *RedisCache) Get(key string) ([]byte, error) {
	return r.c.Get(key).Bytes()
}

func (r *RedisCache) Delete(key string) error {
	return r.c.Del(key).Err()
}

func (r *RedisCache) Close() error {
	if err := r.c.Close(); err != nil {
		return err
	}
	return nil
}
