package cache

import (
	"ks-knoc-server/internal/common/errno"

	"github.com/go-redis/redis/v7"
	"github.com/spf13/viper"
)

// Cache 定义了缓存的接口，以适配不同的缓存实现
type Cache interface {
	Set(key string, data []byte) error
	Delete(key string) error
	Get(key string) ([]byte, error)
	Close() error
}

// CacheOptions 缓存配置
type CacheOptions struct{}

func (c *CacheOptions) Init(rdb *redis.Client) (Cache, error) {
	engine := viper.GetString("cache.engine")
	switch engine {
	case "redis":
		return NewRedisCache(rdb), nil
	case "local":
		return NewBigCache(), nil
	default:
		return nil, errno.InternalServerError.Add("未知的缓存引擎 " + engine)
	}
}

func NewCacheOptions() *CacheOptions {
	return &CacheOptions{}
}
