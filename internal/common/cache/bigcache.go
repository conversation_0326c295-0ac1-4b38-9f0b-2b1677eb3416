package cache

import (
	"context"
	"time"

	bigCache "github.com/allegro/bigcache/v3"
)

type BigCacheOptions struct {
	Config  bigCache.Config
	handler *bigCache.BigCache
}

func NewBigCache() *BigCacheOptions {
	return &BigCacheOptions{
		Config: bigCache.Config{
			Shards:             1024,
			LifeWindow:         10 * time.Minute, // 缓存的有效期为10分钟
			CleanWindow:        30 * time.Minute, // 清理过期的key的轮询时间间隔为30分钟
			MaxEntriesInWindow: 1000 * 10 * 60,
			MaxEntrySize:       500,
			StatsEnabled:       false,
			Verbose:            true,
			HardMaxCacheSize:   0,
		},
	}
}

func (b *BigCacheOptions) Init() (Cache, error) {
	ctx := context.Background()
	cache, err := bigCache.New(ctx, b.Config)
	if err != nil {
		return nil, err
	}
	b.handler = cache
	return b, nil
}

func (b *BigCacheOptions) GetHandler() *bigCache.BigCache {
	return b.handler
}

func (b *BigCacheOptions) Set(key string, data []byte) error {
	return b.handler.Set(key, data)
}

func (b *BigCacheOptions) Delete(key string) error {
	return b.handler.Delete(key)
}

func (b *BigCacheOptions) Get(key string) ([]byte, error) {
	return b.handler.Get(key)
}

func (b *BigCacheOptions) Close() error {
	return b.handler.Close()
}
