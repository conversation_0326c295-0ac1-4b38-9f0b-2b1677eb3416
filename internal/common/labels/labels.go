package labels

import (
	"strconv"
	"strings"
)

// Label 一个Label标签就是一个键值对
type Label struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Labels []Label

func (ls Labels) Len() int {
	return len(ls)
}

func (ls Labels) Swap(i, j int) {
	ls[i], ls[j] = ls[j], ls[i]
}

func (ls Labels) Less(i, j int) bool {
	return ls[i].Name < ls[j].Name
}

// Range 循环在每一个Label上调用方法f
func (ls Labels) Range(f func(l Label)) {
	for _, l := range ls {
		f(l)
	}
}

func (ls Labels) String() string {
	s := strings.Builder{}

	s.WriteString("{")
	i := 0
	ls.Range(func(l Label) {
		if i > 0 {
			s.WriteString(",")
			s.WriteString(" ")
		}
		s.WriteString(l.Name)
		s.WriteString("=")
		s.WriteString(strconv.Quote(l.Value))
		i++
	})
	s.WriteString("}")
	return s.String()
}

func (ls Labels) Map() map[string]string {
	m := make(map[string]string, len(ls))
	ls.Range(func(l Label) {
		m[l.Name] = l.Value
	})
	return m
}

func (ls Labels) IsEmpty() bool {
	return len(ls) == 0
}
