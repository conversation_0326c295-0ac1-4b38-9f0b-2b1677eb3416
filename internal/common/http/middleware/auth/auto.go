package auth

import (
	"strings"

	"github.com/gin-gonic/gin"

	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/internal/common/http/middleware"
)

const authHeaderCount = 2

// AutoStrategy ...
type AutoStrategy struct {
	jwt JWTStrategy
}

var _ middleware.AuthStrategy = &AutoStrategy{}

// NewAutoStrategy ...
func NewAutoStrategy(jwt JWTStrategy) AutoStrategy {
	return AutoStrategy{
		jwt: jwt,
	}
}

// AuthFunc ...
func (a AutoStrategy) AuthFunc() gin.HandlerFunc {
	return func(c *gin.Context) {
		operator := middleware.AuthOperator{}
		authHeader := strings.SplitN(c.Request.Header.Get("Authorization"), " ", 2)

		if len(authHeader) != authHeaderCount {
			core.SendResponse(
				c,
				errno.ErrHeaderFormat,
				nil,
			)
			c.Abort()

			return
		}

		switch authHeader[0] {
		case "Bearer":
			operator.SetStrategy(a.jwt)
		default:
			core.SendResponse(c, errno.ErrAuthorization, nil)
			c.Abort()

			return
		}

		operator.AuthFunc()(c)

		c.Next()
	}
}
