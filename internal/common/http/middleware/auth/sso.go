package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// LoginValidate 登录验证
func LoginValidate() gin.HandlerFunc {

	return func(context *gin.Context) {
		if context.Request.URL.Path == "/sso" {
			context.Next()
			return
		}

		_, err := context.Cookie("username")
		if err != nil {
			context.JSON(http.StatusOK, gin.H{
				"code": 20064,
				"msg":  viper.GetString("url.sso"),
			})
			context.Abort()
			return
		}

		context.Next()
		return
	}

}
