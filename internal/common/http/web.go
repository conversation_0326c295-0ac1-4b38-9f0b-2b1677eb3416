package http

import (
	"flag"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

var GinPort = flag.String("port", "", "custom gin port")

// WebOptions ...
type WebOptions struct {
	Mode            string
	Middlewares     []gin.HandlerFunc `json:"-"`
	EnableProfiling bool
	EnableMetrics   bool
	*gin.Engine
	*http.Server
}

// NewWebOptions ...
func NewWebOptions() *WebOptions {
	// 设置gin的mode, 默认是info
	gin.SetMode(viper.GetString("mode"))

	// 设置启动端口
	httpPort := ""
	if *GinPort != "" {
		httpPort = ":" + *GinPort
	} else if viper.GetString("port") != "" {
		httpPort = viper.GetString("port")
	} else {
		httpPort = ":8080"
	}

	return &WebOptions{
		Middlewares:     []gin.HandlerFunc{},
		EnableProfiling: true,
		EnableMetrics:   true,
		Engine:          gin.New(),
		Server:          &http.Server{Addr: httpPort},
	}
}
