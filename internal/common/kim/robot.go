package kim

import (
	"ks-knoc-server/internal/common/rest"
	"net/url"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type MsgType string

const (
	MsgTypeText     MsgType = "text"
	MsgTypeMarkdown MsgType = "markdown"
)

type KimRobotMarkdown struct {
	MsgType  MsgType `json:"msgtype"`
	Markdown struct {
		Content string `json:"content"`
	} `json:"markdown"`
}

func NewKimRobotMarkdown(content string) *KimRobotMarkdown {
	return &KimRobotMarkdown{
		MsgType: MsgTypeMarkdown,
		Markdown: struct {
			Content string `json:"content"`
		}{
			Content: content,
		},
	}
}

// NewKimRobotRequest 创建一个钉Kim机器人请求, 方法默认用Post
func NewKimRobotRequest(robotUrl string, content *KimRobotMarkdown) (*rest.Request, error) {
	hookUrl, err := url.Parse(robotUrl)
	if err != nil {
		zap.L().Error("parse robot url failed", zap.Error(err), zap.String("robotUrl", robotUrl))
		return nil, err
	}
	params := make(map[string]string)
	for key, value := range hookUrl.Query() {
		params[key] = value[0]
	}
	schema := rest.SchemeHTTP
	if viper.GetString("kim.robot.schema") == "https" {
		schema = rest.SchemeHTTPS
	}
	req := rest.NewClient(hookUrl.Host, hookUrl.Path)
	return req.Post().WithParams(params).WithScheme(schema).Body(content), nil
}

type KimRobotResponse struct {
	Success      bool   `json:"success"`
	Errmsg       string `json:"errmsg"`
	Status       int    `json:"status"`
	MessageKey   string `json:"messageKey"`
	SuccessCount int    `json:"successCount"`
	FailedCount  int    `json:"failedCount"`
}
