package sdk

import (
	"encoding/json"
	"errors"
	"fmt"
	urllib "net/url"
	"reflect"
	"regexp"
	"strings"

	sdklog "ks-knoc-server/internal/common/itsdk/sdk/log"
	sdkrequest "ks-knoc-server/internal/common/itsdk/sdk/request"
)

var baseRequestFields []string

func init() {
	req := sdkrequest.BaseRequest{}
	reqType := reflect.TypeOf(req)
	for i := 0; i < reqType.NumField(); i++ {
		baseRequestFields = append(baseRequestFields, reqType.Field(i).Name)
	}
}

// ParameterBuilder ...
type ParameterBuilder interface {
	BuildURL(url string, paramJSON []byte) (string, error)
	BuildBody(paramJSON []byte) (string, error)
}

// GetParameterBuilder ...
func GetParameterBuilder(method string, logger sdklog.Logger) ParameterBuilder {
	if method == MethodGet || method == MethodDelete || method == MethodHead {
		return &WithoutBodyBuilder{logger}
	}
	return &WithBodyBuilder{logger}
}

// WithBodyBuilder supports PUT/POST/PATCH methods.
// It has path and body (json) parameters, but no query parameters.
type WithBodyBuilder struct {
	Logger sdklog.Logger
}

// BuildURL ...
func (b WithBodyBuilder) BuildURL(url string, paramJSON []byte) (string, error) {
	paramMap := make(map[string]interface{})
	err := json.Unmarshal(paramJSON, &paramMap)
	if err != nil {
		b.Logger.Errorf("%s", err.Error())
		return "", err
	}

	replacedURL, err := replaceURLWithPathParam(url, paramMap)
	if err != nil {
		b.Logger.Errorf("%s", err.Error())
		return "", err
	}

	encodedURL, err := encodeURL(replacedURL, nil)
	if err != nil {
		return "", err
	}

	b.Logger.Infof("URL=%s", encodedURL)
	return encodedURL, nil
}

// BuildBody ...
func (b WithBodyBuilder) BuildBody(paramJSON []byte) (string, error) {
	paramMap := make(map[string]interface{})
	err := json.Unmarshal(paramJSON, &paramMap)
	if err != nil {
		b.Logger.Errorf("%s", err.Error())
		return "", err
	}

	// remove base request fields
	for k := range paramMap {
		if includes(baseRequestFields, k) {
			delete(paramMap, k)
		}
	}

	body, _ := json.Marshal(paramMap)
	b.Logger.Infof("Body=%s", string(body))
	return string(body), nil
}

// WithoutBodyBuilder supports GET/DELETE methods.
// It only builds path and query parameters.
type WithoutBodyBuilder struct {
	Logger sdklog.Logger
}

// BuildURL ...
func (b WithoutBodyBuilder) BuildURL(url string, paramJSON []byte) (string, error) {
	paramMap := make(map[string]interface{})
	err := json.Unmarshal(paramJSON, &paramMap)
	if err != nil {
		b.Logger.Errorf("%s", err.Error())
		return "", err
	}

	resultURL, err := replaceURLWithPathParam(url, paramMap)
	if err != nil {
		b.Logger.Errorf("%s", err.Error())
		return "", err
	}

	queryParams := buildQueryParams(paramMap, url)
	encodedURL, err := encodeURL(resultURL, queryParams)
	if err != nil {
		return "", err
	}

	b.Logger.Infof("%s", string(paramJSON))
	b.Logger.Infof("URL=%s", encodedURL)
	return encodedURL, nil
}

// BuildBody ...
func (b WithoutBodyBuilder) BuildBody(paramJSON []byte) (string, error) {
	return "", nil
}

func replaceURLWithPathParam(url string, paramMap map[string]interface{}) (string, error) {
	r, _ := regexp.Compile("{[a-zA-Z0-9-_]+}")
	matches := r.FindAllString(url, -1)
	for _, match := range matches {
		field := strings.TrimLeft(match, "{")
		field = strings.TrimRight(field, "}")
		value, ok := paramMap[field]
		if !ok {
			return "", errors.New("Can not find path parameter: " + field)
		}

		valueStr := fmt.Sprintf("%v", value)
		url = strings.Replace(url, match, valueStr, -1)
	}

	return url, nil
}

func buildQueryParams(paramMap map[string]interface{}, url string) urllib.Values {
	values := urllib.Values{}
	accessMap(paramMap, url, "", values)
	return values
}

func accessMap(paramMap map[string]interface{}, url, prefix string, values urllib.Values) {
	for k, v := range paramMap {
		// exclude fields of Client class and path parameters
		if shouldIgnoreField(url, k) {
			continue
		}

		switch e := v.(type) {
		case []interface{}:
			for i, n := range e {
				switch f := n.(type) {
				case map[string]interface{}:
					subPrefix := fmt.Sprintf("%s.%d.", k, i+1)
					accessMap(f, url, subPrefix, values)
				case nil:
				default:
					values.Set(fmt.Sprintf("%s%s.%d", prefix, k, i+1), fmt.Sprintf("%s", n))
				}
			}
		case nil:
		default:
			values.Set(fmt.Sprintf("%s%s", prefix, k), fmt.Sprintf("%v", v))
		}
	}
}

func shouldIgnoreField(url, field string) bool {
	flag := "{" + field + "}"
	if strings.Contains(url, flag) {
		return true
	}

	if includes(baseRequestFields, field) {
		return true
	}

	return false
}

func encodeURL(requestURL string, values urllib.Values) (string, error) {
	urlObj, err := urllib.Parse(requestURL)
	if err != nil {
		return "", err
	}

	urlObj.RawPath = EscapePath(urlObj.Path, false)
	uri := urlObj.EscapedPath()

	if values != nil {
		queryParam := values.Encode()
		// RFC 3986, ' ' should be encoded to 20%, '+' to 2B%
		queryParam = strings.Replace(queryParam, "+", "%20", -1)
		if queryParam != "" {
			uri += "?" + queryParam
		}
	}

	return uri, nil
}
