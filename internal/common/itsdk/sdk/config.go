package sdk

import (
	"time"

	sdklog "ks-knoc-server/internal/common/itsdk/sdk/log"
)

// Config model
type Config struct {
	Scheme   string
	Endpoint string
	Timeout  time.Duration
	LogLevel sdklog.Level
}

var defaultEndpoint = "is-gateway.corp.kuaishou.com"

// NewConfig returns a pointer of Config
// scheme only accepts http or https
// endpoint is the host to access, the connection could not be created if it's error
func NewConfig() *Config {
	return &Config{
		Scheme:   SchemeHTTPS,
		Timeout:  30 * time.Second,
		LogLevel: sdklog.WarnLevel,
	}
}

// WithScheme ...
func (c *Config) WithScheme(scheme string) *Config {
	c.Scheme = scheme
	return c
}

// WithEndpoint ...
func (c *Config) WithEndpoint(endpoint string) *Config {
	c.Endpoint = endpoint
	return c
}

// WithTimeout ...
func (c *Config) WithTimeout(timeout time.Duration) *Config {
	c.Timeout = timeout
	return c
}

// WithLogLevel ...
func (c *Config) WithLogLevel(level sdklog.Level) *Config {
	c.LogLevel = level
	return c
}
