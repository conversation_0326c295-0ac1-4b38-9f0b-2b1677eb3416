package response

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"

	sdkerrors "ks-knoc-server/internal/common/itsdk/sdk/errors"
)

// Response ...
type Response interface {
	ParseErrorFromHTTPResponse(body []byte) error
}

// ErrorResponse ...
type ErrorResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message,omitempty"`
	RequestID string `json:"requestID"`
}

// BaseResponse ...
type BaseResponse struct {
	ErrorResponse
}

type BaseBPMResponse struct {
	Code      interface{} `json:"code"`
	Message   string      `json:"message,omitempty"`
	RequestID string      `json:"requestID"`
	State     string      `json:"state"`
	Msg       string      `json:"msg"`
	TimeStamp interface{} `json:"timestamp"`
}

// ParseErrorFromHTTPResponse For BPM BaseResponse
func (r *BaseBPMResponse) ParseErrorFromHTTPResponse(body []byte) error {
	var err error
	if err = json.Unmarshal(body, r); err != nil {
		return err
	}
	var c int
	switch r.Code.(type) {
	case string:
		c, err = strconv.Atoi(r.Code.(string))
	case int:
		c = r.Code.(int)
	}

	if c > 0 {
		return sdkerrors.NewMEDUSDKError(c, r.Message, r.RequestID)
	}

	return nil
}

// ParseErrorFromHTTPResponse ...
func (r *BaseResponse) ParseErrorFromHTTPResponse(body []byte) error {
	if err := json.Unmarshal(body, r); err != nil {
		return err
	}
	if r.Code > 0 {
		return sdkerrors.NewMEDUSDKError(r.Code, r.Message, r.RequestID)
	}

	return nil
}

// ParseFromHTTPResponse ...
func ParseFromHTTPResponse(rawResponse *http.Response, response Response) error {
	defer rawResponse.Body.Close()
	body, err := ioutil.ReadAll(rawResponse.Body)
	fmt.Println(string(body))
	if err != nil {
		return err
	}
	if rawResponse.StatusCode != 200 {
		return fmt.Errorf("request fail with status: %s, with body: %s", rawResponse.Status, body)
	}
	if err := response.ParseErrorFromHTTPResponse(body); err != nil {
		return err
	}

	return json.Unmarshal(body, &response)
}

func ParseFromHttpResponse(rawResponse *http.Response, response Response) error {
	defer rawResponse.Body.Close()
	body, err := ioutil.ReadAll(rawResponse.Body)
	if err != nil {
		return err
	}
	if err = response.ParseErrorFromHTTPResponse(body); err != nil {
		return err
	}
	return json.Unmarshal(body, &response)
}
