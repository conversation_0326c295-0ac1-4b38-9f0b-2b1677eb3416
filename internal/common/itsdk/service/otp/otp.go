package otp

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
)

// OTP otp model
type OTP struct {
	ErrCode   int8   `json:"err_code"`
	FailedCnt int8   `json:"failed_cnt"`
	Msg       string `json:"msg"`
	Success   bool   `json:"success"`
}

// AuthOTP otp auth
func AuthOTP(username, token string) (*OTP, error) {
	urls := "https://otp.corp.kuaishou.com/api/v1/verify?username=" + username
	data := url.Values{}
	data.Set("code", token)
	data.Set("app_token", "Y9xQdqTmxCRfeuottcRPfCnrCn2zAT8h")
	res, err := http.PostForm(urls, data)
	if err != nil {
		return nil, err
	}

	defer res.Body.Close()
	b, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	var t OTP
	if err = json.Unmarshal(b, &t); err != nil {
		return nil, err
	}

	return &t, nil
}
