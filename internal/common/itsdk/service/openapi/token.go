package openapi

import (
	"fmt"

	"ks-knoc-server/internal/common/itsdk/sdk/request"
	"ks-knoc-server/internal/common/itsdk/sdk/response"
)

// TokenRequest token请求model
type TokenRequest struct {
	*request.BaseRequest
}

// TokenResponse token响应model
type TokenResponse struct {
	*response.BaseResponse
	Result TokenResult `json:"result"`
}

// TokenResult token result model
type TokenResult struct {
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
	ExpireTime   int    `json:"expireTime"`
	AppID        int    `json:"appId"`
}

// NewTokenRequest ...
func NewTokenRequest(appKey, secret string) (req *TokenRequest) {
	req = &TokenRequest{
		BaseRequest: &request.BaseRequest{
			URL:    fmt.Sprintf("/token/get?appKey=%s&secretKey=%s", appKey, secret),
			Method: "GET",
			Header: map[string]string{"Content-Type": "application/json"},
		},
	}
	return
}

// NewTokenResponse ...
func NewTokenResponse() *TokenResponse {
	return &TokenResponse{
		BaseResponse: &response.BaseResponse{},
	}
}

// GetToken 获取token信息
func (c *Client) GetToken(req *TokenRequest) (resp *TokenResponse, err error) {
	resp = NewTokenResponse()

	err = c.Send(req, resp)
	return
}

// GetToken 获取Token信息
func (c *Client) GetCardToken(appKey string, req *TokenRequest) (resp *TokenResponse, err error) {
	resp = NewTokenResponse()
	// 发送请求
	err = c.Send(req, resp)
	return
}
