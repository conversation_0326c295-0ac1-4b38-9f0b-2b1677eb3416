package openapi

import (
	"ks-knoc-server/internal/common/itsdk/sdk/request"
	"ks-knoc-server/internal/common/itsdk/sdk/response"
)

// UserKimRequest 发送用户Kim请求
type UserKimRequest struct {
	*request.BaseRequest
}

// UserKimResponse ...
type UserKimResponse struct {
	*response.BaseResponse
	Status    int    `json:"status"`
	Message   string `json:"message"`
	Data      Data   `json:"data"`
	I18N      I18N   `json:"i18n"`
	Host      string `json:"host"`
	Port      int    `json:"port"`
	Timestamp string `json:"timestamp"`
}

type IHRUsersSearchRequest struct {
	*request.BaseRequest
}

type IHRUsersSearchResponse struct {
	*response.BaseResponse
	Data []IHRData `json:"data"`
}
type IHRData struct {
	EmployeeNatureCode string `json:"employeeNatureCode"`
	Name               string `json:"name"`
	Username           string `json:"username"`
	Email              string `json:"email"`
	Photo              string `json:"photo"`
	DisplayName        string `json:"display_name"`
	Department         struct {
		Code string `json:"code"`
		Name string `json:"name"`
	} `json:"department"`
	Manager struct {
		Name     string `json:"name"`
		Username string `json:"username"`
		Number   string `json:"number"`
		Email    string `json:"email"`
	} `json:"manager"`
	Status struct {
		Code string `json:"code"`
		Name string `json:"name"`
	} `json:"status"`
	DeptPath string `json:"dept_path"`
}

// Data ...
type Data struct {
	KwaiUserID         string `json:"kwai_user_id"`
	Username           string `json:"username"`
	ID                 string `json:"id"`
	Name               string `json:"name"`
	AvatarURL          string `json:"avatarUrl"`
	OrgDisplayName     string `json:"orgDisplayName"`
	Gender             string `json:"gender"`
	EmployeeNatureCode string `json:"employeeNatureCode"`
	Email              string `json:"email"`
	StatusCode         string `json:"statusCode"`
	IfFrontLine        bool   `json:"ifFrontLine"`
}

// I18N ...
type I18N struct {
	ZhCN string `json:"zhCN"`
	EnUS string `json:"enUS"`
}

// NewUserKimRequest ...
func NewUserKimRequest(username string) (req *UserKimRequest) {
	req = &UserKimRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/openapi/v2/user/user/" + username,
			Method: "GET",
			Header: map[string]string{"Content-Type": "application/json"},
		},
	}
	return
}

// NewUserKimResponse ...
func NewUserKimResponse() *UserKimResponse {
	return &UserKimResponse{
		BaseResponse: &response.BaseResponse{},
	}
}

// GetKimUser ...
func (c *Client) GetKimUser(req *UserKimRequest) (resp *UserKimResponse, err error) {
	resp = NewUserKimResponse()

	err = c.Send(req, resp)
	return
}

func NewIHRUsersSearchRequest(username string) (req *IHRUsersSearchRequest) {
	req = &IHRUsersSearchRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/mdata/v1/persons/-/simple/limit?name=" + username,
			Method: "GET",
			Header: map[string]string{"Content-Type": "application/json"},
		},
	}
	return
}

func NewIHRSearchResponse() *IHRUsersSearchResponse {
	return &IHRUsersSearchResponse{
		BaseResponse: &response.BaseResponse{},
	}
}
func (c *Client) GetIHRSearch(req *IHRUsersSearchRequest) (resp *IHRUsersSearchResponse, err error) {
	resp = NewIHRSearchResponse()

	err = c.Send(req, resp)
	return
}
