package openapi

import (
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func TestGenerateBusinessId(t *testing.T) {
	businessID := GenerateBPMBusinessID("server_network_policy")
	t.Log(businessID)
}

func initTestConfig() error {
	viper.SetConfigFile("/Users/<USER>/GolandProjects/Knoc/knoc/config/conf/config.yaml")
	viper.SetConfigType("yaml")
	viper.AutomaticEnv()
	viper.SetEnvPrefix("Knoc")
	replacer := strings.NewReplacer(".", "_")
	viper.SetEnvKeyReplacer(replacer)
	if err := viper.ReadInConfig(); err != nil {
		return err
	}

	return nil
}

// TestBPMProcessStart 测试工单发起功能
func TestBPMProcessStart(t *testing.T) {
	// 初始化配置
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	// 准备参数
	processKey := "server_network_policy"
	businessId := GenerateBPMBusinessID(processKey)
	businessExplain := fmt.Sprintf("%s-%s", "后端测试提交", time.Now().Format("20060102150405"))
	username := "maxiaoyu"
	comments := "后端测试"
	processVariables := ProcessVariables{}
	// 生成新的opensdk的对象
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	resp, err := sdk.BPMProcessStart(processKey, businessId, businessExplain, username, comments, processVariables)
	if err != nil {
		t.Log("Error, ", err)
	}
	t.Logf("工单ID为%s, Response为%#v", businessId, resp)
}

// TestBPMDetail 测试获取工单详情
func TestBPMDetail(t *testing.T) { // 初始化配置
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}

	businessId := "server_network_policy_20220713172631"

	resp, err := sdk.GetBPMDetail(businessId)
	if err != nil {
		t.Log("Error, ", err)
	}
	for _, v := range resp.DetailTasks {
		t.Log(v.TaskID, v.TaskName, v.UserName, v.TaskKey)
	}
}

func TestBPMPass(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper init Failed, err is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	userId := "dengxiaohui"
	comments := "通过"
	taskId := "618539220"
	resp, err := sdk.GetBPMPass(userId, comments, taskId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

/*
这个接口官方提供的错误response并不是很明确，比如说无法审批的错误的taskId号，返回的错误信息是bpm reject errors
但是比如说当前提交的拒绝人根本不是审批人列表中的用户的时候，也会返回bpm reject ERROR，因此无法判断具体的问题。
*/
func TestBPMReject(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	userId := "zhukun03"
	comments := "测试后端审批"
	taskId := "74474171"
	targetNodeId := "application"
	resp, err := sdk.GetBPMReject(userId, comments, taskId, targetNodeId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

// TestBPMConsult 如果当前审批人和userId不匹配的话，会报错msg fail，code为1，code类型为string
// processkey参数缺少校验，实际测试，ProcessKey填错了这个接口也能用。
func TestBPMConsult(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	assignee := "guanqinglin"
	comments := "Test Consult"
	processkey := "server_network_policy"
	taskId := "74474171"
	userId := "zhukun03"
	resp, err := sdk.GetBPMConsult(assignee, comments, processkey, taskId, userId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

/*
不合法的内容统一返回的内容是msg fail
1. 当前用户不是本人
2，oldAssignee不在审批人列表
*/
func TestBPMShiftSign(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	assignee := "gaoning"
	oldAssignee := "wb_zhaokang"
	comments := "后端转签测试"
	taskIds := []string{"74507166"}
	userId := "maxiaoyu"
	resp, err := sdk.GetBPMShiftSign(assignee, oldAssignee, comments, userId, taskIds)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

func TestBPMFrontAddSign(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	assignee := "caiqinglan"
	comments := "后端前加签测试"
	taskId := "74474009"
	userId := "maxiaoyu"
	resp, err := sdk.GetBPMFrontAddSign(assignee, taskId, comments, userId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

func TestBPMAfterAddSign(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	assignee := "quchen"
	comments := "后端后加签测试"
	taskId := "74474009"
	userId := "guoruiming"
	resp, err := sdk.GetBPMAfterAddSign(assignee, taskId, comments, userId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

func TestBPMRevokeForInitiator(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	businessId := "server_network_policy_20211214192928"
	comments := "测试发起人撤回工单"
	userId := "wb_zhaokang"
	resp, err := sdk.GetBPMRevokeForInitiator(businessId, comments, userId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}

// TestGetBPMApprovingCount 测试结果，如果说获取的processKey写错的话，不会报错说你processKey错了，只会返回0个。
func TestGetBPMApprovingCount(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	processKeys := []string{"server_network_policy"}
	UserId := "maxiaoyu"
	resp, err := sdk.GetBPMApprovingCount(processKeys, UserId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message, resp.Count)
	}
}

func TestSendKimCard(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewCardSDK("", viper.GetString("knoc"), viper.GetString("openapi.url"), true)
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}

	actionBtn1 := &InterActiveRequestCardMixCardBlockAction{
		Style: "blue",
		Type:  "button",
		Text: &struct {
			Content string `json:"content,omitempty"`
			Type    string `json:"type,omitempty"`
		}{
			Content: "跳转详情",
			Type:    "plainText",
		},
		Url: "https://knoc.test.gifshow.com/workorder/agency",
	}

	actionBtn2 := &InterActiveRequestCardMixCardBlockAction{
		Style: "red",
		Type:  "button",

		Text: &struct {
			Content string `json:"content,omitempty"`
			Type    string `json:"type,omitempty"`
		}{
			Content: "忽略",
			Type:    "plainText",
		},
	}

	mixCardTitle := &InterActiveRequestCardMixCardBlock{
		BlockID: "1",
		Type:    "content",
		Text: &struct {
			Content string `json:"content,omitempty"`
			Type    string `json:"type,omitempty"`
		}{
			Type:    "kimMd",
			Content: "## 工单流程执行失败",
		},
	}
	mixCardBlockBtnHeader := &InterActiveRequestCardMixCardBlock{
		BlockID: "1",
		Type:    "content",
		Text: &struct {
			Content string `json:"content,omitempty"`
			Type    string `json:"type,omitempty"`
		}{
			Type:    "plainText",
			Content: fmt.Sprintf("工单标题：%s\n工单创建人：%s\n工单执行人: %s", "Title", "wb_zhaokang", "wb_zhaokang"),
		},
	}

	mixCardBlockBtn := &InterActiveRequestCardMixCardBlock{
		BlockID: "12",
		Type:    "action",
		Actions: []InterActiveRequestCardMixCardBlockAction{
			*actionBtn1,
			*actionBtn2,
		},
		Layout: "auto",
	}

	i := &InterActiveRequestCard{
		MsgType:  "mixCard",
		Username: "wb_zhaokang",
		MixCard: InterActiveRequestCardMixCard{
			AppKey:      viper.GetString("knoc"),
			UpdateMulti: 1,
			Config: InterActiveRequestCardMixCardConfig{
				Forward:     true,
				ForwardType: 2,
			},
			Blocks: []InterActiveRequestCardMixCardBlock{
				*mixCardTitle,
				*mixCardBlockBtnHeader,
				*mixCardBlockBtn,
			},
		},
	}
	resp, err := sdk.SendKimInterActiveMsg("mixCard", "", i)
	if err != nil {
		zap.L().Error(fmt.Sprintf("Send Message Card to User %s failed, err is %s", "wb_zhaokang", err))
	}
	if resp.InterActiveMixCardResponse.Status != 0 {
		zap.L().Error(fmt.Sprintf("Send Message Card to User %s failed, err is %s", "wb_zhaokang", resp.InterActiveMixCardResponse.Message))
	}
}

func TestGetKimUser(t *testing.T) {
	err := initTestConfig()
	if err != nil {
		t.Log("Viper Read Config File Error, error is ", err)
	}
	sdk, err := NewOpenSDK()
	if err != nil {
		t.Logf("NewOpenSDK Error err is %s", err)
	}
	//processKeys := []string{"server_network_policy"}
	UserId := "maxiaoyu"
	resp, err := sdk.GetKimUser(UserId)
	if err != nil {
		t.Log("Error ", err)
	} else {
		t.Log(resp.Code, resp.Message)
	}
}
