package openapi

import (
	"github.com/spf13/viper"
)

// OpenSDK ...
type OpenSDK interface {
	GetKimUser(username string) (*UserKimResponse, error)
	// BPMProcessStart BPM流程启动
	BPMProcessStart(processKey, businessId, businessExplain, username, comment string,
		processVariables ProcessVariables) (*BPMCommonResponse, error)
	// GetBPMDetail 获取流程工单详情
	GetBPMDetail(processKey string) (res *BPMCommonResponse, err error)
	// GetBPMPass 同意工单
	GetBPMPass(userId, comment, taskId string) (res *BPMCommonResponse, err error)
	// GetBPMReject 拒绝工单
	GetBPMReject(userId, comment, taskId, targetNodeId string) (res *BPMCommonResponse, err error)
	// GetBPMConsult BPM征询
	GetBPMConsult(assignee, comments, processkey, taskId, userId string) (res *BPMCommonResponse, err error)
	// GetBPMShiftSign BPM转签
	GetBPMShiftSign(assignee, oldAssignee, comments, userId string, taskId []string) (res *BPMCommonResponse, err error)
	// GetBPMFrontAddSign 前加签
	GetBPMFrontAddSign(assignee, taskId, comments, userId string) (res *BPMCommonResponse, err error)
	// GetBPMAfterAddSign 后加签
	GetBPMAfterAddSign(assignee, taskId, comments, userId string) (res *BPMCommonResponse, err error)
	// GetBPMRevokeForInitiator 发起人撤回
	GetBPMRevokeForInitiator(businessId, comments, userId string) (res *BPMCommonResponse, err error)
	// GetBPMApprovingCount 获取待办数量
	GetBPMApprovingCount(processKeys []string, userId string) (res *BPMApprovingCountResponse, err error)

	// CardSDK 相关接口
	// SendKimInterActiveMsg 向kim发送Card 信息
	SendKimInterActiveMsg(msgType, username string, i *InterActiveRequestCard) (*InteractiveMsgCommonResponse, error)
	GetIHRUsersSearch(username string) (*IHRUsersSearchResponse, error)
}

type openSDK struct {
	Type   string
	Token  string
	Client *Client
}

var _ OpenSDK = (*openSDK)(nil)

// NewOpenSDK 初始化OpenSDK
func NewOpenSDK() (OpenSDK, error) {
	c, err := NewClientWithOpenAPI("", "")
	if err != nil {
		return nil, err
	}
	// 从配置文件读取appKey
	t := NewTokenRequest(viper.GetString("openapi.key"), viper.GetString("openapi.secret"))
	token, err := c.GetToken(t)
	if err != nil {
		return nil, err
	}

	return &openSDK{
		Type:   "openapi",
		Token:  token.Result.AccessToken,
		Client: c,
	}, nil
}

// NewOpenSDK 初始化OpenSDK
func NewCardSDK(secretID, secretKey, endPoint string, useToken bool) (OpenSDK, error) {
	c, err := NewCardWithOpenAPI(secretID, secretKey, endPoint)
	if err != nil {
		return nil, err
	}
	// 声明一个token变量用于保存token字符串
	var token string
	if useToken {
		t := NewTokenRequest("secretKey", "")
		tokenResp, err := c.GetToken(t)
		if err != nil {
			return nil, err
		}
		token = tokenResp.Result.AccessToken
	} else {
		token = ""
	}

	return &openSDK{
		Type:   "openapi",
		Token:  token,
		Client: c,
	}, nil
}

// SendKimInterActiveMsg 发送Kim交互式的消息，包含消息卡片等
func (o *openSDK) SendKimInterActiveMsg(msgType, username string, i *InterActiveRequestCard) (*InteractiveMsgCommonResponse, error) {
	s := NewKimInterActiveMessageSendToUserRequest(msgType, username, i)
	s.AddHeader("Authorization", "Bearer "+o.Token)
	res, err := o.Client.GetKimInterActiveMessageSendToUserResponse(s)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetKimUser(username string) (*UserKimResponse, error) {
	k := NewUserKimRequest(username)
	k.AddHeader("Authorization", "Bearer "+o.Token)
	res, err := o.Client.GetKimUser(k)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// BPMProcessStart 发起流程
func (o *openSDK) BPMProcessStart(
	processKey, businessId, businessExplain, username, comment string,
	processVariables ProcessVariables) (*BPMCommonResponse, error) {
	b := NewBPMProcessStartRequest(processKey, businessId, businessExplain, username, comment, processVariables)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err := o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

// GetBPMDetail 获取BPM流程信息详情
func (o *openSDK) GetBPMDetail(businessId string) (res *BPMCommonResponse, err error) {
	b := NewBPMProcessDetailRequest(businessId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMDetailResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

// GetBPMPass 获取BPM工单响应内容
func (o *openSDK) GetBPMPass(userId, comment, taskId string) (res *BPMCommonResponse, err error) {
	b := NewBPMPassRequest(userId, comment, taskId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMPassResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMReject(userId, comment, taskId, targetNodeId string) (res *BPMCommonResponse, err error) {
	b := NewBPMRejectRequest(userId, comment, taskId, targetNodeId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMConsult(assignee, comments, processkey, taskId, userId string) (res *BPMCommonResponse, err error) {
	b := NewBPMConsultRequest(assignee, comments, processkey, taskId, userId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMShiftSign(
	assignee, oldAssignee, comments, userId string, taskIds []string) (res *BPMCommonResponse, err error) {
	b := NewBPMShiftSignRequest(assignee, oldAssignee, comments, userId, taskIds)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMFrontAddSign(
	assignee, taskId, comments, userId string) (res *BPMCommonResponse, err error) {
	b := NewBPMFrontAddSignRequest(assignee, taskId, comments, userId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMAfterAddSign(
	assignee, taskId, comments, userId string) (res *BPMCommonResponse, err error) {
	b := NewBPMAfterAddSignRequest(assignee, taskId, comments, userId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMRevokeForInitiator(
	businessId, comments, userId string) (res *BPMCommonResponse, err error) {
	b := NewBPMRevokeForInitiatorRequest(businessId, comments, userId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.GetBPMCommonResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (o *openSDK) GetBPMApprovingCount(processKeys []string, userId string) (res *BPMApprovingCountResponse, err error) {
	b := NewBPMApprovingCountRequest(processKeys, userId)
	b.AddHeader("Authorization", "Bearer "+o.Token)
	res, err = o.Client.BPMApprovingCountResponse(b)
	if err != nil {
		return nil, err
	}
	return res, err
}
func (o *openSDK) GetIHRUsersSearch(username string) (*IHRUsersSearchResponse, error) {
	i := NewIHRUsersSearchRequest(username)
	i.AddHeader("Authorization", "Bearer "+o.Token)
	res, err := o.Client.GetIHRSearch(i)
	if err != nil {
		return nil, err
	}
	return res, err
}
