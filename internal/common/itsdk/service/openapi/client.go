package openapi

import (
	"github.com/spf13/viper"

	"ks-knoc-server/internal/common/itsdk/sdk"
)

const (
	// 测试环境的域名
	testEndpoint = "is-gateway-test.corp.kuaishou.com"
	// 正式环境的域名
	defaultEndpoint = "is-gateway.corp.kuaishou.com"
	serviceName     = "OpenAPI"
)

// Client ...
type Client struct {
	sdk.Client
}

// NewClientWithOpenAPI ...
func NewClientWithOpenAPI(secretID, secretKey string) (client *Client, err error) {
	client = &Client{}
	config := sdk.NewConfig().WithEndpoint(viper.GetString("openapi.url"))
	client.Init(serviceName).WithSecret(secretID, secretKey).WithConfig(config)
	return
}

// NewClientWithOpenAPI 初始化client
func NewCardWithOpenAPI(secretID, secretKey, ep string) (client *Client, err error) {
	client = &Client{}
	// 初始化配置
	config := &sdk.Config{}
	// 如果说传递了ep，那么就以传递的ep为准，优先级最高
	if ep != "" {
		config = sdk.NewConfig().WithEndpoint(ep)
		// 如果说ep为空，配置文件的url不为空的话，那么就以配置文件的为准。
	} else if viper.GetString("openapi.url") != "" && ep == "" {
		config = sdk.NewConfig().WithEndpoint(viper.GetString("openapi.url"))
	} else {
		config = sdk.NewConfig().WithEndpoint(defaultEndpoint)
	}

	client.Init(serviceName).WithSecret(secretID, secretKey).WithConfig(config)
	return
}
