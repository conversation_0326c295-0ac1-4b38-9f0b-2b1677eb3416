package openapi

import (
	"ks-knoc-server/internal/common/itsdk/sdk/request"
	"ks-knoc-server/internal/common/itsdk/sdk/response"
)

// InterActiveMsgCommonRequest Kim交互式卡片消息请求
type InterActiveMsgCommonRequest struct {
	*request.BaseRequest
	*InterActiveRequestCard
}

// InteractiveMsgCommonResponse 通用交互式卡片消息响应
type InteractiveMsgCommonResponse struct {
	*response.BaseResponse
	*InterActiveMixCardResponse
}

// InterActiveRequestCard Kim开放能力请求
type InterActiveRequestCard struct {
	MixCard  InterActiveRequestCardMixCard `json:"mixCard"`
	MsgType  string                        `json:"msgType"`
	Username string                        `json:"username"`
}

// InterActiveRequestCardMixCard ...
type InterActiveRequestCardMixCard struct {
	AppKey      string                               `json:"appKey"`
	Blocks      []InterActiveRequestCardMixCardBlock `json:"blocks"`
	Config      InterActiveRequestCardMixCardConfig  `json:"config"`
	UpdateMulti int64                                `json:"updateMulti"`
}

// InterActiveRequestCardMixCardConfig ...
type InterActiveRequestCardMixCardConfig struct {
	Forward     bool  `json:"forward"`
	ForwardType int64 `json:"forwardType"`
}

// InterActiveRequestCardMixCardBlock ...
type InterActiveRequestCardMixCardBlock struct {
	Actions []InterActiveRequestCardMixCardBlockAction `json:"actions,omitempty"`
	BlockID string                                     `json:"blockId"`
	Layout  string                                     `json:"layout,omitempty"`
	Text    *struct {
		Content string `json:"content,omitempty"`
		Type    string `json:"type,omitempty"`
	} `json:"text,omitempty"`
	Type string `json:"type"`
}

// InterActiveRequestCardMixCardBlockAction ...
type InterActiveRequestCardMixCardBlockAction struct {
	Style string `json:"style"`
	Text  *struct {
		Content string `json:"content,omitempty"`
		Type    string `json:"type,omitempty"`
	} `json:"text,omitempty"`

	Type string `json:"type"`
	Url  string `json:"url,omitempty"`
}
type I18N1 struct {
	ZhCN string `json:"zhCN"`
	EnUS string `json:"enUS"`
}

type Confirm struct {
	Title struct {
		Type    string `json:"type"`
		Content string `json:"content"`
	} `json:"title"`
	Text struct {
		Type    string `json:"type"`
		Content string `json:"content"`
	} `json:"text"`
}

// NewKimInterActiveMessageSendToUserRequest 发送Kim交互式卡片信息给具体用户
func NewKimInterActiveMessageSendToUserRequest(msgType, username string, i *InterActiveRequestCard) (req *InterActiveMsgCommonRequest) {
	req = &InterActiveMsgCommonRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/openapi/v2/message/send",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		// InterActiveRequestCard Card请求
		InterActiveRequestCard: i,
	}
	return
}

// NewKimInterActiveMessageSendToUserResponse 构建Kim交互式卡片信息响应（For User）
func NewKimInterActiveMessageSendToUserResponse() *InteractiveMsgCommonResponse {
	return &InteractiveMsgCommonResponse{
		BaseResponse:               &response.BaseResponse{},
		InterActiveMixCardResponse: &InterActiveMixCardResponse{},
	}
}

// GetKimInterActiveMessageSendToUserResponse 获取GetKimInterActiveMessageSendToUserResponse
func (c *Client) GetKimInterActiveMessageSendToUserResponse(req request.Request) (resp *InteractiveMsgCommonResponse, err error) {
	resp = NewKimInterActiveMessageSendToUserResponse()
	err = c.Send(req, resp)
	return
}

// InterActiveMixCardResponse ...
type InterActiveMixCardResponse struct {
	Data struct {
		MessageKey string `json:"messageKey"`
	} `json:"data"`
	Host string `json:"host"`
	I18n struct {
		EnUS string `json:"enUS"`
		ZhCN string `json:"zhCN"`
	} `json:"i18n"`
	Message   string `json:"message"`
	Port      int64  `json:"port"`
	Status    int64  `json:"status"`
	Timestamp string `json:"timestamp"`
}
