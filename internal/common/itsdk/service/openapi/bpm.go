package openapi

import (
	"fmt"
	"time"

	"ks-knoc-server/internal/common/itsdk/sdk/request"
	"ks-knoc-server/internal/common/itsdk/sdk/response"
)

// GenerateBPMBusinessID 返回用户BPM工单BusinessID
// 用户工单逻辑为BPM流程ProcessKey + 时间戳，时间戳只精确到秒
// 示例：server_network_policy_2021121314230623
func GenerateBPMBusinessID(processKey string) (bpmID string) {
	bpmID = fmt.Sprintf("%s_%s", processKey, time.Now().Format("20060102150405"))
	return bpmID
}

// BPMCommonResponse 通用Response结构体
type BPMCommonResponse struct {
	*response.BaseBPMResponse
	*BPMDetailResult `json:"result"`
}

// NewBPMCommonResponse 通用BPMResponse
func NewBPMCommonResponse() *BPMCommonResponse {
	return &BPMCommonResponse{
		BaseBPMResponse: &response.BaseBPMResponse{},
		BPMDetailResult: &BPMDetailResult{
			DetailTasks: []TaskDetail{},
		},
	}
}

// GetBPMCommonResponse 获取CommonResponse
func (c *Client) GetBPMCommonResponse(req request.Request) (resp *BPMCommonResponse, err error) {
	resp = NewBPMCommonResponse()
	err = c.Send(req, resp)
	return
}

// BPMProcessStartRequest BPM启动流程Request
type BPMProcessStartRequest struct {
	*request.BaseRequest
	// ProcessKey 由专业流程设计器设计的时候指定，这个key是唯一不变的
	ProcessKey string `json:"processKey"`
	// BusinessID 工单的ID，这个ID标识当前的工单的ID号是什么，bpm提交给了业务侧来实现，并没有规律，暂定process_key + timestamp
	BusinessID string `json:"businessId"`
	// BusinessExplain 工单说明
	BusinessExplain string `json:"businessExplain"`
	// FormType 暂时没有具体的应用，接口文档要求和ProcessKey保持一致
	FormType string `json:"formType"`
	// Username 工单的发起人
	Username string `json:"username"`
	// Comment 工单发起的评论
	Comments string `json:"comments"`
	// ProcessVariables 保存流程变量，流程变量主要用于判断流程分支走向
	ProcessVariables `json:"processVariables"`
}

// ProcessVariables 流程变量，流程变量主要用于控制流程分支
type ProcessVariables struct{}

// NewBPMProcessStartRequest 发起工单
func NewBPMProcessStartRequest(
	processKey, businessId, businessExplain, username, comments string,
	processVariables ProcessVariables) (req *BPMProcessStartRequest) {
	req = &BPMProcessStartRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/start",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		ProcessKey:       processKey,
		BusinessID:       businessId,
		BusinessExplain:  businessExplain,
		FormType:         processKey,
		Username:         username,
		Comments:         comments,
		ProcessVariables: processVariables,
	}
	return
}

type TaskDetail struct {
	// ApproveTime 审核时间
	ApproveTime string        `json:"approveTime"`
	Attachments []interface{} `json:"attachments"`
	// Comments 审批评论，备注
	Comments   string `json:"comments"`
	CreateTime string `json:"createTime"`
	// Extend1 扩展字段1
	Extend1 interface{} `json:"extend1"`
	// Extend2 扩展字段2
	Extend2 interface{} `json:"extend2"`
	// Extend3 扩展字段3
	Extend3 string `json:"extend3"`
	// IsAgentApprover 是否是代理人
	IsAgentApprover int64 `json:"isAgentApprover"`
	// Operation 操作
	Operation string `json:"operation"`
	// OperationName 操作中文名称
	OperationName   string `json:"operationName"`
	OperationResult string `json:"operationResult"`
	// TaskID 任务ID
	TaskID string `json:"taskId"`
	// TaskKey 任务key
	TaskKey string `json:"taskKey"`
	// TaskName 任务名字
	TaskName string `json:"taskName"`
	// UserID 审核人工号
	UserID string `json:"userId"`
	// UserIDAgented 代理人ID
	UserIDAgented string      `json:"userIdAgented"`
	UserList      interface{} `json:"userList"`
	// UserName 审核人姓名
	UserName string `json:"userName"`
	// UserUsername 审核人用户名
	UserUsername string `json:"userUsername"`
}

type BPMDetailResult struct {
	// BusinessID 业务ID
	BusinessID string `json:"businessId"`
	// FormType 表单类型，同processKey
	FormType string `json:"formType"`
	// WorkFlowID 流程ID
	WorkFlowID string `json:"workFlowId"`
	// WorkFlowName 流程名称
	WorkFlowName string `json:"workFlowName"`
	// ApplyExplain 申请说明
	ApplyExplain string `json:"applyExplain"`
	// InstanceID 流程实例ID
	InstanceID string `json:"instanceId"`
	// InitiatorID 发起人工号
	InitiatorID string `json:"initiatorId"`
	// InitiatorName 发起人姓名
	InitiatorName string `json:"initiatorName"`
	// CurrentAuditName 当前审批人
	CurrentAuditName string `json:"currentAuditName"`
	// CurrentAuditUserNameList 当前审批人用户名列表
	CurrentAuditUserNameList []string `json:"currentAuditUserNameList"`
	// InitiatorUserName 发起人用户名
	InitiatorUsername string `json:"initiatorUsername"`
	// InitiatorOrg 发起人部门
	InitiatorOrg string `json:"initiatorOrg"`
	// InitiatorTime 发起时间
	InitiatorTime string `json:"initiatorTime"`
	// Avatar 发起人头像
	Avatar string `json:"avatar"`
	// ProcessState 流程Code
	ProcessState string `json:"processState"`
	// ProcessStateName 流程名称
	ProcessStateName string `json:"processStateName"`
	// ProcessKey 流程的ProcessKey
	ProcessKey string `json:"processKey"`
	// HasToDoTask 是否有未完成的Task
	HasToDoTask bool `json:"hasToDoTask"`
	// DetailTasks task明细
	DetailTasks []TaskDetail `json:"detailTasks"`
}

type BPMProcessDetailRequest struct {
	*request.BaseRequest
	BusinessID string `json:"businessId"`
}

func NewBPMProcessDetailRequest(businessId string) (req *BPMProcessDetailRequest) {
	req = &BPMProcessDetailRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/detail",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		BusinessID: businessId,
	}
	return
}

func NewBPMDetailResponse() *BPMCommonResponse {
	return &BPMCommonResponse{
		BaseBPMResponse: &response.BaseBPMResponse{},
		BPMDetailResult: &BPMDetailResult{
			DetailTasks: []TaskDetail{},
		},
	}
}

func (c *Client) GetBPMDetailResponse(req request.Request) (resp *BPMCommonResponse, err error) {
	resp = NewBPMDetailResponse()
	err = c.Send(req, resp)
	return
}

type BPMPassRequest struct {
	*request.BaseRequest
	UserID   string `json:"userId"`
	Comments string `json:"comments"`
	TaskID   string `json:"taskId"`
}

// NewBPMPassRequest creates a new BPMPassRequest
func NewBPMPassRequest(userid, comments, taskid string) (req *BPMPassRequest) {
	req = &BPMPassRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/pass",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		UserID:   userid,
		Comments: comments,
		TaskID:   taskid,
	}
	return
}

// GetBPMPassResponse 获取BPM通过的通用Response
func (c *Client) GetBPMPassResponse(req request.Request) (resp *BPMCommonResponse, err error) {
	resp = NewBPMCommonResponse()
	err = c.Send(req, resp)
	return
}

type BPMRejectRequest struct {
	*request.BaseRequest
	UserID       string `json:"userId"`
	Comments     string `json:"comments"`
	TaskID       string `json:"taskId"`
	TargetNodeId string `json:"targetNodeId"`
}

func NewBPMRejectRequest(userId, comments, taskId, targetNodeId string) (req *BPMRejectRequest) {
	req = &BPMRejectRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/reject",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		UserID:       userId,
		Comments:     comments,
		TaskID:       taskId,
		TargetNodeId: targetNodeId,
	}
	return req
}

type BPMConsultRequest struct {
	*request.BaseRequest
	// Assignee 要征询的用户
	Assignee   string `json:"assignee"`
	Comments   string `json:"comments"`
	ProcessKey string `json:"processKey"`
	TaskID     string `json:"taskId"`
	UserID     string `json:"userId"`
}

func NewBPMConsultRequest(assignee, comments, processkey, taskId, userId string) (req *BPMConsultRequest) {
	req = &BPMConsultRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/consult",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		Assignee:   assignee,
		Comments:   comments,
		ProcessKey: processkey,
		TaskID:     taskId,
		UserID:     userId,
	}
	return
}

type BPMShiftSignRequest struct {
	*request.BaseRequest
	Assignee    string   `json:"assignee"`
	OldAssignee string   `json:"oldAssignee"`
	Comments    string   `json:"comments"`
	TaskIDs     []string `json:"taskIds"`
	UserID      string   `json:"userId"`
}

// NewBPMShiftSignRequest BPM转签 经过实际的测试发现，转签可以由他人来操作，比如说当前有a,b,c三个人，b可以把a做转签，转签到d用户
func NewBPMShiftSignRequest(
	assignee, oldAssignee, comments, userId string, taskIds []string) (req *BPMShiftSignRequest) {
	req = &BPMShiftSignRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/shiftSign",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		Assignee:    assignee,
		OldAssignee: oldAssignee,
		Comments:    comments,
		TaskIDs:     taskIds,
		UserID:      userId,
	}
	return
}

type BPMFrontAddSignRequest struct {
	*request.BaseRequest
	Assignee          string `json:"assignee"`
	Comments          string `json:"comments"`
	TaskID            string `json:"taskId"`
	UserID            string `json:"userId"`
	*ProcessVariables `json:"variables"`
}

func NewBPMFrontAddSignRequest(
	assignee, taskId, comments, userId string) (req *BPMFrontAddSignRequest) {
	req = &BPMFrontAddSignRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/frontAddSign",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		Assignee:         assignee,
		Comments:         comments,
		TaskID:           taskId,
		UserID:           userId,
		ProcessVariables: &ProcessVariables{},
	}
	return
}

type BPMAfterAddSignRequest struct {
	*request.BaseRequest
	Assignee          string `json:"assignee"`
	Comments          string `json:"comments"`
	TaskID            string `json:"taskId"`
	UserID            string `json:"userId"`
	*ProcessVariables `json:"variables"`
}

func NewBPMAfterAddSignRequest(
	assignee, taskId, comments, userId string) (req *BPMAfterAddSignRequest) {
	req = &BPMAfterAddSignRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/afterAddSign",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		Assignee:         assignee,
		Comments:         comments,
		TaskID:           taskId,
		UserID:           userId,
		ProcessVariables: &ProcessVariables{},
	}
	return
}

type BPMRevokeForInitiator struct {
	*request.BaseRequest
	BusinessID string `json:"businessId"`
	Comments   string `json:"comments"`
	UserID     string `json:"userId"`
}

// NewBPMRevokeForInitiatorRequest 该接口目前存在bug，申请人不是发起人或者审批相关人也可以撤销审批
func NewBPMRevokeForInitiatorRequest(businessId, comments, userId string) (req *BPMRevokeForInitiator) {
	req = &BPMRevokeForInitiator{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/revokeForInitiator",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		BusinessID: businessId,
		Comments:   comments,
		UserID:     userId,
	}
	return
}

type BPMApprovingCount struct {
	*request.BaseRequest
	ProcessKeys []string `json:"processKeys"`
	UserID      string   `json:"userId"`
}

type BPMApprovingCountResponse struct {
	*response.BaseBPMResponse
	Count int `json:"result"`
}

func NewBPMApprovingCountRequest(processKeys []string, userId string) (req *BPMApprovingCount) {
	req = &BPMApprovingCount{
		BaseRequest: &request.BaseRequest{
			URL:    "/ks-bpm/bpm-activiti-web/api/v1/process/task/approving/count",
			Method: "POST",
			Header: map[string]string{"Content-Type": "application/json"},
		},
		ProcessKeys: processKeys,
		UserID:      userId,
	}
	return
}

func (c *Client) BPMApprovingCountResponse(req request.Request) (resp *BPMApprovingCountResponse, err error) {
	resp = &BPMApprovingCountResponse{
		BaseBPMResponse: &response.BaseBPMResponse{},
	}
	err = c.Send(req, resp)
	return
}
