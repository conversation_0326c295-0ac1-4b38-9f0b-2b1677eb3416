package openapi

import (
	"ks-knoc-server/internal/common/itsdk/sdk"
)

const (
	defaultEndpoint = "otp.corp.kuaishou.com"
	serviceName     = "KimOpen"
)

// Client ...
type Client struct {
	sdk.Client
}

// NewClientWithSecurity ...
func NewClientWithSecurity(secretID, secretKey string) (client *Client, err error) {
	client = &Client{}
	config := sdk.NewConfig().WithEndpoint(defaultEndpoint)
	client.Init(serviceName).WithSecret(secretID, secretKey).WithConfig(config)
	return
}
