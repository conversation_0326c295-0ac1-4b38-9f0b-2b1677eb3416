package openapi

// SecuritySDK 安全SDK
type SecuritySDK interface {
	AuthSecurityOTP(username, code string) (*OTPResponse, error)
}

type securitySDK struct {
	Client *Client
}

var _ SecuritySDK = (*securitySDK)(nil)

// NewSecuritySDK ...
func NewSecuritySDK() (SecuritySDK, error) {
	c, err := NewClientWithSecurity("", "")
	if err != nil {
		return nil, err
	}

	return &securitySDK{
		Client: c,
	}, nil
}

func (s *securitySDK) AuthSecurityOTP(username, code string) (*OTPResponse, error) {
	e := NewOTPRequest(username, code)
	res, err := s.Client.AuthOTP(e)
	if err != nil {
		return nil, err
	}
	return res, nil
}
