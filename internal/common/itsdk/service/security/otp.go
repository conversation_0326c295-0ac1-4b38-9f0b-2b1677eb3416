package openapi

import (
	"ks-knoc-server/internal/common/itsdk/sdk/request"
	"ks-knoc-server/internal/common/itsdk/sdk/response"
)

// OTPRequest ...
type OTPRequest struct {
	*request.BaseRequest
	Code     string `json:"code"`
	AppToken string `json:"app_token"`
}

// OTPResponse ...
type OTPResponse struct {
	*response.BaseResponse
	ErrCode   int    `json:"err_code"`
	FailedCnt int    `json:"failed_cnt"`
	Msg       string `json:"msg"`
	Success   bool   `json:"success"`
}

// NewOTPRequest ...
func NewOTPRequest(username, code string) (req *OTPRequest) {
	req = &OTPRequest{
		BaseRequest: &request.BaseRequest{
			URL:    "/api/v1/verify?username=" + username,
			Method: "POST",
		},
		Code:     code,
		AppToken: "",
	}
	return
}

// NewOTPResponse ...
func NewOTPResponse() *OTPResponse {
	return &OTPResponse{
		BaseResponse: &response.BaseResponse{},
	}
}

// AuthOTP ...
func (c *Client) AuthOTP(req *OTPRequest) (resp *OTPResponse, err error) {

	resp = NewOTPResponse()

	err = c.Send(req, resp)
	return
}
