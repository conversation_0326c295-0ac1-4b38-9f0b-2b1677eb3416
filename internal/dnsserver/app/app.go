package app

import (
	"context"
	"errors"
	"log"
	"net/http"
	"sync/atomic"
	"time"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/dnsserver/options"
	"ks-knoc-server/internal/dnsserver/server"
	v1 "ks-knoc-server/internal/dnsserver/service/v1"
	"ks-knoc-server/pkg/shutdown"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// App ...
type App struct {
	basename    string
	name        string
	description string
	options     *options.Options
	runFunc     RunFunc
	cacheReady  atomic.Bool // 使用原子操作的布尔值来管理缓存就绪状态
}

// RunFunc ...
type RunFunc func(basename string) error

// Option ...
type Option func(*App)

// WithOptions ...
func WithOptions(opt *options.Options) Option {
	return func(a *App) {
		a.options = opt
		a.options.LogsOptions.FileRename(config.APIName)
	}
}

// WithRunFunc ...
func WithRunFunc(run RunFunc) Option {
	return func(a *App) {
		a.runFunc = run
	}
}

// NewApp ...
func NewApp(name, basename string, opts ...Option) *App {
	a := &App{
		name:     name,
		basename: basename,
	}

	// 初始化缓存就绪状态为false
	a.cacheReady.Store(false)

	for _, o := range opts {
		o(a)
	}
	return a
}

// Run 启动服务
func (a *App) Run() {
	var err error

	// 检查缓存就绪状态的函数
	isCacheReady := func() bool {
		return a.cacheReady.Load()
	}

	// 创建server，传入缓存就绪检查函数
	srv, err := server.NewServer(a.options, isCacheReady)
	if err != nil {
		log.Fatal("server startup err: " + err.Error())
	}

	// 程序意外关闭之前，将内存中的日志flush到磁盘
	defer func() {
		_ = srv.Log.Sync()
	}()

	// 初始化service层
	ds := srv.GetDataStore()
	svc := v1.NewService(ds)
	recordSvc := svc.Record()

	// 在原有路由基础上添加一个中间件，确保请求在缓存就绪后才能处理
	srv.Web.Use(func(c *gin.Context) {
		// 健康检查接口不需要阻塞
		if c.Request.URL.Path == "/check/health" {
			c.Next()
			return
		}

		// 检查缓存是否就绪
		if !a.cacheReady.Load() {
			// 如果缓存未就绪，返回503
			core.SendResponse(c, nil, errors.New("服务正在初始化RPZ缓存，请稍后重试"))
			c.Abort()
			return
		}

		c.Next()
	})

	// 启动一个goroutine初始化RPZ缓存
	go func() {
		srv.Log.Info("开始初始化RPZ缓存")
		// 调用recordService的初始化方法
		if err := recordSvc.InitRpzCache(context.Background()); err != nil {
			srv.Log.Error("RPZ缓存初始化失败", zap.Error(err))
		} else {
			// 缓存初始化成功，设置就绪状态为true
			a.cacheReady.Store(true)
			srv.Log.Info("RPZ缓存初始化完成，服务已就绪")
		}
	}()

	srv.Log.Info("DnsServer Start Succeed.")

	a.options.WebOptions.Server.Handler = srv.Web
	if err = a.options.WebOptions.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		srv.Log.Fatal("http server startup err", zap.Error(err))
	}

	shutdown.NewHook().Close(
		func() {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			if err = a.options.WebOptions.Shutdown(ctx); err != nil {
				srv.Log.Error("server shutdown err", zap.Error(err))
			}
		},
	)
}
