package options

import (
	"encoding/json"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/http"
	"ks-knoc-server/internal/common/logger"
	"ks-knoc-server/internal/common/mq"

	"github.com/spf13/pflag"
)

var cfg = pflag.StringP("config", "c", "", "Config file path.")

// Options 初始化依赖的选项
type Options struct {
	WebOptions   *http.WebOptions   `json:"http"`
	MySQLOptions *db.MySQLOptions   `json:"mysql"`
	LogsOptions  *logger.LogOptions `json:"log"`
	KafkaOptions *mq.KafkaOptions   `json:"kafka"`
}

// NewOptions 配置文件以及依赖项初始化
func NewOptions() *Options {
	pflag.Parse()

	if err := config.NewConf(*cfg); err != nil {
		panic(err)
	}

	o := Options{
		WebOptions:   http.NewWebOptions(),
		MySQLOptions: db.NewMySQLOptions(),
		LogsOptions:  logger.NewLogOptions(),
		KafkaOptions: mq.NewKafkaOptions(),
	}
	return &o
}

func (o *Options) String() string {
	data, _ := json.Marshal(o)
	return string(data)
}
