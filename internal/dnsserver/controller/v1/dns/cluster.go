package dns

import (
	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"

	param "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/core"
)

// UpdateCluster 更新集群
func (dc *DnsController) UpdateCluster(c *gin.Context) {
	zap.L().Debug("UpdateCluster Function Called")
	span, ctx := apm.StartSpan(c, "UpdateCluster", "controller")
	defer span.End()

	var req param.UpdateClusterParam
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UpdateCluster BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("UpdateCluster Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := dc.svc.Cluster().UpdateCluster(ctx, &req); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (dc *DnsController) CreateCluster(c *gin.Context) {
	zap.L().Debug("CreateCluster Function Called")
	span, ctx := apm.StartSpan(c, "CreateCluster", "controller")
	defer span.End()

	var req param.ClusterParam
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("CreateCluster BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("CreateCluster Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := dc.svc.Cluster().CreateCluster(ctx, &req); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// GetCluster 查询集群列表
func (dc *DnsController) GetClusterList(c *gin.Context) {
	zap.L().Debug("GetCluster Function Called")
	span, ctx := apm.StartSpan(c, "GetCluster", "controller")
	defer span.End()

	clusters, err := dc.svc.Cluster().GetClusterList(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, clusters)
}
