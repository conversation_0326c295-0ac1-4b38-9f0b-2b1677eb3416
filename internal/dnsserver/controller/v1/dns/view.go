package dns

import (
	"errors"
	param "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/core"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (dc *DnsController) GetViewByID(c *gin.Context) {
	zap.L().Debug("GetViewByID Function called")
	span, ctx := apm.StartSpan(c, "GetViewByID", "controller")
	defer span.End()

	viewID, err := strconv.ParseInt(c.Param("view_id"), 10, 64)
	if err != nil {
		zap.L().Error("GetViewByID ParseInt Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if viewID == 0 {
		core.SendResponse(c, errors.New("view_id不合法"), nil)
		return
	}

	view, err := dc.svc.View().GetViewByID(ctx, viewID)
	if err != nil {
		zap.L().Error("GetViewByID Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, view)
}

// GetViewTree 获取视图树
func (dc *DnsController) GetViewTree(c *gin.Context) {
	zap.L().Debug("GetViewTree Function called")
	span, ctx := apm.StartSpan(c, "GetViewTree", "controller")
	defer span.End()

	tree, err := dc.svc.View().GetViewTree(ctx)
	if err != nil {
		zap.L().Error("GetViewTree Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, tree)
}

// CreateView 创建视图
func (dc *DnsController) CreateView(c *gin.Context) {
	zap.L().Debug("CreateView Function called")
	span, ctx := apm.StartSpan(c, "CreateView", "controller")
	defer span.End()

	var req param.CreateViewParams
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("CreateView ShouldBindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 校验参数
	if err := req.Validate(); err != nil {
		zap.L().Error("CreateView Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 创建视图
	if err := dc.svc.View().CreateView(ctx, &req); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (dc *DnsController) UpdateView(c *gin.Context) {
	zap.L().Debug("UpdateView Function called")
	span, ctx := apm.StartSpan(c, "UpdateView", "controller")
	defer span.End()

	var req param.UpdateViewParams
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UpdateView ShouldBindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("UpdateView Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := dc.svc.View().UpdateView(ctx, &req); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
