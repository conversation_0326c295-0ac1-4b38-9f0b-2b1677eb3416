package dns

import (
	"errors"
	param "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (dc *DnsController) GetZoneTypes(c *gin.Context) {
	zap.L().Debug("GetZoneTypes Function Called")
	span, ctx := apm.StartSpan(c, "GetZoneTypes", "controller")
	defer span.End()

	resp, err := dc.svc.Zone().GetZoneTypes(ctx)
	if err != nil {
		zap.L().<PERSON>rror("GetZoneTypes Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (dc *DnsController) GetZoneByID(c *gin.Context) {
	zap.L().Debug("GetZoneByID Function Called")
	span, ctx := apm.StartSpan(c, "GetZoneByID", "controller")
	defer span.End()

	zoneID := utils.ToInt64(c.Param("id"))
	if zoneID == 0 {
		zap.L().Error("获取ZoneID失败", zap.String("zone_id", c.Param("id")))
		e := apm.CaptureError(ctx, errors.New("zone_id is required"))
		e.Send()
		core.SendResponse(c, errors.New("ZoneID不合法或为空"), nil)
		return
	}

	zone, err := dc.svc.Zone().GetZoneByID(ctx, zoneID)
	if err != nil {
		zap.L().Error("获取Zone失败", zap.Error(err), zap.Int64("zone_id", zoneID))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, zone)
}

func (dc *DnsController) GetZoneList(c *gin.Context) {
	zap.L().Debug("GetZoneList Function Called")
	span, ctx := apm.StartSpan(c, "GetZoneList", "controller")
	defer span.End()

	zoneList, err := dc.svc.Zone().GetZoneList(ctx)
	if err != nil {
		zap.L().Error("GetZoneList Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, zoneList)
}

func (dc *DnsController) CreateZone(c *gin.Context) {
	zap.L().Debug("CreateZone Function Called")
	span, ctx := apm.StartSpan(c, "CreateZone", "controller")
	defer span.End()

	var req param.CreateZoneParams
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("CreateZone ShouldBindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("CreateZone Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := dc.svc.Zone().CreateZone(ctx, &req); err != nil {
		zap.L().Error("CreateZone Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (dc *DnsController) DeleteZone(c *gin.Context) {
	zap.L().Debug("DeleteZone Function Called")
	span, ctx := apm.StartSpan(c, "DeleteZone", "controller")
	defer span.End()

	zoneID := utils.ToInt64(c.Param("id"))
	if zoneID == 0 {
		zap.L().Error("ZoneID不合法, 不可以为0", zap.String("zone_id", c.Param("id")))
		e := apm.CaptureError(ctx, errors.New("zone_id is required"))
		e.Send()
		core.SendResponse(c, errors.New("ZoneID不合法或为空"), nil)
		return
	}

	if err := dc.svc.Zone().DeleteZone(ctx, zoneID); err != nil {
		zap.L().Error("DeleteZone Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
