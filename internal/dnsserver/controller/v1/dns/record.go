package dns

import (
	api "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/core"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// GetRecordInViews 获取DNS记录在视图中的分布
func (dc *DnsController) GetRecordInViews(c *gin.Context) {
	zap.L().Debug("GetRecordInViews Controller Called")
	span, ctx := apm.StartSpan(c, "GetRecordInViews", "controller")
	defer span.End()

	r := &api.QueryRecordInViewsRequest{}
	if err := c.ShouldBindJSON(r); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := r.Validate(); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	recordList, err := dc.svc.Record().GetRecordInViews(ctx, r)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, recordList)

}

func (dc *DnsController) GetRecordList(c *gin.Context) {
	zap.L().Debug("GetRecordList Controller Called")
	span, ctx := apm.StartSpan(c, "GetRecordList", "controller")
	defer span.End()

	r := &api.QueryDnsRecordRequest{}
	if err := c.ShouldBindJSON(r); err != nil {
		zap.L().Error("GetRecordList BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := r.Validate(); err != nil {
		zap.L().Error("GetRecordList Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	recordList, err := dc.svc.Record().GetRecordList(ctx, r)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, recordList)
}

func (dc *DnsController) CreateBlacklistRecord(c *gin.Context) {
	zap.L().Debug("CreateBlacklistRecord Controller Called")
	span, ctx := apm.StartSpan(c, "CreateBlacklistRecord", "controller")
	defer span.End()

	req := make([]*api.CreateBlacklistDnsRecordRequest, 0)
	if err := c.ShouldBindJSON(&req); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 逐个校验每一个dns record记录项
	for _, r := range req {
		if err := r.Validate(); err != nil {
			e := apm.CaptureError(ctx, err)
			e.Send()
			core.SendResponse(c, err, nil)
			return
		}
	}

	record, err := dc.svc.Record().CreateBlacklistRecord(ctx, req)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, record)
}

func (dc *DnsController) UpdateRecord(c *gin.Context) {
	zap.L().Debug("UpdateRecord Controller Called")
	span, ctx := apm.StartSpan(c, "UpdateRecord", "controller")
	defer span.End()

	r := &api.UpdateDnsRecordRequest{}
	if err := c.ShouldBindJSON(r); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := r.Validate(); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	record, err := dc.svc.Record().UpdateRecord(ctx, r)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, record)
}
