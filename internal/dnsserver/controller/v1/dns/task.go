package dns

import (
	api "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/core"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// GetTaskList 获取任务列表，默认按创建时间倒序排列
func (dc *DnsController) GetTaskList(c *gin.Context) {
	zap.L().Debug("GetTaskList Controller called")
	span, ctx := apm.StartSpan(c.Request.Context(), "GetTaskList", "controller")
	defer span.End()

	req := &api.TaskListRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		zap.L().Error("GetTaskList BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("GetTaskList Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := dc.svc.Task().GetTaskList(ctx, req)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

// RetryTaskDetail 重试单个TaskDetail
func (dc *DnsController) RetryTaskDetail(c *gin.Context) {
	zap.L().Debug("RetryTaskDetail Controller called")
	span, ctx := apm.StartSpan(c.Request.Context(), "RetryTaskDetail", "controller")
	defer span.End()

	req := &api.RetryTaskDetailRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		zap.L().Error("RetryTaskDetail BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("RetryTaskDetail Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := dc.svc.Task().RetryTaskDetail(ctx, req.DetailID); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
