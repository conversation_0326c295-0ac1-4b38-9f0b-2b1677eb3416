package dns

import (
	"ks-knoc-server/internal/common/core"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// GetOfficeList 查询职场列表
func (dc *DnsController) GetOfficeList(c *gin.Context) {
	zap.L().Debug("GetOfficeList Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetOfficeList", "controller")
	defer span.End()

	officeList, err := dc.svc.Office().GetOfficeList(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, officeList)
}
