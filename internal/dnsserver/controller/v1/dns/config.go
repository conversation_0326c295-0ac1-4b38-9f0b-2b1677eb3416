package dns

import (
	"errors"
	"fmt"

	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (dc *DnsController) GetConfigByViewId(c *gin.Context) {
	zap.L().Info("GetConfigByViewId", zap.String("view_id", c.<PERSON>("view_id")))
	span, ctx := apm.StartSpan(c, "controller", "GetConfigByViewId")
	defer span.End()

	viewId := utils.ToInt64(c.Query("view_id"))
	zoneId := utils.ToInt64(c.Query("zone_id"))

	if viewId <= 0 {
		err := errors.New("view_id is invalid")
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if zoneId <= 0 {
		err := errors.New("zone_id is invalid")
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 获取区域文件响应
	resp, err := dc.svc.Config().GenerateZoneFile(ctx, viewId, zoneId)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 设置响应头，使浏览器将响应识别为文件下载
	c.Header("Content-Disposition", "attachment; filename=\""+resp.FileName+"\"")
	c.Header("Content-Type", resp.ContentType)
	c.Data(200, resp.ContentType, []byte(resp.Content))
}

func (dc *DnsController) GetMainConfig(c *gin.Context) {
	zap.L().Info("GetMainConfig")
	span, ctx := apm.StartSpan(c, "controller", "GetMainConfig")
	defer span.End()

	resp, err := dc.svc.Config().GenerateMainConfigCompressedFile(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 设置响应头，使浏览器将响应识别为文件下载
	c.Header("Content-Disposition", "attachment; filename=\""+resp.FileName+"\"")
	c.Header("Content-Type", resp.ContentType)
	c.Header("Content-Length", fmt.Sprintf("%d", len(resp.Data)))

	// 直接返回ZIP文件数据流
	c.Data(200, resp.ContentType, resp.Data)
}
