package dns

import (
	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/core"
)

func (dc *DnsController) CreateServer(c *gin.Context) {
	zap.L().Debug("CreateServer Controller Called")
	span, _ := apm.StartSpan(c, "CreateServer", "controller")
	defer span.End()

	var server *api.CreateServerRequest
	if err := c.ShouldBindJSON(&server); err != nil {
		zap.L().Error("CreateServer ShouldBindJSON error", zap.Error(err))
		core.SendResponse(c, err, nil)
		return
	}

	if err := server.Validate(); err != nil {
		zap.L().Error("CreateServer Validate error", zap.Error(err))
		core.SendResponse(c, err, nil)
		return
	}

	if err := dc.svc.Server().CreateServer(c, server); err != nil {
		zap.L().Error("CreateServer error", zap.Error(err))
		e := apm.CaptureError(c, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (dc *DnsController) GetServerList(c *gin.Context) {
	zap.L().Debug("GetServerList Controller Called")
	span, _ := apm.StartSpan(c, "GetServerList", "controller")
	defer span.End()

	var req api.QueryServerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("GetServerList ShouldBindJSON error", zap.Error(err))
		core.SendResponse(c, err, nil)
		return
	}

	servers, err := dc.svc.Server().GetServerList(c, &req)
	if err != nil {
		zap.L().Error("GetServerList error", zap.Error(err))
		e := apm.CaptureError(c, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, servers)
}
