package server

import (
	"errors"
	"time"

	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/dnsserver/options"
	"ks-knoc-server/internal/dnsserver/route"
	"ks-knoc-server/internal/dnsserver/store"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CacheReadyChecker 是一个检查缓存是否就绪的函数类型
type CacheReadyChecker func() bool

// Server ...
type Server struct {
	DB           *gorm.DB
	Log          *zap.Logger
	Web          *gin.Engine
	Cache        *cache.Cache
	DataStore    *store.DataStore
	isCacheReady CacheReadyChecker
}

// NewServer 启动服务相关中间件初始化
func NewServer(opts *options.Options, isCacheReady CacheReadyChecker) (*Server, error) {
	var err error

	// 初始化logger
	logger, err := opts.LogsOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化Mysql, 同时初始化gorm gen的请求
	mysqlHandler, err := opts.MySQLOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化cache
	cache, err := cache.NewBigCache().Init()
	if err != nil {
		return nil, err
	}

	// 初始化kafka
	producer, err := opts.KafkaOptions.Init()
	if err != nil {
		return nil, err
	}

	// 如果kafka初始化失败，则返回错误
	if producer == nil {
		return nil, errors.New("kafka初始化失败, 请检查配置")
	}

	// 初始化DataStore
	ds := &store.DataStore{
		Db:       mysqlHandler,
		Cache:    cache,
		Producer: producer,
	}

	// 生成路由的同时注入ds
	g := route.APIServerRouter(opts.WebOptions.Engine, ds)
	g.Use(ginzap.Ginzap(logger, time.RFC3339, true))
	g.Use(ginzap.RecoveryWithZap(logger, true))

	server := &Server{
		Log:          logger,
		DB:           mysqlHandler,
		Web:          g,
		Cache:        &cache,
		DataStore:    ds,
		isCacheReady: isCacheReady,
	}

	return server, nil
}

// IsCacheReady 检查缓存是否就绪
func (s *Server) IsCacheReady() bool {
	if s.isCacheReady == nil {
		return true // 默认认为缓存已就绪
	}
	return s.isCacheReady()
}

// GetDataStore 返回数据存储实例
func (s *Server) GetDataStore() *store.DataStore {
	return s.DataStore
}
