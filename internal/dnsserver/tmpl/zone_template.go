package tmpl

// ZoneTemplate DNS区域文件模板
const ZoneTemplate = `$TTL {{.TTL}}
@       IN      SOA     {{.SOA.PrimaryNS}}     {{.SOA.AdminEmail}} (
                        {{.SOA.Serial}}      ; Serial
                        {{.SOA.Refresh}}     ; Refresh
                        {{.SOA.Retry}}       ; Retry
                        {{.SOA.Expire}}      ; Expire
                        {{.SOA.NegativeTTL}} ) ; Negative Cache TTL

{{- if gt (len .NSRecords) 0}}
; NS记录
@       IN      NS      {{index .NSRecords 0}}
{{- range $idx, $ns := slice .NSRecords 1}}
        IN      NS      {{$ns}}
{{- end}}
{{- end}}

{{- if gt (len .NSServers) 0}}
; NS服务器的A记录
{{- range $name, $ip := .NSServers}}
{{printf "%-40s" $name}} IN      A       {{$ip}}
{{- end}}
{{- end}}

{{- if gt (len .ARecords) 0}}

; A记录
{{- range $domain, $records := .ARecords}}
{{- $first := true}}
{{- range $idx, $record := $records}}
{{- if $first}}
{{printf "%-70s" $domain}} IN      A       {{$record.IP}}
{{- $first = false}}
{{- else}}
{{printf "%-70s" ""}} IN      A       {{$record.IP}}
{{- end}}
{{- end}}
{{- end}}
{{- end}}

{{- if gt (len .CNAMERecords) 0}}

; CNAME记录
{{- range .CNAMERecords}}
{{printf "%-70s" .Domain}} IN      CNAME   {{.Target}}
{{- end}}
{{- end}}`

const NamedViewZonesTemplate = `# named.{{.ViewCode}}.zones - Zone configurations for view {{.ViewName}}
# Generated at: {{.GeneratedAt}}
# Server Role: {{.ServerRole}}

# Root hints zone
zone "." IN {
    type hint;
    file "named.ca";
};

{{- if eq .ServerRole "master"}}
# Master server configuration
{{- range $zone := .Zones}}
zone "{{$zone.Name}}" {
    type master;
    file "db/{{$.ViewCode}}/db.{{$zone.Name}}";
    notify yes;
    allow-transfer { key {{$.ViewKeyName}}; };
};

{{- end}}

{{- if .HasBlacklistZones}}
# Blacklist zones (Response Policy Zones)
{{- range $zone := .BlacklistZones}}
zone "{{$zone.Name}}" {
    type master;
    file "db/{{$.ViewCode}}/db.{{$zone.Name}}";
    notify yes;
    allow-transfer { key {{$.ViewKeyName}}; };
};

{{- end}}
{{- end}}

{{- else}}
# Slave server configuration
{{- range $zone := .Zones}}
zone "{{$zone.Name}}" {
    type slave;
    masters {
    {{- range $master := $.MasterServers}}
        {{- if $master.IPv4Enabled}}
        {{$master.IP}} key {{$.ViewKeyName}};
        {{- end}}
        {{- if $master.IPv6Enabled}}
        {{$master.IPv6IP}} key {{$.ViewKeyName}};
        {{- end}}
    {{- end}}
    };
    file "slaves/{{$.ViewCode}}/db.{{$zone.Name}}";
};

{{- end}}

{{- if .HasBlacklistZones}}
# Blacklist zones (Response Policy Zones) - Slave configuration
{{- range $zone := .BlacklistZones}}
zone "{{$zone.Name}}" {
    type slave;
    masters {
    {{- range $master := $.MasterServers}}
        {{- if $master.IPv4Enabled}}
        {{$master.IP}} key {{$.ViewKeyName}};
        {{- end}}
        {{- if $master.IPv6Enabled}}
        {{$master.IPv6IP}} key {{$.ViewKeyName}};
        {{- end}}
    {{- end}}
    };
    file "slaves/{{$.ViewCode}}/db.{{$zone.Name}}";
};

{{- end}}
{{- end}}
{{- end}}

# End of named.{{.ViewCode}}.zones
`

// NamedDefaultZonesTemplate 生成named.default.zones文件的模板
// default view只包含blacklist黑名单zone，不包含其他业务zone
const NamedDefaultZonesTemplate = `# named.default.zones - Zone configurations for default view
# Generated at: {{.GeneratedAt}}
# Server Role: {{.ServerRole}}
# Note: Default view only contains blacklist zones for security protection

# Root hints zone
zone "." IN {
    type hint;
    file "named.ca";
};

{{- if eq .ServerRole "master"}}
{{- if .HasBlacklistZones}}
# Blacklist zones (Response Policy Zones) for default view
{{- range $zone := .BlacklistZones}}
zone "{{$zone.Name}}" {
    type master;
    file "db/default/db.{{$zone.Name}}";
    allow-update { key default_key; };
    allow-transfer { key default_key; };
    {{- if $.SlaveServers}}
    also-notify {
        {{- range $slave := $.SlaveServers}}
        {{$slave.IP}} key default_key;
        {{- end}}
    };
    {{- end}}
};

{{- end}}
{{- else}}
# No blacklist zones configured for default view
{{- end}}

{{- else}}
# Slave server configuration for default view
{{- if .HasBlacklistZones}}
# Blacklist zones (Response Policy Zones) - Slave configuration for default view
{{- range $zone := .BlacklistZones}}
zone "{{$zone.Name}}" {
    type slave;
    masters {
    {{- range $master := $.MasterServers}}
        {{- if $master.IPv4Enabled}}
        {{$master.IP}} key default_key;
        {{- end}}
        {{- if $master.IPv6Enabled}}
        {{$master.IPv6IP}} key default_key;
        {{- end}}
    {{- end}}
    };
    file "slaves/default/db.{{$zone.Name}}";
};

{{- end}}
{{- else}}
# No blacklist zones configured for default view
{{- end}}
{{- end}}

# End of named.default.zones
`
