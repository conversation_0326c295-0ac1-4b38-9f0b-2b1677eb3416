package route

import (
	"net/http"

	"ks-knoc-server/internal/common/http/middleware"
	"ks-knoc-server/internal/common/sso"
	"ks-knoc-server/internal/dnsserver/controller/v1/check"
	"ks-knoc-server/internal/dnsserver/controller/v1/dns"
	"ks-knoc-server/internal/dnsserver/store"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm/module/apmgin"
)

// APIServerRouter APIServer路由
func APIServerRouter(g *gin.Engine, db *store.DataStore, mw ...gin.HandlerFunc) *gin.Engine {

	// 应用中间件
	g.Use(apmgin.Middleware(g))
	g.Use(gin.Recovery())
	g.Use(middleware.NoCache)
	g.Use(middleware.Options)
	g.Use(middleware.Secure)
	g.Use(mw...)
	// jwtStrategy, _ := newJwtAuth().(auth.JWTStrategy)

	g.GET("/sso", sso.LoginValidate)

	g.NoRoute(func(c *gin.Context) {
		c.String(http.StatusNotFound, "The incorrect API route, Please contact the Kwai IT Developer.")
	})

	// 定义API相关路由
	api := g.Group("/api")
	{
		// 初始化API v1版本的路由
		v1 := api.Group("/v1")
		// v1.Use(jwtStrategy.AuthFunc())
		{
			// 初始化cmdb的控制器
			dnsController := dns.NewDnsController(db)

			// 注册路由
			registerOfficeRoutes(v1, dnsController)
			registerClusterRoutes(v1, dnsController)
			registerViewRoutes(v1, dnsController)
			registerServerRoutes(v1, dnsController)
			registerZoneRoutes(v1, dnsController)
			registerRecordRoutes(v1, dnsController)
			registerConfigRoutes(v1, dnsController)
			registerTaskRoutes(v1, dnsController)
		}
	}

	// 定义服务的健康检查接口
	c := g.Group("/check")
	{
		c.GET("/health", check.HealthCheck)
	}

	return g
}
