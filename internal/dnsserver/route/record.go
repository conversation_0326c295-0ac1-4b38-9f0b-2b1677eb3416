package route

import (
	"ks-knoc-server/internal/dnsserver/controller/v1/dns"

	"github.com/gin-gonic/gin"
)

func registerRecordRoutes(v1 *gin.RouterGroup, dnsController *dns.DnsController) {
	record := v1.Group("/record")
	{
		record.POST("/blacklist", dnsController.CreateBlacklistRecord)
		record.PUT("", dnsController.UpdateRecord)
		record.POST("/list", dnsController.GetRecordList)
		record.POST("/inviews", dnsController.GetRecordInViews)
	}
}
