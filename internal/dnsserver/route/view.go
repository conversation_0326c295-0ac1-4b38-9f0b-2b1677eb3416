package route

import (
	"ks-knoc-server/internal/dnsserver/controller/v1/dns"

	"github.com/gin-gonic/gin"
)

func registerViewRoutes(v1 *gin.RouterGroup, dnsController *dns.DnsController) {
	view := v1.Group("/view")
	{
		view.POST("", dnsController.CreateView)
		view.GET("/tree", dnsController.GetViewTree)
		view.PUT("", dnsController.UpdateView)
		view.GET("/:view_id", dnsController.GetViewByID)
	}
}
