package route

import (
	"ks-knoc-server/internal/dnsserver/controller/v1/dns"

	"github.com/gin-gonic/gin"
)

func registerZoneRoutes(v1 *gin.RouterGroup, dnsController *dns.DnsController) {
	zone := v1.Group("/zone")
	{
		zone.GET("", dnsController.GetZoneList)
		zone.GET("/:id", dnsController.GetZoneByID)
		zone.POST("", dnsController.CreateZone)
		zone.DELETE("/:id", dnsController.DeleteZone)
		zone.GET("/types", dnsController.GetZoneTypes)
	}
}
