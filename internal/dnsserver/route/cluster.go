package route

import (
	"ks-knoc-server/internal/dnsserver/controller/v1/dns"

	"github.com/gin-gonic/gin"
)

const (
	clusterRoutePrefix = "/cluster"
)

func registerClusterRoutes(v1 *gin.RouterGroup, dnsController *dns.DnsController) {
	cluster := v1.Group(clusterRoutePrefix)
	{
		cluster.POST("", dnsController.CreateCluster)
		cluster.GET("", dnsController.GetClusterList)
		cluster.PUT("", dnsController.UpdateCluster)
	}
}
