package main

import (
	"ks-knoc-server/config"
	"ks-knoc-server/internal/common/db"

	"github.com/spf13/pflag"
	"gorm.io/gen"
)

var (
	cfg = pflag.StringP("config", "c", "", "Config file path.")
)

// 用户升成初始化的一些表结构操作
func main() {
	pflag.Parse()

	if err := config.NewConf(*cfg); err != nil {
		panic(err)
	}

	// 这里如果连接数据库失败了，直接报错就可以了，不需要继续执行了
	handler, err := db.NewMySQLOptions().Init()
	if err != nil {
		panic(err)
	}

	g := gen.NewGenerator(gen.Config{
		OutPath:      "../../common/base/model/dnsserver/query",
		ModelPkgPath: "../../common/base/model/dnsserver/model",
		// gen.WithDefaultQuery 生成一个全局的Query对象Q
		// gen.WithQueryInterface 升成Query接口
		Mode: gen.WithDefaultQuery | gen.WithQueryInterface,
	})

	g.UseDB(handler)
	g.ApplyBasic(g.GenerateAllTable()...)
	g.Execute()
}
