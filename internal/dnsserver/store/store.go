package store

import (
	"ks-knoc-server/internal/common/cache"

	"github.com/IBM/sarama"
	"gorm.io/gorm"
)

type Factory interface {
	Office() OfficeStore
	NS() NSStore
	Zone() ZoneStore
	Record() RecordStore
	Cluster() ClusterStore
	View() ViewStore
	Config() ConfigStore
	Task() TaskStore
	Server() ServerStore
	Common() CommonStore
}

type DataStore struct {
	Db       *gorm.DB
	Cache    cache.Cache
	Producer sarama.SyncProducer
}

var _ Factory = (*DataStore)(nil)

func (ds *DataStore) Common() CommonStore {
	return newCommonStore(ds)
}

func (ds *DataStore) Server() ServerStore {
	return newServerStore(ds)
}

func (ds *DataStore) Task() TaskStore {
	return newTaskStore(ds)
}

func (ds *DataStore) Office() OfficeStore {
	return newOfficeStore(ds)
}

func (ds *DataStore) NS() NSStore {
	return newNSStore(ds)
}

func (ds *DataStore) Zone() ZoneStore {
	return newZoneStore(ds)
}

func (ds *DataStore) Record() RecordStore {
	return newRecordStore(ds)
}

func (ds *DataStore) Cluster() ClusterStore {
	return newClusterStore(ds)
}

func (ds *DataStore) View() ViewStore {
	return newViewStore(ds)
}

func (ds *DataStore) Config() ConfigStore {
	return newConfigStore(ds)
}
