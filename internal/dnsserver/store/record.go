package store

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/IBM/sarama"
	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/cache"
)

const (
	recordTableName = "records"
	rpzCacheKey     = "rpz_cache"
)

var (
	ErrRecordNotModified     = errors.New("DNS记录无更新")
	ErrRRTypeNotSupported    = errors.New("不支持的RR类型")
	ErrRRTypeAContentInvalid = errors.New("a记录内容不合法")
	ErrRRDomainInvalid       = errors.New("域名不合法")
	ErrRRNotExist            = errors.New("域名记录不存在")
	ErrZoneIDInvalid         = errors.New("区域ID不合法")
	ErrViewIDInvalid         = errors.New("视图ID不合法")
	ErrRRNameIsEmpty         = errors.New("记录名不能为空")
	ErrRRTypeInvalid         = errors.New("记录类型不合法")
	ErrRRValueInvalid        = errors.New("记录值不合法")
)

type RecordStore interface {
	// CreateRecordBatch 批量创建DNS记录
	CreateRecordBatch(ctx context.Context, r []*model.DNSRecord) error
	// GetRecordListWithPagination 获取带分页的DNS记录列表
	GetRecordListWithPagination(ctx context.Context, r *api.QueryDnsRecordRequest) ([]*model.DNSRecord, int64, error)
	// GetRecordList 获取DNS记录列表
	GetRecordList(ctx context.Context, zoneID, viewID int64, name, rrType, rrValue string) ([]*model.DNSRecord, error)
	// GetRecordInDefaultView 获取默认视图下的DNS记录
	GetRecordInDefaultView(ctx context.Context, zoneID int64, name, rrType, rrValue string) ([]*model.DNSRecord, error)
	// GetRecordByID 根据RecordID获取DNS记录
	GetRecordByID(ctx context.Context, id int64) (*model.DNSRecord, error)
	// GetAllRecord 获取所有DNS记录
	GetAllRecord(ctx context.Context) ([]*model.DNSRecord, error)
	// UpdateRecord 更新DNS记录
	UpdateRecord(ctx context.Context, rrs []*model.DNSRecord) error
	// DeleteRecordBatch 批量删除DNS记录
	DeleteRecordBatch(ctx context.Context, ids []int64) error
	// SetRpzCache 设置RPZ缓存
	SetRpzCache(rpzCache map[int64]map[int64]*model.RpzTree) error
	// GetRpzCache 获取RPZ缓存
	GetRpzCache() (map[int64]map[int64]*model.RpzTree, error)
	// DeleteRecordByName 根据zoneID和name删除DNS记录
	DeleteRecordByName(ctx context.Context, zoneID int64, name string) error
}

type recordStore struct {
	db       *gorm.DB
	cache    cache.Cache
	producer sarama.SyncProducer
}

func newRecordStore(ds *DataStore) RecordStore {
	return &recordStore{
		db:       ds.Db,
		cache:    ds.Cache,
		producer: ds.Producer,
	}
}

// SetRpzCache 设置RPZ缓存
func (rs *recordStore) SetRpzCache(rpzCache map[int64]map[int64]*model.RpzTree) error {
	raw, err := json.Marshal(rpzCache)
	if err != nil {
		zap.L().Error("SetRpzCache Error", zap.Error(err))
		return err
	}

	return rs.cache.Set(rpzCacheKey, raw)
}

// GetRpzCache 获取RPZ缓存
func (rs *recordStore) GetRpzCache() (map[int64]map[int64]*model.RpzTree, error) {
	raw, err := rs.cache.Get(rpzCacheKey)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	rpzCache := make(map[int64]map[int64]*model.RpzTree)
	if err := json.Unmarshal(raw, &rpzCache); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	return rpzCache, nil
}

// DeleteRecordBatch 批量删除DNS记录
func (rs *recordStore) DeleteRecordBatch(ctx context.Context, ids []int64) error {
	zap.L().Debug("DeleteRecordBatch Store Called")
	span, _ := apm.StartSpan(ctx, "DeleteRecordBatch", "store")
	defer span.End()

	return rs.db.Table(recordTableName).Where("id IN (?)", ids).Delete(&model.DNSRecord{}).Error
}

// GetAllRecord 获取所有DNS记录
func (rs *recordStore) GetAllRecord(ctx context.Context) ([]*model.DNSRecord, error) {
	zap.L().Debug("GetAllRecord Store Called")
	span, _ := apm.StartSpan(ctx, "GetAllRecord", "store")
	defer span.End()

	records := make([]*model.DNSRecord, 0)
	if err := rs.db.Table(recordTableName).Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetRecordList 获取DNS记录列表, 不分页
// Parameters:
// - ctx: context.Context
// - zoneID: 区域ID
// - viewID: 视图ID
// - name: 记录名
// - rrType: 记录类型
// - rrValue: 记录值
func (rs *recordStore) GetRecordList(ctx context.Context, zoneID, viewID int64, name, rrType, rrValue string) ([]*model.DNSRecord, error) {
	zap.L().Debug("GetRecord Store Called")
	span, _ := apm.StartSpan(ctx, "GetRecord", "store")
	defer span.End()

	records := make([]*model.DNSRecord, 0)

	tx := rs.db.Table(recordTableName)
	if zoneID > 0 {
		tx.Where("zone_id = ?", zoneID)
	}

	if viewID > 0 {
		tx.Where("view_id = ?", viewID)
	}

	if name != "" {
		tx.Where("name = ?", name)
	}

	if rrType != "" {
		tx.Where("r_type = ?", rrType)
	}

	if rrValue != "" {
		tx.Where("value = ?", rrValue)
	}

	if err := tx.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetRecordInDefaultView 获取默认视图下的DNS记录
func (rs *recordStore) GetRecordInDefaultView(ctx context.Context, zoneID int64, name, rrType, rrValue string) ([]*model.DNSRecord, error) {
	zap.L().Debug("GetRecordInDefaultView Store Called")
	span, _ := apm.StartSpan(ctx, "GetRecordInDefaultView", "store")
	defer span.End()
	
	records := make([]*model.DNSRecord, 0)

	defaultView := &model.View{}
	if err := rs.db.Table(viewTableName).Where("level_type = ?", model.ViewLevelTypeDefault.String()).First(defaultView).Error; err != nil {
		return nil, err
	}

	tx := rs.db.Table(recordTableName)
	// 手动指定默认视图
	tx.Where("view_id = ?", defaultView.ID)

	if zoneID > 0 {
		tx.Where("zone_id = ?", zoneID)
	}

	if name != "" {
		tx.Where("name = ?", name)
	}

	if rrType != "" {
		tx.Where("r_type = ?", rrType)
	}

	if rrValue != "" {
		tx.Where("value = ?", rrValue)
	}

	if err := tx.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetRecordByID 根据RecordID获取DNS记录
func (rs *recordStore) GetRecordByID(ctx context.Context, id int64) (*model.DNSRecord, error) {
	zap.L().Debug("GetRecordByID Store Called")
	span, _ := apm.StartSpan(ctx, "GetRecordByID", "store")
	defer span.End()

	record := &model.DNSRecord{}
	if err := rs.db.Table(recordTableName).
		Where("id = ?", id).
		First(record).Error; err != nil {
		return nil, err
	}

	return record, nil
}

// GetRecordListWithPagination 获取DNS记录列表
func (rs *recordStore) GetRecordListWithPagination(ctx context.Context, r *api.QueryDnsRecordRequest) ([]*model.DNSRecord, int64, error) {
	zap.L().Debug("GetRecordList Store Called")
	span, _ := apm.StartSpan(ctx, "GetRecordList", "store")
	defer span.End()

	// 初始化一个recordList
	var (
		total      int64
		recordList = make([]*model.DNSRecord, 0)
	)

	// 是否进行分组
	if r.Group {
		if r.GroupBy == "" {
			return nil, 0, errors.New("当开启分组时，分组字段不能为空")
		}

		// 构建基础查询条件
		baseTx := rs.db.Table(recordTableName).Where("zone_id = ?", r.ZoneID)
		if len(r.ViewIDs) != 0 {
			baseTx = baseTx.Where("view_id IN (?)", r.ViewIDs)
		}
		if r.RType != "" {
			baseTx = baseTx.Where("r_type = ?", r.RType)
		}
		if r.Creator != "" {
			baseTx = baseTx.Where("creator = ?", r.Creator)
		}
		if r.Owner != "" {
			baseTx = baseTx.Where("owner = ?", r.Owner)
		}
		if r.Name != "" {
			if r.Like {
				baseTx = baseTx.Where("name LIKE ?", "%"+r.Name+"%")
			} else {
				baseTx = baseTx.Where("name = ?", r.Name)
			}
		}
		if r.Value != "" {
			baseTx = baseTx.Where("value = ?", r.Value)
		}

		// 获取分组总数
		var groupValues []string
		if err := baseTx.Pluck(r.GroupBy, &groupValues).Error; err != nil {
			return nil, 0, fmt.Errorf("分组查询计数失败: %w", err)
		}
		uniqueValues := make(map[string]bool)
		for _, value := range groupValues {
			uniqueValues[value] = true
		}
		total = int64(len(uniqueValues))

		// 构建聚合字段列表
		processedFields := make(map[string]bool)
		processedFields[r.GroupBy] = true

		var aggregateFields []string

		// 1. 添加排序字段（如果不是分组字段）
		if r.OrderBy != "" && !processedFields[r.OrderBy] {
			aggregateFields = append(aggregateFields, fmt.Sprintf("MAX(`%s`) AS `%s`", r.OrderBy, r.OrderBy))
			processedFields[r.OrderBy] = true
		} else if r.OrderBy == "" && !processedFields["create_time"] {
			// 默认排序字段
			aggregateFields = append(aggregateFields, "MAX(`create_time`) AS `create_time`")
			processedFields["create_time"] = true
		}

		// 2. 添加用户指定的字段
		if len(r.SelectFields) > 0 {
			for _, field := range r.SelectFields {
				if !processedFields[field] {
					aggregateFields = append(aggregateFields, fmt.Sprintf("MAX(`%s`) AS `%s`", field, field))
					processedFields[field] = true
				}
			}
		} else {
			// 3. 添加所有模型字段（动态获取）
			stmt := rs.db.Model(&model.DNSRecord{}).Statement
			if err := stmt.Parse(&model.DNSRecord{}); err != nil {
				return nil, 0, fmt.Errorf("获取表结构失败: %w", err)
			}
			schema := stmt.Schema
			if schema != nil {
				for _, fieldSchema := range schema.Fields {
					if !processedFields[fieldSchema.DBName] {
						aggregateFields = append(aggregateFields, fmt.Sprintf("MAX(`%s`) AS `%s`", fieldSchema.DBName, fieldSchema.DBName))
					}
				}
			} else {
				return nil, 0, errors.New("无法获取表结构信息")
			}
		}

		// 构建Raw SQL进行分组查询
		var whereConditions []string
		var args []any

		whereConditions = append(whereConditions, "zone_id = ?")
		args = append(args, r.ZoneID)

		if len(r.ViewIDs) != 0 {
			whereConditions = append(whereConditions, "view_id IN (?)")
			args = append(args, r.ViewIDs)
		}
		if r.RType != "" {
			whereConditions = append(whereConditions, "r_type = ?")
			args = append(args, r.RType)
		}
		if r.Creator != "" {
			whereConditions = append(whereConditions, "creator = ?")
			args = append(args, r.Creator)
		}
		if r.Owner != "" {
			whereConditions = append(whereConditions, "owner = ?")
			args = append(args, r.Owner)
		}
		if r.Name != "" {
			if r.Like {
				whereConditions = append(whereConditions, "name LIKE ?")
				args = append(args, "%"+r.Name+"%")
			} else {
				whereConditions = append(whereConditions, "name = ?")
				args = append(args, r.Name)
			}
		}
		if r.Value != "" {
			whereConditions = append(whereConditions, "value = ?")
			args = append(args, r.Value)
		}

		// 校验GROUP BY字段
		if !isValidFieldName(r.GroupBy) {
			return nil, 0, fmt.Errorf("分组字段名格式不合法: %s", r.GroupBy)
		}

		// 构建完整的Raw SQL
		rawSQL := fmt.Sprintf("SELECT `%s`, %s FROM `%s` WHERE %s GROUP BY `%s`",
			r.GroupBy,
			strings.Join(aggregateFields, ", "),
			recordTableName,
			strings.Join(whereConditions, " AND "),
			r.GroupBy)

		// 添加排序
		if r.OrderBy != "" {
			if !isValidFieldName(r.OrderBy) {
				return nil, 0, fmt.Errorf("排序字段名格式不合法: %s", r.OrderBy)
			}
			rawSQL += fmt.Sprintf(" ORDER BY `%s` %s", r.OrderBy, strings.ToUpper(r.Order))
		} else {
			rawSQL += " ORDER BY `create_time` DESC"
		}

		// 添加分页
		rawSQL += " LIMIT ? OFFSET ?"
		args = append(args, r.PageSize, (r.Page-1)*r.PageSize)

		// 执行Raw SQL查询
		if err := rs.db.Raw(rawSQL, args...).Scan(&recordList).Error; err != nil {
			return nil, 0, fmt.Errorf("分组查询失败: %w", err)
		}

		return recordList, total, nil
	}

	// 非分组查询的处理
	tx := rs.db.Table(recordTableName).Where("zone_id = ?", r.ZoneID)
	// 根据不同的条件查询
	if len(r.ViewIDs) != 0 {
		tx = tx.Where("view_id IN (?)", r.ViewIDs)
	}
	if r.RType != "" {
		tx = tx.Where("r_type = ?", r.RType)
	}
	if r.Creator != "" {
		tx = tx.Where("creator = ?", r.Creator)
	}
	if r.Owner != "" {
		tx = tx.Where("owner = ?", r.Owner)
	}
	if r.Name != "" {
		if r.Like {
			tx = tx.Where("name LIKE ?", "%"+r.Name+"%")
		} else {
			tx = tx.Where("name = ?", r.Name)
		}
	}
	if r.Value != "" {
		tx = tx.Where("value = ?", r.Value)
	}

	if len(r.SelectFields) > 0 {
		// 非分组查询时，如果指定了字段，使用指定的字段
		selectClause, err := buildSafeSelectClause(r.SelectFields, "", nil)
		if err != nil {
			return nil, 0, err
		}
		tx = tx.Clauses(selectClause)
	}

	// 先获取总数
	if err := tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序（放在分组之后）
	if r.OrderBy != "" {
		orderByClause, err := buildSafeOrderByClause(r.OrderBy, r.Order)
		if err != nil {
			return nil, 0, err
		}
		tx = tx.Clauses(orderByClause)
	} else {
		// 默认按照时间进行倒排
		defaultOrderBy, _ := buildSafeOrderByClause("create_time", "desc")
		tx = tx.Clauses(defaultOrderBy)
	}

	// 进行分页查询
	if err := tx.Offset(int((r.Page - 1) * r.PageSize)).Limit(int(r.PageSize)).Find(&recordList).Error; err != nil {
		return nil, 0, err
	}
	return recordList, total, nil
}

// CreateRecord 创建DNS记录
func (rs *recordStore) CreateRecordBatch(ctx context.Context, r []*model.DNSRecord) error {
	zap.L().Debug("CreateRecordBatch Store Called")
	span, _ := apm.StartSpan(ctx, "CreateRecordBatch", "store")
	defer span.End()

	if len(r) == 0 {
		return nil
	}

	// 使用CreateInBatches进行批量创建，每批100条记录
	if err := rs.db.Table(recordTableName).CreateInBatches(r, 100).Error; err != nil {
		return fmt.Errorf("批量创建记录失败: %w", err)
	}

	return nil
}

func (rs *recordStore) UpdateRecord(ctx context.Context, rrs []*model.DNSRecord) error {
	zap.L().Debug("UpdateRecord Store Called")
	span, _ := apm.StartSpan(ctx, "UpdateRecord", "store")
	defer span.End()

	// 如果没有记录需要更新，直接返回
	if len(rrs) == 0 {
		return nil
	}

	// 按照记录ID分组更新数据
	updatesByID := make(map[int64]map[string]any)

	// 准备每条记录的更新内容
	for _, v := range rrs {
		// 先创建空map，然后添加所有字段
		updates := make(map[string]any)

		// 添加必需字段
		updates["value"] = v.Value

		// 有条件地添加可选字段
		if v.TTL > 0 {
			updates["ttl"] = v.TTL
		}

		if v.Description != "" {
			updates["description"] = v.Description
		}

		if v.Creator != "" {
			updates["creator"] = v.Creator
		}

		if v.Owner != "" {
			updates["owner"] = v.Owner
		}

		// 更新时间戳
		updates["update_time"] = v.UpdateTime

		updatesByID[int64(v.ID)] = updates
	}

	// 开始事务
	tx := rs.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 确保事务最终会被提交或回滚
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量更新记录
	for recordID, updates := range updatesByID {
		if err := tx.Table(recordTableName).Where("id = ?", recordID).Updates(updates).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}

func (rs *recordStore) DeleteRecordByID(ctx context.Context, recordID int64) error {
	zap.L().Debug("DeleteRecordByID Store Called")
	span, _ := apm.StartSpan(ctx, "DeleteRecordByID", "store")
	defer span.End()

	return rs.db.Table(recordTableName).Where("id = ?", recordID).Delete(&model.DNSRecord{}).Error
}

func (rs *recordStore) DeleteRecordByName(ctx context.Context, zoneID int64, name string) error {
	zap.L().Debug("DeleteRecordByName Store Called")
	span, _ := apm.StartSpan(ctx, "DeleteRecordByName", "store")
	defer span.End()

	return rs.db.Table(recordTableName).Where("zone_id = ? AND name = ?", zoneID, name).Delete(&model.DNSRecord{}).Error
}