package store

import (
	"context"
	"errors"

	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
)

const (
	clusterTableName = "cluster"
)

type ClusterStore interface {
	CreateCluster(ctx context.Context, cluster *model.Cluster) error
	GetClusterList(ctx context.Context) ([]*model.Cluster, error)
	GetClusterByID(ctx context.Context, id int64) (*model.Cluster, error)
	UpdateCluster(ctx context.Context, id int64, updateMap map[string]any) error
}

type clusterStore struct {
	db *gorm.DB
}

var _ ClusterStore = (*clusterStore)(nil)

func newClusterStore(ds *DataStore) ClusterStore {
	return &clusterStore{
		db: ds.Db,
	}
}

// UpdateCluster 更新集群
func (s *clusterStore) UpdateCluster(ctx context.Context, id int64, updateMap map[string]any) error {
	zap.L().Debug("UpdateCluster Function Called")
	span, ctx := apm.StartSpan(ctx, "UpdateCluster", "store")
	defer span.End()

	if result := s.db.Table(clusterTableName).Where("id = ?", id).Updates(updateMap); result.Error != nil {
		zap.L().Error("更新集群失败", zap.Error(result.Error))
		e := apm.CaptureError(ctx, result.Error)
		e.Send()
		return result.Error
	}
	return nil
}

// GetClusterByID 根据ID获取集群
func (s *clusterStore) GetClusterByID(ctx context.Context, id int64) (*model.Cluster, error) {
	zap.L().Debug("GetClusterByID Function Called")
	span, ctx := apm.StartSpan(ctx, "GetClusterByID", "store")
	defer span.End()

	clusters := make([]*model.Cluster, 0)
	if result := s.db.Table(clusterTableName).Where("id = ?", id).Find(&clusters); result.Error != nil {
		zap.L().Error("根据ID查询集群失败", zap.Error(result.Error))
		e := apm.CaptureError(ctx, result.Error)
		e.Send()
		return nil, result.Error
	}

	if len(clusters) == 0 {
		zap.L().Error("集群不存在", zap.Int64("id", id))
		e := apm.CaptureError(ctx, errors.New("集群不存在"))
		e.Send()
		return nil, errors.New("集群不存在")
	}

	return clusters[0], nil
}

func (s *clusterStore) CreateCluster(ctx context.Context, cluster *model.Cluster) error {
	zap.L().Debug("CreateCluster Function Called")
	span, ctx := apm.StartSpan(ctx, "CreateCluster", "store")
	defer span.End()

	if result := s.db.Table(clusterTableName).Create(cluster); result.Error != nil {
		zap.L().Error("CreateCluster Error", zap.Error(result.Error))
		e := apm.CaptureError(ctx, result.Error)
		e.Send()
		return result.Error
	}
	return nil
}

// GetCluster 获取集群
func (s *clusterStore) GetClusterList(ctx context.Context) ([]*model.Cluster, error) {
	zap.L().Debug("GetClusterList Function Called")
	span, ctx := apm.StartSpan(ctx, "GetClusterList", "store")
	defer span.End()

	clusters := make([]*model.Cluster, 0)
	if result := s.db.Table(clusterTableName).Find(&clusters); result.Error != nil {
		zap.L().Error("GetClusterList Error", zap.Error(result.Error))
		e := apm.CaptureError(ctx, result.Error)
		e.Send()
		return nil, result.Error
	}
	return clusters, nil
}
