package store

import (
	"gorm.io/gorm"
)

type CommonStore interface {
	Transaction(fn func(txStore Factory) error) error
}

type commonStore struct {
	ds *DataStore
}

func newCommonStore(ds *DataStore) CommonStore {
	return &commonStore{
		ds: ds,
	}
}

// Transaction 事务, 暴露事务给外部使用, 屏蔽内部的tx
func (c *commonStore) Transaction(fn func(factory Factory) error) error {
	return c.ds.Db.Transaction(func(tx *gorm.DB) error {
		factory := &DataStore{
			Db:       tx,
			Cache:    c.ds.Cache,
			Producer: c.ds.Producer,
		}
		return fn(factory)
	})
}
