package store

import (
	"context"

	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
)

const (
	viewTableName = "view"
)

type ViewStore interface {
	CreateView(ctx context.Context, req *model.View) error
	GetAllView(ctx context.Context) ([]*model.View, error)
	GetDefaultView(ctx context.Context) (*model.View, error)
	GetViewByType(ctx context.Context, viewType model.ViewLevelType) ([]*model.View, error)
	GetViewByIDs(ctx context.Context, viewIDs []int64) ([]*model.View, error)
	GetViewByParentID(ctx context.Context, parentID int64) ([]*model.View, error)
	UpdateView(ctx context.Context, viewID int64, updateMap map[string]interface{}) error
}

var _ ViewStore = (*viewStore)(nil)

type viewStore struct {
	db *gorm.DB
}

func newViewStore(ds *DataStore) ViewStore {
	return &viewStore{db: ds.Db}
}

// UpdateView 更新视图
func (s *viewStore) UpdateView(ctx context.Context, viewID int64, updateMap map[string]interface{}) error {
	if result := s.db.Table(viewTableName).Where("id = ?", viewID).Updates(updateMap); result.Error != nil {
		return result.Error
	}
	return nil
}

// GetViewByParentID 获取指定父ID的视图
func (s *viewStore) GetViewByParentID(ctx context.Context, parentID int64) ([]*model.View, error) {
	zap.L().Debug("GetViewByParentID Store Called")
	span, _ := apm.StartSpan(ctx, "GetViewByParentID", "store")
	defer span.End()

	var views []*model.View
	if err := s.db.Table(viewTableName).Where("parent_id = ?", parentID).Find(&views).Error; err != nil {
		return nil, err
	}
	return views, nil
}

// GetViewByID 获取指定ID的视图
func (s *viewStore) GetViewByIDs(ctx context.Context, viewIDs []int64) ([]*model.View, error) {
	zap.L().Debug("GetViewByID Store Called")
	span, _ := apm.StartSpan(ctx, "GetViewByID", "store")
	defer span.End()

	views := make([]*model.View, 0)
	if err := s.db.Table(viewTableName).Where("id IN ?", viewIDs).Find(&views).Error; err != nil {
		return nil, err
	}
	return views, nil
}

// GetViewByType 获取指定类型的视图
func (s *viewStore) GetViewByType(ctx context.Context, viewType model.ViewLevelType) ([]*model.View, error) {
	zap.L().Debug("GetViewByType Store Called")
	span, _ := apm.StartSpan(ctx, "GetViewByType", "store")
	defer span.End()

	views := make([]*model.View, 0)
	if err := s.db.Table(viewTableName).Where("level_type = ?", viewType.String()).Find(&views).Error; err != nil {
		return nil, err
	}
	return views, nil
}

func (s *viewStore) GetDefaultView(ctx context.Context) (*model.View, error) {
	var view model.View
	if err := s.db.Table(viewTableName).Where("level = ?", model.ViewLevelTypeDefault.String()).First(&view).Error; err != nil {
		return nil, err
	}
	return &view, nil
}

func (s *viewStore) CreateView(ctx context.Context, req *model.View) error {
	if result := s.db.Table(viewTableName).Create(req); result.Error != nil {
		return result.Error
	}
	return nil
}

func (s *viewStore) GetAllView(ctx context.Context) ([]*model.View, error) {
	zap.L().Debug("GetAllView Function Called")
	span, ctx := apm.StartSpan(ctx, "GetAllView", "store")
	defer span.End()

	var views []*model.View
	if err := s.db.Table(viewTableName).Find(&views).Error; err != nil {
		zap.L().Error("GetAllView Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}
	return views, nil
}
