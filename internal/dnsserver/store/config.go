package store

import (
	"context"
	"errors"

	model "ks-knoc-server/internal/common/base/model/dnsserver"

	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	configTableName = "dns_config"
)

var (
	ErrConfigNotFound = errors.New("DNS配置不存在")
)

type ConfigStore interface {
	// GetConfigByViewID 根据视图ID获取配置
	GetConfigByViewID(ctx context.Context, viewID int64) (*model.DnsConfig, error)

	// GetConfigsByZoneAndView 根据区域ID和视图ID获取配置
	GetConfigsByZoneAndView(ctx context.Context, zoneID, viewID int64) ([]*model.DnsConfig, error)

	// GetAllConfigsByViewID 获取视图下的所有配置
	GetAllConfigsByViewID(ctx context.Context, viewID int64) ([]*model.DnsConfig, error)

	// GetConfigsByViewIds 根据多个视图ID获取配置
	GetConfigsByViewIds(ctx context.Context, viewIDs []int64) (map[int64][]*model.DnsConfig, error)
}

type configStore struct {
	db *gorm.DB
}

func newConfigStore(ds *DataStore) ConfigStore {
	return &configStore{db: ds.Db}
}

// GetConfigByViewID 根据视图ID获取配置
func (s *configStore) GetConfigByViewID(ctx context.Context, viewID int64) (*model.DnsConfig, error) {
	zap.L().Debug("GetConfigByViewID Store Called")
	span, _ := apm.StartSpan(ctx, "GetConfigByViewID", "store")
	defer span.End()

	var config model.DnsConfig
	if err := s.db.Table(configTableName).Where("view_id = ?", viewID).First(&config).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zap.L().Warn("配置不存在", zap.Int64("view_id", viewID))
			return nil, ErrConfigNotFound
		}
		zap.L().Error("查询配置失败", zap.Error(err), zap.Int64("view_id", viewID))
		return nil, err
	}

	// 解析settings字段
	if config.Settings == nil {
		config.Settings = make(map[string]string)
	}

	return &config, nil
}

// GetConfigsByZoneAndView 根据区域ID和视图ID获取配置
func (s *configStore) GetConfigsByZoneAndView(ctx context.Context, zoneID, viewID int64) ([]*model.DnsConfig, error) {
	zap.L().Debug("GetConfigsByZoneAndView Store Called")
	span, _ := apm.StartSpan(ctx, "GetConfigsByZoneAndView", "store")
	defer span.End()

	var configs []*model.DnsConfig
	if err := s.db.Table(configTableName).Where("zone_id = ? AND view_id = ?", zoneID, viewID).Find(&configs).Error; err != nil {
		zap.L().Error("查询配置失败", zap.Error(err), zap.Int64("zone_id", zoneID), zap.Int64("view_id", viewID))
		return nil, err
	}

	// 解析settings字段
	for _, config := range configs {
		if config.Settings == nil {
			config.Settings = make(map[string]string)
		}
	}

	return configs, nil
}

// GetAllConfigsByViewID 获取视图下的所有配置
func (s *configStore) GetAllConfigsByViewID(ctx context.Context, viewID int64) ([]*model.DnsConfig, error) {
	zap.L().Debug("GetAllConfigsByViewID Store Called")
	span, _ := apm.StartSpan(ctx, "GetAllConfigsByViewID", "store")
	defer span.End()

	var configs []*model.DnsConfig
	if err := s.db.Table(configTableName).Where("view_id = ?", viewID).Find(&configs).Error; err != nil {
		zap.L().Error("查询配置失败", zap.Error(err), zap.Int64("view_id", viewID))
		return nil, err
	}

	// 解析settings字段
	for _, config := range configs {
		if config.Settings == nil {
			config.Settings = make(map[string]string)
		}
	}

	return configs, nil
}

// GetConfigsByViewIds 根据多个视图ID获取配置
func (s *configStore) GetConfigsByViewIds(ctx context.Context, viewIDs []int64) (map[int64][]*model.DnsConfig, error) {
	zap.L().Debug("GetConfigsByViewIds Store Called")
	span, _ := apm.StartSpan(ctx, "GetConfigsByViewIds", "store")
	defer span.End()

	var configs []*model.DnsConfig
	if err := s.db.Table(configTableName).Where("view_id IN ?", viewIDs).Find(&configs).Error; err != nil {
		zap.L().Error("查询配置失败", zap.Error(err), zap.Any("view_ids", viewIDs))
		return nil, err
	}

	// 按视图ID分组
	result := make(map[int64][]*model.DnsConfig)
	for _, config := range configs {
		if config.Settings == nil {
			config.Settings = make(map[string]string)
		}

		if _, ok := result[config.ViewID]; !ok {
			result[config.ViewID] = make([]*model.DnsConfig, 0)
		}
		result[config.ViewID] = append(result[config.ViewID], config)
	}

	return result, nil
}
