package store

import (
	"errors"

	"gorm.io/gorm"
)

var (
	ErrNSNotExist = errors.New("NS服务器不存在")
)

type NSStore interface {
	// GetNSList(ctx context.Context, officeId int32) ([]*model.DNSServer, error)
	// GetNSListByConditions(ctx context.Context, conditions []gen.Condition) ([]*model.DNSServer, error)
	// CreateNS(ctx context.Context, nsServer *api.DnsServer) error
	// UpdateNS(ctx context.Context, nsServer *api.DnsServer) error
	// DeleteNS(ctx context.Context, nid int32) error
}

type nsStore struct {
	db *gorm.DB
}

func newNSStore(ds *DataStore) NSStore {
	return &nsStore{
		db: ds.Db,
	}
}

// func (ns *nsStore) DeleteNS(ctx context.Context, nid int32) error {
// 	zap.L().Debug("DeleteNS Store Called")
// 	span, ctx := apm.StartSpan(ctx, "DeleteNS", "store")
// 	defer span.End()

// 	query.SetDefault(ns.db)
// 	n := query.DNSServer

// 	if _, err := n.WithContext(ctx).Where(n.ID.Eq(nid)).First(); err != nil {
// 		return ErrNSNotExist
// 	}

// 	if _, err := n.WithContext(ctx).Where(n.ID.Eq(nid)).Delete(); err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (ns *nsStore) UpdateNS(ctx context.Context, nsServer *api.DnsServer) error {
// 	zap.L().Debug("UpdateNS Store Called")
// 	span, ctx := apm.StartSpan(ctx, "UpdateNS", "store")
// 	defer span.End()

// 	query.SetDefault(ns.db)
// 	n := query.DNSServer

// 	now := time.Now().Unix()

// 	dnsServer := &model.DNSServer{
// 		Hostname:   nsServer.Hostname,
// 		Role:       nsServer.Role,
// 		UpdateTime: now,
// 	}

// 	if _, err := n.WithContext(ctx).Where(n.IP.Eq(nsServer.IP)).Updates(dnsServer); err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (ns *nsStore) GetNSList(ctx context.Context, officeId int32) ([]*model.DNSServer, error) {
// 	zap.L().Debug("GetNSList Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetNSList", "store")
// 	defer span.End()

// 	query.SetDefault(ns.db)
// 	n := query.DNSServer

// 	dnsServerList, err := n.WithContext(ctx).Where(n.OfficeID.Eq(officeId)).Find()
// 	if err != nil {
// 		return nil, err
// 	}
// 	return dnsServerList, nil
// }

// func (ns *nsStore) GetNSListByConditions(ctx context.Context, conditions []gen.Condition) ([]*model.DNSServer, error) {
// 	zap.L().Debug("GetNSListByConditions Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetNSListByConditions", "store")
// 	defer span.End()

// 	query.SetDefault(ns.db)
// 	n := query.DNSServer

// 	dnsServerList, err := n.WithContext(ctx).Where(conditions...).Find()
// 	if err != nil {
// 		return nil, err
// 	}
// 	return dnsServerList, nil
// }

// func (ns *nsStore) CreateNS(ctx context.Context, nsServer *api.DnsServer) error {
// 	zap.L().Debug("CreateNS Store Called")
// 	span, ctx := apm.StartSpan(ctx, "CreateNS", "store")
// 	defer span.End()

// 	query.SetDefault(ns.db)
// 	n := query.DNSServer

// 	now := time.Now().Unix()

// 	dnsServer := &model.DNSServer{
// 		Hostname:   nsServer.Hostname,
// 		Role:       nsServer.Role,
// 		Port:       int32(nsServer.Port),
// 		IP:         nsServer.IP,
// 		OfficeID:   nsServer.OfficeId,
// 		CreateTime: now,
// 		UpdateTime: now,
// 	}

// 	if err := n.WithContext(ctx).Create(dnsServer); err != nil {
// 		return err
// 	}
// 	return nil
// }
