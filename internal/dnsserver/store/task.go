package store

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"

	"github.com/IBM/sarama"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	TaskTable        = "dns_task"
	TaskDetailsTable = "dns_task_details"
)

type TaskStore interface {
	CreateTask(ctx context.Context, task *model.Task, details []*model.TaskDetails) error
	GetTaskList(ctx context.Context, req *api.TaskListRequest) ([]*model.Task, int64, error)

	// DNS Agent需要的方法
	GetTaskDetailsByTaskID(ctx context.Context, taskID int64) ([]*model.TaskDetails, error)
	UpdateTaskStatus(ctx context.Context, taskID int64, status model.TaskStatus, updateTime int64) error
	UpdateTaskDetailStatus(ctx context.Context, detailID int64, status model.TaskDetailStatus, resMsg string, reqTs, resTs int64) error
	UpdateTaskCounts(ctx context.Context, taskID int64, successCount, failedCount int) error

	// 重试相关方法
	GetTaskDetailByID(ctx context.Context, detailID int64) (*model.TaskDetails, error)
	RetryTaskDetail(ctx context.Context, detailID int64) error
}

type taskStore struct {
	db       *gorm.DB
	producer sarama.SyncProducer
}

type TaskForKafka struct {
	ID           int64           `json:"id"`
	Status       string          `json:"status"`
	TargetInfo   json.RawMessage `json:"target_info"` // 使用RawMessage避免二次编码
	Operator     string          `json:"operator"`
	ExecuteTime  int64           `json:"execute_time"`
	TotalDetails int             `json:"total_details"`
	SuccessCount int             `json:"success_count"`
	FailedCount  int             `json:"failed_count"`
	ScheduledAt  int64           `json:"scheduled_at"`
	CreateTime   int64           `json:"create_time"`
	UpdateTime   int64           `json:"update_time"`
}

func newTaskStore(ds *DataStore) TaskStore {
	return &taskStore{
		db:       ds.Db,
		producer: ds.Producer,
	}
}

func (s *taskStore) GetTaskList(ctx context.Context, req *api.TaskListRequest) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64

	tx := s.db.Table(TaskTable).WithContext(ctx)

	// 添加过滤条件
	if req.Status != "" {
		tx = tx.Where("status = ?", req.Status)
	}

	if req.Operator != "" {
		tx = tx.Where("operator = ?", req.Operator)
	}

	// 先计算总数
	if err := tx.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 固定按照创建时间倒序排列
	tx = tx.Order("create_time DESC")

	// 添加分页
	if req.Page > 0 {
		pageSize := 10
		if req.PageSize > 0 {
			pageSize = req.PageSize
		}
		tx = tx.Offset((req.Page - 1) * pageSize).Limit(pageSize)
	}

	if err := tx.Find(&tasks).Error; err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// StartKafkaRecoveryWorker 启动kafka恢复工作线程
func (s *taskStore) StartKafkaRecoveryWorker(ctx context.Context) {
	ticker := time.NewTicker(time.Hour * 24)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.recoveryKafkaTask(ctx)
		}
	}
}

// sendToKafka 发送数据到kafka
func (s *taskStore) sendToKafka(data []byte) error {
	zap.L().Info("向Kafka发送数据")
	msg := &sarama.ProducerMessage{
		Topic: viper.GetString("kafka.topic"),
		Value: sarama.StringEncoder(data),
	}

	if _, _, err := s.producer.SendMessage(msg); err != nil {
		zap.L().Error("向Kafka发送数据失败", zap.Error(err))
		return err
	}

	return nil
}

// recoveryKafkaTask 恢复kafka任务, 注意这里恢复的是task下的具体的某个detail任务
// 一般用法是当某个detail任务执行失败的时候，做了某些改动以后重试
func (s *taskStore) recoveryKafkaTask(ctx context.Context) {}

func (s *taskStore) CreateTask(ctx context.Context, task *model.Task, details []*model.TaskDetails) error {
	// 创建任务，直接使用当前数据库连接，不再开启新事务
	// 如果调用方已经在事务中，这样就能参与到同一个事务中
	if err := s.db.Table(TaskTable).Create(task).Error; err != nil {
		return err
	}

	// 设置 TaskDetails 的 TaskID（批量创建前必须设置）
	for i := range details {
		details[i].TaskID = task.ID
	}

	// 批量创建任务详情
	if err := s.db.Table(TaskDetailsTable).Create(details).Error; err != nil {
		return err
	}

	// 推送kafka消息
	zap.L().Info("开始推送任务到kafka", zap.Any("task", task))

	// 使用中间结构构建消息
	kafkaTask := TaskForKafka{
		ID:           task.ID,
		Status:       string(task.Status),
		TargetInfo:   json.RawMessage(task.TargetInfo), // 直接使用原始bytes
		Operator:     task.Operator,
		ExecuteTime:  task.ExecuteTime,
		TotalDetails: task.TotalDetails,
		SuccessCount: task.SuccessCount,
		FailedCount:  task.FailedCount,
		ScheduledAt:  task.ScheduledAt,
		CreateTime:   task.CreateTime,
		UpdateTime:   task.UpdateTime,
	}
	taskRaw, err := json.Marshal(kafkaTask)
	if err != nil {
		zap.L().Error("序列化任务失败", zap.Error(err))
		return err
	}

	if err := s.sendToKafka(taskRaw); err != nil {
		zap.L().Error("推送任务到kafka失败", zap.Error(err))
		return err
	}

	// 记录创建成功的信息
	zap.L().Info("任务和任务详情创建成功",
		zap.Int64("task_id", task.ID),
		zap.Int("details_count", len(details)))

	return nil
}

func (s *taskStore) GetTaskDetailsByTaskID(ctx context.Context, taskID int64) ([]*model.TaskDetails, error) {
	var details []*model.TaskDetails
	if err := s.db.Table(TaskDetailsTable).Where("task_id = ?", taskID).Find(&details).Error; err != nil {
		return nil, err
	}
	return details, nil
}

func (s *taskStore) UpdateTaskStatus(ctx context.Context, taskID int64, status model.TaskStatus, updateTime int64) error {
	return s.db.Model(&model.Task{}).Where("id = ?", taskID).Update("status", status).Update("update_time", updateTime).Error
}

func (s *taskStore) UpdateTaskDetailStatus(ctx context.Context, detailID int64, status model.TaskDetailStatus, resMsg string, reqTs, resTs int64) error {
	updates := map[string]any{
		"status":  status,
		"res_msg": resMsg,
	}

	// 只有当reqTs > 0时才更新req_ts
	if reqTs > 0 {
		updates["req_ts"] = reqTs
	}

	// 只有当resTs > 0时才更新res_ts
	if resTs > 0 {
		updates["res_ts"] = resTs
	}

	return s.db.Model(&model.TaskDetails{}).Where("id = ?", detailID).Updates(updates).Error
}

func (s *taskStore) UpdateTaskCounts(ctx context.Context, taskID int64, successCount, failedCount int) error {
	return s.db.Model(&model.Task{}).Where("id = ?", taskID).Update("success_count", successCount).Update("failed_count", failedCount).Error
}

// GetTaskDetailByID 根据ID查询单个TaskDetail
func (s *taskStore) GetTaskDetailByID(ctx context.Context, detailID int64) (*model.TaskDetails, error) {
	var detail model.TaskDetails
	if err := s.db.Table(TaskDetailsTable).Where("id = ?", detailID).First(&detail).Error; err != nil {
		return nil, err
	}
	return &detail, nil
}

// RetryTaskDetail 重试单个TaskDetail
func (s *taskStore) RetryTaskDetail(ctx context.Context, detailID int64) error {
	// 开始事务处理
	return s.db.Transaction(func(tx *gorm.DB) error {
		now := time.Now().Unix()

		// 查询要重试的TaskDetail（在事务内查询确保数据一致性）
		var detail model.TaskDetails
		if err := tx.Table(TaskDetailsTable).Where("id = ?", detailID).First(&detail).Error; err != nil {
			return fmt.Errorf("查询要重试的TaskDetail失败: %w", err)
		}

		// 验证TaskDetail状态是否可以重试
		if detail.Status != model.TaskDetailStatusFailed && detail.Status != model.TaskDetailStatusTimeout {
			return fmt.Errorf("TaskDetail状态不是失败状态，无法重试。当前状态: %s", detail.Status)
		}

		// 重置TaskDetail状态为init
		if err := tx.Table(TaskDetailsTable).Where("id = ?", detailID).Updates(map[string]any{
			"status":    model.TaskDetailStatusInit,
			"res_msg":   "",
			"req_ts":    0,
			"res_ts":    0,
			"update_at": now,
		}).Error; err != nil {
			return fmt.Errorf("重置TaskDetail状态失败: %w", err)
		}

		// 查询原始Task信息
		var originalTask model.Task
		if err := tx.Table(TaskTable).Where("id = ?", detail.TaskID).First(&originalTask).Error; err != nil {
			return fmt.Errorf("查询原始Task失败: %w", err)
		}

		// 更新Task状态为created，准备重新执行
		if err := tx.Table(TaskTable).Where("id = ?", detail.TaskID).Updates(map[string]any{
			"status":      model.TaskStatusCreated, // 在dns agent消费的时候，会将status设置为running
			"update_time": now,
		}).Error; err != nil {
			return fmt.Errorf("更新Task状态失败: %w", err)
		}

		// 重新发送到Kafka
		kafkaTask := TaskForKafka{
			ID:           originalTask.ID,
			Status:       string(model.TaskStatusCreated),
			TargetInfo:   json.RawMessage(originalTask.TargetInfo),
			Operator:     originalTask.Operator,
			ExecuteTime:  originalTask.ExecuteTime,
			TotalDetails: originalTask.TotalDetails,
			SuccessCount: originalTask.SuccessCount,
			FailedCount:  originalTask.FailedCount,
			ScheduledAt:  originalTask.ScheduledAt,
			CreateTime:   originalTask.CreateTime,
			UpdateTime:   now,
		}

		taskRaw, err := json.Marshal(kafkaTask)
		if err != nil {
			return fmt.Errorf("序列化重试任务失败: %w", err)
		}

		if err := s.sendToKafka(taskRaw); err != nil {
			return fmt.Errorf("发送重试任务到Kafka失败: %w", err)
		}

		zap.L().Info("重试任务发送成功",
			zap.Int64("task_id", detail.TaskID),
			zap.Int64("detail_id", detailID))

		return nil
	})
}
