package store

import (
	"context"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

const (
	ServerTable = "dns_server"
)

type ServerStore interface {
	GetServerByID(ctx context.Context, id int64) (*model.Server, error)
	GetServersByViewIDs(ctx context.Context, viewIDs []int64) ([]*model.Server, error)
	GetServerListWithPagination(ctx context.Context, req *api.QueryServerRequest) ([]*model.Server, int64, error)
	GetServerListByClusterID(ctx context.Context, clusterID int64) ([]*model.Server, error)
	CreateServer(ctx context.Context, server *model.Server) error
	GetMasterServers(ctx context.Context) ([]*model.Server, error)
	GetSlaveServers(ctx context.Context) ([]*model.Server, error)
}

type serverStore struct {
	ds *DataStore
}

func newServerStore(ds *DataStore) ServerStore {
	return &serverStore{ds: ds}
}

func (s *serverStore) GetMasterServers(ctx context.Context) ([]*model.Server, error) {
	servers := make([]*model.Server, 0)
	if err := s.ds.Db.Table(ServerTable).Where("role = ?", model.ServerRoleMaster).Find(&servers).Error; err != nil {
		return nil, err
	}
	return servers, nil
}

func (s *serverStore) GetSlaveServers(ctx context.Context) ([]*model.Server, error) {
	servers := make([]*model.Server, 0)
	if err := s.ds.Db.Table(ServerTable).Where("role = ?", model.ServerRoleSlave).Find(&servers).Error; err != nil {
		return nil, err
	}
	return servers, nil
}

func (s *serverStore) CreateServer(ctx context.Context, server *model.Server) error {
	if err := s.ds.Db.Table(ServerTable).Create(server).Error; err != nil {
		return err
	}
	return nil
}

func (s *serverStore) GetServerListByClusterID(ctx context.Context, clusterID int64) ([]*model.Server, error) {
	servers := make([]*model.Server, 0)
	if err := s.ds.Db.Table(ServerTable).Where("cluster_id = ?", clusterID).Find(&servers).Error; err != nil {
		return nil, err
	}
	return servers, nil
}

func (s *serverStore) GetServerListWithPagination(ctx context.Context, req *api.QueryServerRequest) ([]*model.Server, int64, error) {
	servers := make([]*model.Server, 0)
	tx := s.ds.Db.Table(ServerTable).WithContext(ctx)

	// 记录一下查询的总数
	count := int64(0)

	if req.ID > 0 {
		tx = tx.Where("id = ?", req.ID)
	}

	if req.ClusterID > 0 {
		tx = tx.Where("cluster_id = ?", req.ClusterID)
	}

	if req.ViewID > 0 {
		tx = tx.Where("view_id = ?", req.ViewID)
	}

	if req.Name != "" {
		tx = tx.Where("name = ?", req.Name)
	}

	if req.IP != "" {
		tx = tx.Where("ip = ?", req.IP)
	}

	if req.Role != "" {
		tx = tx.Where("role = ?", req.Role)
	}

	if req.Status != "" {
		tx = tx.Where("status = ?", req.Status)
	}

	// 先计算一下总数再分页
	if err := tx.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if req.Page > 0 {
		pageSize := 10
		if req.PageSize > 0 {
			pageSize = req.PageSize
		}
		tx = tx.Offset((req.Page - 1) * pageSize).Limit(pageSize)
	}

	if req.OrderBy != "" {
		orderByClause, err := buildSafeOrderByClause(req.OrderBy, req.Order)
		if err != nil {
			return nil, 0, err
		}
		tx = tx.Clauses(orderByClause)
	}

	if err := tx.Find(&servers).Error; err != nil {
		return nil, 0, err
	}
	return servers, count, nil
}

func (s *serverStore) GetServerByID(ctx context.Context, id int64) (*model.Server, error) {
	server := &model.Server{}
	if err := s.ds.Db.Table(ServerTable).Where("id = ?", id).First(server).Error; err != nil {
		return nil, err
	}

	return server, nil
}

// GetServersByViewIDs 根据视图ID查询服务器
func (s *serverStore) GetServersByViewIDs(ctx context.Context, viewIDs []int64) ([]*model.Server, error) {
	span, _ := apm.StartSpan(ctx, "GetServersByViewIDs", "store")
	defer span.End()

	// 初始化servers容器
	servers := make([]*model.Server, 0)

	if len(viewIDs) == 0 {
		zap.L().Info("viewIDs为空，不进行查询")
		return servers, nil
	}

	// 查询服务器
	if err := s.ds.Db.Table(ServerTable).Where("view_id IN (?)", viewIDs).Find(&servers).Error; err != nil {
		return nil, err
	}

	return servers, nil
}
