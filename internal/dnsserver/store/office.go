package store

import (
	"errors"

	"gorm.io/gorm"
)

var (
	ErrOfficeAlreadyExist = errors.New("office职场已经存在")
	ErrOfficeNotExist     = errors.New("office职场不存在")
)

type TsigItem struct {
	ZoneName   string `json:"zone_name"`
	TsigKey    string `json:"tsig_key"`
	TsigSecret string `json:"tsig_secret"`
}

type TsigResponse struct {
	TsigList []TsigItem `json:"tsig_list"`
}

func NewTsigResponse() *TsigResponse {
	return &TsigResponse{
		TsigList: make([]TsigItem, 0),
	}
}

type OfficeStore interface {
	// GetOfficeList(ctx context.Context) ([]*model.DNSOffice, error)
	// GetOfficeByID(ctx context.Context, id int32) (*model.DNSOffice, error)
}

func newOfficeStore(ds *DataStore) OfficeStore {
	return &officeStore{db: ds.Db}
}

type officeStore struct {
	db *gorm.DB
}

// func (os *officeStore) GetOfficeByID(ctx context.Context, id int32) (*model.DNSOffice, error) {
// 	zap.L().Debug("GetOfficeByID Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetOfficeByID", "store")
// 	defer span.End()

// 	o := query.DNSOffice

// 	if office, err := o.WithContext(ctx).Where(o.ID.Eq(id)).First(); err != nil {
// 		return nil, err
// 	} else {
// 		return office, nil
// 	}
// }

// func (os *officeStore) UpdateOffice(ctx context.Context, id int32, office *api.DNSOffice) error {
// 	zap.L().Debug("UpdateOffice Store Called")
// 	span, ctx := apm.StartSpan(ctx, "UpdateOffice", "store")
// 	defer span.End()

// 	query.SetDefault(os.db)
// 	o := query.DNSOffice

// 	now := time.Now().Unix()
// 	if _, err := o.WithContext(ctx).Where(o.ID.Eq(id)).Updates(model.DNSOffice{
// 		Code:       office.Code,
// 		Name:       office.Name,
// 		Comment:    office.Comment,
// 		UpdateTime: now,
// 	}); err != nil {
// 		return err
// 	} else {
// 		return nil
// 	}
// }

// func (os *officeStore) GetOfficeList(ctx context.Context) ([]*model.DNSOffice, error) {
// 	zap.L().Debug("GetOfficeList Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetOfficeList", "store")
// 	defer span.End()

// 	query.SetDefault(os.db)
// 	o := query.DNSOffice

// 	if office, err := o.WithContext(ctx).Find(); err != nil {
// 		return nil, err
// 	} else {
// 		return office, nil
// 	}
// }

// func (os *officeStore) DeleteOffice(ctx context.Context, officeId int32) error {
// 	zap.L().Debug("DeleteOffice Store Called")
// 	span, ctx := apm.StartSpan(ctx, "DeleteOffice", "store")
// 	defer span.End()

// 	query.SetDefault(os.db)
// 	o := query.DNSOffice

// 	_, err := o.WithContext(ctx).Where(o.ID.Eq(officeId)).Delete()
// 	if err != nil {
// 		return err
// 	}

// 	return nil
// }

// func (os *officeStore) CreateOffice(ctx context.Context, office *api.DNSOffice) (*TsigResponse, error) {
// 	zap.L().Debug("CreateOffice Store Called")
// 	span, ctx := apm.StartSpan(ctx, "CreateOffice", "store")
// 	defer span.End()

// 	query.SetDefault(os.db)
// 	q := query.Q
// 	now := time.Now().Unix()

// 	tsigRep := NewTsigResponse()

// 	// 创建一个事务
// 	if err := q.Transaction(func(tx *query.Query) error {
// 		// 创建之前先检查一下有没有, 判断的依据是职场的code
// 		_, err := tx.DNSOffice.WithContext(ctx).Where(tx.DNSOffice.Code.Eq(office.Code)).First()
// 		if err == nil {
// 			return ErrOfficeAlreadyExist
// 		}

// 		// 如果目前当前职场并不冲突，那么可以继续创建
// 		newOffice := model.DNSOffice{
// 			Code:       office.Code,
// 			Name:       office.Name,
// 			Comment:    office.Comment,
// 			CreateTime: now,
// 			UpdateTime: now,
// 		}
// 		if err := tx.DNSOffice.WithContext(ctx).Create(&newOffice); err != nil {
// 			return err
// 		}

// 		// Office创建成功以后要初始化注入5个标准的Zone区域
// 		initialZoneList := map[string]bool{
// 			"corp.kuaishou.com.": false,
// 			"test.gifshow.com.":  false,
// 			"office.it.":         false,
// 			"rpz.it.":            true,
// 			"black.it.":          true,
// 		}

// 		// 遍历5个区域分别插入数据库，注意，这里仅仅是操作数据库，并不参与实际操作dns服务的配置文件
// 		for initZ, rpzOrNot := range initialZoneList {
// 			// 生成对应的tsig的key和secret
// 			key := tsig.GenerateTSIGKey(newOffice.Code, initZ)
// 			zap.L().Debug(fmt.Sprintf("key为%s", key))
// 			secret, _ := tsig.GenerateTSIGSecret()

// 			// 通过这个key和secret来升成对应的dnsZone
// 			dnsZone := &model.DNSZone{
// 				Name:       initZ,
// 				ZoneType:   "master",
// 				OfficeID:   newOffice.ID,
// 				Rpz:        rpzOrNot,
// 				Comment:    "初始化内置区域",
// 				Creator:    "admin",
// 				CreateTime: now,
// 				UpdateTime: now,
// 				TsigKey:    key,
// 				TsigSecret: secret,
// 			}

// 			tsigRep.TsigList = append(tsigRep.TsigList, TsigItem{
// 				ZoneName:   initZ,
// 				TsigKey:    key,
// 				TsigSecret: secret,
// 			})

// 			if err := tx.DNSZone.WithContext(ctx).Create(dnsZone); err != nil {
// 				return err
// 			}

// 			// 创建Zone对应的SOA记录
// 			soa := model.DNSRecord{
// 				ZoneID:     dnsZone.ID,
// 				OfficeID:   dnsZone.OfficeID,
// 				Host:       "@",
// 				RrType:     "SOA",
// 				TTL:        3600,
// 				View:       "default",
// 				Content:    rr.NewSOARecord(dnsZone.Name),
// 				Creator:    dnsZone.Creator,
// 				CreateTime: now,
// 				UpdateTime: now,
// 			}

// 			if err := tx.DNSRecord.WithContext(ctx).Create(&soa); err != nil {
// 				return err
// 			}
// 		}

// 		return nil
// 	}); err != nil {
// 		return nil, err
// 	}

// 	return tsigRep, nil
// }
