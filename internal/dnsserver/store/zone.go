package store

import (
	"context"
	"errors"

	model "ks-knoc-server/internal/common/base/model/dnsserver"

	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const zoneTableName = "zone"

var (
	ErrZoneNotExist = errors.New("区域不存在")
)

type ZoneStore interface {
	GetAllZones(ctx context.Context) ([]*model.Zone, error)
	GetZoneByID(ctx context.Context, zoneID int64) (*model.Zone, error)
	CreateZone(ctx context.Context, zone *model.Zone) error
	DeleteZoneByID(ctx context.Context, zoneID int64) error

	// GetZone(ctx context.Context, name string, officeID int32) (*model.DNSZone, error)
	// GetZoneList(ctx context.Context, officeID int32, zoneName string) ([]*model.DNSZone, error)
	// GetZoneByConditions(ctx context.Context, conditions []gen.Condition) ([]*model.DNSZone, error)
	// CreateZone(ctx context.Context, zone *model.DNSZone) error
	// UpdateZone(ctx context.Context, zoneID int32, forwarders string) error
	// DeleteZone(ctx context.Context, del *api.DeleteZoneRequest) error
}

type zoneStore struct {
	db *gorm.DB
}

func newZoneStore(ds *DataStore) ZoneStore {
	return &zoneStore{
		db: ds.Db,
	}
}

func (zs *zoneStore) DeleteZoneByID(ctx context.Context, zoneID int64) error {
	zap.L().Debug("DeleteZoneByID Store Called")
	span, ctx := apm.StartSpan(ctx, "DeleteZoneByID", "store")
	defer span.End()

	return zs.db.Table(zoneTableName).WithContext(ctx).Where("id = ?", zoneID).Delete(&model.Zone{}).Error
}

func (zs *zoneStore) GetZoneByID(ctx context.Context, zoneID int64) (*model.Zone, error) {
	zap.L().Debug("GetZoneByID Store Called")
	span, ctx := apm.StartSpan(ctx, "GetZoneByID", "store")
	defer span.End()

	zone := &model.Zone{}
	if err := zs.db.Table(zoneTableName).WithContext(ctx).Where("id = ?", zoneID).First(zone).Error; err != nil {
		return nil, err
	}

	return zone, nil
}

func (zs *zoneStore) CreateZone(ctx context.Context, zone *model.Zone) error {
	zap.L().Debug("CreateZone Store Called")
	span, ctx := apm.StartSpan(ctx, "CreateZone", "store")
	defer span.End()

	return zs.db.Table(zoneTableName).WithContext(ctx).Create(zone).Error
}

// GetAllZones 获取所有zone, 这个会包含重复的zone，因为并没有区分view
func (zs *zoneStore) GetAllZones(ctx context.Context) ([]*model.Zone, error) {
	zap.L().Debug("GetAllZones Store Called")
	span, ctx := apm.StartSpan(ctx, "GetAllZones", "store")
	defer span.End()

	zones := make([]*model.Zone, 0)
	if err := zs.db.Table(zoneTableName).WithContext(ctx).Find(&zones).Error; err != nil {
		return nil, err
	}

	return zones, nil
}

// func (zs *zoneStore) GetZoneByConditions(ctx context.Context, conditions []gen.Condition) ([]*model.DNSZone, error) {
// 	zap.L().Debug("GetZoneByConditions Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetZoneByConditions", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone

// 	dnsZones, err := z.WithContext(ctx).Where(conditions...).Find()
// 	if err != nil {
// 		return nil, err
// 	}
// 	return dnsZones, nil
// }

// func (zs *zoneStore) GetZoneList(ctx context.Context, officeID int32, zoneName string) ([]*model.DNSZone, error) {
// 	zap.L().Debug("GetZoneList Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetZoneList", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone

// 	conditions := make([]gen.Condition, 0)
// 	conditions = append(conditions, z.OfficeID.Eq(officeID))
// 	if zoneName != "" {
// 		conditions = append(conditions, z.Name.Like("%"+zoneName+"%"))
// 	}

// 	if dnsZones, err := z.WithContext(ctx).
// 		Where(conditions...).
// 		Find(); err != nil {
// 		return nil, err
// 	} else {
// 		return dnsZones, nil
// 	}
// }

// func (zs *zoneStore) CreateZone(ctx context.Context, zone *model.DNSZone) error {
// 	zap.L().Debug("CreateZone Store Called")
// 	span, ctx := apm.StartSpan(ctx, "CreateZone", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	now := time.Now().Unix()

// 	q := query.Q
// 	if err := q.Transaction(func(tx *query.Query) error {
// 		if err := tx.DNSZone.WithContext(ctx).Create(zone); err != nil {
// 			return err
// 		}

// 		soa := model.DNSRecord{
// 			ZoneID:     zone.ID,
// 			OfficeID:   zone.OfficeID,
// 			Host:       "@",
// 			RrType:     "SOA",
// 			TTL:        3600,
// 			View:       "default",
// 			Content:    rr.NewSOARecord(zone.Name),
// 			Creator:    zone.Creator,
// 			CreateTime: now,
// 			UpdateTime: now,
// 		}

// 		if err := tx.DNSRecord.WithContext(ctx).Create(&soa); err != nil {
// 			return err
// 		}

// 		return nil
// 	}); err != nil {
// 		return err
// 	}

// 	return nil
// }

// func (zs *zoneStore) UpdateZone(ctx context.Context, zoneID int32, forwarders string) error {
// 	zap.L().Debug("UpdateZone Store Called")
// 	span, ctx := apm.StartSpan(ctx, "UpdateZone", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone

// 	if _, err := z.WithContext(ctx).Where(z.ID.Eq(zoneID)).Update(z.Forwarders, forwarders); err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (zs *zoneStore) GetZone(ctx context.Context, name string, officeID int32) (*model.DNSZone, error) {
// 	zap.L().Debug("GetZone Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetZone", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone

// 	dnsZone, err := z.WithContext(ctx).Where(z.Name.Eq(name), z.OfficeID.Eq(officeID)).First()
// 	if err != nil {
// 		return nil, err
// 	}
// 	return dnsZone, nil
// }

// func (zs *zoneStore) DeleteZone(ctx context.Context, del *api.DeleteZoneRequest) error {
// 	zap.L().Debug("DeleteZone Store Called")
// 	span, ctx := apm.StartSpan(ctx, "DeleteZone", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone

// 	if _, err := z.WithContext(ctx).Where(z.Name.Eq(del.Name), z.OfficeID.Eq(del.OfficeID)).Delete(); err != nil {
// 		return err
// 	}

// 	return nil
// }

// func (zs *zoneStore) DeleteZoneByID(ctx context.Context, zoneID int32) error {
// 	zap.L().Debug("DeleteZoneByID Store Called")
// 	span, ctx := apm.StartSpan(ctx, "DeleteZoneByID", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone
// 	if _, err := z.WithContext(ctx).Where(z.ID.Eq(zoneID)).Delete(); err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (zs *zoneStore) GetZoneByID(ctx context.Context, zid int32) (*model.DNSZone, error) {
// 	zap.L().Debug("GetZoneByID Store Called")
// 	span, ctx := apm.StartSpan(ctx, "GetZoneByID", "store")
// 	defer span.End()

// 	query.SetDefault(zs.db)
// 	z := query.DNSZone

// 	dnsZone, err := z.WithContext(ctx).Where(z.ID.Eq(zid)).First()
// 	if err != nil {
// 		return nil, err
// 	}
// 	return dnsZone, nil
// }
