package store

import (
	"fmt"
	"regexp"

	"gorm.io/gorm/clause"
)

// 字段名安全验证：只允许字母、数字、下划线
var fieldNamePattern = regexp.MustCompile(`^[a-zA-Z_][a-zA-Z0-9_]*$`)

// isValidFieldName 验证字段名是否安全
func isValidFieldName(field string) bool {
	return fieldNamePattern.MatchString(field)
}

// buildSafeOrderByClause 构建安全的ORDER BY子句
func buildSafeOrderByClause(orderBy, order string) (clause.OrderBy, error) {
	if !isValidFieldName(orderBy) {
		return clause.OrderBy{}, fmt.Errorf("排序字段名格式不合法: %s", orderBy)
	}

	desc := false
	if order == "desc" {
		desc = true
	}

	return clause.OrderBy{
		Columns: []clause.OrderByColumn{
			{Column: clause.Column{Name: orderBy}, Desc: desc},
		},
	}, nil
}

// buildSafeSelectClause 构建安全的SELECT子句
func buildSafeSelectClause(selectFields []string, groupBy string, fieldsToAggregate []string) (clause.Select, error) {
	var columns []clause.Column

	// 添加分组字段（如果有）
	if groupBy != "" {
		if !isValidFieldName(groupBy) {
			return clause.Select{}, fmt.Errorf("分组字段名格式不合法: %s", groupBy)
		}
		columns = append(columns, clause.Column{Name: groupBy})
	}

	// 添加聚合字段
	for _, field := range fieldsToAggregate {
		if !isValidFieldName(field) {
			return clause.Select{}, fmt.Errorf("聚合字段名格式不合法: %s", field)
		}
		// 直接添加字段名，聚合函数由调用者处理
		columns = append(columns, clause.Column{Name: field})
	}

	// 添加普通选择字段
	for _, field := range selectFields {
		if !isValidFieldName(field) {
			return clause.Select{}, fmt.Errorf("选择字段名格式不合法: %s", field)
		}
		columns = append(columns, clause.Column{Name: field})
	}

	return clause.Select{
		Columns: columns,
	}, nil
}
