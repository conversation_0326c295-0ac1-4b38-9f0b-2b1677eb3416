package v1

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"text/template"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/tmpl"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// getZonesConfig 获取zones配置
func (s *configService) getZonesConfig(ctx context.Context, views []*model.View) (map[string][]*api.NamedViewZonesData, error) {
	var (
		current         = time.Now()
		viewZonesConfig = make(map[string][]*api.NamedViewZonesData)
	)

	// 拿到所有的master和slave服务器
	masterServers, slaveServers, _, err := s.getServers(ctx)
	if err != nil {
		zap.L().Error("获取master servers失败", zap.Error(err))
		return nil, fmt.Errorf("获取master servers失败: %w", err)
	}

	zap.L().Info("找到服务器信息",
		zap.Int("master_count", len(masterServers)),
		zap.Int("slave_count", len(slaveServers)))

	// 获取所有zone（用于office view和default view）
	zones, err := s.store.Zone().GetAllZones(ctx)
	if err != nil {
		zap.L().Error("获取zone列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取zone列表失败: %w", err)
	}

	// 处理office级别的view
	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)

		// 只处理office级别的view
		if viewType != model.ViewLevelTypeOffice {
			continue
		}

		zap.L().Info("处理view的zone配置", zap.String("view", view.Name), zap.String("type", viewType.String()))

		// 初始化view的zone配置，master和slave各一个
		viewZonesConfig[view.Code] = make([]*api.NamedViewZonesData, 2)

		// 为master服务器生成配置
		if len(masterServers) > 0 {
			zap.L().Info("为master服务器生成zone配置",
				zap.String("view", view.Name),
				zap.Int("master_count", len(masterServers)))

			masterZonesConfig := s.buildZoneConfigForRole(view, zones, "master", masterServers, current)
			if masterZonesConfig == nil {
				zap.L().Error("为master服务器生成zone配置失败", zap.String("view", view.Name))
				return nil, fmt.Errorf("为master服务器生成zone配置失败: %s", view.Name)
			}

			viewZonesConfig[view.Code][0] = masterZonesConfig
		} else {
			zap.L().Info("没有master服务器配置, 跳过master zone配置生成", zap.String("view", view.Name))
		}

		// 为slave服务器生成配置
		if len(slaveServers) > 0 {
			zap.L().Info("为slave服务器生成zone配置",
				zap.String("view", view.Name),
				zap.Int("slave_count", len(slaveServers)))

			slaveZonesConfig := s.buildZoneConfigForRole(view, zones, "slave", masterServers, current)
			if slaveZonesConfig == nil {
				zap.L().Error("为slave服务器生成zone配置失败", zap.String("view", view.Name))
				return nil, fmt.Errorf("为slave服务器生成zone配置失败: %s", view.Name)
			}

			viewZonesConfig[view.Code][1] = slaveZonesConfig
		} else {
			zap.L().Info("没有slave服务器配置, 跳过slave zone配置生成", zap.String("view", view.Name))
		}
	}

	// 处理default view的zone配置
	zap.L().Info("处理default view的zone配置")
	viewZonesConfig["default"] = make([]*api.NamedViewZonesData, 2)

	// 为master服务器生成default view配置
	if len(masterServers) > 0 {
		zap.L().Info("为master服务器生成default view zone配置",
			zap.Int("master_count", len(masterServers)))

		masterDefaultZonesConfig := s.buildDefaultZoneConfigForRole(zones, "master", masterServers, slaveServers, current)
		if masterDefaultZonesConfig == nil {
			zap.L().Error("为master服务器生成default view zone配置失败")
			return nil, fmt.Errorf("为master服务器生成default view zone配置失败")
		}

		viewZonesConfig["default"][0] = masterDefaultZonesConfig
	} else {
		zap.L().Info("没有master服务器配置, 跳过master default zone配置生成")
	}

	// 为slave服务器生成default view配置
	if len(slaveServers) > 0 {
		zap.L().Info("为slave服务器生成default view zone配置",
			zap.Int("slave_count", len(slaveServers)))

		slaveDefaultZonesConfig := s.buildDefaultZoneConfigForRole(zones, "slave", masterServers, slaveServers, current)
		if slaveDefaultZonesConfig == nil {
			zap.L().Error("为slave服务器生成default view zone配置失败")
			return nil, fmt.Errorf("为slave服务器生成default view zone配置失败")
		}

		viewZonesConfig["default"][1] = slaveDefaultZonesConfig
	} else {
		zap.L().Info("没有slave服务器配置, 跳过slave default zone配置生成")
	}

	return viewZonesConfig, nil
}

// buildZoneConfigForRole 为指定角色生成zone配置
func (s *configService) buildZoneConfigForRole(view *model.View, zones []*model.Zone,
	serverRole string, masterServers []*api.ServerData, currentTime time.Time) *api.NamedViewZonesData {
	zap.L().Info("为指定角色生成zone配置",
		zap.String("view", view.Name),
		zap.String("role", serverRole))

	zonesConfig := &api.NamedViewZonesData{
		GeneratedAt:       currentTime.Format("2006-01-02 15:04:05"),
		ViewCode:          view.Code,
		ViewName:          view.Name,
		ViewKeyName:       s.getViewKeyName(view.Code),
		ServerRole:        serverRole,
		Zones:             make([]*api.ZoneConfigData, 0),
		BlacklistZones:    make([]*api.ZoneConfigData, 0),
		HasBlacklistZones: false,
		MasterServers:     masterServers,
	}

	for _, zone := range zones {
		zoneConfig := &api.ZoneConfigData{
			Name: zone.Name,
		}

		if zone.ZoneType == string(model.ZoneTypeBlackList) {
			zonesConfig.BlacklistZones = append(zonesConfig.BlacklistZones, zoneConfig)
			zonesConfig.HasBlacklistZones = true
		} else {
			zonesConfig.Zones = append(zonesConfig.Zones, zoneConfig)
		}
	}

	return zonesConfig
}

// buildDefaultZoneConfigForRole 为default view的指定角色生成zone配置
// default view只包含blacklist黑名单zone，不包含其他业务zone
func (s *configService) buildDefaultZoneConfigForRole(zones []*model.Zone, serverRole string,
	masterServers, slaveServers []*api.ServerData, currentTime time.Time) *api.NamedViewZonesData {
	zap.L().Info("为default view的指定角色生成zone配置", zap.String("role", serverRole))

	// 构建slave服务器列表（用于also-notify）
	slaveServerList := make([]*api.SlaveServer, 0)
	for _, server := range slaveServers {
		slaveServerList = append(slaveServerList, &api.SlaveServer{
			IP:   server.IP,
			Port: 53,
		})
	}

	// 获取default view的key名称
	defaultKeyName := s.getDefaultViewKeyName()

	zonesConfig := &api.NamedViewZonesData{
		GeneratedAt:       currentTime.Format("2006-01-02 15:04:05"),
		ViewCode:          "default",
		ViewName:          "default",
		ViewKeyName:       defaultKeyName,
		ServerRole:        serverRole,
		Zones:             make([]*api.ZoneConfigData, 0),
		BlacklistZones:    make([]*api.ZoneConfigData, 0),
		HasBlacklistZones: false,
		MasterServers:     masterServers,
		SlaveServers:      slaveServerList,
	}

	// 对于default view，只包含blacklist类型的zone
	for _, zone := range zones {
		zoneType := model.ToZoneType(zone.ZoneType)

		if zoneType == model.ZoneTypeBlackList {
			zoneConfig := &api.ZoneConfigData{
				Name: zone.Name,
			}
			zonesConfig.BlacklistZones = append(zonesConfig.BlacklistZones, zoneConfig)
			zonesConfig.HasBlacklistZones = true
		}
		// 其他类型的zone不包含在default view中
	}

	zap.L().Info("default view zone配置生成完成",
		zap.String("role", serverRole),
		zap.Int("blacklist_zones", len(zonesConfig.BlacklistZones)))

	return zonesConfig
}

// getZonesConfigFile 生成zones配置文件
func (s *configService) getZonesConfigFile(ctx context.Context, viewZonesConfig map[string][]*api.NamedViewZonesData) error {
	zap.L().Info("开始生成zones配置文件")
	span, _ := apm.StartSpan(ctx, "GenerateZonesConfigFile", "service")
	defer span.End()

	for viewCode, zoneConfigs := range viewZonesConfig {
		for idx, zonesData := range zoneConfigs {
			if zonesData == nil {
				continue
			}

			serverRole := ""
			if idx == 0 {
				serverRole = "master"
			} else {
				serverRole = "slave"
			}

			// 根据view类型选择不同的模板
			var tmplContent *template.Template
			var err error

			if viewCode == "default" {
				// 对于default view使用专用模板
				tmplContent, err = template.New("defaultZones").Parse(tmpl.NamedDefaultZonesTemplate)
				if err != nil {
					zap.L().Error("解析default zones模板失败", zap.Error(err))
					return fmt.Errorf("解析default zones模板失败: %w", err)
				}
			} else {
				// 对于其他view使用通用模板
				tmplContent, err = template.New("zones").Parse(tmpl.NamedViewZonesTemplate)
				if err != nil {
					zap.L().Error("解析zones模板失败", zap.Error(err))
					return fmt.Errorf("解析zones模板失败: %w", err)
				}
			}

			var buf bytes.Buffer
			if err := tmplContent.Execute(&buf, zonesData); err != nil {
				zap.L().Error("渲染zones模板失败", zap.Error(err))
				return fmt.Errorf("渲染zones模板失败: %w", err)
			}

			// 确保zones目录存在
			zonesDir := filepath.Join(DefaultConfigDir, "zones")
			if err := os.MkdirAll(zonesDir, 0755); err != nil {
				zap.L().Error("创建zones目录失败", zap.Error(err), zap.String("dir", zonesDir))
				return fmt.Errorf("创建zones目录失败: %w", err)
			}

			// 保存文件，文件名处理
			var filename string
			if viewCode == "default" {
				// default view的文件名不包含server role，因为master和slave配置相同
				filename = filepath.Join(zonesDir, "named.default.zones")
			} else {
				// 其他view的文件名包含server role
				filename = filepath.Join(zonesDir, fmt.Sprintf("named.%s.%s.zones", zonesData.ViewCode, serverRole))
			}

			if err := os.WriteFile(filename, buf.Bytes(), 0644); err != nil {
				zap.L().Error("保存zones配置文件失败", zap.Error(err), zap.String("filename", filename))
				return fmt.Errorf("保存zones配置文件失败: %w", err)
			}

			zap.L().Info("zones配置文件生成成功",
				zap.String("filename", filename),
				zap.String("view", zonesData.ViewName),
				zap.String("server_role", zonesData.ServerRole),
				zap.Int("zones", len(zonesData.Zones)),
				zap.Int("blacklist_zones", len(zonesData.BlacklistZones)))
		}
	}

	return nil
}

// getZonesConfigObject 生成zones配置文件对象
func (s *configService) getZonesConfigObject(ctx context.Context, viewZonesConfig map[string][]*api.NamedViewZonesData) ([]*api.ConfigFile, error) {
	zap.L().Info("开始生成zones配置文件对象")
	span, _ := apm.StartSpan(ctx, "GenerateZonesConfigObject", "service")
	defer span.End()

	var files []*api.ConfigFile

	for viewCode, zoneConfigs := range viewZonesConfig {
		for _, zonesData := range zoneConfigs {

			if zonesData == nil {
				continue
			}

			// 根据view类型选择不同的模板
			var tmplContent *template.Template
			var err error

			if viewCode == "default" {
				// 对于default view使用专用模板
				tmplContent, err = template.New("defaultZones").Parse(tmpl.NamedDefaultZonesTemplate)
				if err != nil {
					zap.L().Error("解析default zones模板失败", zap.Error(err))
					return nil, fmt.Errorf("解析default zones模板失败: %w", err)
				}
			} else {
				// 对于其他view使用通用模板
				tmplContent, err = template.New("zones").Parse(tmpl.NamedViewZonesTemplate)
				if err != nil {
					zap.L().Error("解析zones模板失败", zap.Error(err))
					return nil, fmt.Errorf("解析zones模板失败: %w", err)
				}
			}

			var buf bytes.Buffer
			if err := tmplContent.Execute(&buf, zonesData); err != nil {
				zap.L().Error("渲染zones模板失败", zap.Error(err))
				return nil, fmt.Errorf("渲染zones模板失败: %w", err)
			}

			// 文件名处理
			var fileName string
			if viewCode == "default" {
				// default view的文件名不包含server role
				fileName = "zones/named.default.zones"
			} else {
				// 其他view的文件名包含server role
				fileName = fmt.Sprintf("zones/named.%s.%s.zones", zonesData.ViewCode, zonesData.ServerRole)
			}

			config := &api.ConfigFile{
				Name:    fileName,
				Content: buf.Bytes(),
			}

			files = append(files, config)
		}
	}

	return files, nil
}

// getZoneDBConfigWithCache 优化版本的getZoneDBConfig，使用缓存数据
func (s *configService) getZoneDBConfigWithCache(cache *ConfigDataCache, view *model.View, zone *model.Zone) (*api.ZoneConfig, error) {
	viewType := model.ToViewLevelType(view.LevelType)
	// 只处理office级别的view, 其他级别的view是用来做数据继承的
	if viewType != model.ViewLevelTypeOffice {
		zap.L().Warn("view不是office级别的view, 跳过生成db配置文件",
			zap.String("view", view.Name), zap.String("view_type", viewType.String()))
		return nil, nil
	}

	// 从缓存中获取该视图下的servers
	servers := s.getServersByViewIDFromCache(cache, view.ID)
	if len(servers) == 0 {
		zap.L().Error("view下没有配置servers, 跳过该view的配置生成", zap.String("view_name", view.Name), zap.String("zone_name", zone.Name))
		return nil, nil // 返回nil, nil表示跳过，不是错误
	}

	// 从缓存中获取view继承链
	viewHierarchy, exists := cache.ViewHierarchyCache[view.ID]
	if !exists {
		zap.L().Error("未找到view继承链缓存", zap.Int64("view_id", view.ID))
		return nil, fmt.Errorf("未找到view继承链缓存: %d", view.ID)
	}

	// 构建一个viewMap, 用于快速查找view
	viewMap := make(map[int64]*model.View)
	for _, v := range viewHierarchy {
		viewMap[v.ID] = v
	}

	// 按照域名存储所有的解析记录
	domainRecords := make(map[int]map[string][]*model.DNSRecord)
	levels := make([]int, 0)

	// 按照视图获取所有记录，从优先级低到高
	for _, vh := range viewHierarchy {
		levels = append(levels, vh.Level)

		// 从缓存中获取记录
		records := s.getRecordsFromCache(cache, zone.ID, vh.ID)
		if len(records) == 0 {
			continue
		}

		// 按域名、记录类型分组记录
		for _, record := range records {
			viewLevel := viewMap[record.ViewID].Level
			if _, ok := domainRecords[viewLevel]; !ok {
				domainRecords[viewLevel] = make(map[string][]*model.DNSRecord)
			}

			// 如果该域名下的视图不存在，初始化
			if _, ok := domainRecords[viewLevel][record.Name]; !ok {
				domainRecords[viewLevel][record.Name] = make([]*model.DNSRecord, 0)
			}

			// 添加记录
			domainRecords[viewLevel][record.Name] = append(domainRecords[viewLevel][record.Name], record)
		}
	}

	// 针对viewLevel进行排序
	sort.Ints(levels)

	zap.L().Info("levels", zap.Any("levels", levels))

	// 初始化一个map用来保存合并后的结果
	mergedRecords := make(map[string][]*model.DNSRecord)
	for _, level := range levels {
		for domain, records := range domainRecords[level] {
			if _, ok := mergedRecords[domain]; !ok {
				mergedRecords[domain] = make([]*model.DNSRecord, 0)
			}
			mergedRecords[domain] = records
		}
	}

	// 替换原有的记录获取部分
	records := make([]*model.DNSRecord, 0)
	for _, record := range mergedRecords {
		records = append(records, record...)
	}

	config := &api.ZoneConfig{
		TTL: 3600,
		SOA: api.SOAConfig{
			PrimaryNS:   "ns1." + zone.Name,
			AdminEmail:  SoaAdmin + "." + EmailSuffix,
			Serial:      time.Now().Format("20060102") + "01",
			Refresh:     "3H",
			Retry:       "15M",
			Expire:      "1W",
			NegativeTTL: "1D",
		},
		NSRecords:    make([]string, 0),
		NSServers:    make(map[string]string),
		ARecords:     make(map[string][]api.ARecord),
		CNAMERecords: make([]api.CNAMERecord, 0),
	}

	// 生成ns记录, 要根据对应views下实际配置的servers来计算
	for idx, server := range servers {
		nsHost := fmt.Sprintf("ns%d", idx+1)
		config.NSRecords = append(config.NSRecords, nsHost)
		config.NSServers[nsHost] = server.IP
	}

	for _, record := range records {
		switch strings.ToLower(record.RType) {
		case "a":
			if _, ok := config.ARecords[record.Name]; !ok {
				config.ARecords[record.Name] = make([]api.ARecord, 0)
			}
			config.ARecords[record.Name] = append(config.ARecords[record.Name], api.ARecord{
				Domain: record.Name,
				IP:     record.Value,
			})
		case "cname":
			config.CNAMERecords = append(config.CNAMERecords, api.CNAMERecord{
				Domain: record.Name,
				Target: record.Value,
			})
		default:
			zap.L().Warn("不支持的记录类型", zap.String("record_type", record.RType))
			return nil, fmt.Errorf("不支持的记录类型: %s", record.RType)
		}
	}
	return config, nil
}

// getDefaultZoneDBConfigWithCache 优化版本的getDefaultZoneDBConfig，使用缓存数据
func (s *configService) getDefaultZoneDBConfigWithCache(cache *ConfigDataCache, zone *model.Zone) (*api.ZoneConfig, error) {
	zap.L().Info("开始获取default view的zone db配置", zap.String("zone", zone.Name))

	defaultView := cache.DefaultView
	if defaultView == nil {
		zap.L().Error("未找到default view")
		return nil, fmt.Errorf("未找到default view")
	}

	// 从缓存中获取slave servers
	slaveServers := cache.SlaveServers
	var useDefaultServer bool
	if len(slaveServers) == 0 {
		zap.L().Warn("default view下没有配置slave servers, 使用默认127.0.0.1配置", zap.String("zone_name", zone.Name))
		useDefaultServer = true
		// 创建一个默认的server用于生成配置
		slaveServers = []*model.Server{
			{
				ServerInfo: model.ServerInfo{
					IP:   "127.0.0.1",
					Name: "localhost",
				},
			},
		}
	}

	// 从缓存中获取default view的记录
	records := s.getRecordsFromCache(cache, zone.ID, defaultView.ID)

	config := &api.ZoneConfig{
		TTL: 3600,
		SOA: api.SOAConfig{
			PrimaryNS:   "ns1." + zone.Name,
			AdminEmail:  SoaAdmin + "." + EmailSuffix,
			Serial:      time.Now().Format("20060102") + "01",
			Refresh:     "3H",
			Retry:       "15M",
			Expire:      "1W",
			NegativeTTL: "1D",
		},
		NSRecords:    make([]string, 0),
		NSServers:    make(map[string]string),
		ARecords:     make(map[string][]api.ARecord),
		CNAMERecords: make([]api.CNAMERecord, 0),
	}

	// 生成ns记录，基于master servers
	for idx, server := range slaveServers {
		nsHost := fmt.Sprintf("ns%d", idx+1)
		config.NSRecords = append(config.NSRecords, nsHost)
		config.NSServers[nsHost] = server.IP
	}

	// 处理DNS记录
	for _, record := range records {
		switch strings.ToLower(record.RType) {
		case "a":
			if _, ok := config.ARecords[record.Name]; !ok {
				config.ARecords[record.Name] = make([]api.ARecord, 0)
			}
			config.ARecords[record.Name] = append(config.ARecords[record.Name], api.ARecord{
				Domain: record.Name,
				IP:     record.Value,
			})
		case "cname":
			config.CNAMERecords = append(config.CNAMERecords, api.CNAMERecord{
				Domain: record.Name,
				Target: record.Value,
			})
		default:
			zap.L().Warn("不支持的记录类型", zap.String("record_type", record.RType))
			return nil, fmt.Errorf("不支持的记录类型: %s", record.RType)
		}
	}

	zap.L().Info("default view zone db配置生成完成",
		zap.String("zone", zone.Name),
		zap.Int("ns_records", len(config.NSRecords)),
		zap.Int("a_records", len(config.ARecords)),
		zap.Int("cname_records", len(config.CNAMERecords)),
		zap.Bool("use_default_server", useDefaultServer))

	return config, nil
}

// buildZoneDBConfigWithCache 优化版本的buildZoneDBConfig，使用缓存数据避免重复查询
func (s *configService) buildZoneDBConfigWithCache(ctx context.Context, cache *ConfigDataCache) ([]*api.ConfigFile, error) {
	zap.L().Info("开始生成优化版本的db配置文件")
	span, _ := apm.StartSpan(ctx, "buildZoneDBConfigWithCache", "service")
	defer span.End()

	var files []*api.ConfigFile

	if len(cache.Zones) == 0 {
		zap.L().Warn("zones为空, 跳过生成db配置文件", zap.Int("zones_count", len(cache.Zones)))
		return nil, nil
	}

	// 如果没有views，只处理default view
	if len(cache.Views) == 0 {
		zap.L().Info("没有views配置, 只处理default view的db文件", zap.Int("zones_count", len(cache.Zones)))
	}

	zap.L().Info("开始生成db配置文件",
		zap.Int("zones_count", len(cache.Zones)),
		zap.Int("views_count", len(cache.Views)))

	processedCount := 0
	skippedCount := 0

	// 处理office view的db文件
	if len(cache.Views) > 0 {
		// 获取office级别的views
		officeViews := cache.ViewsByType[model.ViewLevelTypeOffice.String()]

		for _, view := range officeViews {
			for _, zone := range cache.Zones {
				zap.L().Info("开始处理office view-zone组合",
					zap.String("view", view.Name),
					zap.String("zone", zone.Name))

				zoneConfig, err := s.getZoneDBConfigWithCache(cache, view, zone)
				if err != nil {
					zap.L().Error("获取zone配置失败", zap.Error(err),
						zap.Int64("view_id", view.ID), zap.Int64("zone_id", zone.ID))
					return nil, fmt.Errorf("获取zone配置失败: %w", err)
				}

				if zoneConfig == nil {
					zap.L().Info("zone配置为空, 跳过生成db配置文件",
						zap.String("view", view.Name),
						zap.String("zone", zone.Name),
						zap.String("reason", "view下没有配置servers"))
					skippedCount++
					continue
				}

				// 生成db配置文件
				zoneTmpl, err := template.New("zoneDB").Parse(tmpl.ZoneTemplate)
				if err != nil {
					zap.L().Error("解析zone模板失败", zap.Error(err))
					return nil, fmt.Errorf("解析zone模板失败: %w", err)
				}

				var buf bytes.Buffer
				if err := zoneTmpl.Execute(&buf, zoneConfig); err != nil {
					zap.L().Error("渲染zone模板失败", zap.Error(err))
					return nil, fmt.Errorf("渲染zone模板失败: %w", err)
				}

				fileName := fmt.Sprintf("db/%s/db.%s", view.Code, zone.Name)
				config := &api.ConfigFile{
					Name:    fileName,
					Content: buf.Bytes(),
				}

				files = append(files, config)
				processedCount++

				zap.L().Info("成功生成office view db配置文件",
					zap.String("view", view.Name),
					zap.String("zone", zone.Name),
					zap.String("file_name", fileName),
					zap.Int("file_size", len(buf.Bytes())))
			}
		}
	}

	// 处理default view的db文件
	// default view只包含blacklist类型的zone
	zap.L().Info("开始处理default view的db文件")

	for _, zone := range cache.Zones {
		zoneType := model.ToZoneType(zone.ZoneType)

		// 只处理blacklist类型的zone
		if zoneType != model.ZoneTypeBlackList {
			continue
		}

		zap.L().Info("开始处理default view-zone组合",
			zap.String("view", "default"),
			zap.String("zone", zone.Name),
			zap.String("zone_type", zone.ZoneType))

		zoneConfig, err := s.getDefaultZoneDBConfigWithCache(cache, zone)
		if err != nil {
			zap.L().Error("获取default zone配置失败", zap.Error(err), zap.Int64("zone_id", zone.ID))
			return nil, fmt.Errorf("获取default zone配置失败: %w", err)
		}

		if zoneConfig == nil {
			zap.L().Info("default zone配置为空, 跳过生成db配置文件",
				zap.String("zone", zone.Name),
				zap.String("reason", "没有配置servers"))
			skippedCount++
			continue
		}

		// 生成db配置文件
		zoneTmpl, err := template.New("defaultZoneDB").Parse(tmpl.ZoneTemplate)
		if err != nil {
			zap.L().Error("解析zone模板失败", zap.Error(err))
			return nil, fmt.Errorf("解析zone模板失败: %w", err)
		}

		var buf bytes.Buffer
		if err := zoneTmpl.Execute(&buf, zoneConfig); err != nil {
			zap.L().Error("渲染zone模板失败", zap.Error(err))
			return nil, fmt.Errorf("渲染zone模板失败: %w", err)
		}

		fileName := fmt.Sprintf("db/default/db.%s", zone.Name)
		config := &api.ConfigFile{
			Name:    fileName,
			Content: buf.Bytes(),
		}

		files = append(files, config)
		processedCount++

		zap.L().Info("成功生成default view db配置文件",
			zap.String("zone", zone.Name),
			zap.String("file_name", fileName),
			zap.Int("file_size", len(buf.Bytes())))
	}

	zap.L().Info("优化版本db配置文件生成完成",
		zap.Int("total_files", len(files)),
		zap.Int("processed_count", processedCount),
		zap.Int("skipped_count", skippedCount))

	return files, nil
}
