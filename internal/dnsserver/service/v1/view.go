package v1

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	param "ks-knoc-server/internal/common/base/api/dnsserver"
	message "ks-knoc-server/internal/common/base/message/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/bind/tsig"
	"ks-knoc-server/internal/dnsserver/store"

	"github.com/spf13/viper"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type ViewService interface {
	GetViewTree(ctx context.Context) (*param.ViewTree, error)
	GetViewList(ctx context.Context) ([]*message.QueryViewListMessage, error)
	CreateView(ctx context.Context, req *param.CreateViewParams) error
	UpdateView(ctx context.Context, req *param.UpdateViewParams) error
	GetViewByID(ctx context.Context, viewID int64) (*message.QueryViewDetailMessage, error)
}

type viewService struct {
	store store.Factory
}

var _ ViewService = (*viewService)(nil)

func newViewService(s *service) ViewService {
	return &viewService{store: s.store}
}

func (s *viewService) GetViewByID(ctx context.Context, viewID int64) (*message.QueryViewDetailMessage, error) {
	zap.L().Debug("GetViewByID Service Called")
	span, ctx := apm.StartSpan(ctx, "GetViewByID", "service")
	defer span.End()

	views, err := s.store.View().GetViewByIDs(ctx, []int64{viewID})
	if err != nil {
		zap.L().Error("GetViewByID Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	if len(views) == 0 {
		return nil, errors.New("视图不存在")
	}

	view := views[0]

	acls := make([]string, 0)
	if err := json.Unmarshal(view.ACLs, &acls); err != nil {
		zap.L().Error("Unmarshal ACLs Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	res := &message.QueryViewDetailMessage{
		ID:            view.ID,
		Code:          view.Code,
		Name:          view.Name,
		Description:   view.Description,
		ACLs:          acls,
		SecretControl: view.SecretControl,
		ViewKeyAlgo:   view.ViewKeyAlgo,
		ParentID:      view.ParentID,
		Level:         view.Level,
		LevelType:     view.LevelType,
		CreateTime:    view.CreateTime,
		UpdateTime:    view.UpdateTime,
		Deleted:       view.Deleted,
	}

	return res, nil
}

// UpdateView 更新视图
func (s *viewService) UpdateView(ctx context.Context, req *param.UpdateViewParams) error {
	zap.L().Debug("UpdateView Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateView", "service")
	defer span.End()

	// 首先需要确认要更新的视图是否存在
	views, err := s.store.View().GetViewByIDs(ctx, []int64{req.ID})
	if err != nil {
		zap.L().Error("GetViewByID Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	if len(views) == 0 {
		return errors.New("视图不存在")
	}

	view := views[0]

	// 获取viewLevelType
	viewLevelType := model.ToViewLevelType(view.LevelType)

	// 构建要更新的map
	updateMap := make(map[string]any)

	// 更新name
	if req.Name != "" && req.Name != view.Name {
		updateMap["name"] = req.Name
	}

	// 更新description
	if req.Description != "" && req.Description != view.Description {
		updateMap["description"] = req.Description
	}

	// 更新secretControl, 该更新方法是为了兼容旧版本已经创建过view但是未开启secret控制的情况
	// 新版本强制要求开启secret控制，因此这里需要判断req的SecretControl为true，但是view的SecretControl为false的情况
	if req.SecretControl && !view.SecretControl {
		if viewLevelType == model.ViewLevelTypeOffice || viewLevelType == model.ViewLevelTypeDefault {
			updateMap["secret_control"] = true
			updateMap["view_key_algo"] = req.ViewKeyAlgo

			// 赋值给view，用于后续的加密
			view.SecretControl = true
			view.ViewKeyAlgo = req.ViewKeyAlgo

			if err := s.encryptViewKey(ctx, true, view); err != nil {
				zap.L().Error("加密view key失败", zap.Error(err), zap.Bool("secret_control", true))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return err
			}
			updateMap["view_key"] = view.ViewKey
			updateMap["view_key_md5"] = view.ViewKeyMD5
		} else {
			zap.L().Warn("非Office类型的视图不支持开启secret控制",
				zap.Int64("view_id", view.ID),
				zap.String("view_level_type", view.LevelType),
				zap.String("view_name", view.Name),
			)
			return errors.New("非Office或Default类型的视图不支持开启secret控制")
		}
	} else if req.SecretControl && view.SecretControl {
		if viewLevelType == model.ViewLevelTypeOffice {
			if view.ViewKey == "" || view.ViewKeyMD5 == "" {
				zap.L().Warn("Office类型的视图开启secret控制, 但view key为空, 重新生成对应的view key",
					zap.Int64("view_id", view.ID),
					zap.String("view_level_type", view.LevelType),
					zap.String("view_name", view.Name),
				)

				if err := s.encryptViewKey(ctx, true, view); err != nil {
					zap.L().Error("加密view key失败", zap.Error(err), zap.Bool("secret_control", true))
					e := apm.CaptureError(ctx, err)
					e.Send()
					return err
				}
				updateMap["view_key"] = view.ViewKey
				updateMap["view_key_md5"] = view.ViewKeyMD5
			}
		}
	}

	// 更新acls
	if len(req.ACLs) > 0 {
		acls, err := json.Marshal(req.ACLs)
		if err != nil {
			zap.L().Error("Marshal ACLs Error", zap.Error(err))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return err
		}
		updateMap["acls"] = acls
	}

	if len(updateMap) == 0 {
		zap.L().Warn("没有需要更新的字段", zap.Int64("view_id", view.ID))
		return nil
	}

	// 更新view
	if err := s.store.View().UpdateView(ctx, view.ID, updateMap); err != nil {
		zap.L().Error("UpdateView Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	return nil
}

// CreateView 创建视图
func (s *viewService) CreateView(ctx context.Context, req *param.CreateViewParams) error {
	zap.L().Debug("CreateView Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateView", "service")
	defer span.End()

	// 校验viewLevelType是否合法
	viewLevelType := model.ToViewLevelType(req.LevelType)
	if viewLevelType == model.ViewLevelTypeInvalid {
		return errors.New("不合法的视图级别")
	}

	// 保存要创建的aclSet用于去重
	aclSet := make(map[string]struct{})
	for _, acl := range req.ACLs {
		aclSet[acl] = struct{}{}
	}

	// 获取所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("GetAllView Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 遍历所有视图进行校验
	// 1. 校验视图名称是否已存在
	// 2. 校验视图的acl是否和提交的acl有冲突
	for _, view := range views {
		// view名称查重
		if view.Name == req.Name {
			return errors.New("视图名称已存在")
		}

		// view标识查重
		if view.Code == req.Code {
			return errors.New("视图标识已存在")
		}

		// 校验view的acl是否和提交的acl有冲突
		acls := make([]string, 0)
		if err := json.Unmarshal(view.ACLs, &acls); err != nil {
			zap.L().Error("Unmarshal AddressList Error", zap.Error(err))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return err
		}

		// 打印view的acl
		for _, acl := range acls {
			zap.L().Info("view acl", zap.String("acl", acl))
		}
	}

	// 根据视图级别创建视图
	if err := s.CreateViewByLevelType(ctx, req); err != nil {
		return err
	}
	return nil
}

func (s *viewService) encryptViewKey(ctx context.Context, secretControl bool, view *model.View) error {
	// 如果不需要开启密钥控制，则不需要加密, 直接返回即可
	if !secretControl {
		return nil
	}

	viewKeyAlgo := tsig.ToTSIGKeyAlgo(view.ViewKeyAlgo)
	if !viewKeyAlgo.IsValid() {
		zap.L().Error("view key算法不合法", zap.String("view_key_algo", view.ViewKeyAlgo))
		return errors.New("view key算法不合法")
	}

	// 动态生成一个view的key
	viewKey := viewKeyAlgo.GenerateTSIGKey()

	// 从配置文件中获取对应的密钥
	secret := viper.GetString("keyToken")
	if secret == "" {
		return errors.New("RNDC密钥不存在")
	}

	// TsigKey需要加密保存到数据库，不可以进行明文的保存
	encryptedViewKey, md5Hash, err := tsig.EncryptKey(secret, viewKey)
	if err != nil {
		zap.L().Error("EncryptViewKey Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	view.ViewKey = encryptedViewKey
	view.ViewKeyMD5 = md5Hash

	return nil
}

// CreateDefaultView 创建默认视图
func (s *viewService) CreateDefaultView(ctx context.Context, req *param.CreateViewParams) error {
	zap.L().Debug("CreateDefaultView Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateDefaultView", "service")
	defer span.End()

	// default类型的视图要求暂时只能有一个，所以需要先查询是否存在default视图
	defaultView, err := s.store.View().GetViewByType(ctx, model.ViewLevelTypeDefault)
	if err != nil {
		zap.L().Error("GetViewByType Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 如果存在default视图，则不能创建, 初期设计严格要求Default下只能存在一个视图
	if len(defaultView) > 0 {
		return errors.New("default视图已存在, 当前Default层级只允许存在一个视图")
	}

	// 序列化acl
	acls, err := json.Marshal(req.ACLs)
	if err != nil {
		zap.L().Error("Marshal ACLs Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 初始化默认的modelView
	view := &model.View{
		Name:          req.Name,
		Description:   req.Description,
		ACLs:          json.RawMessage(acls),
		SecretControl: req.SecretControl,
		ParentID:      0, // 默认视图的parentID为0
		Level:         model.ViewLevelTypeDefault.GetViewLevel(),
		LevelType:     model.ViewLevelTypeDefault.String(),
	}

	// 说明需要开启view视图的密钥控制
	if err := s.encryptViewKey(ctx, req.SecretControl, view); err != nil {
		zap.L().Error("加密view视图的key失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 默认视图不存在，说明可以继续创建
	if err := s.store.View().CreateView(ctx, view); err != nil {
		zap.L().Error("创建视图失败", zap.Error(err), zap.String("view类型", model.ViewLevelTypeDefault.String()))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	return nil
}

// CreateViewByLevelType 根据视图级别创建视图
func (s *viewService) CreateViewByLevelType(ctx context.Context, req *param.CreateViewParams) error {
	zap.L().Debug("CreateViewByLevelType Service Called")
	span, _ := apm.StartSpan(ctx, "CreateViewByLevelType", "service")
	defer span.End()

	// 转换视图级别
	viewLevelType := model.ToViewLevelType(req.LevelType)
	if viewLevelType == model.ViewLevelTypeInvalid {
		err := errors.New("不合法的视图级别")
		zap.L().Error("不合法的视图级别", zap.String("level_type", req.LevelType))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 初始化视图
	view := &model.View{
		Code:          req.Code,
		Name:          req.Name,
		Description:   req.Description,
		SecretControl: req.SecretControl,
		Level:         viewLevelType.GetViewLevel(),
		LevelType:     viewLevelType.String(),
	}

	// 当创建的视图是Default类型的视图时，需要先查询是否存在Default视图
	if viewLevelType == model.ViewLevelTypeDefault {
		// default类型的视图要求暂时只能有一个，所以需要先查询是否存在default视图
		defaultView, err := s.store.View().GetViewByType(ctx, model.ViewLevelTypeDefault)
		if err != nil {
			zap.L().Error("GetViewByType Error", zap.Error(err))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return err
		}

		// 如果存在default视图，则不能创建, 初期设计严格要求Default下只能存在一个视图
		if len(defaultView) > 0 {
			return errors.New("default视图已存在, 当前Default层级只允许存在一个视图")
		}

		// 设置parentID
		view.ParentID = 0
	} else {
		if req.ParentID <= 0 {
			return errors.New("非Default类型的视图, parentID不合法")
		}
		// 非Default类型的视图，则需要设置parentID
		view.ParentID = req.ParentID
	}

	// 序列化acl
	acls, err := json.Marshal(req.ACLs)
	if err != nil {
		zap.L().Error("序列化ACLs失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}
	now := time.Now().Unix()
	view.CreateTime = now
	view.UpdateTime = now
	view.ACLs = acls
	view.ViewKeyAlgo = req.ViewKeyAlgo

	// 说明需要开启view视图的密钥控制
	if err := s.encryptViewKey(ctx, req.SecretControl, view); err != nil {
		zap.L().Error("加密view视图的key失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 创建Biz视图
	if err := s.store.View().CreateView(ctx, view); err != nil {
		zap.L().Error("创建视图失败", zap.Error(err), zap.String("view类型", viewLevelType.String()))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	return nil
}

func (s *viewService) buildViewTree(views []*model.View) (map[int64]*param.ViewTree, *param.ViewTree) {
	viewMap := make(map[int64]*param.ViewTree)
	defaultView := &param.ViewTree{}
	for _, view := range views {
		viewMap[view.ID] = &param.ViewTree{
			ID:        view.ID,
			Code:      view.Code,
			Name:      view.Name,
			ParentID:  view.ParentID,
			Level:     view.Level,
			LevelType: view.LevelType,
		}
		if view.LevelType == model.ViewLevelTypeDefault.String() {
			defaultView = viewMap[view.ID]
		}
	}

	return viewMap, defaultView
}

// GetViewTree 获取视图树
func (s *viewService) GetViewTree(ctx context.Context) (*param.ViewTree, error) {
	zap.L().Debug("GetViewTree Service Called")
	span, _ := apm.StartSpan(ctx, "GetViewTree", "service")
	defer span.End()

	// 获取default视图
	defaultView := &param.ViewTree{}

	// 获取所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("GetAllView Error", zap.Error(err))
		return nil, err
	}

	// 构造视图树
	viewMap, defaultView := s.buildViewTree(views)

	for _, view := range viewMap {
		if view.ParentID > 0 {
			parentView, ok := viewMap[view.ParentID]
			if !ok {
				zap.L().Error("父视图不存在", zap.Int64("parent_id", view.ParentID))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return nil, err
			}
			parentView.Children = append(parentView.Children, view)
		}
	}

	return defaultView, nil
}

// GetViewList 获取视图列表
func (s *viewService) GetViewList(ctx context.Context) ([]*message.QueryViewListMessage, error) {
	zap.L().Debug("GetViewList Service Called")
	span, _ := apm.StartSpan(ctx, "GetViewList", "service")
	defer span.End()

	// 获取所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("GetAllView Error", zap.Error(err))
		return nil, err
	}

	// 将views转换为message.QueryViewListMessage
	viewList := make([]*message.QueryViewListMessage, 0)
	for _, view := range views {
		acls := make([]string, 0)
		if err := json.Unmarshal(view.ACLs, &acls); err != nil {
			zap.L().Error("Unmarshal ACLs Error", zap.Error(err))
			return nil, err
		}
		viewList = append(viewList, &message.QueryViewListMessage{
			ID:            view.ID,
			Code:          view.Code,
			Name:          view.Name,
			Description:   view.Description,
			ACLs:          acls,
			SecretControl: view.SecretControl,
			ViewKeyAlgo:   view.ViewKeyAlgo,
			ParentID:      view.ParentID,
			Level:         view.Level,
			LevelType:     view.LevelType,
			CreateTime:    view.CreateTime,
			UpdateTime:    view.UpdateTime,
			Deleted:       view.Deleted,
		})
	}

	return viewList, nil
}
