package v1

import "testing"

func TestCheckDNSTypeCNAME(t *testing.T) {
	tests := []struct {
		recordValue string
		zoneType    string
		wantErr     bool
	}{
		{
			recordValue: "www.baidu.com",
			zoneType:    "rpz",
			wantErr:     true,
		},
	}

	for _, test := range tests {
		err := CheckDNSTypeCNAME(test.recordValue, test.zoneType)
		if (err != nil) != test.wantErr {
			t.Errorf("CheckDNSTypeCNAME(%s, %s) error = %v, wantErr %v", test.recordValue, test.zoneType, err, test.wantErr)
		}
	}
}