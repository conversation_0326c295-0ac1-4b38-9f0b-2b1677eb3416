package v1

import (
	"fmt"
	"strings"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/pkg/utils"
	"regexp"

	"go.uber.org/zap"
)

type RpzTypeValue string

func (r RpzTypeValue) String() string {
	return string(r)
}

const (
	RpzTypeNxdomain RpzTypeValue = "."
	RpzTypeNoData   RpzTypeValue = "*."
	RpzTypeTcpOnly  RpzTypeValue = "rpz-tcp-only."
	RpzTypePassthru RpzTypeValue = "rpz-passthru."
	RpzTypeDrop     RpzTypeValue = "rpz-drop."
	RpzTypeUnknown  RpzTypeValue = ""
)

func ToRpzType(value string) RpzTypeValue {
	switch strings.ToLower(value) {
	case ".":
		return RpzTypeNxdomain
	case "*.":
		return RpzTypeNoData
	case "rpz-tcp-only.":
		return RpzTypeTcpOnly
	case "rpz-passthru.":
		return RpzTypePassthru
	case "rpz-drop.":
		return RpzTypeDrop
	default:
		return RpzTypeUnknown
	}
}

func IsValidRpzType(value string) bool {
	rt := ToRpzType(value)
	return rt != RpzTypeUnknown
}

func CheckDNSTypeA(recordValue string) error {
	if !utils.IsValidIPv4(recordValue) {
		return fmt.Errorf("无效的IPv4地址: %s", recordValue)
	}
	return nil
}

func CheckDNSTypeAAAA(recordValue string) error {
	if !utils.IsValidIPv6(recordValue) {
		return fmt.Errorf("无效的IPv6地址: %s", recordValue)
	}
	return nil
}

// CheckDNSTypeCNAME 检查CNAME记录的值是否是合法的
func CheckDNSTypeCNAME(recordValue, zoneType string) error {
	zap.L().Debug("CheckDNSTypeCNAME", zap.String("recordValue", recordValue), zap.String("zoneType", zoneType))

	if utils.IsValidCNAME(recordValue) {
		return nil
	} else {
		// 有可能是rpz的专用类型
		zt := model.ToZoneType(zoneType)
		if zt == model.ZoneTypeRPZ || zt == model.ZoneTypeBlackList {
			if IsValidRpzType(recordValue) {
				return nil
			}
		}
		return fmt.Errorf("无效的CNAME: %s", recordValue)
	}
}

func CheckDNSTypeNS(recordValue string) error {
	return nil
}

func CheckDNSTypeMX(recordValue string) error {
	// MX记录格式: 优先级 域名
	parts := strings.Fields(recordValue)
	if len(parts) != 2 {
		err := fmt.Errorf("MX记录格式错误，应为'优先级 域名': %s", recordValue)
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	// 验证优先级是否为数字
	if !regexp.MustCompile(`^\d+$`).MatchString(parts[0]) {
		err := fmt.Errorf("MX记录优先级必须为数字: %s", parts[0])
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	// 验证域名
	if !regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`).MatchString(parts[1]) && parts[1] != "." {
		err := fmt.Errorf("MX记录中的域名格式无效: %s", parts[1])
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	return nil
}

func CheckDNSTypePTR(recordValue string) error {
	// 验证域名格式
	domainRegex := regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`)
	if !domainRegex.MatchString(recordValue) && recordValue != "." {
		err := fmt.Errorf("无效的域名格式: %s", recordValue)
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	return nil
}

func CheckDNSTypeSRV(recordValue string) error {
	// SRV记录格式: 优先级 权重 端口 目标
	parts := strings.Fields(recordValue)
	if len(parts) != 4 {
		err := fmt.Errorf("SRV记录格式错误，应为'优先级 权重 端口 目标': %s", recordValue)
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	// 验证前三个字段是否为数字
	for i := 0; i < 3; i++ {
		if !regexp.MustCompile(`^\d+$`).MatchString(parts[i]) {
			err := fmt.Errorf("SRV记录中的优先级、权重和端口必须为数字: %s", parts[i])
			zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
			return err
		}
	}
	// 验证目标域名
	domainRegex := regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`)
	if !domainRegex.MatchString(parts[3]) && parts[3] != "." {
		err := fmt.Errorf("SRV记录中的目标域名格式无效: %s", parts[3])
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	return nil
}

func CheckDNSTypeTXT(recordValue string) error {
	// TXT记录通常允许任意文本，但可以检查长度等
	if len(recordValue) > 255 {
		err := fmt.Errorf("TXT记录长度超过255个字符")
		zap.L().Error("记录值不合法", zap.Error(err), zap.String("value", recordValue))
		return err
	}
	return nil
}
