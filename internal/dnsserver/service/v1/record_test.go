package v1

import (
	"context"
	"testing"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/store"

	"github.com/stretchr/testify/mock"
)

// MockStore 模拟store接口
type MockStore struct {
	mock.Mock
	store.Factory
}

func (m *MockStore) View() store.ViewStore {
	args := m.Called()
	return args.Get(0).(store.ViewStore)
}

func (m *MockStore) Record() store.RecordStore {
	args := m.Called()
	return args.Get(0).(store.RecordStore)
}

// MockViewStore 模拟ViewStore接口
type MockViewStore struct {
	mock.Mock
	store.ViewStore
}

func (m *MockViewStore) GetViewByIDs(ctx context.Context, ids []int64) ([]*model.View, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).([]*model.View), args.Error(1)
}

// MockRecordStore 模拟RecordStore接口
type MockRecordStore struct {
	mock.Mock
	store.RecordStore
}

func (m *MockRecordStore) GetRpzCache() ([]byte, error) {
	args := m.Called()
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockRecordStore) SetRpzCache(rpzCache any) error {
	args := m.Called(rpzCache)
	return args.Error(0)
}

type validatorTest struct {
	name  string
	rr    *model.DNSRecord
	zone  *model.Zone
	value string
}

func TestRecordValidator(t *testing.T) {
	tests := []validatorTest{
		{
			name: "A记录 测试带掩码的",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "***********/24",
		},
		{
			name: "A记录 测试网络号",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "***********",
		},
		{
			name: "A记录 测试地址",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "*************",
		},
		{
			name: "A记录 测试非法字符串",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "invalid ip",
		},
		{
			name: "A记录 测试非法掩码",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "***********/33",
		},
		{
			name: "A记录 测试IP中包含负值",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "192.168.1.-1",
		},
		{
			name: "A记录 测试IP中包含空格",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "192.168.1. 1",
		},
		{
			name: "A记录 测试IP地址越界",
			rr: &model.DNSRecord{
				RType: "A",
			},
			value: "192.168.1.256",
		},
		{
			name: "CNAME记录 测试合法",
			rr: &model.DNSRecord{
				RType: "CNAME",
			},
			zone: &model.Zone{
				Name:     "corp.kuaishou.com",
				ZoneType: string(model.ZoneTypeDomain),
			},
			value: "ksap.corp.kuaishou.com",
		},
		{
			name: "CNAME记录 测试Rpz类型",
			rr: &model.DNSRecord{
				RType: "CNAME",
			},
			zone: &model.Zone{
				Name:     "rpz-exact",
				ZoneType: string(model.ZoneTypeRPZ),
			},
			value: "ksap-h2.corp.kuaishou.com",
		},
		{
			name: "CNAME记录 测试Rpz NXDOMAIN",
			rr: &model.DNSRecord{
				RType: "CNAME",
			},
			zone: &model.Zone{
				Name:     "rpz-exact",
				ZoneType: string(model.ZoneTypeRPZ),
			},
			value: ".",
		},
		{
			name: "CNAME记录 测试Rpz Passthru",
			rr: &model.DNSRecord{
				RType: "CNAME",
			},
			zone: &model.Zone{
				Name:     "rpz-exact",
				ZoneType: string(model.ZoneTypeRPZ),
			},
			value: "rpz-passthru.",
		},
		{
			name: "CNAME记录 测试Rpz Drop",
			rr: &model.DNSRecord{
				RType: "CNAME",
			},
			zone: &model.Zone{
				Name:     "rpz-exact",
				ZoneType: string(model.ZoneTypeRPZ),
			},
			value: "rpz-drop.",
		},
		{
			name: "CNAME记录 测试Rpz NODATA",
			rr: &model.DNSRecord{
				RType: "CNAME",
			},
			zone: &model.Zone{
				Name:     "rpz-exact",
				ZoneType: string(model.ZoneTypeRPZ),
			},
			value: "*.",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if _, err := recordCheck(test.rr.RType, test.zone, test.value); err != nil {
				t.Errorf("recordCheck(%s, %s) = %v, want %v", test.rr.RType, test.value, err, nil)
			}
		})
	}
}

func TestBuildRpzTree(t *testing.T) {
	// 创建recordService实例
	rs := &recordService{}

	// 测试数据
	records := []*model.DNSRecord{
		{
			Name:       "*.corp.kuaishou.com",
			RType:      "A",
			Value:      "192.168.1.2",
			IsWildCard: true,
		},
		{
			Name:       "mail.corp.kuaishou.com",
			RType:      "A",
			Value:      "192.168.1.3",
			IsWildCard: false,
		},
		{
			Name:       "*.a.b.corp.kuaishou.com",
			RType:      "A",
			Value:      "1.1.1.1",
			IsWildCard: true,
		},
		{
			Name:       "b.corp.kuaishou.com",
			RType:      "A",
			Value:      "2.2.2.2",
			IsWildCard: false,
		},
		{
			Name:       "eruka.eff.it",
			RType:      "A",
			Value:      "3.3.3.3",
			IsWildCard: false,
		},
	}

	// 构建RPZ树
	tree := model.NewRpzRoot()
	for _, record := range records {
		rs.addRecordToRpzTree(tree, record)
	}

	// 打印树状结构
	PrintRpzTree(tree)
}

// func TestCheckRpzRecordCoverage(t *testing.T) {
// 	// 创建mock store
// 	mockStore := new(MockStore)
// 	mockViewStore := new(MockViewStore)
// 	mockRecordStore := new(MockRecordStore)
// 	mockStore.On("View").Return(mockViewStore)
// 	mockStore.On("Record").Return(mockRecordStore)

// 	// 创建recordService实例
// 	rs := &recordService{
// 		store: mockStore,
// 	}

// 	// 测试用例1: 非泛解析记录，应该直接返回nil
// 	t.Run("非泛解析记录", func(t *testing.T) {
// 		// 重置所有mock
// 		mockStore.ExpectedCalls = nil
// 		mockViewStore.ExpectedCalls = nil
// 		mockRecordStore.ExpectedCalls = nil

// 		// 设置基本mock
// 		mockStore.On("View").Return(mockViewStore)
// 		mockStore.On("Record").Return(mockRecordStore)

// 		record := &model.DNSRecord{
// 			Name: "test.example.com",
// 		}
// 		zone := &model.Zone{
// 			ID:   1,
// 			Name: "example.com",
// 		}
// 		view := &model.View{
// 			ID: 1,
// 		}

// 		// 即使是非泛解析记录，也需要mock GetRpzCache 方法
// 		// 创建一个空的缓存数据
// 		rpzCache := map[int64]map[int64]*model.RpzTree{}
// 		cacheData, _ := json.Marshal(rpzCache)
// 		mockRecordStore.On("GetRpzCache").Return(cacheData, nil)

// 		err := rs.checkRpzRecordCoverage(context.Background(), record, zone, view)
// 		assert.NoError(t, err)
// 	})

// 	// 测试用例2: office视图，存在上级泛解析，但缺少中间记录
// 	t.Run("office视图-缺少中间记录", func(t *testing.T) {
// 		// 重置所有mock
// 		mockStore.ExpectedCalls = nil
// 		mockViewStore.ExpectedCalls = nil
// 		mockRecordStore.ExpectedCalls = nil

// 		// 设置基本mock
// 		mockStore.On("View").Return(mockViewStore)
// 		mockStore.On("Record").Return(mockRecordStore)

// 		// 设置mock数据
// 		record := &model.DNSRecord{
// 			Name: "*.a.b.corp.kuaishou.com",
// 		}
// 		zone := &model.Zone{
// 			ID:   1,
// 			Name: "corp.kuaishou.com",
// 		}
// 		view := &model.View{
// 			ID:       3,
// 			ParentID: 2, // biz view id
// 		}

// 		// 设置mock返回值
// 		mockViewStore.On("GetViewByIDs", mock.Anything, []int64{2}).Return([]*model.View{
// 			{
// 				ID:       2,
// 				ParentID: 1, // default view id
// 			},
// 		}, nil)

// 		// 构建测试数据 - 模拟缺少中间记录的情况
// 		// 在default视图中有泛解析记录*.corp.kuaishou.com
// 		// 但在biz和office视图中缺少必要的中间记录
// 		rpzCache := map[int64]map[int64]*model.RpzTree{
// 			1: { // default view
// 				1: {
// 					Root: &model.DomainNode{
// 						Children: map[string]*model.DomainNode{
// 							"corp": {
// 								Children: map[string]*model.DomainNode{
// 									"kuaishou": {
// 										Children: map[string]*model.DomainNode{
// 											"com": {
// 												IsWildCard: true,
// 												Records: []*model.DNSRecord{
// 													{Name: "*.corp.kuaishou.com"},
// 												},
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			2: { // biz view - 没有b.corp.kuaishou.com记录
// 				1: {
// 					Root: &model.DomainNode{
// 						// 故意不添加中间记录
// 					},
// 				},
// 			},
// 			3: { // office view - 没有a.b.corp.kuaishou.com记录
// 				1: {
// 					Root: &model.DomainNode{
// 						// 故意不添加中间记录
// 					},
// 				},
// 			},
// 		}

// 		// Mock GetRpzCache 方法
// 		cacheData, _ := json.Marshal(rpzCache)
// 		mockRecordStore.On("GetRpzCache").Return(cacheData, nil)

// 		err := rs.checkRpzRecordCoverage(context.Background(), record, zone, view)
// 		assert.Error(t, err)
// 		assert.Contains(t, err.Error(), "需要先添加以下精确记录")
// 	})

// 	// 测试用例3: office视图，存在上级泛解析，且所有中间记录都存在
// 	t.Run("office视图-中间记录完整", func(t *testing.T) {
// 		// 重置所有mock
// 		mockStore.ExpectedCalls = nil
// 		mockViewStore.ExpectedCalls = nil
// 		mockRecordStore.ExpectedCalls = nil

// 		// 设置基本mock
// 		mockStore.On("View").Return(mockViewStore)
// 		mockStore.On("Record").Return(mockRecordStore)

// 		// 设置mock数据
// 		record := &model.DNSRecord{
// 			Name: "*.a.b.corp.kuaishou.com",
// 		}
// 		zone := &model.Zone{
// 			ID:   1,
// 			Name: "corp.kuaishou.com",
// 		}
// 		view := &model.View{
// 			ID:       3,
// 			ParentID: 2, // biz view id
// 		}

// 		// 设置mock返回值
// 		mockViewStore.On("GetViewByIDs", mock.Anything, []int64{2}).Return([]*model.View{
// 			{
// 				ID:       2,
// 				ParentID: 1, // default view id
// 			},
// 		}, nil)

// 		// 构建测试数据
// 		rpzCache := map[int64]map[int64]*model.RpzTree{
// 			1: { // default view
// 				1: {
// 					Root: &model.DomainNode{
// 						Children: map[string]*model.DomainNode{
// 							"corp": {
// 								Children: map[string]*model.DomainNode{
// 									"kuaishou": {
// 										Children: map[string]*model.DomainNode{
// 											"com": {
// 												IsWildCard: true,
// 												Records: []*model.DNSRecord{
// 													{Name: "*.corp.kuaishou.com"},
// 												},
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			2: { // biz view
// 				1: {
// 					Root: &model.DomainNode{
// 						Children: map[string]*model.DomainNode{
// 							"b": {
// 								Children: map[string]*model.DomainNode{
// 									"corp": {
// 										Children: map[string]*model.DomainNode{
// 											"kuaishou": {
// 												Children: map[string]*model.DomainNode{
// 													"com": {
// 														Records: []*model.DNSRecord{
// 															{Name: "b.corp.kuaishou.com"},
// 														},
// 													},
// 												},
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 			3: { // office view
// 				1: {
// 					Root: &model.DomainNode{
// 						Children: map[string]*model.DomainNode{
// 							"a": {
// 								Children: map[string]*model.DomainNode{
// 									"b": {
// 										Children: map[string]*model.DomainNode{
// 											"corp": {
// 												Children: map[string]*model.DomainNode{
// 													"kuaishou": {
// 														Children: map[string]*model.DomainNode{
// 															"com": {
// 																Records: []*model.DNSRecord{
// 																	{Name: "a.b.corp.kuaishou.com"},
// 																},
// 															},
// 														},
// 													},
// 												},
// 											},
// 										},
// 									},
// 								},
// 							},
// 						},
// 					},
// 				},
// 			},
// 		}

// 		// Mock GetRpzCache 方法
// 		cacheData, _ := json.Marshal(rpzCache)
// 		mockRecordStore.On("GetRpzCache").Return(cacheData, nil)

// 		err := rs.checkRpzRecordCoverage(context.Background(), record, zone, view)
// 		assert.NoError(t, err)
// 	})
// }
