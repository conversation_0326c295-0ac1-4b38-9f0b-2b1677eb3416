package v1

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/tmpl"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// getViewHierarchy 获取视图继承链，按优先级从低到高排序
func (s *configService) getViewHierarchy(ctx context.Context, viewID int64, viewHierarchy *model.ViewList) (model.ViewList, error) {
	// 获取当前视图
	currentView, err := s.store.View().GetViewByIDs(ctx, []int64{viewID})
	if err != nil {
		return nil, fmt.Errorf("获取视图失败: %w", err)
	}

	if len(currentView) == 0 {
		return nil, fmt.Errorf("视图不存在: %d", viewID)
	}

	// 构建视图继承链
	*viewHierarchy = append(*viewHierarchy, currentView[0])

	// 如果有父视图，递归获取父视图链
	if currentView[0].ParentID > 0 {
		parentHierarchy, err := s.getViewHierarchy(ctx, currentView[0].ParentID, &model.ViewList{})
		if err != nil {
			return nil, err
		}
		// 将父视图链加入结果，确保低优先级（父视图）在前
		*viewHierarchy = append(parentHierarchy, *viewHierarchy...)
	}

	return *viewHierarchy, nil
}

// ServersByCluster 按cluster分组的服务器信息
type ServersByCluster struct {
	ClusterID     int64
	ClusterName   string
	MasterServers []*api.ServerData
	SlaveServers  []*api.ServerData
	Forwarders    []string
}

// getServersByCluster 按cluster分组获取服务器信息
func (s *configService) getServersByCluster(ctx context.Context) ([]*ServersByCluster, error) {
	zap.L().Info("开始按cluster分组获取服务器信息")
	span, ctx := apm.StartSpan(ctx, "getServersByCluster", "service")
	defer span.End()

	var serversByCluster []*ServersByCluster

	// 获取所有集群
	clusters, err := s.store.Cluster().GetClusterList(ctx)
	if err != nil {
		zap.L().Error("获取集群列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取集群列表失败: %w", err)
	}

	// 遍历每个集群，获取该集群下的服务器
	for _, cluster := range clusters {
		// 跳过删除的或状态不是active的集群
		if cluster.Deleted || cluster.Status != model.ClusterStatusActive.String() {
			zap.L().Debug("跳过非活跃集群", zap.String("cluster", cluster.Name), zap.String("status", cluster.Status))
			continue
		}

		// 解析cluster的forwarders配置
		var forwarders []string
		if err := json.Unmarshal(cluster.Forwarders, &forwarders); err != nil {
			zap.L().Error("解析cluster forwarders失败", zap.Error(err), zap.String("cluster", cluster.Name))
			forwarders = defaultForwarders
		} else {
			// 过滤空字符串
			var validForwarders []string
			for _, f := range forwarders {
				if trimmed := strings.TrimSpace(f); trimmed != "" {
					validForwarders = append(validForwarders, trimmed)
				}
			}
			if len(validForwarders) == 0 {
				forwarders = defaultForwarders
			} else {
				forwarders = validForwarders
				zap.L().Info("使用集群forwarders配置",
					zap.String("cluster", cluster.Name),
					zap.Strings("forwarders", forwarders))
			}
		}

		// 获取该集群下的所有servers
		servers, err := s.store.Server().GetServerListByClusterID(ctx, cluster.ID)
		if err != nil {
			zap.L().Error("获取集群servers失败", zap.Error(err), zap.Int64("cluster_id", cluster.ID))
			continue // 继续处理下一个集群，不中断整个流程
		}

		if len(servers) == 0 {
			zap.L().Warn("集群下没有服务器", zap.String("cluster", cluster.Name))
			continue
		}

		// 按角色分类服务器
		var masters, slaves []*api.ServerData
		for _, server := range servers {
			if server.IP == "" {
				continue // 跳过没有IP的服务器
			}

			serverData := &api.ServerData{
				IP:          server.IP,
				IPv6IP:      "", // ks-knoc-server的Server模型暂时没有IPv6IP字段
				IPv4Enabled: server.IP != "",
				IPv6Enabled: false, // 暂时不启用IPv6，待后续扩展
				ClusterID:   cluster.ID,
			}

			switch server.Role {
			case model.ServerRoleMaster:
				masters = append(masters, serverData)
				zap.L().Info("找到master server",
					zap.String("cluster", cluster.Name),
					zap.String("server", server.Name),
					zap.String("ip", server.IP))
			case model.ServerRoleSlave:
				slaves = append(slaves, serverData)
				zap.L().Info("找到slave server",
					zap.String("cluster", cluster.Name),
					zap.String("server", server.Name),
					zap.String("ip", server.IP))
			default:
				zap.L().Warn("找到未知角色服务器",
					zap.String("cluster", cluster.Name),
					zap.String("server", server.Name),
					zap.String("ip", server.IP))
			}
		}

		// 只有当该cluster有服务器时才添加到结果中
		if len(masters) > 0 {
			serversByCluster = append(serversByCluster, &ServersByCluster{
				ClusterID:     cluster.ID,
				ClusterName:   cluster.Name,
				MasterServers: masters,
				SlaveServers:  slaves,
				Forwarders:    forwarders,
			})

			zap.L().Info("集群服务器统计",
				zap.String("cluster", cluster.Name),
				zap.Int("masters", len(masters)),
				zap.Int("slaves", len(slaves)))
		}
	}

	if len(serversByCluster) == 0 {
		zap.L().Warn("未找到任何active的集群服务器，创建默认配置")
		// 返回一个默认的配置以避免配置生成失败
		serversByCluster = []*ServersByCluster{
			{
				ClusterID:   0,
				ClusterName: "default",
				MasterServers: []*api.ServerData{
					{
						IP:          "127.0.0.1",
						IPv6IP:      "",
						IPv4Enabled: true,
						IPv6Enabled: false,
						ClusterID:   0,
					},
				},
				SlaveServers: []*api.ServerData{},
				Forwarders:   defaultForwarders,
			},
		}
		zap.L().Info("使用默认的localhost配置")
	}

	return serversByCluster, nil
}

// getServers 获取所有cluster中的master和slave以及对应的forwarders map
// 为了保持向后兼容性而保留的方法，内部调用新的getServersByCluster方法
func (s *configService) getServers(ctx context.Context) ([]*api.ServerData, []*api.ServerData, map[int64][]string, error) {
	zap.L().Info("开始获取master servers（兼容性方法）")
	span, ctx := apm.StartSpan(ctx, "getServers", "service")
	defer span.End()

	// 调用新的按cluster分组的方法
	serversByCluster, err := s.getServersByCluster(ctx)
	if err != nil {
		return nil, nil, nil, err
	}

	var (
		masters              []*api.ServerData
		slaves               []*api.ServerData
		clusterForwardersMap = make(map[int64][]string)
	)

	// 将按cluster分组的结果合并为旧格式
	for _, cluster := range serversByCluster {
		// 合并master服务器
		masters = append(masters, cluster.MasterServers...)

		// 合并slave服务器
		slaves = append(slaves, cluster.SlaveServers...)

		// 保存forwarders映射
		clusterForwardersMap[cluster.ClusterID] = cluster.Forwarders
	}

	zap.L().Info("服务器信息汇总",
		zap.Int("total_masters", len(masters)),
		zap.Int("total_slaves", len(slaves)),
		zap.Int("clusters", len(serversByCluster)))

	return masters, slaves, clusterForwardersMap, nil
}

func (s *configService) getViewConfigData(ctx context.Context, role string,
	masterServers, slaveServers []*api.ServerData, clusterForwardersMap map[int64][]string) (*api.NamedConfViewsData, error) {
	// 准备配置数据
	viewsData := &api.NamedConfViewsData{
		GeneratedAt:   time.Now().Format("2006-01-02 15:04:05"),
		ServerRole:    role,
		SlaveServers:  make([]*api.SlaveServer, 0),
		MasterServers: make([]*api.MasterServer, 0),
		Views:         make([]*api.ViewConfig, 0),
	}

	// 根据角色设置从服务器列表
	if role == "master" {
		// master配置中需要知道slave服务器信息（用于also-notify, allow-transfer）
		for _, server := range slaveServers {
			viewsData.SlaveServers = append(viewsData.SlaveServers, &api.SlaveServer{
				IP:   server.IP,
				Port: 53, // DNS默认端口
			})
		}
	}

	// 对于slave角色，设置master服务器列表（用于match-clients排除）
	if role == "slave" {
		for _, server := range masterServers {
			viewsData.MasterServers = append(viewsData.MasterServers, &api.MasterServer{
				IP:   server.IP,
				Port: 53, // DNS默认端口
			})
		}
	}

	// 获取所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("获取视图列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图列表失败: %w", err)
	}

	// 获取所有zones，用于根据zone类型确定RPZ zones
	allZones, err := s.store.Zone().GetAllZones(ctx)
	if err != nil {
		zap.L().Error("获取zone列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取zone列表失败: %w", err)
	}

	// 过滤出RPZ和黑名单类型的zones
	rpzZones := make([]string, 0)
	for _, zone := range allZones {
		zoneType := model.ToZoneType(zone.ZoneType)
		if zoneType == model.ZoneTypeRPZ || zoneType == model.ZoneTypeBlackList {
			rpzZones = append(rpzZones, zone.Name)
		}
	}

	// 处理office级别的view（排除default和biz）
	var defaultView *model.View
	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)

		if viewType == model.ViewLevelTypeDefault {
			defaultView = view
			continue
		}

		if viewType != model.ViewLevelTypeOffice {
			continue // 只处理office级别的view
		}

		// 生成ACL名称（不再直接解析ACL地址列表）
		aclName := s.getACLNameForView(view)

		// 生成view key名称（只需要key名称，不需要解密内容）
		var keyName string
		if view.ViewKey != "" {
			// 使用view.Code生成标准的key名称，与keys配置文件中的key名称保持一致
			keyName = fmt.Sprintf("office_%s_key", view.Code)
			zap.L().Debug("生成view key名称", zap.String("view", view.Name), zap.String("key_name", keyName))
		} else {
			zap.L().Warn("view没有配置key，跳过生成", zap.String("view", view.Name), zap.String("view_code", view.Code))
			continue // 跳过没有key的view
		}

		// 获取forwarders配置 - 优先使用当前role的第一个服务器所属cluster的forwarders
		var forwarders []string
		if role == "master" && len(masterServers) > 0 {
			// master角色使用第一个master服务器所属cluster的forwarders
			if fwds, exists := clusterForwardersMap[masterServers[0].ClusterID]; exists {
				forwarders = fwds
			}
		} else if role == "slave" && len(slaveServers) > 0 {
			// slave角色使用第一个slave服务器所属cluster的forwarders
			if fwds, exists := clusterForwardersMap[slaveServers[0].ClusterID]; exists {
				forwarders = fwds
			}
		}

		// 如果没有找到对应的forwarders，使用第一个可用的
		if len(forwarders) == 0 && len(clusterForwardersMap) > 0 {
			for _, fwds := range clusterForwardersMap {
				forwarders = fwds
				break
			}
		}

		// 最后的后备选项
		if len(forwarders) == 0 {
			forwarders = defaultForwarders
		}

		// 构建ResponsePolicy配置
		var responsePolicy *api.ResponsePolicy
		if len(rpzZones) > 0 {
			responsePolicy = &api.ResponsePolicy{
				Zones: rpzZones,
			}
		}

		viewConfig := &api.ViewConfig{
			ViewName:       view.Code, // 使用view.Code作为配置中的名称
			KeyName:        keyName,
			ACLName:        aclName,           // 使用ACL名称引用
			AllowQuery:     defaultAllowQuery, // 默认允许所有查询
			Recursion:      "yes",             // 默认开启递归
			ResponsePolicy: responsePolicy,    // RPZ配置
			Forwarders:     forwarders,
		}

		viewsData.Views = append(viewsData.Views, viewConfig)
		zap.L().Info("处理view配置", zap.String("view", view.Name), zap.String("type", view.LevelType))
	}

	// 处理默认view配置
	if defaultView != nil {
		// 构建默认view的ResponsePolicy配置
		var defaultResponsePolicy *api.ResponsePolicy
		if len(rpzZones) > 0 {
			defaultResponsePolicy = &api.ResponsePolicy{
				Zones: rpzZones,
			}
		}

		// 获取默认view的ACL名称
		defaultACLName := s.getACLNameForView(defaultView)

		viewsData.DefaultView = &api.ViewConfig{
			ViewName:       "default",
			KeyName:        "default_key",
			ACLName:        defaultACLName, // 使用ACL名称引用
			AllowQuery:     []string{"any"},
			Recursion:      "yes",
			ResponsePolicy: defaultResponsePolicy, // 默认view也配置RPZ
			Forwarders:     defaultForwarders,
		}
	}

	return viewsData, nil
}

// renderAndSaveViewsConfigForRole 为指定角色渲染views模板并保存到文件
func (s *configService) renderAndSaveViewsConfigForRole(data *api.NamedConfViewsData, role string) error {
	// 渲染模板
	tmplContent, err := template.New("views").Parse(tmpl.NamedConfViewsTemplate)
	if err != nil {
		zap.L().Error("解析views模板失败", zap.Error(err))
		return fmt.Errorf("解析views模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, data); err != nil {
		zap.L().Error("渲染views模板失败", zap.Error(err))
		return fmt.Errorf("渲染views模板失败: %w", err)
	}

	// 确保目录存在
	if err := os.MkdirAll(DefaultConfigDir, 0755); err != nil {
		zap.L().Error("创建配置目录失败", zap.Error(err), zap.String("dir", DefaultConfigDir))
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 保存文件
	filename := filepath.Join(DefaultConfigDir, fmt.Sprintf("named.conf.views.%s", role))
	if err := os.WriteFile(filename, buf.Bytes(), 0644); err != nil {
		zap.L().Error("保存views配置文件失败", zap.Error(err), zap.String("filename", filename))
		return fmt.Errorf("保存views配置文件失败: %w", err)
	}

	zap.L().Info("named.conf.views配置文件生成成功",
		zap.String("filename", filename),
		zap.String("server_role", role),
		zap.Int("slave_servers", len(data.SlaveServers)),
		zap.Int("views", len(data.Views)))

	return nil
}

// GenerateViewsConfigFile 生成named.conf.views配置文件
func (s *configService) GenerateViewsConfigFile(ctx context.Context) error {
	zap.L().Info("开始生成named.conf.views配置文件")
	span, ctx := apm.StartSpan(ctx, "GenerateViewsConfigFile", "service")
	defer span.End()

	// 获取集群和服务器信息
	masterServers, slaveServers, clusterForwardersMap, err := s.getServers(ctx)
	if err != nil {
		zap.L().Error("获取服务器信息失败", zap.Error(err))
		return fmt.Errorf("获取服务器信息失败: %w", err)
	}

	// 为master和slave分别生成配置
	masterViewsData, err := s.getViewConfigData(ctx, "master", masterServers, slaveServers, clusterForwardersMap)
	if err != nil {
		return fmt.Errorf("生成master views配置失败: %w", err)
	}

	if err := s.renderAndSaveViewsConfigForRole(masterViewsData, "master"); err != nil {
		zap.L().Error("保存master views配置文件失败", zap.Error(err))
		return fmt.Errorf("保存master views配置文件失败: %w", err)
	}

	slaveViewsData, err := s.getViewConfigData(ctx, "slave", masterServers, slaveServers, clusterForwardersMap)
	if err != nil {
		return fmt.Errorf("生成slave views配置失败: %w", err)
	}

	if err := s.renderAndSaveViewsConfigForRole(slaveViewsData, "slave"); err != nil {
		zap.L().Error("保存slave views配置文件失败", zap.Error(err))
		return fmt.Errorf("保存slave views配置文件失败: %w", err)
	}

	return nil
}

// getViewsConfigForRoleContent 获取指定角色views配置对象
func (s *configService) getViewsConfigForRoleContent(viewsData *api.NamedConfViewsData, role string) (*api.ConfigFile, error) {
	// 渲染模板
	tmplContent, err := template.New("views").Parse(tmpl.NamedConfViewsTemplate)
	if err != nil {
		return nil, fmt.Errorf("解析views模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, viewsData); err != nil {
		return nil, fmt.Errorf("渲染views模板失败: %w", err)
	}

	fileName := fmt.Sprintf("named.conf.views.%s", role)
	return &api.ConfigFile{
		Name:    fileName,
		Content: buf.Bytes(),
	}, nil
}

// generateViewsConfigContent 生成master和slave的views配置对象
func (s *configService) generateViewsConfigContent(ctx context.Context) ([]*api.ConfigFile, error) {
	zap.L().Debug("初始化views配置")
	span, _ := apm.StartSpan(ctx, "getViewsConfig", "service")
	defer span.End()

	var files []*api.ConfigFile

	// 获取集群和服务器信息
	masterServers, slaveServers, clusterForwardersMap, err := s.getServers(ctx)
	if err != nil {
		zap.L().Error("获取服务器信息失败", zap.Error(err))
		return nil, fmt.Errorf("获取服务器信息失败: %w", err)
	}

	// 获取master视图配置数据
	masterViewsData, err := s.getViewConfigData(ctx, "master", masterServers, slaveServers, clusterForwardersMap)
	if err != nil {
		zap.L().Error("获取视图配置数据失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图配置数据失败: %w", err)
	}

	masterConfig, err := s.getViewsConfigForRoleContent(masterViewsData, "master")
	if err != nil {
		zap.L().Error("获取视图配置数据失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图配置数据失败: %w", err)
	}

	files = append(files, masterConfig)

	// 获取slave视图配置数据
	slaveViewsData, err := s.getViewConfigData(ctx, "slave", masterServers, slaveServers, clusterForwardersMap)
	if err != nil {
		zap.L().Error("获取视图配置数据失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图配置数据失败: %w", err)
	}

	slaveConfig, err := s.getViewsConfigForRoleContent(slaveViewsData, "slave")
	if err != nil {
		zap.L().Error("获取视图配置数据失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图配置数据失败: %w", err)
	}

	files = append(files, slaveConfig)

	return files, nil
}

// generateViewConfigByCluster 为每个cluster生成独立的view配置
func (s *configService) generateViewConfigByCluster(ctx context.Context) (map[string][]*api.ConfigFile, error) {
	zap.L().Info("开始为每个cluster生成独立的view配置")
	span, ctx := apm.StartSpan(ctx, "generateViewConfigByCluster", "service")
	defer span.End()

	// 获取按cluster分组的服务器信息
	serversByCluster, err := s.getServersByCluster(ctx)
	if err != nil {
		zap.L().Error("获取服务器信息失败", zap.Error(err))
		return nil, fmt.Errorf("获取服务器信息失败: %w", err)
	}

	// 获取所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("获取视图列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图列表失败: %w", err)
	}

	// 获取所有zones，用于根据zone类型确定RPZ zones
	allZones, err := s.store.Zone().GetAllZones(ctx)
	if err != nil {
		zap.L().Error("获取zone列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取zone列表失败: %w", err)
	}

	// 过滤出RPZ和黑名单类型的zones
	rpzZones := make([]string, 0)
	for _, zone := range allZones {
		zoneType := model.ToZoneType(zone.ZoneType)
		if zoneType == model.ZoneTypeRPZ || zoneType == model.ZoneTypeBlackList {
			rpzZones = append(rpzZones, zone.Name)
		}
	}

	// 找到默认视图
	var defaultView *model.View
	for _, view := range views {
		if model.ToViewLevelType(view.LevelType) == model.ViewLevelTypeDefault {
			defaultView = view
			break
		}
	}

	// 结果map，key为cluster名称，value为该cluster的配置文件列表
	configsByCluster := make(map[string][]*api.ConfigFile)

	// 为每个cluster生成配置
	for _, clusterServers := range serversByCluster {
		zap.L().Info("为cluster生成配置",
			zap.String("cluster", clusterServers.ClusterName),
			zap.Int("masters", len(clusterServers.MasterServers)),
			zap.Int("slaves", len(clusterServers.SlaveServers)))

		// 为master和slave角色分别生成配置
		var clusterConfigs []*api.ConfigFile

		// 生成master配置
		if len(clusterServers.MasterServers) > 0 {
			masterConfig, err := s.generateViewConfigForClusterRole(
				clusterServers, "master", views, defaultView, rpzZones)
			if err != nil {
				zap.L().Error("生成master配置失败", zap.Error(err), zap.String("cluster", clusterServers.ClusterName))
				return nil, fmt.Errorf("生成cluster %s master配置失败: %w", clusterServers.ClusterName, err)
			}
			clusterConfigs = append(clusterConfigs, masterConfig)
		}

		// 生成slave配置
		if len(clusterServers.SlaveServers) > 0 {
			slaveConfig, err := s.generateViewConfigForClusterRole(
				clusterServers, "slave", views, defaultView, rpzZones)
			if err != nil {
				zap.L().Error("生成slave配置失败", zap.Error(err), zap.String("cluster", clusterServers.ClusterName))
				return nil, fmt.Errorf("生成cluster %s slave配置失败: %w", clusterServers.ClusterName, err)
			}
			clusterConfigs = append(clusterConfigs, slaveConfig)
		}

		configsByCluster[clusterServers.ClusterName] = clusterConfigs
	}

	return configsByCluster, nil
}

// generateViewConfigForClusterRole 为指定cluster和角色生成view配置
func (s *configService) generateViewConfigForClusterRole(
	clusterServers *ServersByCluster,
	role string,
	views []*model.View,
	defaultView *model.View,
	rpzZones []string) (*api.ConfigFile, error) {

	// 准备配置数据
	viewsData := &api.NamedConfViewsData{
		GeneratedAt:   time.Now().Format("2006-01-02 15:04:05"),
		ServerRole:    role,
		SlaveServers:  make([]*api.SlaveServer, 0),
		MasterServers: make([]*api.MasterServer, 0),
		Views:         make([]*api.ViewConfig, 0),
	}

	// 对于master角色，设置同cluster内的slave服务器列表（用于also-notify）
	if role == "master" {
		for _, server := range clusterServers.SlaveServers {
			viewsData.SlaveServers = append(viewsData.SlaveServers, &api.SlaveServer{
				IP:   server.IP,
				Port: 53, // DNS默认端口
			})
		}
	}

	// 对于slave角色，设置同cluster内的master服务器列表（用于match-clients排除）
	if role == "slave" {
		for _, server := range clusterServers.MasterServers {
			viewsData.MasterServers = append(viewsData.MasterServers, &api.MasterServer{
				IP:   server.IP,
				Port: 53, // DNS默认端口
			})
		}
	}

	// 处理office级别的view（排除default和biz）
	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)

		if viewType != model.ViewLevelTypeOffice {
			continue // 只处理office级别的view
		}

		// 生成ACL名称
		aclName := s.getACLNameForView(view)

		// 生成view key名称
		var keyName string
		if view.ViewKey != "" {
			keyName = fmt.Sprintf("office_%s_key", view.Code)
			zap.L().Debug("生成view key名称", zap.String("view", view.Name), zap.String("key_name", keyName))
		} else {
			zap.L().Warn("view没有配置key，跳过生成", zap.String("view", view.Name), zap.String("view_code", view.Code))
			continue // 跳过没有key的view
		}

		// 构建ResponsePolicy配置
		var responsePolicy *api.ResponsePolicy
		if len(rpzZones) > 0 {
			responsePolicy = &api.ResponsePolicy{
				Zones: rpzZones,
			}
		}

		viewConfig := &api.ViewConfig{
			ViewName:       view.Code, // 使用view.Code作为配置中的名称
			KeyName:        keyName,
			ACLName:        aclName,
			AllowQuery:     defaultAllowQuery,
			Recursion:      "yes",
			ResponsePolicy: responsePolicy,
			Forwarders:     clusterServers.Forwarders, // 使用该cluster的forwarders
		}

		viewsData.Views = append(viewsData.Views, viewConfig)
		zap.L().Info("处理view配置",
			zap.String("view", view.Name),
			zap.String("cluster", clusterServers.ClusterName))
	}

	// 处理默认view配置
	if defaultView != nil {
		var defaultResponsePolicy *api.ResponsePolicy
		if len(rpzZones) > 0 {
			defaultResponsePolicy = &api.ResponsePolicy{
				Zones: rpzZones,
			}
		}

		defaultACLName := s.getACLNameForView(defaultView)

		viewsData.DefaultView = &api.ViewConfig{
			ViewName:       "default",
			KeyName:        "default_key",
			ACLName:        defaultACLName,
			AllowQuery:     []string{"any"},
			Recursion:      "yes",
			ResponsePolicy: defaultResponsePolicy,
			Forwarders:     clusterServers.Forwarders, // 使用该cluster的forwarders
		}
	}

	// 渲染模板
	tmplContent, err := template.New("views").Parse(tmpl.NamedConfViewsTemplate)
	if err != nil {
		return nil, fmt.Errorf("解析views模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, viewsData); err != nil {
		return nil, fmt.Errorf("渲染views模板失败: %w", err)
	}

	// 文件名格式: named.conf.views.{role}
	fileName := fmt.Sprintf("named.conf.views.%s", role)
	return &api.ConfigFile{
		Name:    fileName,
		Content: buf.Bytes(),
	}, nil
}

// saveViewConfigsByCluster 保存按cluster分组的view配置文件
func (s *configService) saveViewConfigsByCluster(ctx context.Context) error {
	zap.L().Info("开始保存按cluster分组的view配置文件")
	span, ctx := apm.StartSpan(ctx, "saveViewConfigsByCluster", "service")
	defer span.End()

	// 生成按cluster分组的配置
	configsByCluster, err := s.generateViewConfigByCluster(ctx)
	if err != nil {
		zap.L().Error("生成cluster配置失败", zap.Error(err))
		return fmt.Errorf("生成cluster配置失败: %w", err)
	}

	// 为每个cluster保存配置文件
	for clusterName, configs := range configsByCluster {
		// 创建cluster目录: view/{cluster_name}/
		clusterDir := filepath.Join(DefaultConfigDir, "view", clusterName)
		if err := os.MkdirAll(clusterDir, 0755); err != nil {
			zap.L().Error("创建cluster目录失败", zap.Error(err),
				zap.String("cluster", clusterName),
				zap.String("dir", clusterDir))
			return fmt.Errorf("创建cluster目录失败: %w", err)
		}

		// 保存该cluster的所有配置文件
		for _, config := range configs {
			filePath := filepath.Join(clusterDir, config.Name)
			if err := os.WriteFile(filePath, config.Content, 0644); err != nil {
				zap.L().Error("保存cluster配置文件失败", zap.Error(err),
					zap.String("cluster", clusterName),
					zap.String("file", config.Name),
					zap.String("path", filePath))
				return fmt.Errorf("保存cluster配置文件失败: %w", err)
			}

			zap.L().Info("成功保存cluster配置文件",
				zap.String("cluster", clusterName),
				zap.String("file", config.Name),
				zap.String("path", filePath),
				zap.Int("size", len(config.Content)))
		}
	}

	zap.L().Info("按cluster分组的view配置文件保存完成",
		zap.Int("clusters", len(configsByCluster)))

	return nil
}

// GenerateViewsConfigFileByCluster 生成按cluster分组的view配置文件
func (s *configService) GenerateViewsConfigFileByCluster(ctx context.Context) error {
	zap.L().Info("开始生成按cluster分组的named.conf.views配置文件")
	span, ctx := apm.StartSpan(ctx, "GenerateViewsConfigFileByCluster", "service")
	defer span.End()

	return s.saveViewConfigsByCluster(ctx)
}
