package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"

	"github.com/stretchr/testify/assert"
)

const (
	testRecordName = "mydengdeng.top" // 要劫持的域名（就是这个域名本身）
)

var (
	allView       map[int64]*model.View
	defaultView   *model.View
	bizView       map[string]*model.View
	officeView    map[string]*model.View
	zoneMap       map[string]*model.Zone
	rpzZone       *model.Zone
	serverViewMap map[model.ViewLevelType][]*model.Server
	viewServerMap map[int64][]*model.Server
	serverMap     map[string]*model.Server
)

// ExpectedNSUpdateOperation 期望的nsupdate操作
type ExpectedNSUpdateOperation struct {
	Action   string // create, update, delete
	Name     string
	Type     string
	OldValue []string // 支持多个值
	NewValue []string // 支持多个值
	TTL      int
}

// 初始化所有的view信息
func initView(t *testing.T, ctx context.Context) {
	store := testStoreFactory

	views, err := store.View().GetAllView(ctx)
	if err != nil {
		t.Fatalf("获取视图失败: %v", err)
	}

	allView = make(map[int64]*model.View)
	bizView = make(map[string]*model.View)
	officeView = make(map[string]*model.View)

	for _, view := range views {
		if view.LevelType == string(model.ViewLevelTypeDefault) {
			defaultView = view
		} else if view.LevelType == string(model.ViewLevelTypeBiz) {
			bizView[view.Code] = view
		} else if view.LevelType == string(model.ViewLevelTypeOffice) {
			officeView[view.Code] = view
		}

		allView[view.ID] = view
	}
}

func initZone(t *testing.T, ctx context.Context) {
	store := testStoreFactory

	zones, err := store.Zone().GetAllZones(ctx)
	if err != nil {
		t.Fatalf("获取zone失败: %v", err)
	}

	zoneMap = make(map[string]*model.Zone)

	for _, zone := range zones {
		zoneMap[zone.Name] = zone

		if zone.Name == "rpz" {
			rpzZone = zone
		}
	}
}

func initServer(t *testing.T, ctx context.Context) {
	store := testStoreFactory

	servers, err := store.Server().GetMasterServers(ctx)
	if err != nil {
		t.Fatalf("获取服务器失败: %v", err)
	}

	serverViewMap = make(map[model.ViewLevelType][]*model.Server)
	viewServerMap = make(map[int64][]*model.Server)
	serverMap = make(map[string]*model.Server)

	// 构建一个map，key是view的类型，value是view的id
	for _, server := range servers {
		if server.ViewID == 0 {
			t.Logf("server %s 没有view", server.IP)
			continue
		}

		serverView := allView[server.ViewID]
		viewType := model.ViewLevelType(serverView.LevelType)

		if _, ok := serverViewMap[viewType]; !ok {
			serverViewMap[viewType] = make([]*model.Server, 0)
		}

		serverViewMap[viewType] = append(serverViewMap[viewType], server)
		serverMap[server.IP] = server
		if _, ok := viewServerMap[server.ViewID]; !ok {
			viewServerMap[server.ViewID] = make([]*model.Server, 0)
		}
		viewServerMap[server.ViewID] = append(viewServerMap[server.ViewID], server)
	}
}

func getTasks(ctx context.Context, zone *model.Zone, view *model.View, changes *model.RecordChanges) (*model.Task, []*model.TaskDetails, error) {
	recordSvc := testRecordService

	task, details, err := recordSvc.generateTasks(ctx, zone, changes)
	if err != nil {
		return nil, nil, err
	}

	return task, details, nil
}

func TestInheritanceScenarios(t *testing.T) {
	// 初始化上下文
	ctx := context.Background()
	// 使用task_test中的setupTestDependencies
	setupTestDependencies(t)
	// 初始化视图
	initView(t, ctx)
	// 初始化zone
	initZone(t, ctx)
	// 初始化server
	initServer(t, ctx)

	// 清理记录
	defer func() {
		if err := cleanupRecords(ctx); err != nil {
			t.Logf("清理记录失败: %v", err)
		}
	}()

	// 场景1: default有记录，biz没有，office没有 -> office继承default
	t.Run("default有_biz无_office无_继承default", func(t *testing.T) {
		// 在default创建记录
		ret, err := createRecord(t, ctx, defaultView.ID, rpzZone.ID, "1.1.1.1", false)
		if err != nil {
			t.Fatalf("创建记录失败: %v", err)
		}

		// 查看所有的impact, 是否符合预期，当前是在default添加记录，那么此时office应该可以继承default的记录
		impactViewMap := make(map[int64]bool)
		for _, impact := range ret.Changes.Impacts {
			// 使用断言验证impact不为空
			assert.Equal(t, []string{}, impact.OldValue)
			assert.Equal(t, []string{"1.1.1.1"}, impact.NewValue)
			impactViewMap[impact.ViewID] = true
		}

		// 验证生成tasks的数量
		_, details, err := getTasks(ctx, rpzZone, defaultView, ret.Changes)
		if err != nil {
			t.Fatalf("生成tasks失败: %v", err)
		}

		// 验证TaskDetails的基本信息
		assert.NotEmpty(t, details, "TaskDetails不应为空")
		t.Logf("生成了 %d 个TaskDetails", len(details))

		expectedOperation := ExpectedNSUpdateOperation{
			Action:   "create",
			Name:     testRecordName,
			Type:     "A",
			OldValue: []string{},
			NewValue: []string{"1.1.1.1"},
			TTL:      3600,
		}

		// 验证每个TaskDetail都是正确的nsupdate操作
		// 所有受影响的office服务器都应该执行相同的create操作
		verifySimpleOperation(t, details, expectedOperation)

		// 验证TaskDetails数量与受影响的视图数量的关系
		// 在default视图创建记录时，应该影响所有office视图
		expectedOfficeViews := len(officeView)
		if expectedOfficeViews > 0 {
			expectedTaskDetails := 0
			for _, v := range officeView {
				servers, ok := serverViewMap[model.ViewLevelTypeOffice]
				if !ok {
					continue
				}

				if len(servers) == 0 {
					continue
				}

				for _, server := range servers {
					if allView[server.ViewID].Code == v.Code {
						expectedTaskDetails++
					}
				}
			}

			if expectedTaskDetails > 0 {
				assert.Equal(t, expectedTaskDetails, len(details), "TaskDetails数量应该等于受影响的office服务器数量")
			}
		}
	})

	// 场景2: default有记录，biz有记录，office没有 -> office继承biz
	t.Run("default有_biz有_office无_继承biz", func(t *testing.T) {
		// 在biz下创建记录，office此时应该由default继承改为从biz继承
		devView, ok := bizView["dev"]
		if !ok {
			t.Fatalf("biz视图不存在")
		}

		// 在biz下创建记录, 解析值为2.2.2.2，此时office层级的1.1.1.1应该被覆盖
		ret, err := createRecord(t, ctx, devView.ID, rpzZone.ID, "2.2.2.2", false)
		if err != nil {
			t.Fatalf("创建记录失败: %v", err)
		}

		for _, impact := range ret.Changes.Impacts {
			// 使用断言验证impact不为空
			assert.Equal(t, []string{"1.1.1.1"}, impact.OldValue)
			assert.Equal(t, []string{"2.2.2.2"}, impact.NewValue)
		}

		_, details, err := getTasks(ctx, rpzZone, devView, ret.Changes)
		if err != nil {
			t.Fatalf("生成tasks失败: %v", err)
		}

		// 在这个场景中，每个office服务器应该有两个操作：
		// 1. 删除从default继承的记录 (delete 1.1.1.1)
		// 2. 添加从biz继承的记录 (create 2.2.2.2)
		expectedOperations := []ExpectedNSUpdateOperation{
			{
				Action:   "delete",
				Name:     testRecordName,
				Type:     "A",
				OldValue: []string{"1.1.1.1"},
				NewValue: []string{},
				TTL:      3600,
			},
			{
				Action:   "create",
				Name:     testRecordName,
				Type:     "A",
				OldValue: []string{},
				NewValue: []string{"2.2.2.2"},
				TTL:      3600,
			},
		}

		verifyInheritanceOperation(t, details, expectedOperations)
	})

	// 场景3: default有记录，biz没有，office有记录 -> office不继承
	t.Run("default有_biz有_office有，office优先级最高", func(t *testing.T) {
		y, ok := officeView["y"]
		if !ok {
			t.Fatalf("元中心视图不存在")
		}

		result, err := createRecord(t, ctx, y.ID, rpzZone.ID, "3.3.3.3", false)
		if err != nil {
			t.Fatalf("创建记录失败: %v", err)
		}

		for _, impact := range result.Changes.Impacts {
			assert.Equal(t, []string{"2.2.2.2"}, impact.OldValue)
			assert.Equal(t, []string{"3.3.3.3"}, impact.NewValue)
		}

		_, details, err := getTasks(ctx, rpzZone, y, result.Changes)
		if err != nil {
			t.Fatalf("生成tasks失败: %v", err)
		}

		expectedOperations := []ExpectedNSUpdateOperation{
			{
				Action:   "delete",
				Name:     testRecordName,
				Type:     "A",
				OldValue: []string{"2.2.2.2"},
				NewValue: []string{},
				TTL:      3600,
			},
			{
				Action:   "create",
				Name:     testRecordName,
				Type:     "A",
				OldValue: []string{},
				NewValue: []string{"3.3.3.3"},
				TTL:      3600,
			},
		}

		verifyInheritanceOperation(t, details, expectedOperations)
	})

	// 场景4：删除biz记录，测试继承变更
	// 当前状态：default有1.1.1.1，biz有2.2.2.2，office有3.3.3.3
	// 删除biz记录后：没有自己记录的office视图会从继承biz改为继承default
	t.Run("删除biz记录后继承变更", func(t *testing.T) {
		devView, ok := bizView["dev"]
		if !ok {
			t.Fatalf("dev视图不存在")
		}

		// 先把要删除的这一条记录先查到
		records, err := testStoreFactory.Record().GetRecordList(ctx, rpzZone.ID, devView.ID, testRecordName, "A", "2.2.2.2")
		if err != nil {
			t.Fatalf("查询记录失败: %v", err)
		}

		if len(records) == 0 {
			t.Fatalf("记录不存在")
		}

		t.Logf("在dev视图中找到 %d 条记录", len(records))
		for i, record := range records {
			t.Logf("记录 %d: ID=%d, Value=%s", i, record.ID, record.Value)
		}

		record := records[0]

		result, err := deleteRecord(t, ctx, devView.ID, rpzZone.ID, record.ID, false)
		if err != nil {
			t.Fatalf("删除记录失败: %v", err)
		}

		for _, impact := range result.Changes.Impacts {
			impactView := allView[impact.ViewID]
			parentView := impactView.ParentID
			if parentView == 0 {
				t.Fatalf("impactView的父视图不存在")
			}
			impactParentView := allView[parentView]
			if impactView.Code == "y" {
				// y是office视图，删除biz并不会影响office视图下的变更
				assert.Equal(t, []string{"3.3.3.3"}, impact.OldValue)
				assert.Equal(t, []string{"3.3.3.3"}, impact.NewValue)
			} else if impactParentView.Code == "dev" {
				// dev是biz视图，删除biz的2.2.2.2，其他没有配置的dev下的office视图会从default继承1.1.1.1
				assert.Equal(t, []string{"2.2.2.2"}, impact.OldValue)
				assert.Equal(t, []string{"1.1.1.1"}, impact.NewValue)
			} else {
				t.Fatalf("impactView的父视图不是dev或y")
			}
		}

		_, details, err := getTasks(ctx, rpzZone, devView, result.Changes)
		if err != nil {
			t.Fatalf("生成tasks失败: %v", err)
		}

		// 根据不同的影响类型，构建期望的操作
		// 1. 对于有自己记录的office视图（如y），不会有任何操作
		// 2. 对于没有自己记录的office视图，会从继承biz改为继承default
		expectedOperationsByServer := make(map[string][]ExpectedNSUpdateOperation)

		// 再次遍历impact，构建期望的操作
		for _, impact := range result.Changes.Impacts {
			viewServers, ok := viewServerMap[impact.ViewID]
			if !ok {
				continue
			}

			if len(viewServers) == 0 {
				continue
			}

			// 根据impact的变化类型，构建期望操作
			for _, server := range viewServers {
				if len(impact.OldValue) > 0 && len(impact.NewValue) > 0 && impact.OldValue[0] != impact.NewValue[0] {
					// 值发生变化：删除旧值，创建新值
					expectedOperationsByServer[server.IP] = append(expectedOperationsByServer[server.IP],
						ExpectedNSUpdateOperation{
							Action:   "delete",
							Name:     testRecordName,
							Type:     "A",
							OldValue: impact.OldValue,
							NewValue: []string{},
							TTL:      3600,
						},
						ExpectedNSUpdateOperation{
							Action:   "create",
							Name:     testRecordName,
							Type:     "A",
							OldValue: []string{},
							NewValue: impact.NewValue,
							TTL:      3600,
						},
					)
				} else if len(impact.OldValue) == 0 && len(impact.NewValue) > 0 {
					// 新增值：创建操作
					expectedOperationsByServer[server.IP] = append(expectedOperationsByServer[server.IP],
						ExpectedNSUpdateOperation{
							Action:   "create",
							Name:     testRecordName,
							Type:     "A",
							OldValue: []string{},
							NewValue: impact.NewValue,
							TTL:      3600,
						},
					)
				} else if len(impact.OldValue) > 0 && len(impact.NewValue) == 0 {
					// 删除值：删除操作
					expectedOperationsByServer[server.IP] = append(expectedOperationsByServer[server.IP],
						ExpectedNSUpdateOperation{
							Action:   "delete",
							Name:     testRecordName,
							Type:     "A",
							OldValue: impact.OldValue,
							NewValue: []string{},
							TTL:      3600,
						},
					)
				}
				// 如果 OldValue 和 NewValue 相同，则不需要任何操作
			}
		}

		// 验证每个服务器的操作
		verifyInheritanceOperation(t, details, expectedOperationsByServer)
	})

	// // 场景4: default有记录，biz有记录，office有记录 -> office不继承
	// t.Run("default有_biz有_office有_不继承", func(t *testing.T) {
	// 	cleanupRecords(t, svc, ctx, testData)

	// 	createRecord(t, ctx, defaultView.ID, rpzZone.ID, "***********")
	// 	createRecord(t, ctx, bizView["biz"].ID, rpzZone.ID, "***********")
	// 	createRecord(t, ctx, officeView["office"].ID, rpzZone.ID, "***********")

	// 	verifyNoInheritance(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, "***********")
	// 	verifyNoInheritance(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, "***********")

	// 	// 清理本次测试创建的记录
	// 	cleanupRecords(t, svc, ctx, testData)
	// })

	// // 场景5: default没有，biz有记录，office没有 -> office继承biz
	// t.Run("default无_biz有_office无_继承biz", func(t *testing.T) {
	// 	cleanupRecords(t, svc, ctx, testData)

	// 	createRecord(t, ctx, bizView["biz"].ID, rpzZone.ID, "***********")

	// 	verifyInheritance(t, ctx, officeView["office"].ID, rpzZone.ID, []string{"***********"})

	// 	// 清理本次测试创建的记录
	// 	cleanupRecords(t, svc, ctx, testData)
	// })

	// // 场景6: default没有，biz有记录，office有记录 -> office不继承
	// t.Run("default无_biz有_office有_不继承", func(t *testing.T) {
	// 	cleanupRecords(t, svc, ctx, testData)

	// 	createRecord(t, svc, ctx, testData.BizView.ID, testData.Zone.ID, "***********")
	// 	createRecord(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, "***********")

	// 	verifyNoInheritance(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, "***********")

	// 	// 清理本次测试创建的记录
	// 	cleanupRecords(t, svc, ctx, testData)
	// })

	// // 场景7: 删除office记录后继承上级
	// t.Run("删除office后继承上级", func(t *testing.T) {
	// 	cleanupRecords(t, svc, ctx, testData)

	// 	// 创建biz记录
	// 	createRecord(t, svc, ctx, testData.BizView.ID, testData.Zone.ID, "***********")
	// 	// 创建office记录
	// 	officeRecord := createRecord(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, "***********")

	// 	// 删除office记录
	// 	deleteRecord(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, officeRecord.ID)

	// 	// 验证继承biz记录
	// 	verifyInheritance(t, svc, ctx, testData.OfficeView.ID, testData.Zone.ID, []string{"***********"})

	// 	// 清理本次测试创建的记录
	// 	cleanupRecords(t, svc, ctx, testData)
	// })
}

// verifyNSUpdateOperation 验证nsupdate操作的具体内容
func verifyNSUpdateOperation(t *testing.T, detail *model.TaskDetails,
	expectedAction, expectedName, expectedType string, expectedOldValue, expectedNewValue []string) {
	// 解析ReqMsg字段
	var recordInfo model.RecordInfo
	err := json.Unmarshal(detail.ReqMsg, &recordInfo)
	assert.NoError(t, err, "ReqMsg应该能够正确解析为RecordInfo")

	// 验证nsupdate操作的具体内容
	assert.Equal(t, expectedAction, recordInfo.Action, "Action应匹配")
	assert.Equal(t, expectedName, recordInfo.Name, "Name应匹配")
	assert.Equal(t, expectedType, recordInfo.Type, "Type应匹配")

	// 计算oldValue和newValue
	oldValue := []string{}
	if recordInfo.OldValue != "" {
		oldValue = []string{recordInfo.OldValue}
	}

	newValue := []string{}
	if recordInfo.NewValue != "" {
		newValue = []string{recordInfo.NewValue}
	}

	assert.Equal(t, expectedOldValue, oldValue, "OldValue应匹配")
	assert.Equal(t, expectedNewValue, newValue, "NewValue应匹配")

	// 验证TaskDetail的基本信息
	assert.Equal(t, expectedAction, detail.OpType, "OpType应匹配Action")
}

// verifySimpleOperation 验证简单的单一操作场景
func verifySimpleOperation(t *testing.T, details []*model.TaskDetails, expectedOp ExpectedNSUpdateOperation) {
	// 验证所有TaskDetails都是相同的操作
	for _, detail := range details {
		verifyNSUpdateOperation(t, detail, expectedOp.Action, expectedOp.Name, expectedOp.Type, expectedOp.OldValue, expectedOp.NewValue)
	}
}

// verifyInheritanceOperation 验证继承操作，支持两种模式：
// 1. 统一操作模式：所有服务器执行相同的操作列表
// 2. 混合操作模式：不同服务器执行不同的操作
func verifyInheritanceOperation(t *testing.T, details []*model.TaskDetails, expectedOps any) {
	// 按服务器IP分组TaskDetails
	serverDetails := make(map[string][]*model.TaskDetails)
	for _, detail := range details {
		serverDetails[detail.ServerIP] = append(serverDetails[detail.ServerIP], detail)
	}

	var expectedOperationsByServer map[string][]ExpectedNSUpdateOperation

	// 根据参数类型构建期望操作映射
	switch ops := expectedOps.(type) {
	case []ExpectedNSUpdateOperation:
		// 统一操作模式：所有服务器执行相同的操作
		expectedOperationsByServer = make(map[string][]ExpectedNSUpdateOperation)
		for serverIP := range serverDetails {
			t.Logf("[]ExpectedNSUpdateOperation:")
			t.Logf("serverIP: %s", serverIP)
			t.Logf("ops: %v", ops)
			expectedOperationsByServer[serverIP] = ops
		}
	case map[string][]ExpectedNSUpdateOperation:
		// 混合操作模式：不同服务器执行不同的操作
		t.Logf("map[string][]ExpectedNSUpdateOperation:")
		t.Logf("ops: %v", ops)
		for _, dt := range details {
			recordInfo := model.RecordInfo{}
			err := json.Unmarshal(dt.ReqMsg, &recordInfo)
			assert.NoError(t, err, "ReqMsg应该能够正确解析为RecordInfo")
			fmt.Println("recordInfo: ", recordInfo.Action, recordInfo.Name, recordInfo.Type, recordInfo.OldValue, recordInfo.NewValue, dt.ServerIP)
		}
		expectedOperationsByServer = ops
	default:
		t.Fatalf("不支持的期望操作类型: %T", expectedOps)
	}

	// 验证总操作数量
	expectedTotalOps := 0
	for _, op := range expectedOperationsByServer {
		expectedTotalOps += len(op)
	}
	assert.Equal(t, expectedTotalOps, len(details), "总操作数应该匹配所有期望操作数量")

	// 验证每个服务器的操作
	for serverIP, expectedServerOps := range expectedOperationsByServer {
		actualDetails, found := serverDetails[serverIP]
		assert.True(t, found, "应该找到服务器 %s 的TaskDetails", serverIP)

		server, ok := serverMap[serverIP]
		if !ok {
			t.Fatalf("服务器 %s 不存在", serverIP)
		}

		t.Logf("验证服务器 %s 的 %d 个操作", server.Name, len(expectedServerOps))

		// 验证操作数量
		assert.Equal(t, len(expectedServerOps), len(actualDetails), "服务器 %s 的操作数量应该匹配期望", server.Name)

		// 将实际操作按操作类型分组，便于匹配
		actualOpsByType := make(map[string]*model.TaskDetails)
		for _, detail := range actualDetails {
			var recordInfo model.RecordInfo
			err := json.Unmarshal(detail.ReqMsg, &recordInfo)
			assert.NoError(t, err, "ReqMsg应该能够正确解析为RecordInfo")

			// 使用 action + name + type + values 作为唯一标识
			key := recordInfo.Action + ":" + recordInfo.Name + ":" + recordInfo.Type + ":" + recordInfo.OldValue + "->" + recordInfo.NewValue
			actualOpsByType[key] = detail
		}

		// 验证每个期望的操作都能找到对应的实际操作
		for _, expectedOp := range expectedServerOps {
			key := expectedOp.Action + ":" + expectedOp.Name + ":" + expectedOp.Type + ":" +
				strings.Join(expectedOp.OldValue, ",") + "->" + strings.Join(expectedOp.NewValue, ",")
			actualDetail, found := actualOpsByType[key]
			assert.True(t, found, "服务器 %s 应该有操作: %s", server.Name, key)

			if found {
				verifyNSUpdateOperation(t, actualDetail, expectedOp.Action, expectedOp.Name, expectedOp.Type, expectedOp.OldValue, expectedOp.NewValue)
				t.Logf("服务器 %s 操作验证成功: %s", server.Name, key)
			}
		}
	}
}

// createRecord 使用UpdateRecord创建记录
func createRecord(t *testing.T, ctx context.Context, viewID, zoneID int64, ip string, dryRun bool) (*UpdateResult, error) {
	recordSvc := testRecordService

	req := &api.UpdateDnsRecordRequest{
		Name:        testRecordName,
		ViewID:      viewID,
		ZoneID:      zoneID,
		Description: "test record",
		DryRun:      &dryRun,
		Records: []api.UpdateDnsRecord{
			{
				RecordID: 0, // 新创建记录ID为0
				RType:    "A",
				Value:    ip,
				TTL:      3600,
				Creator:  "test",
				Owner:    "test",
			},
		},
	}

	result, err := recordSvc.UpdateRecord(ctx, req)
	if err != nil {
		t.Fatalf("创建记录失败: %v", err)
	}

	return result, nil
}

// 使用UpdateRecord删除记录
func deleteRecord(t *testing.T, ctx context.Context, viewID, zoneID, recordID int64, dryRun bool) (*UpdateResult, error) {
	recordSvc := testRecordService

	req := &api.UpdateDnsRecordRequest{
		Name:            testRecordName,
		ViewID:          viewID,
		ZoneID:          zoneID,
		Description:     "delete test record",
		DryRun:          &dryRun,
		DeleteRecordIds: []int64{recordID},
	}

	result, err := recordSvc.UpdateRecord(ctx, req)
	if err != nil {
		t.Fatalf("删除记录失败: %v", err)
	}

	return result, nil
}

// 使用UpdateRecord更新记录
func updateRecord(t *testing.T, ctx context.Context, viewID, zoneID, recordID int64, newIP string, dryRun bool) (*UpdateResult, error) {
	recordSvc := testRecordService

	req := &api.UpdateDnsRecordRequest{
		Name:        testRecordName,
		ViewID:      viewID,
		ZoneID:      zoneID,
		Description: "update test record",
		DryRun:      &dryRun,
		Records: []api.UpdateDnsRecord{
			{
				RecordID: recordID,
				RType:    "A",
				Value:    newIP,
				TTL:      3600,
				Creator:  "test",
				Owner:    "test",
			},
		},
	}

	result, err := recordSvc.UpdateRecord(ctx, req)
	if err != nil {
		t.Fatalf("更新记录失败: %v", err)
	}

	return result, nil
}

// 清理记录
func cleanupRecords(ctx context.Context) error {
	store := testStoreFactory
	return store.Record().DeleteRecordByName(ctx, rpzZone.ID, testRecordName)
}
