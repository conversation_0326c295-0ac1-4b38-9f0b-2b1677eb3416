// Package v1 提供DNS服务器的业务逻辑实现
// 本文件包含记录任务相关的功能实现
// 主要用于生成DNS记录更新任务
package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"go.uber.org/zap"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
	pkgjson "ks-knoc-server/pkg/json"
)

// generateTasks 生成DNS记录更新任务
//
// 功能说明:
// 该函数负责将DNS记录变更转换为具体的nsupdate任务，支持跨层级view的记录覆盖功能。
// 主要处理两类变更：
// 1. 继承链子级变更(changes.Changes): 在当前view中的增删改操作，影响子级view的变更情况，如修改biz view会影响到哪些office view
// 2. 父级删除(changes.CrossLevelDeletes): 当子集新增数据时，会覆盖父级的配置，此时需要删除父级的配置。
//
// 调用逻辑:
// UpdateRecord -> getUpdatedRecords -> handleUpdateRecordTasks -> generateTasks
//
// 参数:
//   - ctx: 上下文
//   - zone: 目标DNS区域
//   - view: 当前操作的视图(通常是office view)
//   - changes: 记录变更信息，包含普通变更和跨层级删除
//
// 返回值:
//   - *model.Task: 主任务记录，包含任务整体信息和用户的直接操作
//   - []*model.TaskDetails: 任务详情列表，每个详情对应一个DNS服务器上的一个操作
//   - error: 错误信息
//
// 核心设计:
// - targetInfo.Records: 仅包含用户的直接操作意图，不包含系统计算的继承配置变更
// - 继承链子级变更: 使用view的impacts确定目标服务器，实现view层级的影响传播
// - 父级删除: 使用记录的实际ViewID确定目标服务器，实现精确的记录删除
// - 任务分发: 每个DNS服务器的每个操作都生成独立的TaskDetail，支持并行执行和独立状态跟踪
//
// 服务器-视图过滤机制:
// DNS服务器只配置两种视图：default视图（兜底）+ 对应的office视图（真正生效）
// biz视图不会落到服务器上，只用于配置继承，server.ViewID表示服务器配置的office视图ID
func (rs *recordService) generateTasks(ctx context.Context, zone *model.Zone, changes *model.RecordChanges) (*model.Task, []*model.TaskDetails, error) {
	zap.L().Debug("生成nsupdate任务")

	// 初始化task和task_details
	var (
		task        *model.Task
		taskDetails = make([]*model.TaskDetails, 0)
	)

	// 第一步：检查是否有变更需要处理
	// 只有当存在普通变更或跨层级删除时才需要生成任务
	if len(changes.TaskChanges) == 0 {
		zap.L().Info("无变更，无需下发任务")
		return nil, nil, nil
	}

	// 第二步：获取所有视图信息，用于后续的ViewID到ViewName的映射
	views, err := rs.store.View().GetAllView(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("获取views失败: %w", err)
	}

	// 构建viewMap用于快速定位view对象，key为view的id
	// 这个映射用于将变更记录的ViewID转换为nsupdate命令中的view名称
	viewMap := make(map[int64]*model.View)
	for _, view := range views {
		viewMap[view.ID] = view
	}

	// 第三步：收集所有涉及的视图ID，用于统计和日志
	involvedViewIDs := make(map[int64]bool)
	for _, change := range changes.TaskChanges {
		involvedViewIDs[change.ViewID] = true
	}

	if len(involvedViewIDs) == 0 {
		zap.L().Info("没有涉及的视图变更，无需下发任务")
		return nil, nil, nil
	}

	// 第四步：获取所有master服务器
	// 所有master服务器都配置了所有视图，因此所有服务器都需要处理所有变更
	allMasterServers, err := rs.store.Server().GetMasterServers(ctx)
	if err != nil {
		zap.L().Error("获取DNS服务器失败", zap.Error(err))
		return nil, nil, fmt.Errorf("获取DNS服务器失败: %w", err)
	}

	// 过滤出有效的master节点
	targetServers := make([]*model.Server, 0)
	for _, server := range allMasterServers {
		if server.Role == model.ServerRoleMaster && server.IP != "" {
			targetServers = append(targetServers, server)
		}
	}

	if len(targetServers) == 0 {
		zap.L().Info("没有可用的DNS master服务器, 无需下发任务")
		return nil, nil, nil
	}

	// 最终和server有关的view是哪些，计算这个内容的原因是
	// 当前db配置文件会根据viewID下有没有server关联过来，如果没有的话
	// 默认这个职场其实是没有配置server的，这个时候其实不会生成db文件；
	serverRelatedView := make(map[int64]bool)
	for _, server := range targetServers {
		serverRelatedView[server.ViewID] = true
	}

	// 此时involvedViewIDs中存储的就是最终可以下发任务的viewID
	for viewID := range involvedViewIDs {
		if _, ok := serverRelatedView[viewID]; !ok {
			delete(involvedViewIDs, viewID)
		}
	}

	// 第五步：初始化任务目标信息
	// targetInfo 只包含用户的直接操作意图，不包含系统计算的继承配置变更
	targetInfo := &model.TaskTargetInfo{
		ZoneName: zone.Name,
		Records:  make([]model.RecordInfo, 0),
		Servers:  targetServers,
	}

	// 第六步：构建记录信息列表 - 仅包含用户的直接操作意图
	// 直接使用在getUpdatedRecords开始时缓存的用户真实意图
	// 这样避免了复杂的计算和过滤，准确反映用户想要执行的操作
	for _, change := range changes.Changes {
		targetInfo.Records = append(targetInfo.Records, model.RecordInfo{
			Action:     change.Action,   // create/update/delete
			Name:       change.Name,     // 记录名称
			TTL:        change.TTL,      // 生存时间
			Type:       change.Type,     // 记录类型(A/CNAME等)
			OldValue:   change.OldValue, // 原值(用于update/delete)
			NewValue:   change.NewValue, // 新值(用于create/update)
			MXPriority: 0,               // 暂时不支持mx记录类型的操作，因此默认设置为0
			Enabled:    true,
		})
	}

	zap.L().Debug("共计需要更新DNS记录",
		zap.Int("user_intent_changes", len(changes.Changes)),      // 用户的真实意图变更数量
		zap.Int("total_system_changes", len(changes.TaskChanges)), // 包含系统计算的总变更数量
		zap.Int("target_records", len(targetInfo.Records)),        // targetInfo中的记录数量
		zap.Int("target_servers", len(targetServers)),             // 目标服务器数量
		zap.Int("involved_views", len(involvedViewIDs)))           // 涉及的视图数量

	// 初始化时间戳，用于任务创建和更新时间
	now := time.Now().Unix() // 使用秒级时间戳

	// 第七步：生成具体的任务详情
	// 为每个变更生成对应服务器的任务，这里生成的任务已经按照view做了区分；
	// 重要：只为配置了对应view的服务器生成任务，避免配置文件不匹配导致的refused错误
	var (
		totalPossibleTasks = 0 // 理论上可能生成的任务总数（不考虑过滤）
		filteredOutTasks   = 0 // 被过滤掉的任务数
	)

	for _, change := range changes.TaskChanges {
		// 当前要变更的这个change的viewID没有配置对应的server，跳过，否则会引发refused错误
		if _, ok := involvedViewIDs[change.ViewID]; !ok {
			zap.L().Debug("跳过未配置对应server的服务器",
				zap.Int64("change_view_id", change.ViewID))
			continue
		}

		// 获取changeView变更对象
		changeView, exists := viewMap[change.ViewID]
		if !exists {
			zap.L().Warn("变更记录对应的视图不存在",
				zap.Int64("change_view_id", change.ViewID))
			continue
		}

		// 为合适的服务器生成该变更的任务
		for _, server := range targetServers {
			recordInfo := model.RecordInfo{
				Action:     change.Action,
				Name:       change.Name,
				TTL:        change.TTL,
				Type:       change.Type,
				OldValue:   change.OldValue,
				NewValue:   change.NewValue,
				MXPriority: 0, // 暂时不支持mx记录类型的操作，因此默认设置为0
				Enabled:    true,
			}

			recordInfoJSON, err := json.Marshal(recordInfo)
			if err != nil {
				zap.L().Error("序列化recordInfo失败", zap.Error(err))
				return nil, nil, fmt.Errorf("序列化recordInfo失败: %w", err)
			}

			td := &model.TaskDetails{
				OpType:     change.Action,
				Status:     model.TaskDetailStatusInit,
				ServerName: server.Name,
				ServerIP:   server.IP,
				ZoneName:   zone.Name,
				ViewName:   changeView.Code, // 使用变更记录对应的视图名称
				ReqMsg:     pkgjson.JSON(recordInfoJSON),
				ResMsg:     "",
				ReqTs:      0, // 不在创建时设置req_ts，而是在dnsagent执行时设置
				ResTs:      0,
				CreateAt:   now,
				UpdateAt:   now,
			}

			zap.L().Debug("生成任务详情",
				zap.String("server_ip", server.IP),
				zap.String("server_name", server.Name),
				zap.Int64("server_view_id", server.ViewID),      // 服务器配置的视图ID
				zap.String("change_view_name", changeView.Code), // 变更的视图名称
				zap.String("action", change.Action),
				zap.String("name", change.Name),
				zap.String("type", change.Type))

			taskDetails = append(taskDetails, td)
		}
	}

	// 第八步：对所有的taskDetails进行排序，将删除的放在前面，先删后加
	sort.Slice(taskDetails, func(i, j int) bool {
		// 定义操作优先级：delete > update > create
		priority := map[string]int{
			"delete": 1,
			"update": 2,
			"create": 3,
		}

		// 获取操作类型的优先级
		priorityI := priority[taskDetails[i].OpType]
		priorityJ := priority[taskDetails[j].OpType]

		// 如果优先级不同，按优先级排序（数字小的优先）
		if priorityI != priorityJ {
			return priorityI < priorityJ
		}

		// 如果优先级相同，按服务器IP排序，保证执行顺序的一致性
		return taskDetails[i].ServerIP < taskDetails[j].ServerIP
	})

	// 第九步：创建主任务记录
	// 序列化targetInfo，包含所有目标信息
	targetInfoJSON, err := json.Marshal(targetInfo)
	if err != nil {
		zap.L().Error("序列化targetInfo失败", zap.Error(err))
		return nil, nil, fmt.Errorf("序列化targetInfo失败: %w", err)
	}

	// 创建主任务记录，包含任务的整体状态和统计信息
	task = &model.Task{
		Status:       model.TaskStatusCreated,      // 任务状态：已创建
		TargetInfo:   pkgjson.JSON(targetInfoJSON), // 目标信息(JSON格式)
		Operator:     "admin",                      // TODO: 需要从上下文中获取创建人
		CreateTime:   now,                          // 创建时间
		UpdateTime:   now,                          // 更新时间
		TotalDetails: len(taskDetails),             // 总任务详情数
		SuccessCount: 0,                            // 成功数(初始为0)
		FailedCount:  0,                            // 失败数(初始为0)
	}

	zap.L().Info("生成DNS任务成功",
		zap.Int("total_task_details", len(taskDetails)),
		zap.Int("target_servers", len(targetServers)),
		zap.Int("involved_views", len(involvedViewIDs)),
		zap.Int("changes_count", len(changes.TaskChanges)),
		zap.Int("total_possible_tasks", totalPossibleTasks),
		zap.Int("filtered_out_tasks", filteredOutTasks))

	return task, taskDetails, nil
}

// handleUpdateRecordTasks 处理DNS更新任务
func (rs *recordService) handleUpdateRecordTasks(
	ctx context.Context,
	zone *model.Zone,
	changes *model.RecordChanges,
) error {
	// 生成任务，任务详情
	task, taskDetails, err := rs.generateTasks(ctx, zone, changes)
	if err != nil {
		zap.L().Error("生成任务失败", zap.Error(err))
		return fmt.Errorf("生成任务失败: %w", err)
	}

	if task == nil || len(taskDetails) == 0 {
		zap.L().Info("无需要更新的DNS记录, 无需下发任务")
		return nil
	}

	// 保存任务
	if err := rs.store.Task().CreateTask(ctx, task, taskDetails); err != nil {
		return fmt.Errorf("创建DNS任务失败: %w", err)
	}

	return nil
}
