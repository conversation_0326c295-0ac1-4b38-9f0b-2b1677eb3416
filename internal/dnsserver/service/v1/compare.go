// Package v1 提供DNS服务器的业务逻辑实现
// 本文件包含记录比较相关的功能实现
// 主要用于比较记录的变更情况，比如更新某个视图会产生哪些影响
// 影响主要用于计算会生成哪些nsupdate任务作用于实际的server
package v1

import (
	"context"
	"fmt"
	"math/rand"
	"slices"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/pkg/mapdata"

	"go.uber.org/zap"
)

// compare 比较记录的变更情况
// 字段说明:
//   - request: 更新请求
//   - record: 记录
//   - zone: 区域
//   - view: 视图
//   - current: 当前视图的记录值, key为record id, value为record value
//   - updated: 更新后的记录值, key为record id, value为record value
//   - snapShot: 当前视图的记录快照, key为record id, value为record的对象指针
//   - affectedViews: 影响范围, 即哪些视图会受到该记录变更的影响, 这里的视图都是office层级的视图
//   - impactMap: 影响信息, key为view id, value为view impact对象指针
//   - ts: 时间戳
type compare struct {
	request       *api.UpdateDnsRecordRequest
	record        *api.UpdateDnsRecord
	zone          *model.Zone
	view          *model.View
	current       *model.RecordValueMap
	updated       *model.RecordValueMap
	snapShot      map[int64]*model.DNSRecord
	affectedViews []*model.View
	impactMap     map[int64]*model.ViewImpact
	ts            int64
}

func (rs *recordService) updateCreate(ctx context.Context, cmp *compare) ([]*model.DNSRecord, []model.RecordChange, error) {
	// 初始化一个需要创建的记录列表
	newRecords := make([]*model.DNSRecord, 0)
	recordChanges := make([]model.RecordChange, 0)

	// 首先判断记录的合法性
	if _, err := rs.recordValidator(ctx, cmp.record.RType, cmp.zone, cmp.view, cmp.record.Value, cmp.request.Name); err != nil {
		zap.L().Error("记录值不合法",
			zap.Error(err),
			zap.String("type", cmp.record.RType),
			zap.String("value", cmp.record.Value))
		return nil, nil, fmt.Errorf("记录值不合法: %w", err)
	}

	if cmp.request.Name == "" {
		zap.L().Error("记录名称不能为空")
		return nil, nil, fmt.Errorf("记录名称不能为空")
	}

	// 检查当前视图中是否已经存在相同名称、类型和值的记录
	for _, exist := range cmp.snapShot {
		if exist.Name == cmp.request.Name &&
			exist.RType == cmp.record.RType &&
			exist.Value == cmp.record.Value {
			zap.L().Error("记录已存在, 无法创建",
				zap.String("name", cmp.request.Name),
				zap.String("type", cmp.record.RType),
				zap.String("value", cmp.record.Value))
			return nil, nil, fmt.Errorf("记录已存在, 无法创建: %s", cmp.record.Value)
		}
	}

	// 如果记录不存在，则创建一条新的记录
	newRecord := &model.DNSRecord{
		Name:        cmp.request.Name,
		RType:       cmp.record.RType,
		Value:       cmp.record.Value,
		TTL:         int(cmp.record.TTL),
		ViewID:      cmp.view.ID,
		ZoneID:      cmp.zone.ID,
		Owner:       cmp.record.Owner,
		Creator:     cmp.record.Creator,
		Description: cmp.request.Description,
		CreateTime:  cmp.ts,
		UpdateTime:  cmp.ts,
	}
	newRecords = append(newRecords, newRecord)

	// 需要将更新的值替换到cmp.updated中
	// 对于新增的记录，是没有id的，因此我们可以模拟一个假的ID
	fakeId := -int64(10000000) - rand.Int63n(1000000000000)
	cmp.updated.Set(fakeId, cmp.record.Value)

	relatedRecordsSnapshot := make(map[int64][]*model.DNSRecord)
	rrs, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, 0, cmp.request.Name, cmp.record.RType, "")
	if err != nil {
		zap.L().Error("获取相关视图记录失败", zap.Error(err), zap.Int64("view_id", cmp.view.ID))
		return nil, nil, fmt.Errorf("获取相关视图记录失败: %w", err)
	}
	for _, rr := range rrs {
		if _, ok := relatedRecordsSnapshot[rr.ViewID]; !ok {
			relatedRecordsSnapshot[rr.ViewID] = make([]*model.DNSRecord, 0)
		}
		relatedRecordsSnapshot[rr.ViewID] = append(relatedRecordsSnapshot[rr.ViewID], rr)
	}

	// 根据affected影响范围补充impacts信息
	if len(cmp.affectedViews) > 0 {
		// 如果要变更的这个view本身是biz层级的，那么需要计算实际生效的值
		if cmp.view.LevelType == model.ViewLevelTypeBiz.String() {
			// 首先需要看当前视图是否存在记录，存在两种情况
			for _, v := range cmp.affectedViews {
				// 创建子视图影响信息
				impact := &model.ViewImpact{
					ViewID:    v.ID,
					ViewName:  v.Name,
					ViewLevel: model.ToViewLevelType(v.LevelType).GetViewLevelDescribe(),
				}

				hasCurrentBizRecords := len(cmp.snapShot) > 0
				if hasCurrentBizRecords {
					// 如果当前视图存在记录，那么添加记录的时候，会影响继承这个biz视图下的子视图
					// office view有记录的，以office的为准；没有记录的，需要添加记录
					relatedRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, v.ID, cmp.request.Name, cmp.record.RType, "")
					if err != nil {
						zap.L().Error("获取相关视图记录失败", zap.Error(err), zap.Int64("view_id", v.ID))
						return nil, nil, fmt.Errorf("获取相关视图记录失败: %w", err)
					}

					if len(relatedRecords) == 0 {
						cmp.updated.Set(fakeId, cmp.record.Value)

						impact.OldValue = cmp.current.ValuesList()
						impact.NewValue = cmp.updated.ValuesList()
						impact.IsChanged = true

						// 如果相关视图没有记录，那么需要添加记录，说明之前就继承了biz，这里需要添加
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  0, // 新记录暂时没有ID
							Name:      cmp.request.Name,
							Type:      cmp.record.RType,
							OldValue:  "", // 新增记录没有旧值
							NewValue:  cmp.record.Value,
							IsChanged: true,
							Action:    model.RecordChangeCreate.String(),
							ViewID:    v.ID,
						})
					} else {
						// 当前office view存在记录，那么就仍然以office的为准
						// 相关视图有自己的记录，不受影响, 因为office级别的view的优先级要高于biz view
						relatedValues := make([]string, 0, len(relatedRecords))
						for _, rr := range relatedRecords {
							relatedValues = append(relatedValues, rr.Value)
						}

						impact.OldValue = relatedValues   // 旧值为相关视图的记录值
						impact.NewValue = impact.OldValue // 新值同上，因为子级配置优先级更高，不会受到上级配置变更带来的影响
						impact.IsChanged = false          // 没有发生变化

						zap.L().Info("当前office view存在记录，那么就仍然以office的为准", zap.Int64("view_id", v.ID))
					}

				} else {
					// 如果当前biz视图不存在记录，那么底层的office如果之前继承了default配置
					// 当前在biz视图新增记录，优先级要高于default，因此需要先把default删掉，再把biz新增的给加上；
					relatedRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, v.ID, cmp.request.Name, cmp.record.RType, "")
					if err != nil {
						zap.L().Error("获取相关视图记录失败", zap.Error(err), zap.Int64("view_id", v.ID))
						return nil, nil, fmt.Errorf("获取相关视图记录失败: %w", err)
					}

					if len(relatedRecords) == 0 {
						// 当前office view没有记录，这个我们需要检查过是否继承过默认视图
						defaultRecords, err := rs.store.Record().GetRecordInDefaultView(ctx, cmp.zone.ID, cmp.request.Name, cmp.record.RType, "")
						if err != nil {
							zap.L().Error("获取default视图记录失败", zap.Error(err))
							return nil, nil, fmt.Errorf("获取default视图记录失败: %w", err)
						}

						if len(defaultRecords) > 0 {
							// 说明之前继承过default，现在需要先删除default，再添加biz
							// 设置current值为继承的default值
							for _, dr := range defaultRecords {
								cmp.current.Set(dr.ID, dr.Value)

								recordChanges = append(recordChanges, model.RecordChange{
									RecordID:  dr.ID,
									Name:      dr.Name,
									Type:      dr.RType,
									OldValue:  dr.Value,
									NewValue:  "",
									TTL:       dr.TTL,
									IsChanged: true,
									Action:    model.RecordChangeDelete.String(),
									ViewID:    v.ID,
								})
							}

							recordChanges = append(recordChanges, model.RecordChange{
								RecordID:  0, // 新记录暂时没有ID
								Name:      cmp.request.Name,
								Type:      cmp.record.RType,
								OldValue:  "", // 新增记录没有旧值
								NewValue:  cmp.record.Value,
								IsChanged: true,
								Action:    model.RecordChangeCreate.String(),
								ViewID:    v.ID,
							})
						} else {
							// 说明之前没有继承过default，现在需要直接添加biz
							recordChanges = append(recordChanges, model.RecordChange{
								RecordID:  0, // 新记录暂时没有ID
								Name:      cmp.request.Name,
								Type:      cmp.record.RType,
								OldValue:  "", // 新增记录没有旧值
								NewValue:  cmp.record.Value,
								IsChanged: true,
								Action:    model.RecordChangeCreate.String(),
								ViewID:    v.ID,
							})
						}

						// 设置impact信息
						impact.OldValue = cmp.current.ValuesList()   // 旧值为当前视图的记录值（可能为空或继承的default值）
						impact.NewValue = []string{cmp.record.Value} // 新值为新增的biz记录值
						impact.IsChanged = !sortedEqual(impact.OldValue, impact.NewValue)
					} else {
						// 当前office view存在记录，依然以office的为准
						// 相关视图有自己的记录，不受影响, 因为office级别的view的优先级要高于biz view
						relatedValues := make([]string, 0, len(relatedRecords))
						for _, rr := range relatedRecords {
							relatedValues = append(relatedValues, rr.Value)
						}

						impact.OldValue = relatedValues   // 旧值为相关视图的记录值
						impact.NewValue = impact.OldValue // 新值同上，因为子级配置优先级更高，不会受到上级配置变更带来的影响
						impact.IsChanged = false          // 没有发生变化

						zap.L().Info("当前office view存在记录，依然以office的为准", zap.Int64("view_id", v.ID))
					}
				}

				// 添加影响信息到impactMap
				if _, ok := cmp.impactMap[v.ID]; !ok {
					// 如果之前并没有影响信息，那么就直接添加
					cmp.impactMap[v.ID] = impact
				} else {
					// 如果之前已经存在impact信息，那么需要合并
					i := cmp.impactMap[v.ID]
					// 用一个容器来存储合并后的值
					newValueMap := mapdata.NewMapData()
					// 先把已经存在的值添加到容器中
					for _, nv := range i.NewValue {
						newValueMap.Set(nv, struct{}{})
					}
					// 再把新的值添加到容器中
					for _, nv := range impact.NewValue {
						newValueMap.Set(nv, struct{}{})
					}
					// 最后将容器中的值转换为切片
					i.NewValue = newValueMap.Keys()
					// 最后更新影响信息
					i.IsChanged = !sortedEqual(i.OldValue, i.NewValue)
				}
			}
		} else {
			// 说明添加的是default view
			// 如果当前default view有记录，那么需要计算实际生效的值
			for _, affectedView := range cmp.affectedViews {
				impact := &model.ViewImpact{
					ViewID:    affectedView.ID,
					ViewName:  affectedView.Name,
					ViewLevel: model.ToViewLevelType(affectedView.LevelType).GetViewLevelDescribe(),
				}

				// 当前这个受影响的视图有没有对应的records记录
				affectedViewRecordsExist := false
				relatedRecords, ok := relatedRecordsSnapshot[affectedView.ID]
				if !ok {
					affectedViewRecordsExist = false
				} else {
					affectedViewRecordsExist = len(relatedRecords) > 0
				}

				if !affectedViewRecordsExist {
					// 相关视图中没有该记录，还得看一下对应office继承的biz视图下是否存在记录，如果继承的biz视图下存在记录的话，那么biz优先级更高
					bizViewRecordsExist := false
					bizRecords, ok := relatedRecordsSnapshot[affectedView.ParentID]
					if ok {
						bizViewRecordsExist = len(bizRecords) > 0
					} else {
						bizViewRecordsExist = false
					}

					if bizViewRecordsExist {
						// 说明继承的biz视图下存在记录，那么biz优先级更高
						bizValues := make([]string, 0, len(bizRecords))
						for _, rr := range bizRecords {
							bizValues = append(bizValues, rr.Value)
						}

						impact.OldValue = bizValues // 旧值为biz视图的记录值
						impact.NewValue = bizValues // 新值为biz视图的记录值
						impact.IsChanged = false    // 没有发生变化
					} else {
						// 说明继承的biz视图下不存在记录，这个时候就需要继承default的配置了
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  0, // 新记录暂时没有ID
							Name:      cmp.request.Name,
							Type:      cmp.record.RType,
							OldValue:  "", // 新增记录没有旧值
							NewValue:  cmp.record.Value,
							IsChanged: true,
							Action:    model.RecordChangeCreate.String(),
							ViewID:    affectedView.ID,
						})

						impact.OldValue = cmp.current.ValuesList() // 旧值为当前视图的记录值
						impact.NewValue = cmp.updated.ValuesList() // 新值也为当前视图的记录值
						impact.IsChanged = !sortedEqual(impact.OldValue, impact.NewValue)
					}
				} else {
					// 如果本来这个office就存在对应的记录，那么直接以office的为准，无需任何变更，因为office层级的配置优先级是最高的
					relatedValues := make([]string, 0, len(relatedRecords))
					for _, rr := range relatedRecords {
						relatedValues = append(relatedValues, rr.Value)
					}

					impact.OldValue = relatedValues   // 旧值为相关视图的记录值
					impact.NewValue = impact.OldValue // 新值同上，因为子级配置优先级更高，不会受到上级配置变更带来的影响
					impact.IsChanged = false          // 没有发生变化
				}

				// 添加影响信息到impactMap
				if _, ok := cmp.impactMap[affectedView.ID]; !ok {
					// 如果之前并没有影响信息，那么就直接添加
					cmp.impactMap[affectedView.ID] = impact
				} else {
					// 如果之前已经存在impact信息，那么需要合并
					i := cmp.impactMap[affectedView.ID]
					// 用一个容器来存储合并后的值
					newValueMap := mapdata.NewMapData()
					// 先把已经存在的值添加到容器中
					for _, nv := range i.NewValue {
						newValueMap.Set(nv, struct{}{})
					}
					// 再把新的值添加到容器中
					for _, nv := range impact.NewValue {
						newValueMap.Set(nv, struct{}{})
					}
					// 最后将容器中的值转换为切片
					i.NewValue = newValueMap.Keys()
					// 最后更新影响信息
					i.IsChanged = !sortedEqual(i.OldValue, i.NewValue)
				}
			}
		}

	} else {
		// 没有影响范围，说明当前视图是一个office层级的视图，只会影响自己
		// 关键判断：当前office视图下是否已经存在同名同类型的记录
		// 1. 如果当前office视图已有记录：直接添加新记录即可
		// 2. 如果当前office视图没有记录：需要检查是否继承了父级记录，如果有则需要先删除继承再添加新记录

		// 首先检查当前office视图下是否已经存在同名同类型的记录
		// 直接使用cmp.snapShot，避免重复查询数据库
		hasCurrentOfficeRecords := len(cmp.snapShot) > 0

		if hasCurrentOfficeRecords {
			// 情况1：当前office视图已有同名同类型记录，直接添加新记录
			// 重复检查已经在前面的代码中处理了，这里只需要生成create操作
			recordChanges = append(recordChanges, model.RecordChange{
				RecordID:  0, // 新记录暂时没有ID
				Name:      cmp.request.Name,
				Type:      cmp.record.RType,
				OldValue:  "", // 新增记录没有旧值
				NewValue:  cmp.record.Value,
				IsChanged: true,
				Action:    model.RecordChangeCreate.String(),
				ViewID:    cmp.view.ID, // 在当前office视图中创建
			})
		} else {
			// 情况2：当前office视图没有同名同类型记录，需要检查是否继承了父级记录
			if cmp.view.ParentID > 0 {
				parentRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, cmp.view.ParentID,
					cmp.request.Name, cmp.record.RType, "")
				if err != nil {
					zap.L().Error("获取父视图记录失败", zap.Error(err), zap.Int64("parent_view_id", cmp.view.ParentID))
					return nil, nil, fmt.Errorf("获取父视图记录失败: %w", err)
				}

				if len(parentRecords) > 0 {
					// 有值，说明之前office继承了父biz视图的配置，我们需要把配置给拿出来
					oldValues := make([]string, 0, len(parentRecords))
					for _, pr := range parentRecords {
						oldValues = append(oldValues, pr.Value)

						// 这些配置需要在实际的DNS Server中删除的
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  pr.ID,
							Name:      pr.Name,
							Type:      pr.RType,
							OldValue:  pr.Value,
							NewValue:  "",
							TTL:       pr.TTL,
							IsChanged: true,
							Action:    model.RecordChangeDelete.String(),
							ViewID:    cmp.view.ID, // 使用实际需要删除的office view ID
						})
					}

					// 生成创建新记录的操作
					recordChanges = append(recordChanges, model.RecordChange{
						RecordID:  0, // 新记录暂时没有ID
						Name:      cmp.request.Name,
						Type:      cmp.record.RType,
						OldValue:  "", // 新增记录没有旧值
						NewValue:  cmp.record.Value,
						IsChanged: true,
						Action:    model.RecordChangeCreate.String(),
						ViewID:    cmp.view.ID, // 在当前office视图中创建
					})

					impact := &model.ViewImpact{
						ViewID:    cmp.view.ID,
						ViewName:  cmp.view.Name,
						ViewLevel: model.ToViewLevelType(cmp.view.LevelType).GetViewLevelDescribe(),
						OldValue:  oldValues,                  // 旧值为父biz视图的记录值
						NewValue:  []string{cmp.record.Value}, // 新值为当前视图的记录值
						IsChanged: !sortedEqual(oldValues, []string{cmp.record.Value}),
					}

					// 添加影响信息
					cmp.impactMap[cmp.view.ID] = impact
				} else {
					// biz层级的视图没有，再向上一层查询，查询default视图
					defaultView, err := rs.store.View().GetDefaultView(ctx)
					if err != nil {
						zap.L().Error("获取default视图失败", zap.Error(err))
						return nil, nil, fmt.Errorf("获取default视图失败: %w", err)
					}

					// 看看default视图中有没有对应的配置
					defaultRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, defaultView.ID,
						cmp.request.Name, cmp.record.RType, "")
					if err != nil {
						zap.L().Error("获取default视图记录失败", zap.Error(err), zap.Int64("default_view_id", defaultView.ID))
						return nil, nil, fmt.Errorf("获取default视图记录失败: %w", err)
					}

					if len(defaultRecords) > 0 {
						// 对应的default中有相关record记录的配置
						// 收集default视图中被覆盖的记录值
						oldValues := make([]string, 0, len(defaultRecords))
						for _, dr := range defaultRecords {
							oldValues = append(oldValues, dr.Value)

							// 这些old Value是需要删除的
							recordChanges = append(recordChanges, model.RecordChange{
								RecordID:  dr.ID,
								Name:      dr.Name,
								Type:      dr.RType,
								OldValue:  dr.Value,
								NewValue:  "",
								TTL:       dr.TTL,
								IsChanged: true,
								Action:    model.RecordChangeDelete.String(),
								ViewID:    cmp.view.ID, // 使用实际需要删除的office view ID
							})
						}

						// 生成创建新记录的操作
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  0, // 新记录暂时没有ID
							Name:      cmp.request.Name,
							Type:      cmp.record.RType,
							OldValue:  "", // 新增记录没有旧值
							NewValue:  cmp.record.Value,
							IsChanged: true,
							Action:    model.RecordChangeCreate.String(),
							ViewID:    cmp.view.ID, // 在当前office视图中创建
						})

						impact := &model.ViewImpact{
							ViewID:    cmp.view.ID,
							ViewName:  cmp.view.Name,
							ViewLevel: model.ToViewLevelType(cmp.view.LevelType).GetViewLevelDescribe(),
							OldValue:  oldValues,                  // 旧值为default视图的记录值
							NewValue:  []string{cmp.record.Value}, // 新值为当前视图的记录值
							IsChanged: !sortedEqual(oldValues, []string{cmp.record.Value}),
						}

						// 添加影响信息
						cmp.impactMap[cmp.view.ID] = impact
					} else {
						// 没有default记录，直接在office视图中创建记录
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  0, // 新记录暂时没有ID
							Name:      cmp.request.Name,
							Type:      cmp.record.RType,
							OldValue:  "", // 新增记录没有旧值
							NewValue:  cmp.record.Value,
							IsChanged: true,
							Action:    model.RecordChangeCreate.String(),
							ViewID:    cmp.view.ID, // 在当前office视图中创建
						})
					}
				}
			} else {
				// 没有父级视图，直接在office视图中创建记录
				recordChanges = append(recordChanges, model.RecordChange{
					RecordID:  0, // 新记录暂时没有ID
					Name:      cmp.request.Name,
					Type:      cmp.record.RType,
					OldValue:  "", // 新增记录没有旧值
					NewValue:  cmp.record.Value,
					IsChanged: true,
					Action:    model.RecordChangeCreate.String(),
					ViewID:    cmp.view.ID, // 在当前office视图中创建
				})
			}
		}
	}

	return newRecords, recordChanges, nil
}

func (rs *recordService) updateUpdate(ctx context.Context, cmp *compare) ([]*model.DNSRecord, []model.RecordChange, error) {
	// 初始化数据容器
	updateRecords := make([]*model.DNSRecord, 0)
	recordChanges := make([]model.RecordChange, 0)

	// 针对更新的场景
	currentRecord, ok := cmp.snapShot[cmp.record.RecordID]
	if !ok {
		zap.L().Error("record快照中不存在您要更新的记录，请检查是否view_id不一致或提交record不存在",
			zap.Int64("record_id", cmp.record.RecordID),
			zap.Int64("snapshot view_id", cmp.view.ID),
			zap.Any("recordsSnapShotMap", cmp.snapShot))
		return nil, nil, fmt.Errorf("要更新的记录不存在, 记录的ID为: %d", cmp.record.RecordID)
	}

	// 验证记录值是否合法
	if _, err := rs.recordValidator(ctx, currentRecord.RType, cmp.zone, cmp.view, cmp.record.Value, currentRecord.Name); err != nil {
		zap.L().Error("记录值不合法", zap.Error(err), zap.Int64("record_id", cmp.record.RecordID),
			zap.String("type", currentRecord.RType), zap.String("value", cmp.record.Value))
		return nil, nil, fmt.Errorf("记录值不合法: %w", err)
	}

	// 检测更新后的记录是否已经存在
	for _, cr := range cmp.snapShot {
		if cr.ID != cmp.record.RecordID && cr.Value == cmp.record.Value {
			// 如果已经存在相同的值，返回错误
			err := fmt.Errorf("更新的值已经存在于同名同类型的其他记录中: %s", cmp.record.Value)
			zap.L().Warn("更新值重复", zap.Error(err),
				zap.Int64("record_id", cmp.record.RecordID),
				zap.String("name", currentRecord.Name),
				zap.String("type", currentRecord.RType),
				zap.String("value", cmp.record.Value))
			return nil, nil, err
		}

		if cr.ID == cmp.record.RecordID {
			// 如果记录ID相同，则说明是当前记录
			// 首先检查记录类型是否发生变化，目前我们要求一条记录类型一旦创建则不允许变更
			// 如果需要变更请先删除原来的记录后重新创建
			if cr.RType != cmp.record.RType {
				err := fmt.Errorf("记录类型不能修改, 请先删除后再进行创建")
				zap.L().Error("记录类型不能修改", zap.Error(err), zap.Int64("record_id", cmp.record.RecordID),
					zap.String("name", currentRecord.Name), zap.String("type", currentRecord.RType), zap.String("value", cmp.record.Value))
				return nil, nil, err
			}

			// 检查是否有任何字段发生变化
			hasChanged := rs.compareRecord(currentRecord, cmp.record, cmp.request.Description)

			// 只有当字段啊发生变化的时候才进行更新
			if hasChanged {
				// 这是要更新的记录
				updatedRecord := *currentRecord // 复制一份
				updatedRecord.Value = cmp.record.Value
				updatedRecord.Description = cmp.request.Description
				updatedRecord.Owner = cmp.record.Owner
				updatedRecord.Creator = cmp.record.Creator
				updatedRecord.TTL = int(cmp.record.TTL)
				updatedRecord.RType = cmp.record.RType
				updatedRecord.UpdateTime = cmp.ts

				// 更新后的记录列表
				updateRecords = append(updateRecords, &updatedRecord)

				// 注意：这里不再直接生成recordChanges，而是在后面的affectedViews循环中处理

				if cr.Value != cmp.record.Value {
					cmp.updated.Set(cmp.record.RecordID, cmp.record.Value)
				}
			}
			continue
		}
	}

	// 检查影响范围
	if len(cmp.affectedViews) > 0 {
		// 有影响范围，说明要变更的视图是一个default层级或者biz层级
		for _, relatedView := range cmp.affectedViews {
			// 获取相关视图中的记录
			relatedRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, relatedView.ID, cmp.request.Name, cmp.record.RType, "")
			if err != nil {
				zap.L().Error("获取相关视图记录失败",
					zap.Error(err),
					zap.Int64("view_id", relatedView.ID))
				return nil, nil, fmt.Errorf("获取相关视图记录失败: %w", err)
			}

			// 创建impact影响
			impact := &model.ViewImpact{
				ViewID:    relatedView.ID,
				ViewName:  relatedView.Name,
				ViewLevel: model.ToViewLevelType(relatedView.LevelType).GetViewLevelDescribe(),
			}

			if len(relatedRecords) == 0 {
				// 如果说相关视图没有记录，这意味着直接继承就可以了
				impact.OldValue = cmp.current.ValuesList()
				impact.NewValue = cmp.updated.ValuesList()
				impact.IsChanged = !sortedEqual(impact.OldValue, impact.NewValue)

				// 为当前office view生成更新记录的操作
				if impact.IsChanged {
					recordChanges = append(recordChanges, model.RecordChange{
						RecordID:  cmp.record.RecordID,
						Name:      cmp.request.Name,
						Type:      cmp.record.RType,
						OldValue:  cmp.current.ValuesList()[0], // 假设只有一个旧值
						NewValue:  cmp.record.Value,
						IsChanged: true,
						Action:    model.RecordChangeUpdate.String(),
						ViewID:    relatedView.ID, // 使用实际需要更新的office view ID
					})
				}
			} else {
				// 相关office视图中存在记录，因为office层级优先级更高，所以无需继承
				relatedValues := mapdata.NewMapData()
				for _, rr := range relatedRecords {
					relatedValues.Set(rr.Value, struct{}{})
				}

				impact.OldValue = relatedValues.Keys()
				impact.NewValue = impact.OldValue
				impact.IsChanged = false
			}

			if _, ok := cmp.impactMap[relatedView.ID]; !ok {
				cmp.impactMap[relatedView.ID] = impact
			} else {
				i := cmp.impactMap[relatedView.ID]
				newValueMap := mapdata.NewMapData()
				for _, v := range i.NewValue {
					newValueMap.Set(v, struct{}{})
				}
				for _, v := range impact.NewValue {
					newValueMap.Set(v, struct{}{})
				}
				i.NewValue = newValueMap.Keys()
				i.IsChanged = !sortedEqual(i.OldValue, i.NewValue)
			}
		}
	} else {
		// 没有影响范围，说明当前视图是一个office层级的视图，只会影响自己
		// 因为是更新所以目前已经使用对应office的配置，因此无需删除继承上层的配置
		zap.L().Debug("当前视图是office层级的视图，不会影响其他视图，因此无需删除继承上层的配置")

		// 为office层级的更新生成更新记录的操作
		if len(updateRecords) > 0 {
			// 找到当前记录的旧值
			var oldValue string
			for _, record := range cmp.snapShot {
				if record.ID == cmp.record.RecordID {
					oldValue = record.Value
					break
				}
			}

			recordChanges = append(recordChanges, model.RecordChange{
				RecordID:  cmp.record.RecordID,
				Name:      cmp.request.Name,
				Type:      cmp.record.RType,
				OldValue:  oldValue,
				NewValue:  cmp.record.Value,
				IsChanged: true,
				Action:    model.RecordChangeUpdate.String(),
				ViewID:    cmp.view.ID, // 在当前office视图中更新
			})
		}
	}

	return updateRecords, recordChanges, nil
}

func (rs *recordService) updateDelete(ctx context.Context, cmp *compare, updatedIds []int64) ([]*model.DNSRecord, []model.RecordChange, error) {
	deleteRecords := make([]*model.DNSRecord, 0)
	recordChanges := make([]model.RecordChange, 0)

	// 如果删除的记录ID列表为空，则无需处理
	if len(cmp.request.DeleteRecordIds) == 0 {
		zap.L().Debug("没有删除记录，因此无需处理")
		return deleteRecords, recordChanges, nil
	}

	// 逐个处理每一个要删除的记录
	for _, deleteID := range cmp.request.DeleteRecordIds {
		// 不允许同时删除和更新
		if slices.Contains(updatedIds, deleteID) {
			zap.L().Error("删除的记录ID在更新的记录ID中存在, 无法同时删除和更新", zap.Int64("delete_id", deleteID))
			return nil, nil, fmt.Errorf("删除的记录ID在更新的记录ID中存在, 无法同时删除和更新")
		}

		// 在快照中获取要删除的记录
		deleteRecord, ok := cmp.snapShot[deleteID]
		if !ok {
			zap.L().Error("ID为 %d 的记录不存在", zap.Int64("record_id", deleteID))
			return nil, nil, fmt.Errorf("ID为 %d 的记录不存在, 无法删除", deleteID)
		}

		// 在删除列表中将要删除的记录添加进去
		deleteRecords = append(deleteRecords, deleteRecord)

		// 先更新cmp.updated状态，删除要删除的记录
		cmp.updated.Delete(deleteID)

		// 把snapshot也更新一下
		delete(cmp.snapShot, deleteID)

		// 计算影响范围
		if len(cmp.affectedViews) > 0 {
			defaultViewRecords := make([]*model.DNSRecord, 0)
			// 当default视图删干净的时候，office需要继承biz的
			// 使用bizViewRecordMap保存对应的biz视图下的解析快照；
			bizViewRecordMap := make(map[int64][]*model.DNSRecord)
			// 有影响范围，说明要变更的视图是一个default层级或者biz层级
			viewLevelType := model.ToViewLevelType(cmp.view.LevelType)
			if viewLevelType == model.ViewLevelTypeBiz {
				// 如果是biz层级的话，得看snapshot中有没有记录，只要还有值就还可以继续继承，只不过继承的值少了一个而已；
				// 查看快照中是否还存在值
				if len(cmp.snapShot) > 0 {
					// 如果快照中还有记录，说明此时office view仍然需要使用biz视图下的配置
					// 仅把当前要删除的删除即可；
					zap.L().Info("biz下仍存在记录, 继续继承biz的配置")
				} else {
					// 如果快照中删完了没有记录了，且此时affectedViews（即office view）不为空
					// 说明这个时候，office view需要继承default view的配置记录了
					defaultRecords, err := rs.store.Record().GetRecordInDefaultView(ctx, cmp.zone.ID, deleteRecord.Name, deleteRecord.RType, "")
					if err != nil {
						zap.L().Error("获取default视图记录失败", zap.Error(err))
						return nil, nil, fmt.Errorf("获取default视图记录失败: %w", err)
					}

					defaultViewRecords = defaultRecords

					if len(defaultRecords) > 0 {
						// 如果default视图中有记录，那么需要计算实际生效的值
						for _, dr := range defaultRecords {
							cmp.updated.Set(dr.ID, dr.Value)
						}
					}
				}

				for _, relatedView := range cmp.affectedViews {
					relatedRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, relatedView.ID, deleteRecord.Name, deleteRecord.RType, "")
					if err != nil {
						zap.L().Error("获取相关视图记录失败",
							zap.Error(err),
							zap.Int64("view_id", relatedView.ID))
						return nil, nil, fmt.Errorf("获取相关视图记录失败: %w", err)
					}

					impact := &model.ViewImpact{
						ViewID:    relatedView.ID,
						ViewName:  relatedView.Name,
						ViewLevel: model.ToViewLevelType(relatedView.LevelType).GetViewLevelDescribe(),
					}

					// 为当前office view生成删除原biz记录的操作
					recordChanges = append(recordChanges, model.RecordChange{
						RecordID:  deleteID,
						Name:      deleteRecord.Name,
						Type:      deleteRecord.RType,
						OldValue:  deleteRecord.Value,
						NewValue:  "",
						IsChanged: true,
						Action:    model.RecordChangeDelete.String(),
						ViewID:    relatedView.ID, // 使用实际需要删除的office view ID
					})

					if len(relatedRecords) == 0 {
						// 相关视图没有记录，说明直接继承
						impact.OldValue = cmp.current.ValuesList()
						impact.NewValue = cmp.updated.ValuesList()
						impact.IsChanged = !sortedEqual(impact.OldValue, impact.NewValue)

						// 如果需要继承default记录，则生成创建操作
						for _, dr := range defaultViewRecords {
							recordChanges = append(recordChanges, model.RecordChange{
								RecordID:  dr.ID,
								Name:      dr.Name,
								Type:      dr.RType,
								OldValue:  "",
								NewValue:  dr.Value,
								IsChanged: true,
								Action:    model.RecordChangeCreate.String(),
								ViewID:    relatedView.ID, // 使用实际需要创建的office view ID
							})
						}

					} else {
						// 相关视图有记录，因为office层级优先级更高，所以无需继承
						relatedValues := make([]string, 0, len(relatedRecords))
						for _, rr := range relatedRecords {
							relatedValues = append(relatedValues, rr.Value)
						}

						impact.OldValue = relatedValues
						impact.NewValue = impact.OldValue
						impact.IsChanged = false
					}

					if _, ok := cmp.impactMap[relatedView.ID]; !ok {
						cmp.impactMap[relatedView.ID] = impact
					} else {
						i := cmp.impactMap[relatedView.ID]
						newValueMap := mapdata.NewMapData()
						for _, nv := range i.NewValue {
							newValueMap.Set(nv, struct{}{})
						}
						newValueMap.Delete(deleteRecord.Value)
						i.NewValue = newValueMap.Keys()
						i.IsChanged = !sortedEqual(i.OldValue, i.NewValue)
					}
				}
			} else {
				// 删除的是default视图的内容
				// 获取所有biz视图
				bizViews, err := rs.store.View().GetViewByType(ctx, model.ViewLevelTypeBiz)
				if err != nil {
					zap.L().Error("获取biz视图失败", zap.Error(err))
					return nil, nil, fmt.Errorf("获取biz视图失败: %w", err)
				}

				bizViewMap := make(map[int64]*model.View)
				for _, bizView := range bizViews {
					bizViewMap[bizView.ID] = bizView
				}

				// 获取可能受影响的记录，不限视图，不限Rrtype
				relatedRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, 0, deleteRecord.Name, "", "")
				if err != nil {
					zap.L().Error("获取可能受影响的记录失败", zap.Error(err))
					return nil, nil, fmt.Errorf("获取可能受影响的记录失败: %w", err)
				}

				// 将default视图中的配置删除到空后，需要继承biz视图中的配置
				// 这里构建一个map，其中key为biz视图ID，value为biz视图中的记录的切片
				for _, rr := range relatedRecords {
					if _, ok := bizViewMap[rr.ViewID]; ok {
						bizViewRecordMap[rr.ViewID] = append(bizViewRecordMap[rr.ViewID], rr)
					}
				}

				// 如果删除的是default层级的，那么得看一下biz层级有没有记录，如果有记录，那么需要继承biz层级的记录
				if len(cmp.snapShot) > 0 {
					// 如果default层级下有记录，那么本来继承default的继续继承，没有继承的没有影响
					zap.L().Info("default下仍存在记录, 继续继承default的配置")
				} else {
					// 如果default层级没有记录，那么需要继承biz层级的记录
					zap.L().Info("default下没有记录, 查找biz层级的记录来继承")
				}

				for _, affectedView := range cmp.affectedViews {
					relatedRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, affectedView.ID, deleteRecord.Name, deleteRecord.RType, "")
					if err != nil {
						zap.L().Error("获取相关视图记录失败",
							zap.Error(err),
							zap.Int64("view_id", affectedView.ID))
						return nil, nil, fmt.Errorf("获取相关视图记录失败: %w", err)
					}

					impact := &model.ViewImpact{
						ViewID:    affectedView.ID,
						ViewName:  affectedView.Name,
						ViewLevel: model.ToViewLevelType(affectedView.LevelType).GetViewLevelDescribe(),
					}

					if len(relatedRecords) > 0 {
						// 相关视图有记录，因为office层级优先级更高，所以无需继承
						relatedValues := make([]string, 0, len(relatedRecords))
						for _, rr := range relatedRecords {
							relatedValues = append(relatedValues, rr.Value)
						}

						impact.OldValue = relatedValues
						impact.NewValue = impact.OldValue
						impact.IsChanged = false
					} else {
						// 相关视图没有记录，这个时候需要检查biz中有没有配置；
						// 通过office视图的ParentID来查找对应的biz视图记录
						if bizRecords, ok := bizViewRecordMap[affectedView.ParentID]; ok {
							if len(bizRecords) > 0 {
								// 如果biz视图中有记录，office本来就继承biz，无需任何操作
								// 只需要计算impact：从继承default变为继承biz（实际上没变化）
								bizValues := make([]string, 0, len(bizRecords))
								for _, dr := range bizRecords {
									bizValues = append(bizValues, dr.Value)
								}

								// 设置impact的值 - 实际上没有变化，因为office本来就继承biz
								impact.OldValue = bizValues // 之前就是继承biz的值
								impact.NewValue = bizValues // 现在还是继承biz的值
								impact.IsChanged = false    // 没有变化
							} else {
								// biz视图也没有记录，从继承default变为没有记录
								impact.OldValue = cmp.current.ValuesList()  // 之前继承的default值
								impact.NewValue = []string{}                // 现在没有值可继承
								impact.IsChanged = len(impact.OldValue) > 0 // 如果之前有值现在没有，则发生变化

								// 为当前office view生成删除原default记录的操作
								if impact.IsChanged {
									recordChanges = append(recordChanges, model.RecordChange{
										RecordID:  deleteID,
										Name:      deleteRecord.Name,
										Type:      deleteRecord.RType,
										OldValue:  deleteRecord.Value,
										NewValue:  "",
										IsChanged: true,
										Action:    model.RecordChangeDelete.String(),
										ViewID:    affectedView.ID, // 使用实际需要删除的office view ID
									})
								}
							}
						} else {
							// 没有对应的biz视图，从继承default变为没有记录
							impact.OldValue = cmp.current.ValuesList()  // 之前继承的default值
							impact.NewValue = cmp.updated.ValuesList()  // 现在没有值可继承
							impact.IsChanged = len(impact.OldValue) > 0 // 如果之前有值现在没有，则发生变化

							// 为当前office view生成删除原default记录的操作
							if impact.IsChanged {
								recordChanges = append(recordChanges, model.RecordChange{
									RecordID:  deleteID,
									Name:      deleteRecord.Name,
									Type:      deleteRecord.RType,
									OldValue:  deleteRecord.Value,
									NewValue:  "",
									IsChanged: true,
									Action:    model.RecordChangeDelete.String(),
									ViewID:    affectedView.ID, // 使用实际需要删除的office view ID
								})
							}
						}
					}

					// 直接设置impact到map中
					cmp.impactMap[affectedView.ID] = impact
				}
			}

		} else {
			// 没有影响范围，说明当前视图是一个office层级的视图，只会影响自己
			// 但是有一个点我们是需要确认的，就是如果说当前office层级针对这个记录已经没有配置了，这个时候需要检查是否要继承上级的配置
			zap.L().Debug("当前视图是office层级的视图")
			snapShotCopy := make(map[int64]*model.DNSRecord)
			for k, v := range cmp.snapShot {
				snapShotCopy[k] = v
			}

			// 删除当前要删除的记录
			delete(snapShotCopy, deleteRecord.ID)

			// 如果删除后snapShotCopy为空，说明当前域名在office视图下没有任何解析记录了
			// 此时需要检查是否要继承父级记录
			if len(snapShotCopy) == 0 {
				zap.L().Debug("删除记录后当前域名在office视图下没有任何解析记录，检查是否需要继承父级记录",
					zap.String("name", deleteRecord.Name))

				// 查询biz层级记录
				bizRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, cmp.view.ParentID, deleteRecord.Name, deleteRecord.RType, "")
				if err != nil {
					zap.L().Error("获取biz层级记录失败", zap.Error(err), zap.Int64("biz_view_id", cmp.view.ParentID))
					return nil, nil, fmt.Errorf("获取biz层级记录失败: %w", err)
				}

				if len(bizRecords) > 0 {
					zap.L().Info("在biz层级找到记录，生成继承任务",
						zap.String("name", deleteRecord.Name),
						zap.String("type", deleteRecord.RType),
						zap.Int("count", len(bizRecords)))

					// 先生成删除原office记录的操作
					recordChanges = append(recordChanges, model.RecordChange{
						RecordID:  deleteID,
						Name:      deleteRecord.Name,
						Type:      deleteRecord.RType,
						OldValue:  deleteRecord.Value,
						NewValue:  "",
						IsChanged: true,
						Action:    model.RecordChangeDelete.String(),
						ViewID:    cmp.view.ID, // 在当前office视图中删除
					})

					// 在biz层级找到记录，生成继承任务
					for _, bizRecord := range bizRecords {
						// 记录继承变更，生成create任务
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  bizRecord.ID,
							Name:      bizRecord.Name,
							Type:      bizRecord.RType,
							OldValue:  "",
							NewValue:  bizRecord.Value,
							TTL:       bizRecord.TTL,
							IsChanged: true,
							Action:    model.RecordChangeCreate.String(),
							ViewID:    cmp.view.ID, // 注意：这里是office视图ID，表示在office视图中应用biz记录
						})

						// 更新内存映射
						cmp.updated.Set(bizRecord.ID, bizRecord.Value)
					}

					// 创建影响信息
					impact := &model.ViewImpact{
						ViewID:    cmp.view.ID,
						ViewName:  cmp.view.Name,
						ViewLevel: model.ToViewLevelType(cmp.view.LevelType).GetViewLevelDescribe(),
						OldValue:  cmp.current.ValuesList(),
						NewValue:  cmp.updated.ValuesList(),
					}
					impact.IsChanged = !sortedEqual(impact.OldValue, impact.NewValue)
					cmp.impactMap[cmp.view.ID] = impact
				} else {
					// 说明biz层级没有记录，继续排查default层级有没有记录
					defaultView, err := rs.store.View().GetDefaultView(ctx)
					if err != nil {
						zap.L().Error("获取default视图失败", zap.Error(err))
						return nil, nil, fmt.Errorf("获取default视图失败: %w", err)
					}

					defaultRecords, err := rs.store.Record().GetRecordList(ctx, cmp.zone.ID, defaultView.ID, deleteRecord.Name, deleteRecord.RType, "")
					if err != nil {
						zap.L().Error("获取default视图记录失败", zap.Error(err), zap.Int64("view_id", defaultView.ID))
						return nil, nil, fmt.Errorf("获取default视图记录失败: %w", err)
					}

					if len(defaultRecords) > 0 {
						zap.L().Info("在default层级找到记录，生成继承任务",
							zap.String("name", deleteRecord.Name),
							zap.String("type", deleteRecord.RType),
							zap.Int("count", len(defaultRecords)))

						// 先生成删除原office记录的操作
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  deleteID,
							Name:      deleteRecord.Name,
							Type:      deleteRecord.RType,
							OldValue:  deleteRecord.Value,
							NewValue:  "",
							IsChanged: true,
							Action:    model.RecordChangeDelete.String(),
							ViewID:    cmp.view.ID, // 在当前office视图中删除
						})

						// 在default层级找到记录，生成继承任务
						for _, defaultRecord := range defaultRecords {
							// 记录继承变更，生成create任务
							recordChanges = append(recordChanges, model.RecordChange{
								RecordID:  defaultRecord.ID,
								Name:      defaultRecord.Name,
								Type:      defaultRecord.RType,
								OldValue:  "",
								NewValue:  defaultRecord.Value,
								TTL:       defaultRecord.TTL,
								IsChanged: true,
								Action:    model.RecordChangeCreate.String(),
								ViewID:    cmp.view.ID, // 注意：这里是office视图ID，表示在office视图中应用default记录
							})

							// 更新内存映射
							cmp.updated.Set(defaultRecord.ID, defaultRecord.Value)
						}

						// 创建影响信息
						impact := &model.ViewImpact{
							ViewID:    cmp.view.ID,
							ViewName:  cmp.view.Name,
							ViewLevel: model.ToViewLevelType(cmp.view.LevelType).GetViewLevelDescribe(),
							OldValue:  cmp.current.ValuesList(),
							NewValue:  cmp.updated.ValuesList(),
						}
						impact.IsChanged = !sortedEqual(impact.OldValue, impact.NewValue)

						cmp.impactMap[cmp.view.ID] = impact
					} else {
						zap.L().Debug("在父级视图中未找到同名同类型记录，无需继承",
							zap.String("name", deleteRecord.Name),
							zap.String("type", deleteRecord.RType))

						// 即使没有继承记录，也要生成删除操作
						recordChanges = append(recordChanges, model.RecordChange{
							RecordID:  deleteID,
							Name:      deleteRecord.Name,
							Type:      deleteRecord.RType,
							OldValue:  deleteRecord.Value,
							NewValue:  "",
							IsChanged: true,
							Action:    model.RecordChangeDelete.String(),
							ViewID:    cmp.view.ID, // 在当前office视图中删除
						})
					}
				}
			} else {
				// 删除后当前域名在office视图下还有其他记录，只需要删除当前记录即可
				zap.L().Debug("删除记录后当前域名在office视图下还有其他记录，只需要删除当前记录",
					zap.String("name", deleteRecord.Name),
					zap.Int("remaining_records", len(snapShotCopy)))

				// 生成删除操作
				recordChanges = append(recordChanges, model.RecordChange{
					RecordID:  deleteID,
					Name:      deleteRecord.Name,
					Type:      deleteRecord.RType,
					OldValue:  deleteRecord.Value,
					NewValue:  "",
					IsChanged: true,
					Action:    model.RecordChangeDelete.String(),
					ViewID:    cmp.view.ID, // 在当前office视图中删除
				})
			}
		}
	}

	return deleteRecords, recordChanges, nil
}

func (rs *recordService) compareRecord(oldRR *model.DNSRecord, newRR *api.UpdateDnsRecord, newDescription string) (hasChanged bool) {
	if oldRR.Value != newRR.Value {
		hasChanged = true
		return
	}

	if oldRR.Description != newDescription {
		hasChanged = true
		return
	}

	if oldRR.Owner != newRR.Owner {
		hasChanged = true
		return
	}

	if oldRR.Creator != newRR.Creator {
		hasChanged = true
		return
	}

	if oldRR.TTL != int(newRR.TTL) {
		hasChanged = true
		return
	}

	return
}
