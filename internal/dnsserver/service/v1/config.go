package v1

import (
	"archive/zip"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"sort"
	"strings"
	"text/template"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/store"
	"ks-knoc-server/internal/dnsserver/tmpl"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

const (
	SoaAdmin    = "wangzhichao03"
	EmailSuffix = "kuaishou.com"
)

var (
	// 配置文件保存目录
	DefaultConfigDir = "/Users/<USER>/Documents/tmp"
	// 默认的转发器
	defaultForwarders = []string{"************", "*********"}
	// 默认允许查询的源
	defaultAllowQuery = []string{
		"127.0.0.1",
		"::1",
		"10.0.0.0/8",
		"**********/12",
		"************/24",
		"************/24",
	}
)

type ConfigService interface {
	// GetConfigByViewId 根据视图ID获取配置
	GetConfigByViewId(ctx context.Context, viewId string) (*model.ZoneFileResponse, error)
	// GenerateZoneFile 生成区域文件
	GenerateZoneFile(ctx context.Context, viewID, zoneID int64) (*model.ZoneFileResponse, error)
	// GenerateMainConfigCompressedFile 生成主配置文件ZIP压缩包
	GenerateMainConfigCompressedFile(ctx context.Context) (*api.ConfigZipResponse, error)
	// GenerateKeysConfig 生成keys配置文件
	GenerateKeysConfig(ctx context.Context, keysData *api.NamedConfKeysData) error
	// GenerateACLsConfig 生成acls配置文件
	GenerateACLsConfig(ctx context.Context, aclsData *api.NamedConfACLsData) error
	// GenerateViewsConfigFile 生成views配置文件（旧版本，统一配置）
	GenerateViewsConfigFile(ctx context.Context) error
	// GenerateViewsConfigFileByCluster 生成按cluster分组的view配置文件（新版本，职场隔离）
	GenerateViewsConfigFileByCluster(ctx context.Context) error
}

var _ ConfigService = (*configService)(nil)

type configService struct {
	store store.Factory
}

func newConfigService(svc *service) ConfigService {
	return &configService{
		store: svc.store,
	}
}

// GenerateMainConfigInfo 以文件的形式生成DNS配置信息
func (s *configService) GenerateMainConfigInfo(ctx context.Context) (map[string]any, error) {
	zap.L().Info("开始生成DNS主配置文件")
	span, ctx := apm.StartSpan(ctx, "GenerateMainConfig", "service")
	defer span.End()

	// 获取所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("获取视图列表失败", zap.Error(err))
		return nil, fmt.Errorf("获取视图列表失败: %w", err)
	}

	// 生成named.conf.views配置文件（按cluster分组）
	if err := s.GenerateViewsConfigFileByCluster(ctx); err != nil {
		zap.L().Error("生成views配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成views配置文件失败: %w", err)
	}

	// 获取acls配置
	aclsData, err := s.getAclsConfig(ctx, views)
	if err != nil {
		zap.L().Error("获取acls配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取acls配置失败: %w", err)
	}

	// 生成named.conf.acls配置文件
	if err := s.GenerateACLsConfig(ctx, aclsData); err != nil {
		zap.L().Error("生成acls配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成acls配置文件失败: %w", err)
	}

	// 获取keys配置
	keysData, err := s.getKeysConfig(views)
	if err != nil {
		zap.L().Error("获取keys配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取keys配置失败: %w", err)
	}

	// 生成named.conf.keys配置文件
	if err := s.GenerateKeysConfig(ctx, keysData); err != nil {
		zap.L().Error("生成keys配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成keys配置文件失败: %w", err)
	}

	// 生成named.conf.views配置文件（按cluster分组）
	if err := s.GenerateViewsConfigFileByCluster(ctx); err != nil {
		zap.L().Error("生成views配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成views配置文件失败: %w", err)
	}

	// 生成named.conf.zones配置文件
	viewZonesConfig, err := s.getZonesConfig(ctx, views)
	if err != nil {
		zap.L().Error("生成zones配置失败", zap.Error(err))
		return nil, fmt.Errorf("生成zones配置失败: %w", err)
	}

	if err := s.getZonesConfigFile(ctx, viewZonesConfig); err != nil {
		zap.L().Error("生成zones配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成zones配置文件失败: %w", err)
	}

	// TODO: 后续实现其他配置文件的生成
	// - named.conf.controls
	// - named.conf.options
	// - named.conf (主配置文件)
	result := map[string]any{
		"status":  "success",
		"message": "DNS配置文件生成成功",
		"files":   []string{"named.conf.keys", "named.conf.acls", "named.conf.views", "named.conf.zones"},
		"path":    DefaultConfigDir,
	}

	return result, nil
}

// GenerateZoneFile 生成区域文件
func (s *configService) GenerateZoneFile(ctx context.Context, viewID, zoneID int64) (*model.ZoneFileResponse, error) {
	zap.L().Info("GenerateZoneFile", zap.Int64("view_id", viewID), zap.Int64("zone_id", zoneID))
	span, ctx := apm.StartSpan(ctx, "GenerateZoneFile", "service")
	defer span.End()

	// 获取区域
	zone, err := s.store.Zone().GetZoneByID(ctx, zoneID)
	if err != nil {
		zap.L().Error("获取区域失败", zap.Error(err))
		return nil, err
	}

	// 获取视图
	view, err := s.store.View().GetViewByIDs(ctx, []int64{viewID})
	if err != nil {
		zap.L().Error("获取视图失败", zap.Error(err))
		return nil, err
	}

	if len(view) == 0 {
		return nil, errors.New("视图不存在")
	}

	viewType := model.ToViewLevelType(view[0].LevelType)
	if viewType != model.ViewLevelTypeOffice {
		return nil, errors.New("只允许导出Office层级的区域文件, 不允许导出Biz和Default层级的配置, 当前视图类型: " + viewType.String())
	}

	// 获取视图继承链
	viewHierarchy := model.NewViewList()
	viewHierarchy, err = s.getViewHierarchy(ctx, viewID, &viewHierarchy)
	if err != nil {
		zap.L().Error("获取视图继承链失败", zap.Error(err), zap.Int64("view_id", viewID))
		return nil, err
	}

	// 构建一个viewMap
	viewMap := make(map[int64]*model.View)
	for _, v := range viewHierarchy {
		viewMap[v.ID] = v
	}

	// 按域名存储所有解析记录
	// 格式: domainRecords[优先级][域名] = 记录切片
	domainRecords := make(map[int]map[string][]*model.DNSRecord)
	levels := make([]int, 0)

	// 按视图获取所有记录，从优先级低到高
	for _, vh := range viewHierarchy {
		levels = append(levels, vh.Level)
		records, err := s.store.Record().GetRecordList(ctx, zone.ID, vh.ID, "", "", "")
		if err != nil {
			zap.L().Error("获取记录失败", zap.Error(err), zap.Int64("view_id", vh.ID), zap.Int64("zone_id", zone.ID))
			return nil, err
		}

		// 按域名、记录类型分组记录
		for _, record := range records {
			// 如果域名不存在，初始化
			viewLevel := viewMap[record.ViewID].Level
			if _, ok := domainRecords[viewLevel]; !ok {
				domainRecords[viewLevel] = make(map[string][]*model.DNSRecord)
			}

			// 如果该域名下的视图不存在，初始化
			if _, ok := domainRecords[viewLevel][record.Name]; !ok {
				domainRecords[viewLevel][record.Name] = make([]*model.DNSRecord, 0)
			}

			// 添加记录
			domainRecords[viewLevel][record.Name] = append(domainRecords[viewLevel][record.Name], record)
		}
	}

	// 针对viewLevel进行排序
	sort.Ints(levels)

	zap.L().Info("levels", zap.Any("levels", levels))

	// 初始化一个map用来保存合并后的结果
	mergedRecords := make(map[string][]*model.DNSRecord)
	for _, level := range levels {
		for domain, records := range domainRecords[level] {
			if _, ok := mergedRecords[domain]; !ok {
				mergedRecords[domain] = make([]*model.DNSRecord, 0)
			}
			mergedRecords[domain] = records
		}
	}

	// 替换原有的记录获取部分
	records := make([]*model.DNSRecord, 0)
	for _, record := range mergedRecords {
		records = append(records, record...)
	}

	// 获取工作目录
	workDir, err := os.Getwd()
	if err != nil {
		zap.L().Error("获取工作目录失败", zap.Error(err))
		return nil, err
	}

	zap.L().Info("工作目录", zap.String("work_dir", workDir))

	// 从常量中获取对应的模板，一般模板不会经常发生变化
	zoneTmpl, err := template.New("zone").Parse(tmpl.ZoneTemplate)
	if err != nil {
		zap.L().Error("解析模板失败", zap.Error(err))
		return nil, err
	}

	config := api.ZoneConfig{
		TTL: 3600,
		SOA: api.SOAConfig{
			PrimaryNS:   "ns1." + zone.Name,
			AdminEmail:  SoaAdmin + "." + EmailSuffix,
			Serial:      time.Now().Format("20060102") + "01",
			Refresh:     "3H",
			Retry:       "15M",
			Expire:      "1W",
			NegativeTTL: "1D",
		},
		NSRecords: []string{"ns1", "ns2"},
		NSServers: map[string]string{
			"ns1": "***********",
			"ns2": "***********",
		},
		ARecords:     make(map[string][]api.ARecord),
		CNAMERecords: make([]api.CNAMERecord, 0),
	}

	for _, record := range records {
		switch strings.ToLower(record.RType) {
		case "a":
			if _, ok := config.ARecords[record.Name]; !ok {
				config.ARecords[record.Name] = make([]api.ARecord, 0)
			}
			config.ARecords[record.Name] = append(config.ARecords[record.Name], api.ARecord{
				Domain: record.Name,
				IP:     record.Value,
			})
		case "cname":
			config.CNAMERecords = append(config.CNAMERecords, api.CNAMERecord{
				Domain: record.Name,
				Target: record.Value,
			})
		default:
			zap.L().Warn("不支持的记录类型", zap.String("record_type", record.RType))
			return nil, fmt.Errorf("不支持的记录类型: %s", record.RType)
		}
	}

	var buf bytes.Buffer
	err = zoneTmpl.Execute(&buf, config)
	if err != nil {
		zap.L().Error("生成区域文件内容失败", zap.Error(err))
		return nil, err
	}

	// 构建响应
	response := &model.ZoneFileResponse{
		Content:     buf.String(),
		ZoneName:    zone.Name,
		ViewID:      viewID,
		ContentType: "text/plain",
		FileName:    zone.Name + ".zone",
	}

	return response, nil
}

func (s *configService) GetConfigByViewId(ctx context.Context, viewIdStr string) (*model.ZoneFileResponse, error) {

	// // 解析视图ID
	// viewId, err := strconv.ParseInt(viewIdStr, 10, 64)
	// if err != nil {
	// 	zap.L().Error("解析视图ID失败", zap.Error(err), zap.String("view_id", viewIdStr))
	// 	return nil, errors.New("无效的视图ID")
	// }

	// // 这里我们可以直接硬编码一个示例区域名称，或者从数据库中获取
	// // 在实际情况中，应该从请求参数中获取或从数据库中查找
	// zoneName := "example.com"

	// 调用生成区域文件的方法
	return nil, nil
}

func (s *configService) GenerateMainConfigCompressedFile(ctx context.Context) (*api.ConfigZipResponse, error) {
	zap.L().Info("开始生成DNS主配置文件ZIP压缩包（优化版本）")
	span, ctx := apm.StartSpan(ctx, "GenerateMainConfig", "service")
	defer span.End()

	// 1. 一次性加载所有配置数据到缓存
	cache, err := s.loadConfigDataCache(ctx)
	if err != nil {
		zap.L().Error("加载配置数据缓存失败", zap.Error(err))
		return nil, fmt.Errorf("加载配置数据缓存失败: %w", err)
	}

	// 初始化allFiles用来保存所有需要打包的文件
	var allFiles []*api.ConfigFile

	// 2. 获取keys配置（使用缓存中的views）
	keysData, err := s.getKeysConfig(cache.Views)
	if err != nil {
		zap.L().Error("获取keys配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取keys配置失败: %w", err)
	}

	// 生成named.conf.keys配置文件
	keysFile, err := s.generateKeysConfigContent(ctx, keysData)
	if err != nil {
		zap.L().Error("生成keys配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成keys配置文件失败: %w", err)
	}
	allFiles = append(allFiles, keysFile)

	// 3. 获取acls配置（使用缓存中的views）
	aclsData, err := s.getAclsConfig(ctx, cache.Views)
	if err != nil {
		zap.L().Error("获取acls配置失败", zap.Error(err))
		return nil, fmt.Errorf("获取acls配置失败: %w", err)
	}

	// 生成named.conf.acls配置文件
	aclsFile, err := s.generateACLsConfigContent(ctx, aclsData)
	if err != nil {
		zap.L().Error("生成acls配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成acls配置文件失败: %w", err)
	}
	allFiles = append(allFiles, aclsFile)

	// 4. 生成named.conf.views配置文件（按cluster分组）
	configsByCluster, err := s.generateViewConfigByCluster(ctx)
	if err != nil {
		zap.L().Error("生成views配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成views配置文件失败: %w", err)
	}

	// 将所有cluster的配置文件添加到ZIP中，保持目录结构
	for clusterName, configs := range configsByCluster {
		for _, config := range configs {
			// 修改文件名以包含cluster路径
			clusterConfig := &api.ConfigFile{
				Name:    fmt.Sprintf("view/%s/%s", clusterName, config.Name),
				Content: config.Content,
			}
			allFiles = append(allFiles, clusterConfig)
		}
	}

	// 5. 生成zones配置（使用缓存中的views）
	viewZonesConfig, err := s.getZonesConfig(ctx, cache.Views)
	if err != nil {
		zap.L().Error("生成zones配置失败", zap.Error(err))
		return nil, fmt.Errorf("生成zones配置失败: %w", err)
	}

	// 生成named.conf.zones配置文件
	zonesFiles, err := s.getZonesConfigObject(ctx, viewZonesConfig)
	if err != nil {
		zap.L().Error("生成zones配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成zones配置文件失败: %w", err)
	}
	allFiles = append(allFiles, zonesFiles...)

	// 6. 生成db配置文件（使用缓存数据）
	// dbFiles, err := s.buildZoneDBConfig(ctx, cache.Zones, cache.Views)
	dbFiles, err := s.buildZoneDBConfigWithCache(ctx, cache)
	if err != nil {
		zap.L().Error("生成db配置文件失败", zap.Error(err))
		return nil, fmt.Errorf("生成db配置文件失败: %w", err)
	}
	zap.L().Info("生成db配置文件完成", zap.Int("db_files_count", len(dbFiles)))
	allFiles = append(allFiles, dbFiles...)

	// 计算views文件数量
	viewsFilesCount := 0
	for _, configs := range configsByCluster {
		viewsFilesCount += len(configs)
	}

	zap.L().Info("所有配置文件生成完成",
		zap.Int("total_files", len(allFiles)),
		zap.Int("keys_files", 1),
		zap.Int("acls_files", 1),
		zap.Int("views_files", viewsFilesCount),
		zap.Int("clusters", len(configsByCluster)),
		zap.Int("zones_files", len(zonesFiles)),
		zap.Int("db_files", len(dbFiles)))

	// 7. 打包成ZIP
	zipData, err := s.createZipArchive(allFiles)
	if err != nil {
		zap.L().Error("打包ZIP文件失败", zap.Error(err))
		return nil, fmt.Errorf("打包ZIP文件失败: %w", err)
	}

	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("dns_config_%s.zip", timestamp)

	zap.L().Info("DNS配置文件ZIP压缩包生成成功",
		zap.String("fileName", fileName),
		zap.Int("fileCount", len(allFiles)),
		zap.Int("zipSize", len(zipData)),
		zap.Int("cached_views", len(cache.Views)),
		zap.Int("cached_zones", len(cache.Zones)),
		zap.Int("cached_servers", len(cache.Servers)),
		zap.Int("cached_records", len(cache.RecordsByZoneView)))

	return &api.ConfigZipResponse{
		FileName:    fileName,
		ContentType: "application/zip",
		Data:        zipData,
	}, nil
}

// createZipArchive 将配置文件打包成ZIP压缩包
func (s *configService) createZipArchive(files []*api.ConfigFile) ([]byte, error) {
	var buf bytes.Buffer
	zipWriter := zip.NewWriter(&buf)
	defer zipWriter.Close()

	zap.L().Info("开始创建ZIP压缩包", zap.Int("files_count", len(files)))

	for i, file := range files {
		zap.L().Debug("添加文件到ZIP",
			zap.Int("index", i),
			zap.String("file_name", file.Name),
			zap.Int("file_size", len(file.Content)))

		// 创建文件在ZIP中的路径
		writer, err := zipWriter.Create(file.Name)
		if err != nil {
			return nil, fmt.Errorf("创建ZIP文件 %s 失败: %w", file.Name, err)
		}

		// 写入文件内容
		if _, err := io.Copy(writer, bytes.NewReader(file.Content)); err != nil {
			return nil, fmt.Errorf("写入ZIP文件 %s 内容失败: %w", file.Name, err)
		}
	}

	if err := zipWriter.Close(); err != nil {
		return nil, fmt.Errorf("关闭ZIP文件失败: %w", err)
	}

	zap.L().Info("ZIP压缩包创建完成", zap.Int("zip_size", buf.Len()))
	return buf.Bytes(), nil
}
