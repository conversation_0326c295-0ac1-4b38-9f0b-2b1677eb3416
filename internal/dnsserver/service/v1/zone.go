package v1

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	param "ks-knoc-server/internal/common/base/api/dnsserver"
	"ks-knoc-server/internal/common/base/constants"
	message "ks-knoc-server/internal/common/base/message/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/store"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

var (
	ErrZoneNameInvalid            = errors.New("zone name invalid")
	ErrZoneTypeInvalid            = errors.New("zone type invalid")
	ErrZoneNotExist               = errors.New("区域不存在, 请检查区域名称")
	ErrForwarderInvalid           = errors.New("forwarder ip invalid")
	ErrOnlyForwardZoneCanBeUpdate = errors.New("当前仅支持更新forward类型的zone")
)

type ZoneService interface {
	// CreateZone 创建zone
	CreateZone(ctx context.Context, zone *param.CreateZoneParams) error
	// GetZoneList 获取zone列表
	GetZoneList(ctx context.Context) ([]*message.QueryZoneMessage, error)
	// GetZoneByID 获取zone详情
	GetZoneByID(ctx context.Context, zoneID int64) (*model.Zone, error)
	// DeleteZone 删除zone
	DeleteZone(ctx context.Context, zoneID int64) error
	// GetZoneTypes 获取zone类型
	GetZoneTypes(ctx context.Context) (any, error)
	// UpdateZone 更新zone
	// UpdateZone(ctx context.Context, zone *api.UpdateZoneRequest) error
}

type zoneService struct {
	store store.Factory
}

func newZoneService(srv *service) ZoneService {
	return &zoneService{
		store: srv.store,
	}
}

func (zs *zoneService) GetZoneTypes(ctx context.Context) (any, error) {
	zts := model.GetZoneTypes()

	resp := make([]message.QueryZoneTypeInfo, 0)
	for _, zt := range zts {
		resp = append(resp, message.QueryZoneTypeInfo{
			ZoneTypeCode: zt.String(),
			ZoneTypeName: zt.ShowName(),
		})
	}

	return resp, nil
}

func (zs *zoneService) DeleteZone(ctx context.Context, zoneID int64) error {
	zap.L().Debug("DeleteZone Service Called")
	span, ctx := apm.StartSpan(ctx, "DeleteZone", "service")
	defer span.End()

	// 需要确保该zone在任何视图下都不存在解析记录
	// viewID = 0, 表示不将view_id作为查询条件
	// name = "", 表示不将name作为查询条件
	// rrType = "", 表示不将rr_type作为查询条件
	// rrValue = "", 表示不将rr_value作为查询条件
	records, err := zs.store.Record().GetRecordList(ctx, zoneID, 0, "", "", "")
	if err != nil {
		zap.L().Error("删除zone失败", zap.Error(err))
		return errors.New("删除zone失败")
	}

	if len(records) > 0 {
		return errors.New("zone下存在解析记录，不能删除")
	}

	if err := zs.store.Zone().DeleteZoneByID(ctx, zoneID); err != nil {
		zap.L().Error("删除zone失败", zap.Error(err))
		return errors.New("删除zone失败")
	}

	return nil
}

func (zs *zoneService) GetZoneByID(ctx context.Context, zoneID int64) (*model.Zone, error) {
	zap.L().Debug("GetZoneByID Service Called")
	span, ctx := apm.StartSpan(ctx, "GetZoneByID", "service")
	defer span.End()

	zone, err := zs.store.Zone().GetZoneByID(ctx, zoneID)
	if err != nil {
		return nil, err
	}

	return zone, nil
}

func (zs *zoneService) GetZoneList(ctx context.Context) ([]*message.QueryZoneMessage, error) {
	zap.L().Debug("GetZoneList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetZoneList", "service")
	defer span.End()

	zones, err := zs.store.Zone().GetAllZones(ctx)
	if err != nil {
		return nil, err
	}

	zoneList := make([]*message.QueryZoneMessage, 0)
	for _, z := range zones {
		zoneResponse := &message.QueryZoneMessage{
			ID:          z.ID,
			Name:        z.Name,
			ZoneType:    z.ZoneType,
			Description: z.Description,
			CreateTime:  z.CreateTime,
			UpdateTime:  z.UpdateTime,
		}

		records, err := zs.store.Record().GetRecordList(ctx, z.ID, 0, "", "", "")
		if err != nil {
			zap.L().Error("获取zone下的记录失败", zap.Error(err))
			return nil, err
		}

		recordSet := make(map[string]struct{})
		for _, r := range records {
			recordSet[r.Name] = struct{}{}
		}

		zoneResponse.RecordCount = int64(len(recordSet))

		zoneList = append(zoneList, zoneResponse)
	}

	return zoneList, nil
}

// CreateZone 创建zone
func (zs *zoneService) CreateZone(ctx context.Context, zone *param.CreateZoneParams) error {
	zap.L().Debug("CreateZone Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateZone", "service")
	defer span.End()

	// 判断zone type是否合法
	zt := model.ToZoneType(zone.ZoneType)
	if !zt.IsValid() {
		return errors.New("区域类型不合法, 可用的类型为: domain, blacklist, forward, host, rpz")
	}

	// 首先查询所有的zone，然后逐一做比对
	zones, err := zs.store.Zone().GetAllZones(ctx)
	if err != nil {
		return err
	}

	// 逐一比对zone是否有冲突
	for _, z := range zones {
		// 如果zone的名称相同，则认为有冲突
		if z.Name == zone.Name {
			return fmt.Errorf("已存在同名的zone [%s]", zone.Name)
		}

		// 如果添加的zone是forward类型的zone，暂时不支持
		if zone.ZoneType == model.ZoneTypeForward.String() {
			return fmt.Errorf("forward类型的zone不能添加")
		}

		// 如果添加的zone是已经存在zone的父域名，则认为有冲突
		// 比如：
		// 1. 如果z.Name = "example.com"，zone.Name = "com"，则认为有冲突
		// 2. 如果z.Name = "example.com"，zone.Name = "example.com"，则认为有冲突
		// 3. 如果z.Name = "example.com"，zone.Name = "example.cn"，则认为没有冲突
		if strings.HasSuffix(z.Name, constants.Dot+zone.Name) {
			return fmt.Errorf("已存在子域名 [%s] 不能添加父域名 [%s]", z.Name, zone.Name)
		}

		// 如果添加的zone是已经存在zone的子域名，则认为有冲突
		// 比如：
		// 如果z.Name = "example.com", zone.Name = "a.example.com"，则认为有冲突
		if strings.HasSuffix(zone.Name, constants.Dot+z.Name) {
			return fmt.Errorf("已存在父域名 [%s] 不能添加子域名 [%s]", z.Name, zone.Name)
		}
	}

	// 创建zone
	now := time.Now().Unix()
	if err := zs.store.Zone().CreateZone(ctx, &model.Zone{
		Name:        zone.Name,
		ZoneType:    zone.ZoneType,
		Description: zone.Description,
		CreateTime:  now,
		UpdateTime:  now,
	}); err != nil {
		return err
	}

	return nil
}

// func (zs *zoneService) UpdateZone(ctx context.Context, zone *api.UpdateZoneRequest) error {
// 	zap.L().Debug("UpdateZone Service Called")
// 	span, ctx := apm.StartSpan(ctx, "UpdateZone", "service")
// 	defer span.End()

// 	// 先查一下要更新的zone是否存在
// 	z, err := zs.store.Zone().GetZone(ctx, dns.Fqdn(zone.Name), zone.OfficeId)
// 	if err != nil {
// 		return ErrZoneNotExist
// 	}

// 	// 限定zone的更新类型为forward
// 	if z.ZoneType != "forward" {
// 		return ErrOnlyForwardZoneCanBeUpdate
// 	}

// 	if forwarders, err := api.CheckForwarders(zone.Forwarders); err != nil {
// 		return ErrForwarderInvalid
// 	} else {
// 		z.Forwarders = forwarders
// 	}

// 	// 更新zone，仅仅更新forwarders
// 	if err := zs.store.Zone().UpdateZone(ctx, z.ID, z.Forwarders); err != nil {
// 		return err
// 	}

// 	return nil
// }
