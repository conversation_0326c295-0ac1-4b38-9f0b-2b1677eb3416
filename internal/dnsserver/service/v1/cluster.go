package v1

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	param "ks-knoc-server/internal/common/base/api/dnsserver"
	message "ks-knoc-server/internal/common/base/message/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/bind/tsig"
	"ks-knoc-server/internal/dnsserver/store"
	"ks-knoc-server/pkg/array"

	"github.com/spf13/viper"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type ClusterService interface {
	CreateCluster(ctx context.Context, cluster *param.ClusterParam) error
	GetClusterList(ctx context.Context) ([]*message.QueryClusterMessage, error)
	UpdateCluster(ctx context.Context, param *param.UpdateClusterParam) error
}

var _ ClusterService = (*clusterService)(nil)

type clusterService struct {
	store store.Factory
}

func newClusterService(svc *service) ClusterService {
	return &clusterService{
		store: svc.store,
	}
}

func (s *clusterService) GetClusterList(ctx context.Context) ([]*message.QueryClusterMessage, error) {
	zap.L().Debug("GetClusterList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetClusterList", "service")
	defer span.End()

	// 调用数据库获取集群列表
	clusters, err := s.store.Cluster().GetClusterList(ctx)
	if err != nil {
		zap.L().Error("GetClusterList Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	// 遍历集群列表，构建集群信息的mapping
	msg := make([]*message.QueryClusterMessage, 0)
	for _, cluster := range clusters {
		// 解析forwarders
		forwarders := make([]string, 0)
		if err := json.Unmarshal(cluster.Forwarders, &forwarders); err != nil {
			zap.L().Error("GetClusterList Forwarders Unmarshal Error", zap.Error(err))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return nil, err
		}

		// 解析loadBalanceIPs
		loadBalanceIPs := make([]string, 0)
		if err := json.Unmarshal(cluster.LoadBalanceIPs, &loadBalanceIPs); err != nil {
			zap.L().Error("GetClusterList LoadBalanceIPs Unmarshal Error", zap.Error(err))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return nil, err
		}

		// 构建响应集群信息
		msg = append(msg, &message.QueryClusterMessage{
			ID:             cluster.ID,
			Name:           cluster.Name,
			Status:         cluster.Status,
			Forwarders:     forwarders,
			ForVPN:         cluster.ForVPN,
			ForTest:        cluster.ForTest,
			ForDrill:       cluster.ForDrill,
			HasLoadBalance: cluster.HasLoadBalance,
			LoadBalanceIPs: loadBalanceIPs,
			CreateTime:     cluster.CreateTime,
			UpdateTime:     cluster.UpdateTime,
			Description:    cluster.Description,
		})
	}

	return msg, nil
}

// CreateCluster 创建集群
func (s *clusterService) CreateCluster(ctx context.Context, param *param.ClusterParam) error {
	zap.L().Debug("CreateCluster Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateCluster", "service")
	defer span.End()

	// 当前时间戳
	now := time.Now().Unix()

	// 初始化cluster
	clusterModel := &model.Cluster{
		Name:           param.Name,
		Description:    param.Description,
		Status:         model.ClusterStatusInactive.String(), // 默认创建的时候是未激活的
		ForVPN:         param.ForVPN,
		ForTest:        param.ForTest,
		ForDrill:       param.ForDrill,
		HasLoadBalance: param.HasLoadBalance,
		SecretControl:  param.SecretControl,
		CreateTime:     now,
		UpdateTime:     now,
	}

	// 初始化forwarders
	forwarders, err := json.Marshal(param.Forwarders)
	if err != nil {
		zap.L().Error("CreateCluster Forwarders Marshal Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}
	clusterModel.Forwarders = forwarders

	// 初始化loadBalanceIPs
	loadBalanceIPs, err := json.Marshal(param.LoadBanlanceIPs)
	if err != nil {
		zap.L().Error("CreateCluster LoadBalanceIPs Marshal Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}
	clusterModel.LoadBalanceIPs = loadBalanceIPs

	// 生成TSIG Key
	rndcKeyInfo := tsig.ToTSIGKeyAlgo(param.RndcKeyArgo)
	if !rndcKeyInfo.IsValid() {
		zap.L().Error("RNDC密钥算法不合法", zap.String("rndcKeyArgo", param.RndcKeyArgo))
		e := apm.CaptureError(ctx, errors.New("RNDC密钥算法不合法"))
		e.Send()
		return errors.New("RNDC密钥算法不合法")
	}

	// 动态生成一个rndc的key
	rndcKey := rndcKeyInfo.GenerateTSIGKey()
	zap.L().Info("Generate RNDC Key", zap.String("rndcKey", rndcKey))

	// 从配置文件中获取对应的密钥
	secret := viper.GetString("keyToken")
	if secret == "" {
		zap.L().Error("RNDC密钥不存在")
		e := apm.CaptureError(ctx, errors.New("RNDC密钥不存在"))
		e.Send()
		return errors.New("RNDC密钥不存在")
	}

	// Rndckey需要加密保存到数据库，不可以进行明文的保存
	encryptedRndcKey, md5Hash, err := tsig.EncryptKey(secret, rndcKey)
	if err != nil {
		zap.L().Error("EncryptRndcKey Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 初始化rndcKeyInfo
	clusterModel.ClusterRndcKeyInfo = &model.ClusterRndcKeyInfo{
		RndcKeyAlgo: rndcKeyInfo.String(),
		RndcKey:     encryptedRndcKey,
		RndcKeyMd5:  md5Hash,
	}

	// 调用数据库创建集群
	if err := s.store.Cluster().CreateCluster(ctx, clusterModel); err != nil {
		zap.L().Error("CreateCluster Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	return nil
}

func (s *clusterService) UpdateCluster(ctx context.Context, param *param.UpdateClusterParam) error {
	zap.L().Debug("UpdateCluster Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateCluster", "service")
	defer span.End()

	// 首先看要更新的集群存不存在
	cluster, err := s.store.Cluster().GetClusterByID(ctx, param.ID)
	if err != nil {
		zap.L().Error("根据ID查询集群失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	// 初始化updateMap
	updateMap := make(map[string]any)

	// 看看哪些字段需要更新
	if param.Name != cluster.Name {
		updateMap["name"] = param.Name
	}

	if param.Description != cluster.Description {
		updateMap["description"] = param.Description
	}

	if param.Description != cluster.Description {
		updateMap["description"] = param.Description
	}

	if param.Status != cluster.Status {
		switch param.Status {
		case model.ClusterStatusActive.String(), model.ClusterStatusInactive.String():
			updateMap["status"] = param.Status
		default:
			zap.L().Error("集群状态不合法，只能是active或者inactive", zap.String("status", param.Status))
			e := apm.CaptureError(ctx, errors.New("集群状态不合法，只能是active或者inactive"))
			e.Send()
			return errors.New("集群状态不合法，只能是active或者inactive")
		}
	}

	if param.ForVPN != cluster.ForVPN {
		updateMap["for_vpn"] = param.ForVPN
	}

	if param.ForTest != cluster.ForTest {
		updateMap["for_test"] = param.ForTest
	}

	if param.ForDrill != cluster.ForDrill {
		updateMap["for_drill"] = param.ForDrill
	}

	if param.HasLoadBalance != cluster.HasLoadBalance {
		updateMap["has_load_balance"] = param.HasLoadBalance
	}

	forwarders := make([]string, 0)
	if err := json.Unmarshal(cluster.Forwarders, &forwarders); err != nil {
		zap.L().Error("Forwarders反序列化失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	if !array.StringArray(forwarders).ArrayEqual(param.Forwarders) {
		updateMap["forwarders"] = param.Forwarders
	}

	loadBalanceIPs := make([]string, 0)
	if err := json.Unmarshal(cluster.LoadBalanceIPs, &loadBalanceIPs); err != nil {
		zap.L().Error("LoadBalanceIPs反序列化失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	if !array.StringArray(loadBalanceIPs).ArrayEqual(param.LoadBanlanceIPs) {
		updateMap["load_banlance_ips"] = param.LoadBanlanceIPs
	}

	if len(updateMap) == 0 {
		zap.L().Warn("没有需要更新的字段", zap.Int64("id", param.ID))
		return nil
	}

	// 更新集群
	if err := s.store.Cluster().UpdateCluster(ctx, param.ID, updateMap); err != nil {
		zap.L().Error("UpdateCluster Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	return nil
}
