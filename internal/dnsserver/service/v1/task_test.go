package v1

import (
	"context"
	"encoding/json"
	"testing"

	"gorm.io/gorm"

	"ks-knoc-server/config"
	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/mq"
	"ks-knoc-server/internal/dnsserver/store"
)

const (
	// 测试配置文件路径
	configPath = "/Users/<USER>/Documents/kwai/ks-knoc-server/config/conf/config_dns.yaml"
)

// 全局测试资源
var (
	testDB            *gorm.DB
	testStoreFactory  store.Factory
	testRecordService *recordService
	err               error
	ctx               context.Context
)

// 创建一个临时结构体用于显示，将 target_info 解码为明文
type TaskDisplay struct {
	ID           int64  `json:"id"`
	Status       string `json:"status"`
	TargetInfo   any    `json:"target_info"` // 这里用 interface{} 来显示解码后的内容
	Operator     string `json:"operator"`
	ExecuteTime  int64  `json:"execute_time"`
	TotalDetails int    `json:"total_details"`
	SuccessCount int    `json:"success_count"`
	FailedCount  int    `json:"failed_count"`
	CreateTime   int64  `json:"create_time"`
	UpdateTime   int64  `json:"update_time"`
}

// setupTestDependencies 设置测试依赖, 数据库，配置文件，cache
func setupTestDependencies(t *testing.T) {
	if err := config.NewConf(configPath); err != nil {
		t.Fatalf("初始化配置失败: %v", err)
	}

	testDB, err = db.NewMySQLOptions().Init()
	if err != nil {
		t.Fatalf("初始化数据库失败: %v", err)
	}

	testCache, err := cache.NewBigCache().Init()
	if err != nil {
		t.Fatalf("初始化缓存失败: %v", err)
	}

	testProducer, err := mq.NewKafkaOptions().Init()
	if err != nil {
		t.Fatalf("初始化kafka失败: %v", err)
	}

	testStoreFactory = &store.DataStore{
		Db:       testDB,
		Cache:    testCache,
		Producer: testProducer,
	}

	testRecordService = &recordService{
		store: testStoreFactory,
	}
}

func TestGenerateTasks(t *testing.T) {
	// 初始化测试依赖
	setupTestDependencies(t)

	// 初始化context
	ctx = context.Background()

	// 是否模拟更新
	dryRun := true
	// 模拟生成changes
	req := &api.UpdateDnsRecordRequest{
		DryRun:      &dryRun,
		ZoneID:      1, // zone rpz
		ViewID:      2, // view dev
		Name:        "k.com",
		Description: "for test",
		Records: []api.UpdateDnsRecord{
			{
				RecordID: 2175,
				RType:    "CNAME",
				Value:    "yilong.it.eff",
				TTL:      3600,
				Creator:  "maxiaoyu",
				Owner:    "maxiaoyu",
			},
		},
		DeleteRecordIds: []int64{},
	}

	// rpz zone
	rpzZone := &model.Zone{
		ID:          1,
		Name:        "rpz",
		ZoneType:    "rpz",
		Description: "",
	}

	// 获取对应的变更影响
	updateResult, err := testRecordService.UpdateRecord(ctx, req)
	if err != nil {
		t.Fatalf("更新记录失败: %v", err)
	}

	t.Log("获取要更新的记录成功")

	// 获取当前的变更影响
	changes := updateResult.Changes
	for _, impact := range changes.Impacts {
		t.Logf("impact: %+v", impact)
	}

	// 生成任务
	task, taskDetails, err := testRecordService.generateTasks(ctx, rpzZone, changes)
	if err != nil {
		t.Fatalf("生成任务失败: %v", err)
	}

	// 解码 target_info
	var targetInfo any
	if err := json.Unmarshal(task.TargetInfo, &targetInfo); err != nil {
		t.Fatalf("Failed to unmarshal target_info: %v", err)
	}

	taskDisplay := TaskDisplay{
		ID:           task.ID,
		Status:       string(task.Status),
		TargetInfo:   targetInfo,
		Operator:     task.Operator,
		ExecuteTime:  task.ExecuteTime,
		TotalDetails: task.TotalDetails,
		SuccessCount: task.SuccessCount,
		FailedCount:  task.FailedCount,
		CreateTime:   task.CreateTime,
		UpdateTime:   task.UpdateTime,
	}

	taskJSON, err := json.MarshalIndent(taskDisplay, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal task to JSON: %v", err)
	}
	t.Logf("Task: \n%s", string(taskJSON))

	t.Logf("Task Details:")
	for i, detail := range taskDetails {
		detailJSON, err := json.MarshalIndent(detail, "", "  ")
		if err != nil {
			t.Fatalf("Failed to marshal task detail to JSON: %v", err)
		}
		t.Logf("--- Detail %d ---\n%s", i+1, string(detailJSON))
	}
}

// TestAddOneRecord 测试添加一条记录
func TestAddOneRecord(t *testing.T) {
	setupTestDependencies(t)
	ctx := context.Background()

	// 模拟真实更新
	dryRun := false
	// 模拟生成changes
	req := &api.UpdateDnsRecordRequest{
		DryRun:      &dryRun,
		ZoneID:      1, // zone rpz
		ViewID:      2, // view dev
		Name:        "wzc03.com",
		Description: "for test",
		Records: []api.UpdateDnsRecord{
			{
				RecordID: 0,
				RType:    "A",
				Value:    "*******",
				TTL:      3600,
				Creator:  "maxiaoyu",
				Owner:    "maxiaoyu",
			},
		},
		DeleteRecordIds: make([]int64, 0),
	}

	// rpz zone
	rpzZone := &model.Zone{
		ID:          1,
		Name:        "rpz",
		ZoneType:    "rpz",
		Description: "",
	}

	// 获取对应的变更影响
	result, err := testRecordService.UpdateRecord(ctx, req)
	if err != nil {
		t.Fatalf("更新记录失败: %v", err)
	}

	t.Logf("获取变更影响成功: %+v", result)

	changes := result.Changes
	for _, impact := range changes.Impacts {
		t.Logf("impact: %+v", impact)
	}

	// 生成任务
	task, taskDetails, err := testRecordService.generateTasks(ctx, rpzZone, changes)
	if err != nil {
		t.Fatalf("生成任务失败: %v", err)
	}

	t.Log("生成任务成功")
	t.Log("生成任务详情成功")

	// 解码 target_info
	var targetInfo any
	if err := json.Unmarshal(task.TargetInfo, &targetInfo); err != nil {
		t.Fatalf("Failed to unmarshal target_info: %v", err)
	}

	taskDisplay := TaskDisplay{
		ID:           task.ID,
		Status:       string(task.Status),
		TargetInfo:   targetInfo,
		Operator:     task.Operator,
		ExecuteTime:  task.ExecuteTime,
		TotalDetails: task.TotalDetails,
		SuccessCount: task.SuccessCount,
		FailedCount:  task.FailedCount,
		CreateTime:   task.CreateTime,
		UpdateTime:   task.UpdateTime,
	}

	taskJSON, err := json.MarshalIndent(taskDisplay, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal task to JSON: %v", err)
	}
	t.Logf("Task: \n%s", string(taskJSON))

	t.Logf("Task Details:")
	for i, detail := range taskDetails {
		detailJSON, err := json.MarshalIndent(detail, "", "  ")
		if err != nil {
			t.Fatalf("Failed to marshal task detail to JSON: %v", err)
		}
		t.Logf("--- Detail %d ---\n%s", i+1, string(detailJSON))
	}
}
