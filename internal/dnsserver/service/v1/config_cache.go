package v1

import (
	"context"
	"fmt"

	model "ks-knoc-server/internal/common/base/model/dnsserver"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// ConfigDataCache 配置数据缓存结构
type ConfigDataCache struct {
	// 基础数据
	Views   []*model.View
	Zones   []*model.Zone
	Servers []*model.Server

	// 索引映射
	ViewMap   map[int64]*model.View
	ZoneMap   map[int64]*model.Zone
	ServerMap map[int64]*model.Server

	// 视图相关缓存
	ViewsByType        map[string][]*model.View
	ViewHierarchyCache map[int64][]*model.View
	ServersByViewID    map[int64][]*model.Server

	// DNS记录缓存
	RecordsByZoneView map[string][]*model.DNSRecord // key: "zoneID_viewID"

	// 服务器分类缓存
	MasterServers []*model.Server
	SlaveServers  []*model.Server
	DefaultView   *model.View
}

// loadConfigDataCache 一次性加载所有配置数据到缓存
func (s *configService) loadConfigDataCache(ctx context.Context) (*ConfigDataCache, error) {
	zap.L().Info("开始加载配置数据缓存")
	span, ctx := apm.StartSpan(ctx, "loadConfigDataCache", "service")
	defer span.End()

	cache := &ConfigDataCache{
		ViewMap:            make(map[int64]*model.View),
		ZoneMap:            make(map[int64]*model.Zone),
		ServerMap:          make(map[int64]*model.Server),
		ViewsByType:        make(map[string][]*model.View),
		ViewHierarchyCache: make(map[int64][]*model.View),
		ServersByViewID:    make(map[int64][]*model.Server),
		RecordsByZoneView:  make(map[string][]*model.DNSRecord),
	}

	// 1. 加载所有视图
	views, err := s.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("加载视图数据失败", zap.Error(err))
		return nil, fmt.Errorf("加载视图数据失败: %w", err)
	}
	cache.Views = views

	// 2. 加载所有区域
	zones, err := s.store.Zone().GetAllZones(ctx)
	if err != nil {
		zap.L().Error("加载区域数据失败", zap.Error(err))
		return nil, fmt.Errorf("加载区域数据失败: %w", err)
	}
	cache.Zones = zones

	// 3. 加载所有服务器（需要完整的Server模型以获取ViewID等信息）
	masterServers, err := s.store.Server().GetMasterServers(ctx)
	if err != nil {
		zap.L().Error("加载master服务器数据失败", zap.Error(err))
		return nil, fmt.Errorf("加载master服务器数据失败: %w", err)
	}
	cache.MasterServers = masterServers

	slaveServers, err := s.store.Server().GetSlaveServers(ctx)
	if err != nil {
		zap.L().Error("加载slave服务器数据失败", zap.Error(err))
		return nil, fmt.Errorf("加载slave服务器数据失败: %w", err)
	}
	cache.SlaveServers = slaveServers

	// 合并所有服务器
	cache.Servers = append(cache.MasterServers, cache.SlaveServers...)

	// 4. 构建索引映射
	s.buildIndexMaps(cache)

	// 5. 构建视图分类缓存
	s.buildViewTypeCache(cache)

	// 6. 构建视图继承链缓存
	if err := s.buildViewHierarchyCache(cache); err != nil {
		zap.L().Error("构建视图继承链缓存失败", zap.Error(err))
		return nil, fmt.Errorf("构建视图继承链缓存失败: %w", err)
	}

	// 7. 构建服务器-视图映射缓存
	s.buildServerViewCache(cache)

	// 8. 批量加载DNS记录
	if err := s.loadDNSRecordsCache(ctx, cache); err != nil {
		zap.L().Error("加载DNS记录缓存失败", zap.Error(err))
		return nil, fmt.Errorf("加载DNS记录缓存失败: %w", err)
	}

	zap.L().Info("配置数据缓存加载完成",
		zap.Int("views_count", len(cache.Views)),
		zap.Int("zones_count", len(cache.Zones)),
		zap.Int("servers_count", len(cache.Servers)),
		zap.Int("master_servers_count", len(cache.MasterServers)),
		zap.Int("slave_servers_count", len(cache.SlaveServers)),
		zap.Int("view_hierarchies_count", len(cache.ViewHierarchyCache)),
		zap.Int("dns_records_count", len(cache.RecordsByZoneView)))

	return cache, nil
}

// buildIndexMaps 构建索引映射
func (s *configService) buildIndexMaps(cache *ConfigDataCache) {
	// 视图索引
	for _, view := range cache.Views {
		cache.ViewMap[view.ID] = view
		if view.LevelType == model.ViewLevelTypeDefault.String() {
			cache.DefaultView = view
		}
	}

	// 区域索引
	for _, zone := range cache.Zones {
		cache.ZoneMap[zone.ID] = zone
	}

	// 服务器索引
	for _, server := range cache.Servers {
		cache.ServerMap[server.ID] = server
	}
}

// buildViewTypeCache 构建视图类型缓存
func (s *configService) buildViewTypeCache(cache *ConfigDataCache) {
	for _, view := range cache.Views {
		viewType := view.LevelType
		if _, exists := cache.ViewsByType[viewType]; !exists {
			cache.ViewsByType[viewType] = make([]*model.View, 0)
		}
		cache.ViewsByType[viewType] = append(cache.ViewsByType[viewType], view)
	}
}

// buildViewHierarchyCache 构建视图继承链缓存
func (s *configService) buildViewHierarchyCache(cache *ConfigDataCache) error {
	for _, view := range cache.Views {
		hierarchy, err := s.getViewHierarchyFromCache(view.ID, cache)
		if err != nil {
			return fmt.Errorf("构建视图 %d 的继承链失败: %w", view.ID, err)
		}
		cache.ViewHierarchyCache[view.ID] = hierarchy
	}
	return nil
}

// buildServerViewCache 构建服务器-视图映射缓存
func (s *configService) buildServerViewCache(cache *ConfigDataCache) {
	for _, server := range cache.Servers {
		if server.ViewID > 0 {
			if _, exists := cache.ServersByViewID[server.ViewID]; !exists {
				cache.ServersByViewID[server.ViewID] = make([]*model.Server, 0)
			}
			cache.ServersByViewID[server.ViewID] = append(cache.ServersByViewID[server.ViewID], server)
		}
	}
}

// loadDNSRecordsCache 批量加载DNS记录缓存
func (s *configService) loadDNSRecordsCache(ctx context.Context, cache *ConfigDataCache) error {
	zap.L().Info("开始批量加载DNS记录")

	// 使用一次查询获取所有DNS记录，然后在内存中分组
	allRecords, err := s.store.Record().GetAllRecord(ctx)
	if err != nil {
		zap.L().Error("获取所有DNS记录失败", zap.Error(err))
		return fmt.Errorf("获取所有DNS记录失败: %w", err)
	}

	zap.L().Info("成功获取所有DNS记录", zap.Int("total_records", len(allRecords)))

	// 在内存中按zone_id和view_id分组
	for _, record := range allRecords {
		cacheKey := fmt.Sprintf("%d_%d", record.ZoneID, record.ViewID)
		if _, exists := cache.RecordsByZoneView[cacheKey]; !exists {
			cache.RecordsByZoneView[cacheKey] = make([]*model.DNSRecord, 0)
		}
		cache.RecordsByZoneView[cacheKey] = append(cache.RecordsByZoneView[cacheKey], record)
	}

	zap.L().Info("DNS记录批量加载完成",
		zap.Int("total_records", len(allRecords)),
		zap.Int("cached_groups", len(cache.RecordsByZoneView)))

	return nil
}

// getViewHierarchyFromCache 从缓存中获取视图继承链
func (s *configService) getViewHierarchyFromCache(viewID int64, cache *ConfigDataCache) ([]*model.View, error) {
	// 如果已经缓存了，直接返回
	if hierarchy, exists := cache.ViewHierarchyCache[viewID]; exists {
		return hierarchy, nil
	}

	var hierarchy []*model.View
	currentViewID := viewID

	// 避免无限循环
	visited := make(map[int64]bool)

	for currentViewID > 0 {
		if visited[currentViewID] {
			break // 检测到循环，退出
		}
		visited[currentViewID] = true

		view, exists := cache.ViewMap[currentViewID]
		if !exists {
			break
		}

		hierarchy = append(hierarchy, view)
		currentViewID = view.ParentID
	}

	return hierarchy, nil
}

// getRecordsFromCache 从缓存中获取DNS记录
func (s *configService) getRecordsFromCache(cache *ConfigDataCache, zoneID, viewID int64) []*model.DNSRecord {
	cacheKey := fmt.Sprintf("%d_%d", zoneID, viewID)
	if records, exists := cache.RecordsByZoneView[cacheKey]; exists {
		return records
	}
	return nil
}

// getServersByViewIDFromCache 从缓存中获取指定视图的服务器
func (s *configService) getServersByViewIDFromCache(cache *ConfigDataCache, viewID int64) []*model.Server {
	if servers, exists := cache.ServersByViewID[viewID]; exists {
		return servers
	}
	return nil
}
