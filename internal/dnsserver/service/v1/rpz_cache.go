package v1

import (
	"context"
	"errors"
	"fmt"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"sort"
	"strings"

	"go.uber.org/zap"
)

// updateRpzCache 更新RPZ缓存
func (rs *recordService) updateRpzCache(record *model.DNSRecord, action string) error {
	rpzCache, err := rs.store.Record().GetRpzCache()
	if err != nil {
		zap.L().Error("GetRpzCache Error", zap.Error(err))
		return err
	}

	switch action {
	case "update":
		zoneRecords, ok := rpzCache[record.ViewID]
		if !ok {
			zap.L().Debug("view_id为 %d 的RPZ缓存不存在", zap.Int64("view_id", record.ViewID))
			return nil
		}
		rpzTree, ok := zoneRecords[record.ZoneID]
		if !ok {
			zap.L().Debug("zone_id为 %d 的RPZ缓存不存在", zap.Int64("zone_id", record.ZoneID))
			return nil
		}

		// 先删除旧记录，再添加新记录
		// 从所有节点中移除该记录
		rs.removeRecordFromRpzTree(rpzTree, record.ID)
		// 添加新记录
		rs.addRecordToRpzTree(rpzTree, record)

		zap.L().Debug("RPZ缓存记录已更新",
			zap.Int64("view_id", record.ViewID),
			zap.Int64("zone_id", record.ZoneID),
			zap.Int64("record_id", record.ID))

		// 调用PrintRpzTree打印RPZ缓存
		// PrintRpzTree(rpzTree)

	case "delete":
		zoneRecords, ok := rpzCache[record.ViewID]
		if !ok {
			zap.L().Debug("view_id为 %d 的RPZ缓存不存在", zap.Int64("view_id", record.ViewID))
			return nil
		}
		rpzTree, ok := zoneRecords[record.ZoneID]
		if !ok {
			zap.L().Debug("zone_id为 %d 的RPZ缓存不存在", zap.Int64("zone_id", record.ZoneID))
			return nil
		}

		// 从所有节点中移除该记录
		rs.removeRecordFromRpzTree(rpzTree, record.ID)

		zap.L().Debug("RPZ缓存记录已删除",
			zap.Int64("view_id", record.ViewID),
			zap.Int64("zone_id", record.ZoneID),
			zap.Int64("record_id", record.ID))

		// 调用PrintRpzTree打印RPZ缓存
		// PrintRpzTree(rpzTree)

	case "create":
		// 检查视图是否存在于缓存中
		zoneRecords, ok := rpzCache[record.ViewID]
		if !ok {
			// 如果视图不存在，创建新的视图映射
			rpzCache[record.ViewID] = make(map[int64]*model.RpzTree)
			zoneRecords = rpzCache[record.ViewID]
		}

		// 检查区域是否存在于缓存中
		rpzTree, ok := zoneRecords[record.ZoneID]
		if !ok {
			// 如果区域不存在，创建新的RPZ树
			rpzTree = model.NewRpzRoot()
			zoneRecords[record.ZoneID] = rpzTree
		}

		// 添加记录到RPZ树
		rs.addRecordToRpzTree(rpzTree, record)

		zap.L().Debug("RPZ缓存记录已创建",
			zap.Int64("view_id", record.ViewID),
			zap.Int64("zone_id", record.ZoneID),
			zap.Int64("record_id", record.ID))

		// 调用PrintRpzTree打印RPZ缓存
		// PrintRpzTree(rpzTree)

	default:
		zap.L().Error("action只能是 update, delete 或 create")
		return errors.New("action只能是 update, delete 或 create")
	}

	// 将缓存写入cache缓存
	if err := rs.store.Record().SetRpzCache(rpzCache); err != nil {
		zap.L().Error("SetRpzCache Error", zap.Error(err))
		return err
	}

	return nil
}

// InitRpzCache 初始化RPZ缓存，从数据库加载所有RPZ和黑名单区域的记录
func (rs *recordService) InitRpzCache(ctx context.Context) error {
	zap.L().Info("正在初始化RPZ缓存...")

	rpzCache := make(map[int64]map[int64]*model.RpzTree)

	// 查询所有RPZ和黑名单类型的区域
	zones, err := rs.store.Zone().GetAllZones(ctx)
	if err != nil {
		zap.L().Error("获取区域列表失败，RPZ缓存初始化失败", zap.Error(err))
		return err
	}

	// 过滤出RPZ和黑名单类型的区域
	rpzZones := make([]*model.Zone, 0)
	for _, zone := range zones {
		zoneType := model.ToZoneType(zone.ZoneType)
		if zoneType == model.ZoneTypeRPZ || zoneType == model.ZoneTypeBlackList {
			rpzZones = append(rpzZones, zone)
		}
	}

	// 如果没有RPZ或黑名单区域，直接标记缓存为就绪并返回
	if len(rpzZones) == 0 {
		rs.mu.Lock()
		rs.cacheReady = true
		rs.mu.Unlock()
		zap.L().Info("没有RPZ或黑名单区域，RPZ缓存初始化完成")
		return nil
	}

	// 查询这些区域下的所有记录
	records, err := rs.store.Record().GetAllRecord(ctx)
	if err != nil {
		zap.L().Error("获取区域记录失败，RPZ缓存初始化失败", zap.Error(err))
		return err
	}

	// 按ViewID和ZoneID对记录进行分组
	// Eg: map[view_id]map[zone_id]map[int64]*model.DNSRecord
	recordsMap := make(map[int64]map[int64]map[int64]*model.DNSRecord)
	viewIDs := make(map[int64]bool)
	for _, record := range records {
		viewIDs[record.ViewID] = true

		// 初始化view_id的映射, 如果没有对应的view_id则进行初始化，一个view其实对应的就是一个职场
		if _, ok := recordsMap[record.ViewID]; !ok {
			recordsMap[record.ViewID] = make(map[int64]map[int64]*model.DNSRecord)
		}

		// 初始化zone_id的映射, 如果没有对应的zone_id则进行初始化
		if _, ok := recordsMap[record.ViewID][record.ZoneID]; !ok {
			recordsMap[record.ViewID][record.ZoneID] = make(map[int64]*model.DNSRecord)
		}

		// 添加记录到映射中
		recordsMap[record.ViewID][record.ZoneID][record.ID] = record
	}

	// 构建缓存
	rs.mu.Lock()

	// 初始化所有视图的映射
	for viewID := range viewIDs {
		if _, exists := rpzCache[viewID]; !exists {
			rpzCache[viewID] = make(map[int64]*model.RpzTree)
		}

		// 初始化所有区域的树
		for _, zone := range rpzZones {
			tree := model.NewRpzRoot()

			// 如果有记录，则添加到树中
			if records, ok := recordsMap[viewID][zone.ID]; ok {
				for _, record := range records {
					rs.addRecordToRpzTree(tree, record)
				}
				zap.L().Info("区域RPZ树构建完成",
					zap.Int64("zone_id", zone.ID),
					zap.String("zone_name", zone.Name),
					zap.Int64("view_id", viewID))
			}

			rpzCache[viewID][zone.ID] = tree
		}
	}

	// 标记缓存已就绪
	rs.cacheReady = true

	// 将缓存写入cache缓存
	if err := rs.store.Record().SetRpzCache(rpzCache); err != nil {
		zap.L().Error("SetRpzCache Error", zap.Error(err))
		return err
	}

	rs.mu.Unlock()

	zap.L().Info("RPZ缓存初始化完成")

	return nil
}

// IsCacheReady 检查缓存是否就绪
func (rs *recordService) IsCacheReady() bool {
	rs.mu.RLock()
	defer rs.mu.RUnlock()
	return rs.cacheReady
}

// findNodeInTree 在RPZ树中查找指定域名对应的节点
func (rs *recordService) findNodeInTree(tree *model.RpzTree, name string) (*model.DomainNode, bool) {
	labels := strings.Split(name, ".")
	node := tree.Root

	// 从根节点开始查找
	for i := len(labels) - 1; i >= 0; i-- {
		if labels[i] == "" {
			continue
		}

		child, exists := node.Children[labels[i]]
		if !exists {
			return nil, false
		}
		node = child
	}

	return node, true
}

// 检查是否存在上级泛解析记录并返回需要添加的明细记录
// 例如：当添加 *.a.b.corp.kuaishou.com 时，如果存在 *.corp.kuaishou.com 泛解析
// 需要添加 a.b.corp.kuaishou.com 和 b.corp.kuaishou.com 两条明细记录
// 以确保解析链的完整性，避免出现 NXDOMAIN 的情况
func (rs *recordService) checkParentWildcardRecords(tree *model.RpzTree, name string) ([]string, bool) {
	// 解析域名标签，例如: *.a.b.corp.kuaishou.com -> [*, a, b, corp, kuaishou, com]
	labels := strings.Split(name, ".")
	if len(labels) <= 2 || labels[0] != "*" { // 至少需要 [*, label, suffix]
		return nil, false
	}

	// 去掉第一个*，得到基础域名标签
	baseLabels := labels[1:] // [a, b, corp, kuaishou, com]

	// 保存需要添加的明细记录
	requiredRecords := make([]string, 0)

	// 记录最高级存在的泛解析记录的索引
	// 例如：对于 *.a.b.corp.kuaishou.com，如果 *.corp.kuaishou.com 存在
	// 则 highestWildcardIndex = 2 (对应 corp.kuaishou.com 的起始位置)
	highestWildcardIndex := -1

	// 遍历所有可能的上级泛解析记录，从高到低
	// 例如：先检查 *.com，再检查 *.kuaishou.com，然后是 *.corp.kuaishou.com 等
	for i := len(baseLabels) - 1; i >= 1; i-- {
		parentWildcard := "*." + strings.Join(baseLabels[i:], ".")
		parentNode, exists := rs.findWildcardNode(tree, parentWildcard)
		if exists && parentNode != nil && parentNode.IsWildCard {
			highestWildcardIndex = i
			break
		}
	}

	// 如果找到了泛解析记录，则需要添加从当前泛解析到最高级泛解析之间的所有明细记录
	// 这样可以确保 DNS 解析链的完整性，避免出现 NXDOMAIN 的情况
	if highestWildcardIndex > 0 {
		// 从当前域名到最高级泛解析之间的所有层级都需要添加明细记录
		// 例如：对于 *.a.b.corp.kuaishou.com，如果存在 *.corp.kuaishou.com
		// 需要添加 a.b.corp.kuaishou.com 和 b.corp.kuaishou.com 两条记录
		for i := 1; i <= highestWildcardIndex; i++ {
			// 构建明细记录名称
			// 例如：i=1 时生成 a.b.corp.kuaishou.com，i=2 时生成 b.corp.kuaishou.com
			exactRecord := strings.Join(
				[]string{baseLabels[0], strings.Join(baseLabels[i:], ".")}, ".")
			requiredRecords = append(requiredRecords, exactRecord)
		}
	}

	return requiredRecords, len(requiredRecords) > 0
}

// findWildcardNode 查找泛解析节点
func (rs *recordService) findWildcardNode(tree *model.RpzTree, wildcardName string) (*model.DomainNode, bool) {
	// 将泛解析名称转换为标签，例如: *.corp.kuaishou.com -> [*, corp, kuaishou, com]
	labels := strings.Split(wildcardName, ".")
	if len(labels) == 0 || labels[0] != "*" {
		return nil, false
	}

	// 去除*号，只保留后面的部分
	// 例如: *.corp.kuaishou.com -> [corp, kuaishou, com]
	domainLabels := labels[1:]

	// 从根节点开始查找
	node := tree.Root
	for i := len(domainLabels) - 1; i >= 0; i-- {
		if domainLabels[i] == "" {
			continue
		}

		child, exists := node.Children[domainLabels[i]]
		if !exists {
			return nil, false
		}
		node = child
	}

	// 检查节点是否为泛解析节点
	if node.IsWildCard {
		return node, true
	}

	// 检查节点是否有*子节点
	wildChild, exists := node.Children["*"]
	if exists && wildChild.IsWildCard {
		return node, true // 返回父节点，而不是*节点
	}

	return nil, false
}

// 合并两个RPZ树，将sourceTree合并到targetTree中
func (rs *recordService) mergeRpzTree(targetTree, sourceTree *model.RpzTree) {
	if sourceTree == nil || targetTree == nil {
		return
	}

	// 递归合并节点
	rs.mergeNode(targetTree.Root, sourceTree.Root)
}

// 递归合并节点
func (rs *recordService) mergeNode(targetNode, sourceNode *model.DomainNode) {
	if sourceNode == nil || targetNode == nil {
		return
	}

	// 合并记录，需要做并集，但相同域名的记录要覆盖
	recordMap := make(map[string]*model.DNSRecord)

	// 先添加target的记录
	for _, record := range targetNode.Records {
		recordMap[record.Name] = record
	}

	// 再添加source的记录，会覆盖相同域名的记录
	for _, record := range sourceNode.Records {
		recordMap[record.Name] = record
	}

	// 将合并后的记录转回切片
	targetNode.Records = make([]*model.DNSRecord, 0, len(recordMap))
	for _, record := range recordMap {
		targetNode.Records = append(targetNode.Records, record)
	}

	// 递归合并子节点
	for label, sourceChild := range sourceNode.Children {
		targetChild, exists := targetNode.Children[label]
		if !exists {
			// 如果目标节点不存在该子节点，创建新节点并复制source的内容
			newNode := model.NewDomainNode(label)
			// 复制记录
			newNode.Records = make([]*model.DNSRecord, len(sourceChild.Records))
			copy(newNode.Records, sourceChild.Records)
			// 复制泛解析标志
			newNode.IsWildCard = sourceChild.IsWildCard
			// 递归复制子节点
			for childLabel, child := range sourceChild.Children {
				newNode.Children[childLabel] = child
			}
			targetNode.Children[label] = newNode
		} else {
			// 如果存在，递归合并
			rs.mergeNode(targetChild, sourceChild)
		}

	}
}

func (rs *recordService) checkRpzRecordCoverage(ctx context.Context, record *model.DNSRecord, zone *model.Zone, view *model.View) error {
	zap.L().Debug("检查RPZ记录覆盖")
	if !strings.HasPrefix(record.Name, "*.") {
		zap.L().Debug("记录不是泛解析记录，不需要检查覆盖")
		return nil
	}

	// 解析域名标签
	labels := strings.Split(record.Name, ".")
	if len(labels) <= 1 {
		zap.L().Debug("记录不是泛解析记录，不需要检查覆盖")
		return nil
	}

	// 检查是否是子级别的泛解析
	if labels[0] != "*" {
		return nil
	}

	// 缓存已就绪，从缓存中查找
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	// 获取RPZ缓存
	rpzCache, err := rs.store.Record().GetRpzCache()
	if err != nil {
		zap.L().Error("GetRpzCache Error", zap.Error(err))
		return err
	}

	// 根据视图类型进行不同的检查
	switch model.ToViewLevelType(view.LevelType) {
	case model.ViewLevelTypeOffice:
		// 构建一个合并后的树
		var (
			mergedTree  = model.NewRpzRoot()
			officeTree  *model.RpzTree
			bizTree     *model.RpzTree
			defaultTree *model.RpzTree
		)

		// 如果是office类型的，那么就需要先把所有的继承规则进行覆盖整合后，再进行判断是否可以进行添加
		if _, ok := rpzCache[view.ID]; !ok {
			zap.L().Error("office视图不存在", zap.Int64("view_id", view.ID))
			return fmt.Errorf("office视图不存在: %v", view.ID)
		}

		if _, ok := rpzCache[view.ID][zone.ID]; !ok {
			zap.L().Error("office视图找不到对应的zone", zap.Int64("view_id", view.ID), zap.String("zone_name", zone.Name))
			return fmt.Errorf("office视图找不到对应的zone: %s", zone.Name)
		}

		// 获取officeTree
		officeTree = rpzCache[view.ID][zone.ID]

		// 获取biz视图
		bizViewID := view.ParentID
		if _, ok := rpzCache[bizViewID]; !ok {
			zap.L().Error("biz视图不存在", zap.Int64("biz_view_id", bizViewID))
			return fmt.Errorf("biz视图不存在: %v", bizViewID)
		}

		if _, ok := rpzCache[bizViewID][zone.ID]; !ok {
			zap.L().Error("biz视图找不到对应的zone", zap.Int64("biz_view_id", bizViewID), zap.String("zone_name", zone.Name))
			return fmt.Errorf("biz视图找不到对应的zone: %s", zone.Name)
		}

		// 获取BizTree
		bizTree = rpzCache[bizViewID][zone.ID]

		// 获取default视图
		bizViews, err := rs.store.View().GetViewByIDs(ctx, []int64{bizViewID})
		if err != nil {
			zap.L().Error("查询biz视图失败", zap.Error(err))
			return fmt.Errorf("查询biz视图失败: %v", err)
		}

		if len(bizViews) == 0 {
			zap.L().Error("biz视图不存在", zap.Int64("biz_view_id", bizViewID))
			return fmt.Errorf("biz视图不存在: %v", bizViewID)
		}

		bizView := bizViews[0]
		if _, ok := rpzCache[bizView.ParentID]; !ok {
			zap.L().Error("default视图不存在", zap.Int64("default_view_id", bizView.ID))
			return fmt.Errorf("default视图不存在: %v", bizView.ID)
		}

		// defaultView下其实可以没有对应的Zone
		if _, ok := rpzCache[bizView.ParentID][zone.ID]; ok {
			defaultTree = rpzCache[bizView.ParentID][zone.ID]
		}

		// 开始进行合并，按照优先级顺序：default -> biz -> office
		// 1. 先合并default树
		if defaultTree != nil {
			rs.mergeRpzTree(mergedTree, defaultTree)
		}

		// 2. 再合并biz树，会覆盖default的配置
		if bizTree != nil {
			rs.mergeRpzTree(mergedTree, bizTree)
		}

		// 3. 最后合并office树，会覆盖前面的配置
		if officeTree != nil {
			rs.mergeRpzTree(mergedTree, officeTree)
		}

		// 检查上级泛解析记录和需要的中间记录
		requiredRecords, hasParentWildcard := rs.checkParentWildcardRecords(mergedTree, record.Name)
		if hasParentWildcard {
			var missingRecords []string
			for _, reqRecord := range requiredRecords {
				midNode, midFound := rs.findNodeInTree(mergedTree, reqRecord)
				if !midFound || len(midNode.Records) == 0 {
					missingRecords = append(missingRecords, reqRecord)
				}
			}

			if len(missingRecords) > 0 {
				return fmt.Errorf("警告: 添加泛解析记录 %s 需要先添加以下精确记录: %s, 否则导致对应记录无解析记录", record.Name, strings.Join(missingRecords, ", "))
			}
		}

		return nil

	case model.ViewLevelTypeBiz:

	case model.ViewLevelTypeDefault:

	}

	return nil
}

// removeRecordFromNode 递归从节点及其子节点中移除指定 ID 的记录
func (rs *recordService) removeRecordFromNode(node *model.DomainNode, recordID int64) {
	if node == nil {
		return
	}

	// 从当前节点的记录中移除指定 ID 的记录
	if len(node.Records) > 0 {
		newRecords := make([]*model.DNSRecord, 0, len(node.Records))
		for _, record := range node.Records {
			if record.ID != recordID {
				newRecords = append(newRecords, record)
			}
		}
		node.Records = newRecords
	}

	// 递归处理所有子节点
	for _, child := range node.Children {
		rs.removeRecordFromNode(child, recordID)
	}
}

// removeRecordFromRpzTree 从 RPZ 树中移除指定 ID 的记录
func (rs *recordService) removeRecordFromRpzTree(tree *model.RpzTree, recordID int64) {
	if tree == nil || tree.Root == nil {
		return
	}

	// 递归移除记录
	rs.removeRecordFromNode(tree.Root, recordID)
}

// addRecordToDomainTree 将DNS记录添加到域名树中
func (rs *recordService) addRecordToRpzTree(tree *model.RpzTree, record *model.DNSRecord) {
	// 获取RPZ树的根节点
	root := tree.Root

	// 检查记录是否为泛解析
	// 方法一：检查 IsWildCard 标志
	// 方法二：检查域名是否以 * 开头
	isWildcard := record.IsWildCard || (len(record.Name) > 0 && record.Name[0] == '*')

	// 解析域名标签
	labels := strings.Split(record.Name, ".")

	// 当前节点从根开始
	currentNode := root

	// 如果是空名称或@符号，表示区域根
	if record.Name == "" || record.Name == "@" {
		// 直接添加记录到根节点
		root.Records = append(root.Records, record)
		// 注意：区域根不应该是泛解析记录，但为了兼容现有代码，保留这个检查
		if isWildcard {
			root.IsWildCard = true
		}
		return
	}

	// 反向遍历标签，构建或查找路径
	// 例如：
	// 非泛解析：www.example.com 反向遍历为 [com, example, www]
	// 泛解析：*.example.com 反向遍历为 [com, example, *]
	for i := len(labels) - 1; i >= 0; i-- {
		label := labels[i]
		if label == "" {
			continue
		}

		// 检查当前节点是否已有此标签的子节点
		child, exists := currentNode.Children[label]
		if !exists {
			// 不存在则创建新节点
			child = model.NewDomainNode(label)

			// 如果当前标签是*，则标记节点为泛解析
			if label == "*" {
				child.IsWildCard = true
			}

			currentNode.Children[label] = child
		}

		// 如果是泛解析记录，则当前节点也应该标记为泛解析
		// 这是为了确保在遍历树的过程中可以正确识别泛解析节点
		if i == 0 && isWildcard {
			child.IsWildCard = true
		}

		// 移动到子节点继续处理
		currentNode = child
	}

	// 将记录添加到最终节点
	currentNode.Records = append(currentNode.Records, record)
}

// PrintRpzTree 以树状图的形式展示RPZ解析树
func PrintRpzTree(tree *model.RpzTree) {
	zap.L().Debug("PrintRpzTree Function Called")

	var result strings.Builder
	result.WriteString("RPZ Tree:\n")
	printRpzNode(&result, tree.Root, "", true)
	fmt.Println(result.String())
}

// printRpzNode 递归打印RPZ域名树的节点及其子节点，以ASCII树状图形式展示
//
// 参数:
//   - sb: 字符串构建器，用于构造树状图输出
//   - node: 当前要打印的域名节点
//   - prefix: 当前行的前缀字符串，用于构建树的层次结构和连接线
//   - isLast: 指示当前节点是否是其父节点的最后一个子节点
//
// 输出格式:
//
//	├── 非最后节点
//	└── 最后节点
//	│   连接线
//	    子节点缩进
//
// 处理逻辑:
//  1. 根据节点位置选择适当的树形连接符(├── 或 └──)
//  2. 显示节点标签，对空标签显示为"@"，对通配符节点添加标记
//  3. 在节点下方显示所有DNS记录，格式为 "- [记录类型] 记录值"
//  4. 按字母顺序排序所有子节点
//  5. 递归处理每个子节点，保持正确的树形结构和缩进
//
// 示例输出:
//
//	└── @
//	      - [A] 192.168.1.1
//	    ├── www
//	    │     - [A] 192.168.1.2
//	    └── mail
//	          - [A] 192.168.1.3
func printRpzNode(sb *strings.Builder, node *model.DomainNode, prefix string, isLast bool) {
	// 确定当前节点的前缀
	nodePrefix := prefix
	if isLast {
		nodePrefix += "└── "
	} else {
		nodePrefix += "├── "
	}

	// 打印当前节点
	label := node.Label
	if label == "" {
		label = "@"
	}
	if node.IsWildCard {
		label += " (泛解析)"
	}

	sb.WriteString(nodePrefix + label + "\n")

	// 打印记录信息
	recordPrefix := prefix
	if isLast {
		recordPrefix += "    "
	} else {
		recordPrefix += "│   "
	}

	for _, record := range node.Records {
		recordInfo := fmt.Sprintf("[%s] %s", record.RType, record.Value)
		sb.WriteString(recordPrefix + "  - " + recordInfo + "\n")
	}

	// 获取子节点列表并排序
	children := make([]string, 0, len(node.Children))
	for child := range node.Children {
		children = append(children, child)
	}
	sort.Strings(children)

	// 递归打印子节点
	childPrefix := prefix
	if isLast {
		childPrefix += "    "
	} else {
		childPrefix += "│   "
	}

	for i, childLabel := range children {
		isLastChild := i == len(children)-1
		printRpzNode(sb, node.Children[childLabel], childPrefix, isLastChild)
	}
}
