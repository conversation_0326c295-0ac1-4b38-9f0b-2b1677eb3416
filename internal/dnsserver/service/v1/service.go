package v1

import (
	"ks-knoc-server/internal/dnsserver/store"
)

type Service interface {
	Office() OfficeService
	Zone() ZoneService
	Record() RecordService
	Cluster() ClusterService
	View() ViewService
	Config() ConfigService
	Server() ServerService
	Task() TaskService
}

var _ Service = (*service)(nil)

type service struct {
	store store.Factory
}

// NewService ...
func NewService(store store.Factory) Service {
	return &service{
		store: store,
	}
}

func (s *service) Task() TaskService {
	return newTaskService(s)
}

func (s *service) Server() ServerService {
	return newServerService(s)
}

func (s *service) Config() ConfigService {
	return newConfigService(s)
}

func (s *service) Office() OfficeService {
	return newOfficeService(s)
}

func (s *service) Zone() ZoneService {
	return newZoneService(s)
}

func (s *service) Record() RecordService {
	return newRecordService(s)
}

func (s *service) Cluster() ClusterService {
	return newClusterService(s)
}

func (s *service) View() ViewService {
	return newViewService(s)
}
