package v1

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"text/template"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/bind/tsig"
	"ks-knoc-server/internal/dnsserver/tmpl"

	"github.com/spf13/viper"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// getViewKeyName 根据view名称和类型获取对应的key名称
func (s *configService) getViewKeyName(viewCode string) string {
	return fmt.Sprintf("office_%s_key", viewCode)
}

// getDefaultViewKeyName 获取default view的key名称
func (s *configService) getDefaultViewKeyName() string {
	return "default_key"
}

func (s *configService) getKeysConfig(views []*model.View) (*api.NamedConfKeysData, error) {
	keyToken := viper.GetString("keyToken")
	if keyToken == "" {
		err := errors.New("keyToken配置不存在，无法解密密钥")
		zap.L().Error("keyToken配置不存在", zap.Error(err))
		return nil, err
	}

	// 准备配置数据
	keysData := &api.NamedConfKeysData{
		GeneratedAt: time.Now().Format("2006-01-02 15:04:05"),
		ViewKeys:    make([]*api.KeyData, 0),
	}

	// TODO: 完善cluster的RNDC key处理逻辑
	// 当前版本暂时不处理RNDC key
	// 需要实现：
	// 1. 获取所有集群的RNDC key
	// 2. 解密RNDC key
	// 3. 设置keysData.RndcKey
	keysData.RndcKey = nil

	// 处理view keys，因为当前的设计，key是直接保存在view中的
	for _, view := range views {
		if view.SecretControl && view.ViewKey != "" {
			// 解密view key
			viewSecret, err := tsig.DecryptKey(keyToken, view.ViewKey, view.ViewKeyMD5)
			if err != nil {
				zap.L().Error("解密view key失败", zap.Error(err), zap.String("view", view.Name))
				continue // 跳过错误的key，继续处理其他key
			}

			// 根据view类型选择合适的key名称
			var keyName string
			if view.LevelType == model.ViewLevelTypeDefault.String() {
				keyName = s.getDefaultViewKeyName()
			} else {
				keyName = s.getViewKeyName(view.Code)
			}

			keysData.ViewKeys = append(keysData.ViewKeys, &api.KeyData{
				KeyName:   keyName,
				Algorithm: view.ViewKeyAlgo,
				Secret:    viewSecret,
			})
			zap.L().Info("成功解密View key", zap.String("view", view.Name), zap.String("keyName", keyName))
		}
	}

	return keysData, nil
}

// renderAndSaveKeysConfigFile 渲染模板并保存named.conf.keys文件
func (s *configService) renderAndSaveKeysConfigFile(keysData *api.NamedConfKeysData) error {
	// 渲染模板
	tmplContent, err := template.New("keys").Parse(tmpl.NamedConfKeysTemplate)
	if err != nil {
		zap.L().Error("解析模板失败", zap.Error(err))
		return fmt.Errorf("解析模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, keysData); err != nil {
		zap.L().Error("渲染模板失败", zap.Error(err))
		return fmt.Errorf("渲染模板失败: %w", err)
	}

	// 确保目录存在
	if err := os.MkdirAll(DefaultConfigDir, 0755); err != nil {
		zap.L().Error("创建配置目录失败", zap.Error(err), zap.String("dir", DefaultConfigDir))
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 保存文件
	filename := filepath.Join(DefaultConfigDir, "named.conf.keys")
	if err := os.WriteFile(filename, buf.Bytes(), 0644); err != nil {
		zap.L().Error("保存配置文件失败", zap.Error(err), zap.String("filename", filename))
		return fmt.Errorf("保存配置文件失败: %w", err)
	}

	zap.L().Info("named.conf.keys配置文件生成成功",
		zap.String("filename", filename),
		zap.Int("rndc_keys", func() int {
			if keysData.RndcKey != nil {
				return 1
			}
			return 0
		}()),
		zap.Int("view_keys", len(keysData.ViewKeys)))

	return nil
}

// renderKeysConfigTemplate 渲染keys配置模板
func (s *configService) renderKeysConfigTemplate(keysData *api.NamedConfKeysData) ([]byte, error) {
	tmplContent, err := template.New("keys").Parse(tmpl.NamedConfKeysTemplate)
	if err != nil {
		zap.L().Error("解析模板失败", zap.Error(err))
		return nil, fmt.Errorf("解析模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, keysData); err != nil {
		zap.L().Error("渲染模板失败", zap.Error(err))
		return nil, fmt.Errorf("渲染模板失败: %w", err)
	}

	return buf.Bytes(), nil
}

// generateKeysConfigContent 生成named.conf.keys配置文件内容到内存
func (s *configService) generateKeysConfigContent(ctx context.Context, keysData *api.NamedConfKeysData) (*api.ConfigFile, error) {
	zap.L().Info("开始生成named.conf.keys配置文件内容")
	span, _ := apm.StartSpan(ctx, "generateKeysConfigContent", "service")
	defer span.End()

	// 渲染模板
	content, err := s.renderKeysConfigTemplate(keysData)
	if err != nil {
		zap.L().Error("渲染模板失败", zap.Error(err))
		return nil, err
	}

	zap.L().Info("named.conf.keys配置文件内容生成成功",
		zap.Int("rndc_keys", func() int {
			if keysData.RndcKey != nil {
				return 1
			}
			return 0
		}()),
		zap.Int("view_keys", len(keysData.ViewKeys)))

	return &api.ConfigFile{
		Name:    "named.conf.keys",
		Content: content,
	}, nil
}

// GenerateKeysConfig 生成named.conf.keys配置文件
func (s *configService) GenerateKeysConfig(ctx context.Context, keysData *api.NamedConfKeysData) error {
	zap.L().Info("开始生成named.conf.keys配置文件")
	span, _ := apm.StartSpan(ctx, "GenerateKeysConfig", "service")
	defer span.End()

	// 渲染模板并保存文件
	return s.renderAndSaveKeysConfigFile(keysData)
}
