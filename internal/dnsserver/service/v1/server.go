package v1

import (
	"context"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/store"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type ServerService interface {
	CreateServer(ctx context.Context, server *api.CreateServerRequest) error
	GetServerList(ctx context.Context, req *api.QueryServerRequest) (*api.ServerListResponse, error)
}

type serverService struct {
	store store.Factory
}

func newServerService(srv *service) ServerService {
	return &serverService{
		store: srv.store,
	}
}

func (s *serverService) CreateServer(ctx context.Context, server *api.CreateServerRequest) error {
	zap.L().Debug("CreateServer Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateServer", "service")
	defer span.End()

	// 当前时间戳
	now := time.Now().Unix()

	// 构建 server model
	serverModel := &model.Server{
		ServerInfo: model.ServerInfo{
			Name:   server.Name,
			Role:   model.ServerRole(server.Role),
			Status: model.ServerStatus(server.Status),
			IP:     server.IP,
			Port:   server.Port,
		},
		ServerSoftWareInfo: model.ServerSoftWareInfo{
			Version: server.Version,
			DbPath:  server.DbPath,
		},
		ServerCheckInfo: model.ServerCheckInfo{
			Checked:       false,
			LastAliveTime: 0,
		},
		ServerConfInfo: model.ServerConfInfo{
			ViewID: server.ViewID,
		},
		CreateTime: now,
		UpdateTime: now,
		ClusterID:  server.ClusterID,
	}

	// 创建服务器
	if err := s.store.Server().CreateServer(ctx, serverModel); err != nil {
		zap.L().Error("CreateServer Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	return nil
}

func (s *serverService) GetServerList(ctx context.Context, req *api.QueryServerRequest) (*api.ServerListResponse, error) {
	zap.L().Debug("GetServerList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetServerList", "service")
	defer span.End()

	// 获取所有的服务器
	servers, count, err := s.store.Server().GetServerListWithPagination(ctx, req)
	if err != nil {
		zap.L().Error("GetServerList Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	// 构建Response信息
	serverListResponse := &api.ServerListResponse{
		Total:   count,
		Servers: make([]api.ServerResponse, 0),
	}
	for _, server := range servers {
		serverResp := &api.ServerResponse{
			ID:            server.ID,
			Name:          server.Name,
			Role:          server.Role.String(),
			Status:        server.Status.String(),
			ClusterID:     server.ClusterID,
			IP:            server.IP,
			Port:          server.Port,
			Version:       server.Version,
			DbPath:        server.DbPath,
			ViewID:        server.ServerConfInfo.ViewID,
			Checked:       server.Checked,
			LastAliveTime: server.LastAliveTime,
			CreateTime:    server.CreateTime,
			UpdateTime:    server.UpdateTime,
		}
		serverListResponse.Servers = append(serverListResponse.Servers, *serverResp)
	}

	return serverListResponse, nil
}

func (s *serverService) GetServerByID(ctx context.Context, id int64) (*api.ServerResponse, error) {
	zap.L().Debug("GetServerByID Service Called")
	span, ctx := apm.StartSpan(ctx, "GetServerByID", "service")
	defer span.End()

	server, err := s.store.Server().GetServerByID(ctx, id)
	if err != nil {
		return nil, err
	}

	serverResp := &api.ServerResponse{
		ID:            server.ID,
		Name:          server.Name,
		Role:          server.Role.String(),
		Status:        server.Status.String(),
		ClusterID:     server.ClusterID,
		IP:            server.IP,
		Port:          server.Port,
		Version:       server.Version,
		DbPath:        server.DbPath,
		ViewID:        server.ServerConfInfo.ViewID,
		Checked:       server.Checked,
		LastAliveTime: server.LastAliveTime,
		CreateTime:    server.CreateTime,
		UpdateTime:    server.UpdateTime,
	}

	return serverResp, nil
}
