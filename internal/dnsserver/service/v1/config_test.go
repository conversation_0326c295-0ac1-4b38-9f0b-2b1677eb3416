package v1

import (
	"context"
	"fmt"
	"ks-knoc-server/config"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/mq"
	"ks-knoc-server/internal/dnsserver/store"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

const (
	// 测试配置文件路径
	testConfigPath = "/Users/<USER>/Documents/kwai/ks-knoc-server/config/conf/config_dns.yaml"
)

// 全局测试资源
var (
	testConfigService *configService
)

// setupConfigTestDependencies 设置config测试依赖, 数据库，配置文件，cache
func setupConfigTestDependencies(t *testing.T) {
	if err := config.NewConf(testConfigPath); err != nil {
		t.Fatalf("初始化配置失败: %v", err)
	}

	configTestDB, err := db.NewMySQLOptions().Init()
	if err != nil {
		t.Fatalf("初始化数据库失败: %v", err)
	}

	configTestCache, err := cache.NewBigCache().Init()
	if err != nil {
		t.Fatalf("初始化缓存失败: %v", err)
	}

	configTestProducer, err := mq.NewKafkaOptions().Init()
	if err != nil {
		t.Fatalf("初始化kafka失败: %v", err)
	}

	configTestStoreFactory := &store.DataStore{
		Db:       configTestDB,
		Cache:    configTestCache,
		Producer: configTestProducer,
	}

	testConfigService = &configService{
		store: configTestStoreFactory,
	}
}

func TestGenerateKeysConfig(t *testing.T) {
	// 初始化测试依赖
	setupConfigTestDependencies(t)

	// 直接使用DefaultConfigDir，不创建临时目录
	t.Logf("[Info] keys配置文件将生成到: %s", DefaultConfigDir)

	// 初始化context
	ctx := context.Background()

	// 获取views数据
	views, err := testConfigService.store.View().GetAllView(ctx)
	if err != nil {
		t.Fatalf("获取views数据失败: %v", err)
	}

	keysData, err := testConfigService.getKeysConfig(views)
	if err != nil {
		t.Fatalf("获取keys配置失败: %v", err)
	}

	// 调用keys配置生成方法
	if err := testConfigService.GenerateKeysConfig(ctx, keysData); err != nil {
		t.Fatalf("生成keys配置失败: %v", err)
	}

	// 验证配置文件是否生成
	filename := filepath.Join(DefaultConfigDir, "named.conf.keys")
	_, err = os.Stat(filename)
	if err != nil {
		t.Errorf("keys配置文件不存在: %s", filename)
	} else {
		t.Logf("[Success] keys配置文件已生成: %s", filename)
	}

	// 读取并验证文件内容
	if content, err := os.ReadFile(filename); err == nil {
		contentStr := string(content)

		// 验证基本结构
		if !strings.Contains(contentStr, "# named.conf.keys - TSIG keys for DNS server") {
			t.Error("keys配置文件缺少标题注释")
		}
		if !strings.Contains(contentStr, "# View-specific TSIG keys") {
			t.Error("keys配置文件缺少view keys注释")
		}
		if !strings.Contains(contentStr, "# End of named.conf.keys") {
			t.Error("keys配置文件缺少结束注释")
		}

		// 统计生成的key数量
		keyCount := strings.Count(contentStr, `key "`)
		t.Logf("[Info] 生成了 %d 个TSIG keys", keyCount)
		t.Logf("[Info] keys配置文件大小: %d bytes", len(content))
	}

	// 统计有key的view数量
	viewsWithKeys := 0
	for _, view := range views {
		if view.ViewKey != "" {
			viewsWithKeys++
		}
	}

	t.Logf("[Info] 数据库中有 %d 个views，其中 %d 个有配置key", len(views), viewsWithKeys)
	t.Logf("[Info] keys配置文件生成完成")
}

func TestGenerateACLsConfig(t *testing.T) {
	// 初始化测试依赖
	setupConfigTestDependencies(t)

	// 直接使用DefaultConfigDir，不创建临时目录
	t.Logf("[Info] ACLs配置文件将生成到: %s", DefaultConfigDir)

	// 初始化context
	ctx := context.Background()

	// 获取views数据
	views, err := testConfigService.store.View().GetAllView(ctx)
	if err != nil {
		t.Fatalf("获取views数据失败: %v", err)
	}

	aclsData, err := testConfigService.getAclsConfig(ctx, views)
	if err != nil {
		t.Fatalf("获取acls配置失败: %v", err)
	}

	// 调用ACLs配置生成方法
	if err := testConfigService.GenerateACLsConfig(ctx, aclsData); err != nil {
		t.Fatalf("生成ACLs配置失败: %v", err)
	}

	// 验证配置文件是否生成
	filename := filepath.Join(DefaultConfigDir, "named.conf.acls")
	_, err = os.Stat(filename)
	if err != nil {
		t.Errorf("ACLs配置文件不存在: %s", filename)
		return
	} else {
		t.Logf("[Success] ACLs配置文件已生成: %s", filename)
	}

	// 读取并验证文件内容
	content, err := os.ReadFile(filename)
	if err != nil {
		t.Fatalf("读取ACLs配置文件失败: %v", err)
	}

	contentStr := string(content)
	t.Logf("[Debug] ACLs配置文件内容:\n%s", contentStr)

	// 验证基本结构
	if !strings.Contains(contentStr, "# Generated at:") {
		t.Error("ACLs配置文件缺少生成时间注释")
	}
	if !strings.Contains(contentStr, "# ACL configurations for DNS views") {
		t.Error("ACLs配置文件缺少标题注释")
	}

	// 验证默认ACL存在
	if !strings.Contains(contentStr, "acl office_default_acl {") {
		t.Error("ACLs配置文件缺少默认ACL")
	}

	// 统计不同类型的view数量
	var officeViewCount, defaultViewCount, bizViewCount int
	var officeViewsWithACL int

	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)
		switch viewType {
		case model.ViewLevelTypeOffice:
			officeViewCount++
			// 检查是否有ACL配置且有ViewKey（新的逻辑：两者都需要）
			if len(view.ACLs) > 0 {
				officeViewsWithACL++
				// 只有同时配置了ACL和ViewKey的view才会生成ACL配置
				if view.ViewKey != "" {
					expectedACLName := fmt.Sprintf("office_%s_acl", view.Code)
					if !strings.Contains(contentStr, fmt.Sprintf("acl %s {", expectedACLName)) {
						t.Errorf("未找到view %s 对应的ACL配置: %s", view.Name, expectedACLName)
					} else {
						t.Logf("[Success] 找到view %s 的ACL配置: %s", view.Name, expectedACLName)
					}
				} else {
					t.Logf("[Expected] view %s 没有ViewKey，预期跳过ACL生成", view.Name)
				}
			}
		case model.ViewLevelTypeDefault:
			defaultViewCount++
		case model.ViewLevelTypeBiz:
			bizViewCount++
		}
	}

	// 统计生成的ACL数量
	aclCount := strings.Count(contentStr, "acl ")
	t.Logf("[Info] 生成了 %d 个ACL配置", aclCount)
	t.Logf("[Info] 数据库中有 %d 个office views，其中 %d 个有ACL配置", officeViewCount, officeViewsWithACL)
	t.Logf("[Info] 数据库中有 %d 个default views，%d 个biz views", defaultViewCount, bizViewCount)
	t.Logf("[Info] ACLs配置文件大小: %d bytes", len(content))

	t.Logf("[Info] ACLs配置文件生成完成")
}

// testACLNaming 测试ACL命名规则
func TestACLNaming(t *testing.T) {
	t.Log("[Info] 开始测试ACL命名规则")

	// 创建测试view数据
	testViews := []*model.View{
		{
			ID:        1,
			Code:      "test_office",
			Name:      "测试办公视图",
			LevelType: string(model.ViewLevelTypeOffice),
		},
		{
			ID:        2,
			Code:      "default",
			Name:      "默认视图",
			LevelType: string(model.ViewLevelTypeDefault),
		},
		{
			ID:        3,
			Code:      "test_biz",
			Name:      "测试业务视图",
			LevelType: string(model.ViewLevelTypeBiz),
		},
	}

	// 测试getACLNameForView方法
	for _, view := range testViews {
		expectedACLName := ""
		viewType := model.ToViewLevelType(view.LevelType)

		switch viewType {
		case model.ViewLevelTypeOffice:
			expectedACLName = fmt.Sprintf("office_%s_acl", view.Code)
		case model.ViewLevelTypeDefault:
			expectedACLName = "office_default_acl"
		case model.ViewLevelTypeBiz:
			// biz级别的view不应该生成ACL，跳过
			continue
		}

		actualACLName := testConfigService.getACLNameForView(view)
		if actualACLName != expectedACLName {
			t.Errorf("ACL命名规则错误 - view: %s, 期望: %s, 实际: %s",
				view.Name, expectedACLName, actualACLName)
		} else {
			t.Logf("[Success] ACL命名正确 - view: %s -> %s", view.Name, actualACLName)
		}
	}

	t.Log("[Info] ACL命名规则测试完成")
}

// TestGenerateACLsAndViewsIntegration 测试ACL和Views配置的集成
func TestGenerateACLsAndViewsIntegration(t *testing.T) {
	// 初始化测试依赖
	setupConfigTestDependencies(t)

	t.Logf("[Info] 开始测试ACL和Views配置集成")

	// 初始化context
	ctx := context.Background()

	// 先生成ACL配置
	views, err := testConfigService.store.View().GetAllView(ctx)
	if err != nil {
		t.Fatalf("获取views数据失败: %v", err)
	}

	aclsData, err := testConfigService.getAclsConfig(ctx, views)
	if err != nil {
		t.Fatalf("获取acls配置失败: %v", err)
	}

	if err := testConfigService.GenerateACLsConfig(ctx, aclsData); err != nil {
		t.Fatalf("生成ACLs配置失败: %v", err)
	}

	// 再生成Views配置
	viewsFiles, err := testConfigService.generateViewsConfigContent(ctx)
	if err != nil {
		t.Fatalf("生成Views配置失败: %v", err)
	}

	for _, file := range viewsFiles {
		if err := os.WriteFile(filepath.Join(DefaultConfigDir, file.Name), file.Content, 0644); err != nil {
			t.Fatalf("保存views配置文件失败: %v", err)
		}
	}

	// 验证两个配置文件都存在
	aclFile := filepath.Join(DefaultConfigDir, "named.conf.acls")
	masterFile := filepath.Join(DefaultConfigDir, "named.conf.views.master")
	slaveFile := filepath.Join(DefaultConfigDir, "named.conf.views.slave")

	// 读取ACL配置文件
	aclContent, err := os.ReadFile(aclFile)
	if err != nil {
		t.Fatalf("读取ACL配置文件失败: %v", err)
	}
	aclStr := string(aclContent)

	// 读取Views配置文件
	masterContent, err := os.ReadFile(masterFile)
	if err != nil {
		t.Fatalf("读取master views配置文件失败: %v", err)
	}
	masterStr := string(masterContent)

	slaveContent, err := os.ReadFile(slaveFile)
	if err != nil {
		t.Fatalf("读取slave views配置文件失败: %v", err)
	}
	slaveStr := string(slaveContent)

	// 验证ACL和View配置的一致性
	var aclViewPairs []struct {
		ViewName string
		ACLName  string
	}

	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)
		// 只验证同时配置了ACL和ViewKey的office view
		if viewType == model.ViewLevelTypeOffice && len(view.ACLs) > 0 && view.ViewKey != "" {
			aclName := testConfigService.getACLNameForView(view)
			aclViewPairs = append(aclViewPairs, struct {
				ViewName string
				ACLName  string
			}{
				ViewName: view.Code,
				ACLName:  aclName,
			})

			// 验证ACL定义存在
			if !strings.Contains(aclStr, fmt.Sprintf("acl %s {", aclName)) {
				t.Errorf("ACL配置文件中缺少ACL定义: %s", aclName)
				continue
			}

			// 验证在master view配置中引用了这个ACL
			if !strings.Contains(masterStr, aclName) {
				t.Errorf("master views配置文件中没有引用ACL: %s", aclName)
			} else {
				t.Logf("[Success] master配置正确引用ACL: %s", aclName)
			}

			// 验证在slave view配置中引用了这个ACL
			if !strings.Contains(slaveStr, aclName) {
				t.Errorf("slave views配置文件中没有引用ACL: %s", aclName)
			} else {
				t.Logf("[Success] slave配置正确引用ACL: %s", aclName)
			}
		} else if viewType == model.ViewLevelTypeOffice && len(view.ACLs) > 0 {
			// 记录跳过的view（有ACL但没有ViewKey）
			t.Logf("[Info] view %s 有ACL但没有ViewKey，预期跳过", view.Name)
		}
	}

	// 验证默认ACL的引用
	defaultACLName := "office_default_acl"
	if !strings.Contains(aclStr, fmt.Sprintf("acl %s {", defaultACLName)) {
		t.Errorf("ACL配置文件中缺少默认ACL定义: %s", defaultACLName)
	}

	// 验证配置文件结构的一致性
	validateConfigStructure(t, aclStr, masterStr, slaveStr)

	t.Logf("[Info] ACL-Views集成测试完成，验证了 %d 对ACL-View配置", len(aclViewPairs))
}

// validateConfigStructure 验证配置文件结构的一致性
func validateConfigStructure(t *testing.T, aclConfig, masterConfig, slaveConfig string) {
	t.Log("[Info] 开始验证配置文件结构一致性")

	// 验证ACL配置结构
	if !strings.Contains(aclConfig, "# Generated at:") {
		t.Error("ACL配置缺少生成时间")
	}
	if !strings.Contains(aclConfig, "# ACL configurations for DNS views") {
		t.Error("ACL配置缺少标题")
	}

	// 验证master配置结构
	if !strings.Contains(masterConfig, "# Server Role: master") {
		t.Error("master配置缺少角色标识")
	}
	if !strings.Contains(masterConfig, "allow-update") {
		t.Error("master配置缺少allow-update指令")
	}
	if !strings.Contains(masterConfig, "allow-transfer") {
		t.Error("master配置缺少allow-transfer指令")
	}

	// 验证slave配置结构
	if !strings.Contains(slaveConfig, "# Server Role: slave") {
		t.Error("slave配置缺少角色标识")
	}
	if !strings.Contains(slaveConfig, "allow-transfer") {
		t.Error("slave配置缺少allow-transfer指令")
	}

	t.Log("[Info] 配置文件结构验证完成")
}

// TestGenerateMainConfig 测试主配置生成功能
func TestGenerateMainConfig(t *testing.T) {
	// 初始化测试依赖
	setupConfigTestDependencies(t)

	t.Logf("[Info] 开始测试主配置生成，配置文件将生成到: %s", DefaultConfigDir)

	// 初始化context
	ctx := context.Background()

	// 调用主配置生成方法
	result, err := testConfigService.GenerateMainConfigInfo(ctx)
	if err != nil {
		t.Fatalf("生成主配置失败: %v", err)
	}

	// 验证返回结果
	if result == nil {
		t.Fatal("主配置生成返回结果为空")
	}

	// 验证返回结果内容
	if status, exists := result["status"]; !exists || status != "success" {
		t.Errorf("主配置生成状态错误，期望: success, 实际: %v", status)
	}

	if message, exists := result["message"]; !exists || message != "DNS配置文件生成成功" {
		t.Errorf("主配置生成消息错误，期望: DNS配置文件生成成功, 实际: %v", message)
	}

	expectedFiles := []string{"named.conf.keys", "named.conf.acls", "named.conf.views"}
	if files, exists := result["files"]; exists {
		fileList, ok := files.([]string)
		if !ok {
			t.Error("返回的files字段格式错误")
		} else {
			for _, expectedFile := range expectedFiles {
				found := false
				for _, file := range fileList {
					if file == expectedFile {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("返回的files列表中缺少: %s", expectedFile)
				}
			}
			t.Logf("[Info] 返回的files列表正确: %v", fileList)
		}
	} else {
		t.Error("返回结果中缺少files字段")
	}

	// 验证所有配置文件都已生成
	expectedConfigFiles := []string{
		"named.conf.keys",
		"named.conf.acls",
		"named.conf.views.master",
		"named.conf.views.slave",
	}

	for _, configFile := range expectedConfigFiles {
		filename := filepath.Join(DefaultConfigDir, configFile)
		if _, err := os.Stat(filename); err != nil {
			t.Errorf("配置文件未生成: %s", filename)
		} else {
			t.Logf("[Success] 配置文件已生成: %s", filename)
		}
	}

	// 验证zones配置文件
	zonesDir := filepath.Join(DefaultConfigDir, "zones")
	if _, err := os.Stat(zonesDir); err != nil {
		t.Errorf("zones目录未创建: %s", zonesDir)
	} else {
		// 检查zones目录下的文件
		entries, err := os.ReadDir(zonesDir)
		if err != nil {
			t.Errorf("读取zones目录失败: %v", err)
		} else {
			t.Logf("[Info] zones目录下有 %d 个文件", len(entries))
			for _, entry := range entries {
				if !entry.IsDir() {
					t.Logf("[Info] zones文件: %s", entry.Name())
				}
			}
		}
	}

	// 验证配置文件内容的一致性
	validateAllConfigFiles(t)

	t.Logf("[Info] 主配置生成测试完成")
}

// validateAllConfigFiles 验证所有配置文件的一致性
func validateAllConfigFiles(t *testing.T) {
	t.Log("[Info] 开始验证所有配置文件的一致性")

	// 读取所有配置文件
	files := map[string]string{
		"keys":         filepath.Join(DefaultConfigDir, "named.conf.keys"),
		"acls":         filepath.Join(DefaultConfigDir, "named.conf.acls"),
		"views.master": filepath.Join(DefaultConfigDir, "named.conf.views.master"),
		"views.slave":  filepath.Join(DefaultConfigDir, "named.conf.views.slave"),
	}

	contents := make(map[string]string)
	for name, path := range files {
		content, err := os.ReadFile(path)
		if err != nil {
			t.Errorf("读取配置文件失败 %s: %v", name, err)
			continue
		}
		contents[name] = string(content)
	}

	// 验证文件都有生成时间戳
	for name, content := range contents {
		if !strings.Contains(content, "Generated at:") {
			t.Errorf("配置文件 %s 缺少生成时间戳", name)
		}
	}

	// 验证ACL和Views的一致性
	if aclContent, exists := contents["acls"]; exists {
		if masterContent, exists := contents["views.master"]; exists {
			// 检查ACL中定义的每个ACL是否在master配置中被引用
			validateACLReferences(t, aclContent, masterContent, "master")
		}
		if slaveContent, exists := contents["views.slave"]; exists {
			// 检查ACL中定义的每个ACL是否在slave配置中被引用
			validateACLReferences(t, aclContent, slaveContent, "slave")
		}
	}

	t.Log("[Info] 所有配置文件一致性验证完成")
}

// validateACLReferences 验证ACL引用的一致性
func validateACLReferences(t *testing.T, aclContent, viewContent, configType string) {
	// 提取ACL定义
	lines := strings.Split(aclContent, "\n")
	var aclNames []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "acl ") && strings.Contains(line, " {") {
			// 提取ACL名称
			parts := strings.Split(line, " ")
			if len(parts) >= 2 {
				aclName := parts[1]
				aclNames = append(aclNames, aclName)
			}
		}
	}

	// 只验证以office_开头的ACL（这些是真正应该在views中被引用的）
	for _, aclName := range aclNames {
		if strings.HasPrefix(aclName, "office_") {
			if !strings.Contains(viewContent, aclName) {
				t.Errorf("ACL %s 在 %s 配置中未被引用", aclName, configType)
			} else {
				t.Logf("[Success] ACL %s 在 %s 配置中正确引用", aclName, configType)
			}
		} else {
			// 记录跳过的非office ACL
			t.Logf("[Info] 跳过验证非office ACL: %s", aclName)
		}
	}
}

func TestGenerateViewsConfigFile(t *testing.T) {
	// 初始化测试依赖
	setupConfigTestDependencies(t)

	// 直接使用DefaultConfigDir，不创建临时目录
	t.Logf("[Info] views配置文件将生成到: %s", DefaultConfigDir)

	// 初始化context
	ctx := context.Background()

	// 调用新的按cluster分组的views配置生成方法
	err := testConfigService.GenerateViewsConfigFileByCluster(ctx)
	if err != nil {
		t.Fatalf("生成views配置失败: %v", err)
	}

	// 验证view目录是否创建
	viewDir := filepath.Join(DefaultConfigDir, "view")
	if _, err := os.Stat(viewDir); err != nil {
		t.Errorf("view目录不存在: %s", viewDir)
		return
	}
	t.Logf("[Success] view目录已创建: %s", viewDir)

	// 读取view目录，查看有哪些cluster目录
	entries, err := os.ReadDir(viewDir)
	if err != nil {
		t.Fatalf("读取view目录失败: %v", err)
	}

	if len(entries) == 0 {
		t.Error("view目录下没有cluster目录")
		return
	}

	// 验证每个cluster目录下的配置文件
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		clusterName := entry.Name()
		clusterDir := filepath.Join(viewDir, clusterName)
		t.Logf("[Info] 检查cluster目录: %s", clusterDir)

		// 检查是否有配置文件
		clusterFiles, err := os.ReadDir(clusterDir)
		if err != nil {
			t.Errorf("读取cluster目录失败: %v", err)
			continue
		}

		hasConfig := false
		for _, file := range clusterFiles {
			if !file.IsDir() && (file.Name() == "named.conf.views.master" || file.Name() == "named.conf.views.slave") {
				hasConfig = true
				t.Logf("[Success] 找到配置文件: %s/%s", clusterName, file.Name())
			}
		}

		if !hasConfig {
			t.Errorf("cluster %s 没有生成配置文件", clusterName)
		}
	}

	// 验证配置文件内容（选择第一个cluster进行详细验证）
	if len(entries) > 0 {
		firstCluster := entries[0].Name()
		masterFile := filepath.Join(viewDir, firstCluster, "named.conf.views.master")
		slaveFile := filepath.Join(viewDir, firstCluster, "named.conf.views.slave")

		// 验证master配置文件内容
		if content, err := os.ReadFile(masterFile); err == nil {
			contentStr := string(content)

			// 验证基本结构
			if !strings.Contains(contentStr, "# named.conf.views - DNS view configurations") {
				t.Error("master配置文件缺少标题注释")
			}
			if !strings.Contains(contentStr, "# Server Role: master") {
				t.Error("master配置文件缺少角色标识")
			}
			if !strings.Contains(contentStr, `view "default" {`) {
				t.Error("master配置文件缺少default view")
			}

			t.Logf("[Info] master配置文件大小: %d bytes", len(content))
		}

		// 验证slave配置文件内容（如果存在）
		if content, err := os.ReadFile(slaveFile); err == nil {
			contentStr := string(content)

			// 验证基本结构
			if !strings.Contains(contentStr, "# named.conf.views - DNS view configurations") {
				t.Error("slave配置文件缺少标题注释")
			}
			if !strings.Contains(contentStr, "# Server Role: slave") {
				t.Error("slave配置文件缺少角色标识")
			}
			if !strings.Contains(contentStr, `view "default" {`) {
				t.Error("slave配置文件缺少default view")
			}
			// 验证新功能：slave配置应该排除master IP
			if !strings.Contains(contentStr, "/32;") {
				t.Error("slave配置文件缺少master IP排除配置")
			}

			t.Logf("[Info] slave配置文件大小: %d bytes", len(content))
		}
	}

	// 获取实际的views数据来验证
	views, err := testConfigService.store.View().GetAllView(ctx)
	if err != nil {
		t.Fatalf("获取views数据失败: %v", err)
	}

	// 统计office级别的view数量
	officeViewCount := 0
	for _, view := range views {
		if view.LevelType == "office" {
			officeViewCount++
		}
	}

	t.Logf("[Info] 数据库中有 %d 个views，其中 %d 个office级别", len(views), officeViewCount)
	t.Logf("[Info] views配置文件生成完成")
}

func TestGenerateZonesConfig(t *testing.T) {
	// 初始化测试依赖
	setupConfigTestDependencies(t)

	// 直接使用DefaultConfigDir，不创建临时目录
	t.Logf("[Info] 配置文件将生成到: %s", DefaultConfigDir)

	// 初始化context
	ctx := context.Background()

	// 获取实际的views数据来验证生成的文件
	views, err := testConfigService.store.View().GetAllView(ctx)
	if err != nil {
		t.Fatalf("获取views数据失败: %v", err)
	}

	if len(views) == 0 {
		t.Skip("数据库中没有view数据，跳过测试")
	}

	// 调试：打印所有view的详细信息
	t.Logf("[Info] 数据库中的views详细信息:")
	for i, view := range views {
		t.Logf("  View %d: ID=%d, Name=%s, LevelType=%s, Code=%s",
			i+1, view.ID, view.Name, view.LevelType, view.Code)
	}

	// 调用新的按cluster分组的zone配置生成方法
	err = testConfigService.GenerateViewsConfigFileByCluster(ctx)
	if err != nil {
		t.Fatalf("生成zone配置文件失败: %v", err)
	}

	// 验证每个office级别view的master和slave配置文件是否生成
	officeViewCount := 0
	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)

		// 只检查office级别的view
		if viewType != model.ViewLevelTypeOffice {
			t.Logf("[Info] 跳过非office级别的view: %s (level_type: %s)", view.Name, view.LevelType)
			continue
		}

		officeViewCount++

		// 验证master配置文件（在zones子目录中）
		masterFile := filepath.Join(DefaultConfigDir, "zones", "named."+view.Code+".master.zones")
		_, err = os.Stat(masterFile)
		if err != nil {
			t.Errorf("master配置文件不存在: %s", masterFile)
		} else {
			t.Logf("[Success] master配置文件已生成: %s", masterFile)
		}

		// 验证slave配置文件（在zones子目录中）
		slaveFile := filepath.Join(DefaultConfigDir, "zones", "named."+view.Code+".slave.zones")
		_, err = os.Stat(slaveFile)
		if err != nil {
			t.Errorf("slave配置文件不存在: %s", slaveFile)
		} else {
			t.Logf("[Success] slave配置文件已生成: %s", slaveFile)
		}
	}

	// 统计生成的文件数量（在zones子目录中）
	zonesDir := filepath.Join(DefaultConfigDir, "zones")
	files, err := os.ReadDir(zonesDir)
	if err != nil {
		t.Errorf("读取zones目录失败: %v", err)
	} else {
		zoneFiles := 0
		for _, file := range files {
			if strings.HasSuffix(file.Name(), ".zones") {
				zoneFiles++
			}
		}
		t.Logf("[Info] 总共生成了 %d 个zone配置文件", zoneFiles)
		t.Logf("[Info] 数据库中有 %d 个views，其中 %d 个office级别", len(views), officeViewCount)

		// 期望生成的文件数量 = office级别views数量 * 2 (master + slave)
		expectedFiles := officeViewCount * 2
		if zoneFiles != expectedFiles {
			t.Errorf("生成的zone文件数量不正确，期望: %d，实际: %d", expectedFiles, zoneFiles)
		}
	}
}
