package v1

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"text/template"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/tmpl"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

var (
	DefaultAcls    = []string{"any"}
	DefaultACLName = "office_default_acl"
)

// getACLNameForView 根据view获取对应的ACL名称
func (s *configService) getACLNameForView(view *model.View) string {
	viewType := model.ToViewLevelType(view.LevelType)
	if viewType == model.ViewLevelTypeDefault {
		return "office_default_acl"
	}
	return fmt.Sprintf("office_%s_acl", view.Code)
}

func (s *configService) getAclsConfig(ctx context.Context, views []*model.View) (*api.NamedConfACLsData, error) {
	zap.L().Debug("初始化acls配置")
	span, _ := apm.StartSpan(ctx, "getAclsConfig", "service")
	defer span.End()

	aclData := &api.NamedConfACLsData{
		GeneratedAt: time.Now().Format("2006-01-02 15:04:05"),
		ACLs:        make([]*api.ACLData, 0),
	}

	var defaultView *model.View
	for _, view := range views {
		viewType := model.ToViewLevelType(view.LevelType)

		if viewType == model.ViewLevelTypeDefault {
			defaultView = view
			continue
		}

		// 因为除了office级别的view，其它级别的view都是用来做配置继承使用的
		if viewType != model.ViewLevelTypeOffice {
			continue
		}

		var aclList []string
		if len(view.ACLs) > 0 {
			if err := json.Unmarshal(view.ACLs, &aclList); err != nil {
				zap.L().Error("解析ACL失败", zap.Error(err), zap.String("view", view.Name))
				continue
			}
		}

		if len(aclList) == 0 {
			zap.L().Warn("视图没有配置ACL，跳过生成", zap.String("view", view.Name))
			continue
		}

		if view.ViewKey == "" {
			zap.L().Warn("视图没有配置ViewKey，跳过ACL生成", zap.String("view", view.Name), zap.String("view_code", view.Code))
			continue
		}

		// 生成ACL名称
		aclName := s.getACLNameForView(view)
		aclData.ACLs = append(aclData.ACLs, &api.ACLData{
			ACLName:          aclName,
			AddressMatchList: aclList,
			ViewNames:        []string{view.Name},
		})
		zap.L().Info("处理view ACL配置", zap.String("view", view.Name), zap.String("acl_name", aclName))

		// 处理默认的view的acl配置
		if defaultView != nil {
			var defaultACLs []string
			if len(defaultView.ACLs) > 0 {
				if err := json.Unmarshal(defaultView.ACLs, &defaultACLs); err != nil {
					zap.L().Error("解析默认view ACL失败", zap.Error(err))
				}
			}

			if len(defaultACLs) == 0 {
				defaultACLs = DefaultAcls
			}

			aclData.DefaultACL = &api.ACLData{
				ACLName:          DefaultACLName,
				AddressMatchList: defaultACLs,
				ViewNames:        []string{defaultView.Name},
			}
		} else {
			// 如果没有默认view，创建一个默认的ACL
			aclData.DefaultACL = &api.ACLData{
				ACLName:          DefaultACLName,
				AddressMatchList: DefaultAcls,
				ViewNames:        []string{"default"},
			}
		}
	}

	return aclData, nil
}

// renderAndSaveACLsConfig 渲染模板并保存named.conf.acls文件, 注意这里是渲染文件
func (s *configService) renderAndSaveACLsConfig(aclsData *api.NamedConfACLsData) error {
	// 渲染模板
	tmplContent, err := template.New("acls").Parse(tmpl.NamedConfACLsTemplate)
	if err != nil {
		zap.L().Error("解析ACL模板失败", zap.Error(err))
		return fmt.Errorf("解析ACL模板失败: %w", err)
	}

	// 初始化一个数据容器，用于保存渲染后的模板内容
	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, aclsData); err != nil {
		zap.L().Error("渲染ACL模板失败", zap.Error(err))
		return fmt.Errorf("渲染ACL模板失败: %w", err)
	}

	// 确保目录存在
	if err := os.MkdirAll(DefaultConfigDir, 0755); err != nil {
		zap.L().Error("创建配置目录失败", zap.Error(err), zap.String("dir", DefaultConfigDir))
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 保存文件
	filename := filepath.Join(DefaultConfigDir, "named.conf.acls")
	if err := os.WriteFile(filename, buf.Bytes(), 0644); err != nil {
		zap.L().Error("保存ACL配置文件失败", zap.Error(err), zap.String("filename", filename))
		return fmt.Errorf("保存ACL配置文件失败: %w", err)
	}

	zap.L().Info("named.conf.acls配置文件生成成功",
		zap.String("filename", filename),
		zap.Int("office_acls", len(aclsData.ACLs)),
		zap.Bool("has_default_acl", aclsData.DefaultACL != nil))

	return nil
}

// GenerateACLsConfig 生成named.conf.acls配置文件
func (s *configService) GenerateACLsConfig(ctx context.Context, aclsData *api.NamedConfACLsData) error {
	zap.L().Info("开始生成named.conf.acls配置文件")
	span, _ := apm.StartSpan(ctx, "GenerateACLsConfig", "service")
	defer span.End()

	// 渲染模板并保存文件
	return s.renderAndSaveACLsConfig(aclsData)
}

// renderACLsConfigTemplate 渲染ACLs配置模板
func (s *configService) renderACLsConfigTemplate(aclsData *api.NamedConfACLsData) ([]byte, error) {
	tmplContent, err := template.New("acls").Parse(tmpl.NamedConfACLsTemplate)
	if err != nil {
		zap.L().Error("解析ACL模板失败", zap.Error(err))
		return nil, fmt.Errorf("解析ACL模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmplContent.Execute(&buf, aclsData); err != nil {
		zap.L().Error("渲染ACL模板失败", zap.Error(err))
		return nil, fmt.Errorf("渲染ACL模板失败: %w", err)
	}

	return buf.Bytes(), nil
}

// generateACLsConfigContent 生成named.conf.acls配置文件内容到内存
func (s *configService) generateACLsConfigContent(ctx context.Context, aclsData *api.NamedConfACLsData) (*api.ConfigFile, error) {
	zap.L().Debug("开始生成named.conf.acls配置文件内容")
	span, _ := apm.StartSpan(ctx, "generateACLsConfigContent", "service")
	defer span.End()

	// 渲染模板
	if aclsData == nil {
		return nil, fmt.Errorf("aclsData为空")
	}

	content, err := s.renderACLsConfigTemplate(aclsData)
	if err != nil {
		zap.L().Error("渲染ACL模板失败", zap.Error(err))
		return nil, err
	}

	zap.L().Info("named.conf.acls配置文件内容生成成功",
		zap.Int("office_acls", len(aclsData.ACLs)),
		zap.Bool("has_default_acl", aclsData.DefaultACL != nil))

	return &api.ConfigFile{
		Name:    "named.conf.acls",
		Content: content,
	}, nil
}
