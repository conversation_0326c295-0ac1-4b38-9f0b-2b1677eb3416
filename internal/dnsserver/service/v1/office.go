package v1

import (
	"context"

	cmdbMessage "ks-knoc-server/internal/common/base/message/cmdbserver"
	"ks-knoc-server/internal/common/openapi"
	"ks-knoc-server/internal/dnsserver/store"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type OfficeService interface {
	GetOfficeList(ctx context.Context) (*cmdbMessage.QueryOfficeListMessage, error)
}

type officeService struct {
	store store.Factory
}

var _ OfficeService = (*officeService)(nil)

func newOfficeService(srv *service) *officeService {
	return &officeService{store: srv.store}
}

func (o *officeService) GetOfficeList(ctx context.Context) (*cmdbMessage.QueryOfficeListMessage, error) {
	zap.L().Debug("GetOfficeList Service Called")
	span, _ := apm.StartSpan(ctx, "GetOfficeList", "service")
	defer span.End()

	offices, err := openapi.GetOffices()
	if err != nil {
		zap.L().Error("GetOffices Error", zap.Error(err))
		return nil, err
	}

	officeList := cmdbMessage.NewQueryOfficeListMessage()
	for _, office := range offices.Data {
		officeCode := office.Code
		if officeCode == "" {
			zap.L().Warn("职场的编码为空", zap.String("office_name", office.Name))
			continue
		}
		officeList.Add(&cmdbMessage.QueryOfficeMessage{
			ID:          office.ID,
			Name:        office.Name,
			Code:        office.Code,
			Description: office.Description,
			OfficeType:  office.OfficeType,
			Address:     office.Address,
			HostByIDC:   office.HostByIDC,
			Status:      office.Status,
			CreateTime:  office.CreateTime,
			UpdateTime:  office.UpdateTime,
		})
	}

	return &officeList, nil
}
