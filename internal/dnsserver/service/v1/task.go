package v1

import (
	"context"
	"encoding/json"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	message "ks-knoc-server/internal/common/base/message/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/store"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type TaskService interface {
	GetTaskList(ctx context.Context, req *api.TaskListRequest) (*message.TaskListResponse, error)
	RetryTaskDetail(ctx context.Context, detailID int64) error
}

type taskService struct {
	store store.Factory
}

func newTaskService(s *service) TaskService {
	return &taskService{
		store: s.store,
	}
}

// GetTaskList 获取任务列表
func (s *taskService) GetTaskList(ctx context.Context, req *api.TaskListRequest) (*message.TaskListResponse, error) {
	zap.L().Debug("GetTaskList Service called")
	span, ctx := apm.StartSpan(ctx, "GetTaskList", "service")
	defer span.End()

	taskList, total, err := s.store.Task().GetTaskList(ctx, req)
	if err != nil {
		zap.L().Error("GetTaskList Service error", zap.Error(err))
		return nil, err
	}

	// 初始化响应
	resp := message.NewTaskListResponse()
	resp.Total = total
	resp.Page = int64(req.Page)
	resp.PageSize = int64(req.PageSize)

	for _, task := range taskList {
		taskResp := message.NewTaskResponse()
		taskResp.ID = task.ID
		taskResp.Status = task.Status.String()
		taskResp.Operator = task.Operator
		taskResp.ExecuteTime = task.ExecuteTime
		taskResp.TotalDetails = task.TotalDetails
		taskResp.CreateTime = task.CreateTime

		targetInfo := &model.TaskTargetInfo{}
		err = json.Unmarshal(task.TargetInfo, targetInfo)
		if err != nil {
			zap.L().Error("反序列化目标信息失败", zap.Error(err))
			return nil, err
		}
		taskResp.TaskRecords = make([]*message.TaskRecordResponse, 0)
		for _, record := range targetInfo.Records {
			taskRecord := &message.TaskRecordResponse{
				Name:       record.Name,
				Action:     record.Action,
				OldValue:   record.OldValue,
				NewValue:   record.NewValue,
				TTL:        record.TTL,
				MXPriority: record.MXPriority,
				Enabled:    record.Enabled,
			}
			taskResp.TaskRecords = append(taskResp.TaskRecords, taskRecord)
		}

		for _, server := range targetInfo.Servers {
			taskServer := &message.TaskServerResponse{
				ID:     server.ID,
				IP:     server.IP,
				Name:   server.Name,
				Role:   server.Role.String(),
				Status: server.Status.String(),
			}
			taskResp.TaskServers = append(taskResp.TaskServers, taskServer)
		}

		taskDetails, err := s.store.Task().GetTaskDetailsByTaskID(ctx, task.ID)
		if err != nil {
			zap.L().Error("GetTaskDetailsByTaskID Service error", zap.Error(err))
			return nil, err
		}

		for _, detail := range taskDetails {
			record := &model.RecordInfo{}
			err = json.Unmarshal(detail.ReqMsg, record)
			if err != nil {
				zap.L().Error("反序列化记录信息失败", zap.Error(err))
				return nil, err
			}
			taskDetail := &message.TaskDetailResponse{
				ID:         detail.ID,
				OpType:     detail.OpType,
				Status:     detail.Status.String(),
				ServerName: detail.ServerName,
				ServerIP:   detail.ServerIP,
				ZoneName:   detail.ZoneName,
				ViewName:   detail.ViewName,
				Record: struct {
					Name       string `json:"name"`
					RecordType string `json:"type"`
					Action     string `json:"action"`
					Value      string `json:"value"`
					TTL        int    `json:"ttl"`
					OldValue   string `json:"old_value,omitempty"`
					NewValue   string `json:"new_value"`
					MXPriority int    `json:"mx_priority,omitempty"`
					Enabled    bool   `json:"enabled,omitempty"`
				}{
					Name:       record.Name,
					RecordType: record.Type,
					Action:     record.Action,
					Value:      record.OldValue,
					TTL:        record.TTL,
					OldValue:   record.OldValue,
					NewValue:   record.NewValue,
					MXPriority: record.MXPriority,
					Enabled:    record.Enabled,
				},
				Message:    detail.ResMsg,
				Duration:   detail.ResTs - detail.ReqTs,
				CreateTime: detail.CreateAt,
				UpdateTime: detail.UpdateAt,
			}
			taskResp.TaskDetails = append(taskResp.TaskDetails, taskDetail)
		}

		resp.TaskList = append(resp.TaskList, taskResp)
	}

	return resp, nil
}

// RetryTaskDetail 重试单个TaskDetail
func (s *taskService) RetryTaskDetail(ctx context.Context, detailID int64) error {
	zap.L().Info("开始重试TaskDetail", zap.Int64("detail_id", detailID))
	span, ctx := apm.StartSpan(ctx, "RetryTaskDetail", "service")
	defer span.End()

	// 调用store的重试方法（状态验证已在store层处理）
	if err := s.store.Task().RetryTaskDetail(ctx, detailID); err != nil {
		zap.L().Error("重试TaskDetail失败", zap.Error(err), zap.Int64("detail_id", detailID))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return err
	}

	zap.L().Info("TaskDetail重试任务下发成功", zap.Int64("detail_id", detailID))
	return nil
}
