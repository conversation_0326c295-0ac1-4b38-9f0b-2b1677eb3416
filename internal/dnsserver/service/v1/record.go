package v1

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	api "ks-knoc-server/internal/common/base/api/dnsserver"
	message "ks-knoc-server/internal/common/base/message/dnsserver"
	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/dnsserver/store"
	"ks-knoc-server/pkg/utils"

	"github.com/go-sql-driver/mysql"
	"github.com/miekg/dns"
	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	hostnameRegex = regexp.MustCompile(`^(@|\*|[a-z0-9]([a-z0-9-]*[a-z0-9])?)$`)
	rpzRegex      = regexp.MustCompile(`^(@|\*([a-z0-9.-]*[a-z0-9])?\.([a-z0-9.-]*[a-z0-9])?|[a-z0-9]([a-z0-9.-]*[a-z0-9])?)$`)
)

var (
	ErrRecordAlreadyExists = errors.New("记录已存在")
)

type RecordService interface {
	// CreateBlacklistRecord 创建黑名单记录
	CreateBlacklistRecord(ctx context.Context, r []*api.CreateBlacklistDnsRecordRequest) ([]*model.DNSRecord, error)
	// GetRecordList 获取记录列表
	GetRecordList(ctx context.Context, r *api.QueryDnsRecordRequest) (*message.QueryRecordListResponse, error)
	// GetRecordInViews 获取记录在视图中的分布
	GetRecordInViews(ctx context.Context, r *api.QueryRecordInViewsRequest) (*message.QueryRecordListInViewResponse, error)
	// InitRpzCache 初始化rpz解析记录的缓存
	InitRpzCache(ctx context.Context) error
	// IsCacheReady 判断缓存是否就绪
	IsCacheReady() bool
	// UpdateRecord 更新记录
	UpdateRecord(ctx context.Context, r *api.UpdateDnsRecordRequest) (*UpdateResult, error)
}

type recordService struct {
	store store.Factory
	// 保护rpzCache的并发访问
	mu sync.RWMutex
	// 缓存就绪状态
	cacheReady bool
}

func newRecordService(srv *service) RecordService {
	rs := &recordService{
		store:      srv.store,
		cacheReady: false,
	}

	return rs
}

// 创建一个辅助函数来排序并比较两个字符串切片
func sortedEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	// 创建切片的副本以避免修改原始数据
	aCopy := make([]string, len(a))
	copy(aCopy, a)
	bCopy := make([]string, len(b))
	copy(bCopy, b)

	// 对两个切片进行排序
	sort.Strings(aCopy)
	sort.Strings(bCopy)

	// 比较排序后的切片
	return slices.Equal(aCopy, bCopy)
}

type UpdateResult struct {
	Changes *model.RecordChanges `json:"changes"` // 变更记录
}

// findRelatedOfficeViews 递归查找与指定视图相关的office视图
// viewMap: 所有视图的映射表
// viewID: 当前视图 ID
// childrenMap: 父视图到子视图的映射表
// result: 结果收集器，用于收集所有找到的office视图
func (rs *recordService) findRelatedOfficeViews(viewMap map[int64]*model.View, childrenMap map[int64][]int64, viewID int64, result map[int64]*model.View) {
	// 获取当前视图
	view, exist := viewMap[viewID]
	if !exist {
		zap.L().Error("ID为 %d 的视图不存在", zap.Int64("view_id", viewID))
		return
	}

	// 检查当前视图类型
	viewLevelType := model.ToViewLevelType(view.LevelType)

	// 如果是office视图，直接添加到结果中
	if viewLevelType == model.ViewLevelTypeOffice {
		result[viewID] = view
		return
	}

	// 获取当前视图的所有子视图
	children, exist := childrenMap[viewID]
	if !exist || len(children) == 0 {
		// 没有子视图，结束递归
		return
	}

	// 递归处理每个子视图
	for _, childID := range children {
		rs.findRelatedOfficeViews(viewMap, childrenMap, childID, result)
	}
}

// getOfficeViews 获取与当前视图相关的office视图
// 如果view_type为default或biz，则递归查询view的子视图，直到查到所有office视图为止
// 从性能的角度来考量，这种效率并不高，但是view数量并不多，因此这种性能损耗可以忽略
func (rs *recordService) getOfficeViews(ctx context.Context, viewID int64) ([]*model.View, error) {
	// 获取当前视图信息
	views, err := rs.store.View().GetViewByIDs(ctx, []int64{viewID})
	if err != nil {
		zap.L().Error("获取视图信息失败", zap.Error(err), zap.Int64("view_id", viewID))
		return nil, err
	}

	if len(views) == 0 {
		zap.L().Error("ID为 %d 的视图不存在", zap.Int64("view_id", viewID))
		return nil, fmt.Errorf("ID为 %d 的视图不存在", viewID)
	}

	currentView := views[0]
	viewLevelType := model.ToViewLevelType(currentView.LevelType)

	// 如果当前就是office视图，直接返回
	if viewLevelType == model.ViewLevelTypeOffice {
		return []*model.View{currentView}, nil
	}

	// 获取所有视图
	allViews, err := rs.store.View().GetAllView(ctx)
	if err != nil {
		zap.L().Error("获取所有视图失败", zap.Error(err))
		return nil, err
	}

	// 构建视图映射表
	viewMap := make(map[int64]*model.View)
	for _, v := range allViews {
		viewMap[v.ID] = v
	}

	// 构建父子视图关系映射表
	childrenMap := make(map[int64][]int64)
	for _, v := range allViews {
		if v.ParentID > 0 {
			childrenMap[v.ParentID] = append(childrenMap[v.ParentID], v.ID)
		}
	}

	// 使用递归函数查找相关的office视图
	resultMap := make(map[int64]*model.View)
	rs.findRelatedOfficeViews(viewMap, childrenMap, viewID, resultMap)

	// 将结果转换为切片
	result := make([]*model.View, 0, len(resultMap))
	for _, v := range resultMap {
		result = append(result, v)
	}

	// 如果是default视图且没有找到相关的office视图，则获取所有没有指定父视图的office视图
	if viewLevelType == model.ViewLevelTypeDefault && len(result) == 0 {
		for _, v := range allViews {
			if model.ToViewLevelType(v.LevelType) == model.ViewLevelTypeOffice && v.ParentID == 0 {
				result = append(result, v)
			}
		}
	}

	return result, nil
}

// getUpdatedRecords 获取更新后的记录
//
// 返回值:
//   - newRecords: 需要创建的记录
//   - updateRecords: 需要更新的记录
//   - deleteRecords: 需要删除的记录
//   - changes: 记录变更信息
//   - err: 错误信息
func (rs *recordService) getUpdatedRecords(ctx context.Context, r *api.UpdateDnsRecordRequest, zone *model.Zone, view *model.View) (
	newRecords []*model.DNSRecord, updateRecords []*model.DNSRecord, deleteRecords []*model.DNSRecord, changes *model.RecordChanges, err error) {
	var (
		// 记录变更前后的变化
		now = time.Now().Unix()
		// 级联影响的记录mapping
		impactMap = make(map[int64]*model.ViewImpact)
		// 初始化一个切片，用于保存可能会受到级联影响的视图
		affectedViews = make([]*model.View, 0)
	)

	// 【新增】初始化用户真实意图变更列表
	// 在处理过程中直接构建，避免后续复杂的计算和过滤
	userIntentChanges := make([]model.RecordChange, 0)

	// 当前视图的层级类型
	viewLevelType := model.ToViewLevelType(view.LevelType)

	// 如果是biz或default层级，需要检查是否会影响office层级
	if viewLevelType == model.ViewLevelTypeBiz || viewLevelType == model.ViewLevelTypeDefault {
		// 获取与当前视图子级的office视图
		officeViews, err := rs.getOfficeViews(ctx, r.ViewID)
		if err != nil {
			zap.L().Error("获取office视图失败", zap.Error(err), zap.Int64("parent_id", r.ViewID))
			return nil, nil, nil, nil, err
		}

		// 将office视图添加到受影响的视图列表中
		affectedViews = append(affectedViews, officeViews...)
	}

	// 首先需要先对应视图，区域下，该域名解析的记录先给查出来
	// recordType我这里不做限制，目前存在一个记录，在不同的view下type不一样的情况，比如有点是A，有的是CNAME
	recordsSnapShot, err := rs.store.Record().GetRecordList(ctx, zone.ID, view.ID, r.Name, "", "")
	if err != nil {
		zap.L().Error("获取记录失败",
			zap.Error(err),
			zap.Int64("zone_id", zone.ID),
			zap.Int64("view_id", view.ID),
			zap.String("name", r.Name))
		return nil, nil, nil, nil, err
	}

	// 构建一个当前对应解析记录的map的现状，用于快速查找
	recordsSnapShotMap := make(map[int64]*model.DNSRecord)
	for _, record := range recordsSnapShot {
		recordsSnapShotMap[record.ID] = record
	}

	// 初始化changes，用户真实意图将在处理过程中构建
	changes = &model.RecordChanges{
		ViewID:      view.ID,
		ViewName:    view.Name,
		ViewLevel:   viewLevelType.GetViewLevelDescribe(),
		Changes:     make([]model.RecordChange, 0),
		TaskChanges: make([]model.RecordChange, 0), // 当前视图下用户的需求变更，不包含计算的结果
		Impacts:     make([]*model.ViewImpact, 0),
	}

	// 使用currentValues和updatedValues来保存当前视图和更新后的视图的值
	// 供前端用来做变更前后的对比，这个值是增，删，改后的聚合的记录
	currentValues := model.NewRecordValueMap()
	updatedValues := model.NewRecordValueMap()

	// 收集当前视图中所有记录的值（更新前）
	for _, record := range recordsSnapShot {
		currentValues.Set(record.ID, record.Value)
	}

	// 先把updatedValues设置为currentValues一致，后续根据实际情况进行变更
	for k, v := range currentValues {
		updatedValues.Set(k, v)
	}

	// 循环遍历处理每一条数据, Records中会包含要创建和更新的记录
	for _, record := range r.Records {
		// 创建一个compare对象，用于后续的更新操作
		cmp := &compare{
			request:       r,
			record:        &record,
			zone:          zone,
			view:          view,
			current:       &currentValues,
			updated:       &updatedValues,
			snapShot:      recordsSnapShotMap,
			affectedViews: affectedViews,
			impactMap:     impactMap,
			ts:            now,
		}

		// 如果说record为0的话，那么就说明这是一个要新创建的记录
		if record.RecordID == 0 {
			// 【新增】构建用户创建意图
			userIntentChanges = append(userIntentChanges, model.RecordChange{
				RecordID:  0, // 新创建的记录ID为0
				Name:      r.Name,
				Type:      record.RType,
				OldValue:  "", // 新创建的记录没有旧值
				NewValue:  record.Value,
				TTL:       int(record.TTL),
				IsChanged: true,
				Action:    model.RecordChangeCreate.String(),
				ViewID:    view.ID,
			})

			newRecordList, recordChanges, err := rs.updateCreate(ctx, cmp)
			if err != nil {
				zap.L().Error("更新记录失败", zap.Error(err), zap.Int64("record_id", record.RecordID))
				return nil, nil, nil, nil, err
			}
			// 将新创建的记录添加到newRecords中
			newRecords = append(newRecords, newRecordList...)
			changes.TaskChanges = append(changes.TaskChanges, recordChanges...)
		} else {
			// 【新增】构建用户更新意图（包含完整的旧值信息）
			var oldValue, oldType string
			var oldTTL int
			if existingRecord, exists := recordsSnapShotMap[record.RecordID]; exists {
				oldValue = existingRecord.Value
				oldType = existingRecord.RType
				oldTTL = existingRecord.TTL
			}

			userIntentChanges = append(userIntentChanges, model.RecordChange{
				RecordID:  record.RecordID,
				Name:      r.Name,
				Type:      oldType,  // 使用数据库中的类型
				OldValue:  oldValue, // 从数据库记录中获取的旧值
				NewValue:  record.Value,
				TTL:       oldTTL, // 使用数据库中的TTL
				IsChanged: true,
				Action:    model.RecordChangeUpdate.String(),
				ViewID:    view.ID,
			})

			updateRecordList, recordChanges, err := rs.updateUpdate(ctx, cmp)
			if err != nil {
				zap.L().Error("更新记录失败", zap.Error(err), zap.Int64("record_id", record.RecordID))
				return nil, nil, nil, nil, err
			}
			updateRecords = append(updateRecords, updateRecordList...)
			changes.TaskChanges = append(changes.TaskChanges, recordChanges...)
		}
	}

	// 收集更新的记录ID
	updatedIds := make([]int64, 0)
	for _, record := range updateRecords {
		updatedIds = append(updatedIds, record.ID)
	}

	// 【新增】构建用户删除意图
	for _, recordID := range r.DeleteRecordIds {
		var oldValue, oldType string
		var oldTTL int
		if existingRecord, exists := recordsSnapShotMap[recordID]; exists {
			oldValue = existingRecord.Value
			oldType = existingRecord.RType
			oldTTL = existingRecord.TTL
		}

		userIntentChanges = append(userIntentChanges, model.RecordChange{
			RecordID:  recordID,
			Name:      r.Name,
			Type:      oldType,  // 从数据库记录中获取的类型
			OldValue:  oldValue, // 从数据库记录中获取的旧值
			NewValue:  "",       // 删除操作没有新值
			TTL:       oldTTL,   // 从数据库记录中获取的TTL
			IsChanged: true,
			Action:    model.RecordChangeDelete.String(),
			ViewID:    view.ID,
		})
	}

	// 处理删除的记录
	cmp := &compare{
		request:       r,
		view:          view,
		zone:          zone,
		snapShot:      recordsSnapShotMap,
		affectedViews: affectedViews,
		impactMap:     impactMap,
		current:       &currentValues,
		updated:       &updatedValues,
		ts:            now,
	}
	deleteRecordList, recordChanges, err := rs.updateDelete(ctx, cmp, updatedIds)
	if err != nil {
		zap.L().Error("处理删除记录失败", zap.Error(err))
		return nil, nil, nil, nil, err
	}
	deleteRecords = append(deleteRecords, deleteRecordList...)
	changes.TaskChanges = append(changes.TaskChanges, recordChanges...)

	// 把当前的值和更新后的值都记录下来
	changes.ViewOldValues = currentValues.ValuesList()
	changes.ViewNewValues = updatedValues.ValuesList()

	for _, v := range impactMap {
		changes.Impacts = append(changes.Impacts, v)
	}

	// 设置用户的真实意图变更, 不包含继承的一些选项；
	changes.Changes = userIntentChanges

	return newRecords, updateRecords, deleteRecords, changes, nil
}

// UpdateRecord 更新DNS记录
// dryRun: 是否是模拟更新
// 注意：当前批量更新，限定只能改某个view下的某个zone
func (rs *recordService) UpdateRecord(ctx context.Context, r *api.UpdateDnsRecordRequest) (*UpdateResult, error) {
	zap.L().Debug("UpdateRecord Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateRecord", "service")
	defer span.End()

	// 对请求参数进行去重处理
	rs.deduplicateRequest(r)

	// 获取当前视图信息
	views, err := rs.store.View().GetViewByIDs(ctx, []int64{r.ViewID})
	if err != nil || len(views) == 0 {
		zap.L().Error("获取视图信息失败", zap.Error(err), zap.Int64("view_id", r.ViewID))
		return nil, fmt.Errorf("获取视图信息失败: %w", err)
	}
	view := views[0]

	// 获取区域信息
	zone, err := rs.store.Zone().GetZoneByID(ctx, r.ZoneID)
	if err != nil {
		zap.L().Error("获取区域信息失败", zap.Error(err), zap.Int64("zone_id", r.ZoneID))
		return nil, fmt.Errorf("获取区域信息失败: %w", err)
	}

	// 记录区域名称，用于日志
	zap.L().Debug("更新记录", zap.String("zone_name", zone.Name))

	// 初始化一个response
	response := &UpdateResult{}

	// 获取更新后的记录
	newRecords, updateRecords, deleteRecords, changes, err := rs.getUpdatedRecords(ctx, r, zone, view)
	if err != nil {
		zap.L().Error("获取更新记录失败", zap.Error(err))
		return nil, err
	}

	// dryRun模式下，不进行实际更新
	if !*r.DryRun {
		// 设置一个完整的事务，涉及db的变更，以及更新任务的下发
		txErr := rs.store.Common().Transaction(func(txStore store.Factory) error {
			// 执行实际更新
			if len(newRecords) > 0 {
				// 校验创建数据的合法性
				if err := rs.checkRecordsBeforeCreate(ctx, newRecords, zone); err != nil {
					zap.L().Error("校验创建数据的合法性失败", zap.Error(err))
					return err
				}

				if err := txStore.Record().CreateRecordBatch(ctx, newRecords); err != nil {
					zap.L().Error("批量插入记录失败", zap.Error(err))
					return err
				}
			}

			if len(updateRecords) > 0 {
				if err := txStore.Record().UpdateRecord(ctx, updateRecords); err != nil {
					zap.L().Error("批量更新记录失败", zap.Error(err))
					return err
				}
			}

			if len(deleteRecords) > 0 {
				deleteIds := make([]int64, 0, len(deleteRecords))
				for _, record := range deleteRecords {
					deleteIds = append(deleteIds, record.ID)
				}
				if err := txStore.Record().DeleteRecordBatch(ctx, deleteIds); err != nil {
					zap.L().Error("批量删除记录失败", zap.Error(err))
					return err
				}
			}

			// 非dryRun模式下，需要真正的下发操作的任务
			// 创建异步任务处理实际的DNS更新, 这块需要同步操作，涉及到两部分的变更
			// 1. 更新DNS的数据库
			// 2. 更新Record Task的数据库
			zap.L().Debug("创建DNS更新任务")
			// 生成任务，任务详情
			task, taskDetails, err := rs.generateTasks(ctx, zone, changes)
			if err != nil {
				zap.L().Error("生成任务失败", zap.Error(err))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return fmt.Errorf("生成任务失败: %w", err)
			}

			if task == nil || len(taskDetails) == 0 {
				zap.L().Info("无需要更新的DNS记录, 无需下发任务")
				return nil
			}

			if err := txStore.Task().CreateTask(ctx, task, taskDetails); err != nil {
				zap.L().Error("创建DNS任务失败", zap.Error(err))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return err
			}

			return nil
		})

		if txErr != nil {
			zap.L().Error("事务执行失败", zap.Error(txErr))
			return nil, txErr
		}

		wg := &sync.WaitGroup{}

		// 如果都无问题，需要更新rpz缓存
		for _, record := range newRecords {
			wg.Add(1)
			go func(record *model.DNSRecord) {
				defer wg.Done()
				if err := rs.updateRpzCache(record, "create"); err != nil {
					zap.L().Error("更新RPZ缓存失败", zap.Error(err))
				}
			}(record)
		}

		for _, record := range updateRecords {
			wg.Add(1)
			go func(record *model.DNSRecord) {
				defer wg.Done()
				if err := rs.updateRpzCache(record, "update"); err != nil {
					zap.L().Error("更新RPZ缓存失败", zap.Error(err))
				}
			}(record)
		}

		for _, record := range deleteRecords {
			wg.Add(1)
			go func(record *model.DNSRecord) {
				defer wg.Done()
				if err := rs.updateRpzCache(record, "delete"); err != nil {
					zap.L().Error("更新RPZ缓存失败", zap.Error(err))
				}
			}(record)
		}

		wg.Wait()

		zap.L().Debug("RPZ缓存更新完成")
	}

	// 返回变更信息
	response.Changes = changes
	return response, nil
}

// GetRecordInViews 获取记录在视图中的分布
func (rs *recordService) GetRecordInViews(ctx context.Context, r *api.QueryRecordInViewsRequest) (*message.QueryRecordListInViewResponse, error) {
	zap.L().Debug("GetRecordInViews Service Called")
	span, ctx := apm.StartSpan(ctx, "GetRecordInViews", "service")
	defer span.End()

	// 构造一个查询请求
	recordRequest := &api.QueryDnsRecordRequest{
		ZoneID:   r.ZoneID,
		Name:     r.Name,
		Page:     r.Page,
		PageSize: r.PageSize,
		Group:    false, // 此时不需要进行分组合并
		Like:     r.WildCard,
	}

	// 查询zone信息
	zone, err := rs.store.Zone().GetZoneByID(ctx, r.ZoneID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zap.L().Error("查询Zone信息失败", zap.Error(err), zap.Int64("zone_id", r.ZoneID))
			return nil, errors.New("zone不存在")
		}
		zap.L().Error("查询Zone信息失败", zap.Error(err), zap.Int64("zone_id", r.ZoneID))
		return nil, err
	}

	// 转换为message，返回给调用方
	resp := message.NewQueryRecordListInViewResponse()
	resp.Page = r.Page
	resp.PageSize = r.PageSize

	// 查询记录信息
	recordList, total, err := rs.store.Record().GetRecordListWithPagination(ctx, recordRequest)
	if err != nil {
		zap.L().Error("查询记录信息失败", zap.Error(err), zap.String("name", r.Name))
		return nil, err
	}

	// 如果没有数据的话，直接返回空的resp即可
	if len(recordList) == 0 {
		return resp, nil
	}

	// 设置总数
	resp.Total = total

	// 查询View信息
	viewSet := make(map[int64]struct{})
	for _, rr := range recordList {
		if _, ok := viewSet[rr.ViewID]; !ok {
			viewSet[rr.ViewID] = struct{}{}
		}
	}

	// 构建viewIds
	viewIdsList := make([]int64, 0)
	for viewId := range viewSet {
		viewIdsList = append(viewIdsList, viewId)
	}

	// 获取Views
	views, err := rs.store.View().GetViewByIDs(ctx, viewIdsList)
	if err != nil {
		zap.L().Error("查询View信息失败", zap.Error(err))
		return nil, err
	}

	// 构建viewMap, 方便后续快速查询使用
	viewMap := make(map[int64]*model.View)
	for _, view := range views {
		if _, ok := viewMap[view.ID]; !ok {
			viewMap[view.ID] = view
		}
	}

	recordMap := make(map[string]*message.QueryRecordListInViewMessage)
	for _, rr := range recordList {
		// 验证每条记录的类型是否合法
		recordType := model.ToRecordType(rr.RType)
		if recordType == model.RecordTypeInvalid {
			zap.L().Error("不合法的记录类型", zap.String("record_type", rr.RType), zap.Int64("record_id", rr.ID))
			return nil, fmt.Errorf("不合法的记录类型: %s", rr.RType)
		}

		// 使用 name-viewid-rtype 作为唯一键，因为同一个域名在同一个视图下可能有不同类型的记录
		uniqueKey := fmt.Sprintf("%s-%d-%s", rr.Name, rr.ViewID, rr.RType)
		if _, ok := recordMap[uniqueKey]; !ok {
			recordMap[uniqueKey] = &message.QueryRecordListInViewMessage{
				Name:        rr.Name,
				Fqdn:        rr.Name + "." + zone.Name,
				Description: rr.Description,
				ViewID:      rr.ViewID,
				ViewName:    viewMap[rr.ViewID].Name,
				RType:       recordType.String(), // 使用当前记录的实际类型
				Value: []message.QueryRecordListInViewRecordBaseInfo{{
					RecordID:  int64(rr.ID),
					Value:     rr.Value,
					Creator:   rr.Creator,
					Owner:     rr.Owner,
					CreatedAt: rr.CreateTime,
					UpdatedAt: rr.UpdateTime,
				}},
			}
		} else {
			recordMap[uniqueKey].Value = append(recordMap[uniqueKey].Value, message.QueryRecordListInViewRecordBaseInfo{
				RecordID:  int64(rr.ID),
				Value:     rr.Value,
				Creator:   rr.Creator,
				Owner:     rr.Owner,
				CreatedAt: rr.CreateTime,
				UpdatedAt: rr.UpdateTime,
			})
		}
	}

	for _, rr := range recordMap {
		resp.Items = append(resp.Items, rr)
	}

	return resp, nil
}

// GetRecordList 获取Zone下的记录列表, 目前唯一做的限制就是限制只能查询一个Zone下的，目前看同时查询好几个zone下的好像没啥意义
func (rs *recordService) GetRecordList(ctx context.Context, r *api.QueryDnsRecordRequest) (*message.QueryRecordListResponse, error) {
	zap.L().Debug("GetRecordList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetRecordList", "service")
	defer span.End()

	var err error

	// 查询zone信息, Zone是必须传递的
	zone, err := rs.store.Zone().GetZoneByID(ctx, r.ZoneID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zap.L().Error("查询Zone信息失败", zap.Error(err), zap.Int64("zone_id", r.ZoneID))
			return nil, errors.New("zone不存在")
		}
		zap.L().Error("查询Zone信息失败", zap.Error(err), zap.Int64("zone_id", r.ZoneID))
		return nil, err
	}

	// 初始化一个viewList
	var viewList []*model.View
	if len(r.ViewIDs) != 0 {
		// 如果用户指定了查询哪些视图的信息，那么就查询这些视图下的信息
		viewList, err = rs.store.View().GetViewByIDs(ctx, r.ViewIDs)
		if err != nil {
			zap.L().Error("查询视图失败", zap.Error(err), zap.Int64s("view_ids", r.ViewIDs))
			return nil, err
		}
	} else {
		// 如果不指定的话，那么就查询所有视图下的信息
		viewList, err = rs.store.View().GetAllView(ctx)
		if err != nil {
			zap.L().Error("查询所有视图失败", zap.Error(err))
			return nil, err
		}
	}

	// 查询完了以后，如果视图列表为空，那么就返回错误
	if len(viewList) == 0 {
		zap.L().Error("视图不存在", zap.Int64s("view_ids", r.ViewIDs))
		return nil, errors.New("视图不存在")
	}

	// 将视图信息转换为map
	viewMap := make(map[int64]*model.View)
	for _, view := range viewList {
		if _, ok := viewMap[view.ID]; !ok {
			viewMap[view.ID] = view
		}
	}

	// 如果传递了record记录类型，需要提前判断一下这个是否是合法的
	if r.RType != "" {
		rt := model.ToRecordType(r.RType)
		if !rt.IsValid() {
			zap.L().Error("记录类型不合法", zap.String("record_type", r.RType))
			return nil, errors.New("记录类型不合法")
		}
	}

	// 查询对应Zone下的记录列表
	recordList, total, err := rs.store.Record().GetRecordListWithPagination(ctx, r)
	if err != nil {
		zap.L().Error("查询Zone下的记录列表失败", zap.Error(err), zap.Int64("zone_id", r.ZoneID))
		return nil, err
	}

	// 转换为message，返回给调用方
	resp := message.NewQueryRecordListResponse()
	resp.Total = total
	resp.Page = r.Page
	resp.PageSize = r.PageSize
	for _, rr := range recordList {
		resp.Items = append(resp.Items, &message.QueryRecordListMessage{
			ID:          utils.ToInt64(rr.ID),
			Name:        rr.Name,
			Fqdn:        rr.Name + "." + zone.Name,
			Description: rr.Description,
			Creator:     rr.Creator,
			ViewID:      rr.ViewID,
			ViewName:    viewMap[rr.ViewID].Name,
			CreatedAt:   rr.CreateTime,
			UpdatedAt:   rr.UpdateTime,
		})
	}
	return resp, nil
}

// CheckRecordName 校验记录的主机名是否合法
// 针对Rpz类型的区域和非rpz类型的区域，判断规则是不一样的。
// 劫持域和黑洞域都用的是rpz类型的
// 权威域和主机名域用的是标准类型的Zone
func CheckRecordName(zone *model.Zone, name string) error {
	zoneType := model.ToZoneType(zone.ZoneType)
	switch zoneType {
	case model.ZoneTypeDomain, model.ZoneTypeHost:
		if !hostnameRegex.MatchString(name) {
			return errors.New("主机名不合法")
		}
	case model.ZoneTypeRPZ, model.ZoneTypeBlackList:
		if !rpzRegex.MatchString(name) {
			return errors.New("主机名不合法")
		}
	default:
		return fmt.Errorf("不支持对区域类型[%s]的记录进行主机名校验", zoneType)
	}
	return nil
}

func (rs *recordService) checkRecordsBeforeCreate(ctx context.Context, rrs []*model.DNSRecord, zone *model.Zone) error {
	zt := model.ToZoneType(zone.ZoneType)
	for _, rr := range rrs {
		labels := strings.Split(rr.Name, ".")

		// 2025-05-23 临时处理，当zoneType为rpz的时候，暂时不允许添加泛解析记录，因为暂时没有处理rpz的覆盖问题
		// 黑洞区域虽然也是rpz类型的，但是需要区别对待，黑洞是允许加泛解析的
		if zt == model.ZoneTypeRPZ && len(labels) > 1 && strings.TrimSpace(labels[0]) == "*" {
			return fmt.Errorf("RPZ区域不支持泛解析记录")
		}
		// 如果TTL为0，则设置为默认TTL
		if rr.TTL == 0 {
			rr.TTL = model.DefaultTTL
		}

		// 校验view是否存在
		views, err := rs.store.View().GetViewByIDs(ctx, []int64{rr.ViewID})
		if err != nil {
			zap.L().Error("查询view失败", zap.Error(err))
			return err
		}

		if len(views) == 0 {
			zap.L().Error("视图不存在", zap.Int64("view_id", rr.ViewID))
			return errors.New("view不存在")
		}

		// 校验域名的合法性
		if err := CheckRecordName(zone, rr.Name); err != nil {
			zap.L().Error("域名校验失败", zap.Error(err), zap.String("name", rr.Name))
			return fmt.Errorf("域名校验失败: %s, %w", rr.Name, err)
		}

		domainNodeParts := strings.Split(rr.Name, ".")
		if len(domainNodeParts) == 0 {
			zap.L().Error("域名不合法", zap.String("name", rr.Name))
			return fmt.Errorf("域名不合法: %s", rr.Name)
		}

		// 泛解析记录需要设置IsWildCard为true，以*开头
		if strings.TrimSpace(domainNodeParts[0]) == "*" {
			rr.IsWildCard = true
		}

		// 校验value是否是合法的
		value := strings.TrimSpace(rr.Value)
		v, err := rs.recordValidator(ctx, rr.RType, zone, views[0], value, rr.Name)
		if err != nil {
			zap.L().Error("校验记录失败", zap.Error(err))
			return err
		}

		rr.Value = v

		// 检查RPZ记录的覆盖情况
		// 检查rpz覆盖的条件需要满足
		// 1、当前解析是泛解析
		// 2、当前限定rpz类型的区域需要关注，黑洞先不关注，虽然黑洞也是rpz类型的区域
		if rr.IsWildCard && model.ToZoneType(zone.ZoneType) == model.ZoneTypeRPZ {
			if err := rs.checkRpzRecordCoverage(ctx, rr, zone, views[0]); err != nil {
				zap.L().Warn("RPZ记录覆盖检查警告", zap.Error(err))
				return err
			}
		}
	}
	return nil
}

// CreateBlacklistRecord 创建黑名单记录
func (rs *recordService) CreateBlacklistRecord(ctx context.Context, r []*api.CreateBlacklistDnsRecordRequest) ([]*model.DNSRecord, error) {
	zap.L().Debug("CreateBlacklistRecord Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateBlacklistRecord", "service")
	defer span.End()

	var (
		zoneID     int64
		rrList     = make([]*model.DNSRecord, 0)
		now        = time.Now().Unix()
		recordsMap = make(map[string]struct{})
	)

	for _, rr := range r {
		// 校验记录类型
		if !model.ToRecordType(rr.RType).IsValid() {
			zap.L().Error("记录类型不合法", zap.String("record_type", rr.RType))
			return nil, errors.New("记录类型不合法")
		}

		// 如果zoneID为0，说明之前没有设置过
		if zoneID == 0 {
			zoneID = rr.ZoneID
		}

		if zoneID != rr.ZoneID {
			zap.L().Error("批量创建记录时, zoneID不一致", zap.Int64("zone_id", rr.ZoneID))
			return nil, errors.New("批量创建记录时, zoneID不一致")
		}

		// 这里是避免批量添加的记录中存在重复的记录
		uniqueKey := fmt.Sprintf("%s-%s-%s", rr.Name, rr.RType, rr.Value)
		if _, ok := recordsMap[uniqueKey]; ok {
			zap.L().Error("批量创建记录中, 存在重复的记录", zap.String("name", rr.Name), zap.String("type", rr.RType), zap.String("value", rr.Value))
			return nil, fmt.Errorf("批量创建记录中, 存在重复的记录, name: %s, type: %s, value: %s", rr.Name, rr.RType, rr.Value)
		}
		recordsMap[uniqueKey] = struct{}{}
	}

	// 查询对应的zone是否存在
	zone, err := rs.store.Zone().GetZoneByID(ctx, zoneID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zap.L().Error("zone不存在")
			return nil, errors.New("zone不存在")
		}
		zap.L().Error("查询zone失败", zap.Error(err))
		return nil, err
	}

	if zone == nil {
		return nil, errors.New("zone不存在")
	}

	for _, rr := range r {
		wildCard := false
		if strings.HasPrefix(rr.Name, "*") {
			wildCard = true
		}
		newRR := &model.DNSRecord{
			Name:        rr.Name,
			RType:       rr.RType,
			TTL:         rr.TTL,
			Description: rr.Description,
			ZoneID:      zoneID,
			ViewID:      rr.ViewID,
			Creator:     rr.Creator,
			Owner:       rr.Owner,
			Value:       rr.Value,
			IsWildCard:  wildCard,
			CreateTime:  now,
			UpdateTime:  now,
		}

		rrList = append(rrList, newRR)
	}

	// 校验创建数据的合法性
	if err := rs.checkRecordsBeforeCreate(ctx, rrList, zone); err != nil {
		return nil, err
	}

	// 获取视图信息
	views, err := rs.store.View().GetViewByIDs(ctx, []int64{rrList[0].ViewID})
	if err != nil || len(views) == 0 {
		zap.L().Error("获取视图信息失败", zap.Error(err), zap.Int64("view_id", rrList[0].ViewID))
		return nil, fmt.Errorf("获取视图信息失败: %w", err)
	}
	view := views[0]

	// 设置事务，包含记录创建和任务下发
	txErr := rs.store.Common().Transaction(func(txStore store.Factory) error {
		// 创建记录
		if err := rs.store.Record().CreateRecordBatch(ctx, rrList); err != nil {
			var mysqlErr *mysql.MySQLError
			if errors.As(err, &mysqlErr) {
				if mysqlErr.Number == 1062 {
					zap.L().Error("创建记录失败, 记录已存在", zap.Error(err))
					return ErrRecordAlreadyExists
				}
			}
			zap.L().Error("创建记录失败", zap.Error(err))
			return err
		}

		// 下发DNS更新任务
		zap.L().Debug("创建DNS更新任务")
		changes, err := rs.buildRecordChangesForCreate(ctx, rrList, view)
		if err != nil {
			zap.L().Error("构建RecordChanges失败", zap.Error(err))
			return err
		}
		if err := rs.handleUpdateRecordTasks(ctx, zone, changes); err != nil {
			zap.L().Error("创建DNS更新任务失败", zap.Error(err))
			return err
		}

		return nil
	})

	if txErr != nil {
		zap.L().Error("事务执行失败", zap.Error(txErr))
		return nil, txErr
	}

	// 更新缓存, 异步搞就行
	go func() {
		for _, record := range rrList {
			if err := rs.updateRpzCache(record, "create"); err != nil {
				zap.L().Error("更新RPZ缓存失败", zap.Error(err))
			}
		}
	}()

	return rrList, nil
}

func (rs *recordService) recordValidator(ctx context.Context, rType string, zone *model.Zone, view *model.View, value string, name string) (string, error) {
	v, err := recordCheck(rType, zone, value)
	if err != nil {
		zap.L().Error("校验记录失败", zap.Error(err))
		return "", err
	}
	rt := model.ToRecordType(rType)
	switch rt {
	case model.RecordTypeA:
		// 验证记录名称是否是合法的域名
		if err := CheckRecordName(zone, name); err != nil {
			zap.L().Error("A记录名称不合法", zap.Error(err), zap.String("name", name))
			return "", fmt.Errorf("a记录名称不合法: %w", err)
		}
	case model.RecordTypeCNAME:
		// CNAME的记录前提得在当前zone下存在，否则cname的target就是一个不合法的记录
		if model.ValidRPZ(model.ResponsePolicy(value)) {
			return v, nil
		}

		viewType := model.ToViewLevelType(view.LevelType)

		switch viewType {
		case model.ViewLevelTypeBiz:
			// 在Biz视图下，CNAME记录的目标必须是当前Zone下的A记录
			records, err := rs.store.Record().GetRecordList(ctx, zone.ID, view.ID, value, "A", "")
			if err != nil {
				zap.L().Error("查询CNAME记录的目标失败", zap.Error(err))
				return "", err
			}
			if len(records) == 0 {
				zap.L().Error("CNAME记录的目标不存在", zap.String("record_value", v))
				return "", errors.New("CNAME记录的目标不存在")
			}
		case model.ViewLevelTypeOffice:
			// 如果是office层级的view，不仅仅要看当前Zone下的A记录，还需要看Biz层级的Zone下的A记录
			recordsFromOffice, err := rs.store.Record().GetRecordList(ctx, zone.ID, view.ID, value, "A", "")
			if err != nil {
				zap.L().Error("查询CNAME记录的目标失败", zap.Error(err))
				return "", err
			}
			if len(recordsFromOffice) > 0 {
				return v, nil
			}

			zap.L().Debug("office层级没有对应的CNAME记录的目标，查询Biz层级的Zone下的A记录")

			// office层级没有对应的cname，此时需要查询Biz层级的Zone下的A记录
			bizViewID := view.ParentID
			recordsFromBiz, err := rs.store.Record().GetRecordList(ctx, zone.ID, bizViewID, value, "A", "")
			if err != nil {
				zap.L().Error("查询CNAME记录的目标失败", zap.Error(err))
				return "", err
			}
			if len(recordsFromBiz) == 0 {
				zap.L().Error("CNAME记录的目标在Zone下不存在", zap.String("view_type", viewType.String()))
				return "", errors.New("CNAME记录的目标在Zone下不存在")
			}
		default:
			// 查询当前Zone下的CNAME记录的目标是否存在
			records, err := rs.store.Record().GetRecordList(ctx, zone.ID, view.ID, value, "A", "")
			if err != nil {
				zap.L().Error("查询CNAME记录的目标失败", zap.Error(err))
				return "", err
			}

			if len(records) == 0 {
				zap.L().Error("CNAME记录的目标不存在", zap.String("record_value", v))
				return "", errors.New("CNAME记录的目标不存在")
			}
		}

	}
	return v, nil
}

// recordCheck 仅校验记录在内容层面的合规性，不校验和现有数据的冲突
func recordCheck(rType string, zone *model.Zone, value string) (string, error) {
	// 校验记录值不能为空
	if value == "" {
		return "", errors.New("记录值不能为空")
	}

	// 校验记录类型是否合法
	rt := model.ToRecordType(rType)
	switch rt {
	case model.RecordTypeA:
		// 判断A记录对应的IP地址是否是合法的
		if !utils.IsValidIPv4(value) {
			return "", errors.New("dns A记录的值不合法")
		}
	case model.RecordTypeCNAME:
		target := ""
		if model.ToZoneType(zone.ZoneType) == model.ZoneTypeRPZ || model.ToZoneType(zone.ZoneType) == model.ZoneTypeBlackList {
			// 如果value是rpz指定的策略，则直接返回
			if model.ValidRPZ(model.ResponsePolicy(value)) {
				return value, nil
			}
			// 非rpz指定的策略value，末尾不允许有.，否则会导致从根域进行查找
			if strings.HasSuffix(value, ".") {
				return "", errors.New("rpz类型的区域不允许cname以.结尾")
			}
			// 如果不是rpz指定的策略，说明是cname到了一个域名，单纯做的是重定向，做正常域名的检测
			target = dns.Fqdn(fmt.Sprintf("%s.%s", value, zone.Name))
		} else {
			target = dns.Fqdn(value)
		}
		if !utils.IsValidCNAME(target) {
			return "", errors.New("CNAME记录的值不合法")
		}
	case model.RecordTypeMX:
		zap.L().Info("MX还没有被支持")
	case model.RecordTypeTXT:
		zap.L().Info("TXT还没有被支持")
	case model.RecordTypeSOA:
		zap.L().Info("SOA还没有被支持")
	case model.RecordTypeSRV:
		zap.L().Info("SRV还没有被支持")
	default:
		return "", errors.New("不支持的记录类型")
	}

	return value, nil
}

// buildRecordChangesForCreate 为创建的记录构建RecordChanges结构，复用handleUpdateRecordTasks逻辑
func (rs *recordService) buildRecordChangesForCreate(ctx context.Context, records []*model.DNSRecord, view *model.View) (*model.RecordChanges, error) {
	changes := &model.RecordChanges{
		Changes: make([]model.RecordChange, 0, len(records)),
		Impacts: nil,
	}

	for _, record := range records {
		changes.Changes = append(changes.Changes, model.RecordChange{
			RecordID:  record.ID,
			Name:      record.Name,
			Type:      record.RType,
			OldValue:  "",
			NewValue:  record.Value,
			TTL:       record.TTL,
			IsChanged: true,
			Action:    "create",
			ViewID:    view.ID,
		})
	}

	// 如果是default或biz视图，需要生成impacts来获取正确的DNS服务器
	viewLevelType := model.ToViewLevelType(view.LevelType)
	if viewLevelType == model.ViewLevelTypeBiz || viewLevelType == model.ViewLevelTypeDefault {
		// 获取与当前视图相关的office视图
		officeViews, err := rs.getOfficeViews(ctx, view.ID)
		if err != nil {
			return nil, fmt.Errorf("获取office视图失败: %w", err)
		}

		// 为每个office视图生成impact
		impacts := make([]*model.ViewImpact, 0, len(officeViews))
		for _, officeView := range officeViews {
			impact := &model.ViewImpact{
				ViewID:    officeView.ID,
				ViewName:  officeView.Name,
				ViewLevel: model.ToViewLevelType(officeView.LevelType).GetViewLevelDescribe(),
				OldValue:  []string{},
				NewValue:  []string{},
				IsChanged: true,
			}
			impacts = append(impacts, impact)
		}
		changes.Impacts = impacts
	}

	return changes, nil
}

// deduplicateRequest 对UpdateDnsRecordRequest进行去重处理
// 去重逻辑：
// 1. Records: 基于RecordID去重，RecordID=0的记录基于完整内容去重
// 2. DeleteRecordIds: 直接去重
func (rs *recordService) deduplicateRequest(r *api.UpdateDnsRecordRequest) {
	zap.L().Debug("开始去重处理",
		zap.Int("records_count", len(r.Records)),
		zap.Int("delete_ids_count", len(r.DeleteRecordIds)))

	// 1. 对Records进行去重
	if len(r.Records) > 0 {
		recordMap := make(map[int64]*api.UpdateDnsRecord)        // 用于RecordID>0的记录去重
		createRecordMap := make(map[string]*api.UpdateDnsRecord) // 用于RecordID=0的记录去重
		deduplicatedRecords := make([]api.UpdateDnsRecord, 0)

		originalCount := len(r.Records)

		for i, record := range r.Records {
			if record.RecordID > 0 {
				// 更新记录：基于RecordID去重
				if _, exists := recordMap[record.RecordID]; !exists {
					recordMap[record.RecordID] = &record
					deduplicatedRecords = append(deduplicatedRecords, record)
				} else {
					zap.L().Warn("发现重复的更新记录，已去重",
						zap.Int("index", i),
						zap.Int64("record_id", record.RecordID),
						zap.String("type", record.RType),
						zap.String("value", record.Value))
				}
			} else {
				// 创建记录：基于完整内容去重
				// 生成唯一键：RType_Value_TTL_ZoneID_ViewID_Creator_Owner
				key := fmt.Sprintf("%s_%s_%d_%d_%s_%s_%s",
					r.Name, record.RType, r.ZoneID, r.ViewID, record.Value, record.Creator, record.Owner)

				if _, exists := createRecordMap[key]; !exists {
					createRecordMap[key] = &record
					deduplicatedRecords = append(deduplicatedRecords, record)
				} else {
					zap.L().Warn("发现重复的创建记录，已去重",
						zap.Int("index", i),
						zap.String("type", record.RType),
						zap.String("value", record.Value),
						zap.Int32("ttl", record.TTL),
						zap.String("key", key))
				}
			}
		}

		r.Records = deduplicatedRecords

		if originalCount != len(deduplicatedRecords) {
			zap.L().Info("Records去重完成",
				zap.Int("original_count", originalCount),
				zap.Int("deduplicated_count", len(deduplicatedRecords)),
				zap.Int("removed_count", originalCount-len(deduplicatedRecords)))
		}
	}

	// 2. 对DeleteRecordIds进行去重
	if len(r.DeleteRecordIds) > 0 {
		deleteIdMap := make(map[int64]struct{})
		deduplicatedDeleteIds := make([]int64, 0)

		originalCount := len(r.DeleteRecordIds)

		for i, deleteId := range r.DeleteRecordIds {
			if _, exists := deleteIdMap[deleteId]; !exists {
				deleteIdMap[deleteId] = struct{}{}
				deduplicatedDeleteIds = append(deduplicatedDeleteIds, deleteId)
			} else {
				zap.L().Warn("发现重复的删除记录ID，已去重",
					zap.Int("index", i),
					zap.Int64("delete_id", deleteId))
			}
		}

		r.DeleteRecordIds = deduplicatedDeleteIds

		if originalCount != len(deduplicatedDeleteIds) {
			zap.L().Info("DeleteRecordIds去重完成",
				zap.Int("original_count", originalCount),
				zap.Int("deduplicated_count", len(deduplicatedDeleteIds)),
				zap.Int("removed_count", originalCount-len(deduplicatedDeleteIds)))
		}
	}

	zap.L().Debug("去重处理完成",
		zap.Int("final_records_count", len(r.Records)),
		zap.Int("final_delete_ids_count", len(r.DeleteRecordIds)))
}
