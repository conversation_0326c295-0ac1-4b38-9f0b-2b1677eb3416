package dnsserver

import (
	"ks-knoc-server/config"
	"ks-knoc-server/internal/dnsserver/app"
	"ks-knoc-server/internal/dnsserver/options"
)

// NewAPIServer 创建APIServer
func NewAPIServer() *app.App {
	opts := options.NewOptions()
	application := app.NewApp("DNS API Server",
		config.ProjectName,
		app.WithOptions(opts),
		app.WithRunFunc(run(opts)),
	)

	return application
}

func run(opts *options.Options) app.RunFunc {
	return func(basename string) error {
		return nil
	}
}
