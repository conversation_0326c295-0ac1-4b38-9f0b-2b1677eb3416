package options

import (
	"encoding/json"

	"ks-knoc-server/config/knoc"
	"ks-knoc-server/internal/common/http"
	"ks-knoc-server/internal/common/infra"
	"ks-knoc-server/internal/common/logger"
)

// Options 初始化依赖的选项
type Options struct {
	WebOptions   *http.WebOptions   `json:"http"`
	LogsOptions  *logger.LogOptions `json:"log"`
	MysqlOptions *infra.KDBOptions  `json:"mysql"`
}

// NewOptions 配置文件以及依赖项初始化
func NewOptions() *Options {
	// 初始化Kconf配置文件
	if err := knoc.InitConfig(); err != nil {
		panic(err)
	}
	o := Options{
		WebOptions:   http.NewWebOptions(),
		LogsOptions:  logger.NewLogOptions(),
		MysqlOptions: infra.NewKDBOptions(),
	}
	return &o
}

func (o *Options) String() string {
	data, _ := json.Marshal(o)
	return string(data)
}
