package store

import (
	"ks-knoc-server/internal/common/base/model/bpm"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	"go.uber.org/zap"
)

const (
	// OrderAbstractTableName 工单概览表
	OrderAbstractTableName = "bpm_order_abstract"
	// OrderDetailTableName 工单详情表
	OrderDetailTableName = "bpm_order_detail"
)

type BPMOrderStore interface {
	// CreateAgileOrder 创建快流程
	CreateAgileOrder(order *bpm.OrderAbstract) error
	// CreateProfessionalOrder 创建专业流程
	CreateProfessionalOrder(order *bpm.OrderAbstract, detail *bpm.OrderDetail) error
	// UpdateOrder 更新工单
	UpdateOrder(businessID string, value any) error
	// UpdateOrderDetail 更新工单详情
	UpdateOrderDetail(businessID string, value any) error
	// GetOrderByBusinessID 根据业务ID查询工单概要
	GetOrderByBusinessID(businessID string) (*bpm.OrderAbstract, error)
	// GetOrderDetailByBusinessID 根据业务ID查询工单详情
	GetOrderDetailByBusinessID(businessID string) (*bpm.OrderDetail, error)
	// GetOrderListByInitiator 我发起的
	GetOrderListByInitiator(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error)
	// GetOrderListByTodo 待办
	GetOrderListByTodo(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error)
	// GetOrderListByDone 已办
	GetOrderListByDone(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error)
	// GetOrderListByAll 全部
	GetOrderListByAll(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error)
}

type bpmOrderStore struct {
	db *kdb.Korm
}

var _ BPMOrderStore = (*bpmOrderStore)(nil)

func newBpmOrderStore(ds *DataStore) *bpmOrderStore {
	return &bpmOrderStore{
		db: ds.Db,
	}
}



func (b *bpmOrderStore) CreateAgileOrder(order *bpm.OrderAbstract) error {
	if result := b.db.Table(OrderAbstractTableName).Create(order); result.Error != nil {
		return result.Error
	}
	return nil
}

func (b *bpmOrderStore) CreateProfessionalOrder(order *bpm.OrderAbstract, detail *bpm.OrderDetail) error {
	if err := b.db.Transaction(func(tx *kdb.Korm) error {
		// 此时属于一个事物的操作应该统一使用过tx
		if err := tx.Table(OrderAbstractTableName).Create(order).Error; err != nil {
			return err
		}
		// 创建工单详情
		if err := tx.Table(OrderDetailTableName).Create(detail).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

func (b *bpmOrderStore) UpdateOrder(businessID string, value any) error {
	if result := b.db.Table(OrderAbstractTableName).Where("business_id = ?", businessID).Updates(value); result.Error != nil {
		return result.Error
	}
	return nil
}

func (b *bpmOrderStore) UpdateOrderDetail(businessID string, value any) error {
	if result := b.db.Table(OrderDetailTableName).Where("business_id = ?", businessID).Updates(value); result.Error != nil {
		return result.Error
	}
	return nil
}

func (b *bpmOrderStore) GetOrderByBusinessID(businessID string) (*bpm.OrderAbstract, error) {
	order := &bpm.OrderAbstract{}
	if result := b.db.Table(OrderAbstractTableName).Where("business_id = ?", businessID).First(order); result.Error != nil {
		return nil, result.Error
	}
	return order, nil
}

func (b *bpmOrderStore) GetOrderDetailByBusinessID(businessID string) (*bpm.OrderDetail, error) {
	order := &bpm.OrderDetail{}
	if result := b.db.Table(OrderDetailTableName).Where("business_id = ?", businessID).First(order); result.Error != nil {
		return nil, result.Error
	}
	return order, nil
}

// GetOrderListByInitiator 分页查询我发起的订单列表
func (b *bpmOrderStore) GetOrderListByInitiator(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error) {
	orderList := make([]*bpm.OrderAbstract, pageSize)
	tx := b.db.Table(OrderAbstractTableName)
	tx.Where("initiator_username = ?", operator)
	if keyword != "" {
		tx.Where("abstract like ?", "%"+keyword+"%")
	}
	// 获取工单数据
	if result := tx.Order("create_time desc").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&orderList); result.Error != nil {
		return nil, 0, result.Error
	}

	zap.L().Debug("查询信息",
		zap.String("operator", operator),
		zap.String("keyword", keyword),
		zap.Int("page", page),
		zap.Int("pageSize", pageSize))

	// 获取工单总量
	total := int64(0)
	totalTx := b.db.Table(OrderAbstractTableName)
	totalTx.Where("initiator_username = ?", operator)
	if keyword != "" {
		totalTx.Where("abstract like ?", "%"+keyword+"%")
	}
	if result := totalTx.Count(&total); result.Error != nil {
		return nil, 0, result.Error
	}
	return orderList, total, nil
}

// GetOrderListByTodo 分页查询待办订单列表
func (b *bpmOrderStore) GetOrderListByTodo(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error) {
	orderList := make([]*bpm.OrderAbstract, pageSize)
	tx := b.db.Table(OrderAbstractTableName)
	// approval_user 审批人应该是大于等于一个人，因为有竞签的情况，所以不是一个人
	tx.Where("process_state = ?", bpm.Audit).Where("FIND_IN_SET(?, approval_user)", operator)
	if keyword != "" {
		tx.Where("abstract like ?", "%"+keyword+"%")
	}
	if result := tx.Order("create_time desc").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&orderList); result.Error != nil {
		return nil, 0, result.Error
	}

	zap.L().Debug("查询信息",
		zap.String("operator", operator),
		zap.String("keyword", keyword),
		zap.Int("page", page),
		zap.Int("pageSize", pageSize))

	// 获取工单总量
	total := int64(0)
	totalTx := b.db.Table(OrderAbstractTableName)
	totalTx.Where("process_state = ?", bpm.Audit).Where("FIND_IN_SET(?, approval_user)", operator)
	if keyword != "" {
		totalTx.Where("abstract like ?", "%"+keyword+"%")
	}
	if result := totalTx.Count(&total); result.Error != nil {
		return nil, 0, result.Error
	}
	return orderList, total, nil
}

// GetOrderListByDone 分页查询已办订单列表
func (b *bpmOrderStore) GetOrderListByDone(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error) {
	orderList := make([]*bpm.OrderAbstract, pageSize)
	// 查询已办订单, 审批人包含当前登录人, 工单的状态不一定是结束状态，有可能我审批完了，但是其他人还在审批
	tx := b.db.Table(OrderAbstractTableName)
	tx.Where("FIND_IN_SET(?, approval_list)", operator)
	if keyword != "" {
		tx.Where("abstract like ?", "%"+keyword+"%")
	}
	if result := tx.Order("create_time desc").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&orderList); result.Error != nil {
		return nil, 0, result.Error
	}

	zap.L().Debug("查询信息",
		zap.String("operator", operator),
		zap.String("keyword", keyword),
		zap.Int("page", page),
		zap.Int("pageSize", pageSize))

	// 获取工单总量
	total := int64(0)
	totalTx := b.db.Table(OrderAbstractTableName).Where("FIND_IN_SET(?, approval_list)", operator)
	if keyword != "" {
		totalTx.Where("abstract like ?", "%"+keyword+"%")
	}
	if result := totalTx.Count(&total); result.Error != nil {
		return nil, 0, result.Error
	}
	return orderList, total, nil
}

// GetOrderListByAll 分页查询全部订单列表
func (b *bpmOrderStore) GetOrderListByAll(operator string, keyword string, page, pageSize int) ([]*bpm.OrderAbstract, int64, error) {
	orderList := make([]*bpm.OrderAbstract, pageSize)
	tx := b.db.Table(OrderAbstractTableName)
	if keyword != "" {
		tx.Where("abstract like ?", "%"+keyword+"%")
	}

	zap.L().Debug("查询信息",
		zap.String("operator", operator),
		zap.String("keyword", keyword),
		zap.Int("page", page),
		zap.Int("pageSize", pageSize))

	if result := tx.Order("create_time desc").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&orderList); result.Error != nil {
		return nil, 0, result.Error
	}

	// 获取工单总量
	total := int64(0)
	totalTx := b.db.Table(OrderAbstractTableName)
	if keyword != "" {
		totalTx.Where("abstract like ?", "%"+keyword+"%")
	}
	if result := totalTx.Count(&total); result.Error != nil {
		return nil, 0, result.Error
	}
	return orderList, total, nil
}
