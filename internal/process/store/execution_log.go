package store

import (
	"ks-knoc-server/internal/common/base/model/bpm"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
)

const (
	ProcessExecutionLogTableName = "process_execution_log"
)

type BPMExecutionLogStore interface {
	CreateExecutionLog(log []*bpm.ProcessExecutionLog) error
	GetExecutionLogByBusinessID(businessID string) ([]*bpm.ProcessExecutionLog, error)
	GetExecutionLogByTaskID(taskID string) ([]*bpm.ProcessExecutionLog, error)
	GetExecutionLogByActivitiID(activitiID, businessID string) ([]*bpm.ProcessExecutionLog, error)
	GetExecutionLogByEventID(eventID string) ([]*bpm.ProcessExecutionLog, error)
	UpdateExecutionLog(log *bpm.ProcessExecutionLog) error
}

type bpmExecutionLogStore struct {
	db *kdb.Korm
}

var _ BPMExecutionLogStore = (*bpmExecutionLogStore)(nil)

func newBpmExecutionLogStore(ds *DataStore) BPMExecutionLogStore {
	return &bpmExecutionLogStore{
		db: ds.Db,
	}
}

func (b *bpmExecutionLogStore) GetExecutionLogByEventID(eventID string) ([]*bpm.ProcessExecutionLog, error) {
	logs := make([]*bpm.ProcessExecutionLog, 0)
	if result := b.db.Table(ProcessExecutionLogTableName).Where("event_id = ?", eventID).Order("created_at desc").Find(&logs); result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

func (b *bpmExecutionLogStore) GetExecutionLogByActivitiID(activitiID, businessID string) ([]*bpm.ProcessExecutionLog, error) {
	logs := make([]*bpm.ProcessExecutionLog, 0)
	if result := b.db.Table(ProcessExecutionLogTableName).
		Where("node_id = ?", activitiID).
		Where("business_id = ?", businessID).
		Order("created_at desc").Find(&logs); result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

func (b *bpmExecutionLogStore) UpdateExecutionLog(log *bpm.ProcessExecutionLog) error {
	return b.db.Save(log).Error
}

func (b *bpmExecutionLogStore) GetExecutionLogByTaskID(taskID string) ([]*bpm.ProcessExecutionLog, error) {
	logs := make([]*bpm.ProcessExecutionLog, 0)
	if result := b.db.Table(ProcessExecutionLogTableName).Where("task_id = ?", taskID).Order("created_at desc").Find(&logs); result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

func (b *bpmExecutionLogStore) GetExecutionLogByBusinessID(businessID string) ([]*bpm.ProcessExecutionLog, error) {
	logs := make([]*bpm.ProcessExecutionLog, 0)
	if result := b.db.Table(ProcessExecutionLogTableName).Where("business_id = ?", businessID).Order("created_at desc").Find(&logs); result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

func (s *bpmExecutionLogStore) CreateExecutionLog(log []*bpm.ProcessExecutionLog) error {
	return s.db.Table(ProcessExecutionLogTableName).Create(log).Error
}
