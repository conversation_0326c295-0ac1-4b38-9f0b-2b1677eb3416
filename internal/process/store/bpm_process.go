package store

import (
	"context"

	"ks-knoc-server/internal/common/base/model/bpm"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

const processDetailTable = "bpm_process"

type BPMProcessStore interface {
	GetProcessByKey(ctx context.Context, key string) (*bpm.ProcessDetail, error)
}

type bpmProcessStore struct {
	db *kdb.Korm
}

var _ BPMProcessStore = (*bpmProcessStore)(nil)

func newBpmProcessStore(ds *DataStore) BPMProcessStore {
	return &bpmProcessStore{db: ds.Db}
}

// GetProcessByKey 根据流程Key获取流程详情
func (bp *bpmProcessStore) GetProcessByKey(ctx context.Context, key string) (*bpm.ProcessDetail, error) {
	zap.L().Debug("GetProcessByKey Function Called")
	span, _ := apm.StartSpan(ctx, "GetProcessByKey", "store")
	defer span.End()

	var processDetail bpm.ProcessDetail
	if result := bp.db.Table(processDetailTable).Where("process_key = ?", key).First(&processDetail); result.Error != nil {
		return nil, result.Error
	}
	return &processDetail, nil
}
