package store

import (
	"context"

	model "ks-knoc-server/internal/common/base/model/bpm"
	share "ks-knoc-server/internal/common/base/share/bpm"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type InstallOSStore interface {
	// GetInstallInfo 根据sn获取安装信息
	GetInstallInfoBySN(ctx context.Context, sn string) (*model.OsInstall, error)
	// GetInstallInfoById 根据install_id获取安装信息
	GetInstallInfoById(ctx context.Context, osInstallID int64) (*model.OsInstall, error)
	// GetInstallInfoBySNList 根据sn列表获取安装信息列表
	GetInstallInfoBySNList(ctx context.Context, snList []string, businessID string) ([]*model.OsInstall, error)
	// GetInstallInfoByBusinessID 根据工单ID获取安装信息列表
	GetInstallInfoByBusinessID(ctx context.Context, businessID string) ([]*model.OsInstall, error)
	// CreateOsInstall 创建操作系统安装信息
	CreateOsInstall(ctx context.Context, ins *model.OsInstall, tasks []*model.InstallTask) error
	// UpdateInstallInfo 更新安装信息
	UpdateInstallInfo(ctx context.Context, ins *model.OsInstall) error

	// GetInstallTasks 获取安装任务列表
	GetInstallTasks(ctx context.Context, osInstallID int64) ([]*model.InstallTask, error)
	// GetInstallTaskByID 根据task_id获取安装任务
	GetInstallTaskByID(ctx context.Context, taskID int64) (*model.InstallTask, error)
	// UpdateInstallTask 更新安装任务
	UpdateInstallTask(ctx context.Context, task *model.InstallTask) error
}

type installOSStore struct {
	db *kdb.Korm
}

func newInstallOSStore(db *DataStore) InstallOSStore {
	return &installOSStore{
		db: db.Db,
	}
}

// UpdateInstallInfo 更新安装信息
func (s *installOSStore) UpdateInstallInfo(ctx context.Context, ins *model.OsInstall) error {
	zap.L().Debug("UpdateInstallInfo Function Called")
	span, _ := apm.StartSpan(ctx, "UpdateInstallInfo", "Store")
	defer span.End()

	return s.db.Table(model.OsInstall{}.TableName()).Where("id = ?", ins.ID).Updates(ins).Error
}

// GetInstallInfoById 根据install_id获取安装信息
func (s *installOSStore) GetInstallInfoById(ctx context.Context, osInstallID int64) (*model.OsInstall, error) {
	zap.L().Debug("GetInstallInfoById Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallInfoById", "Store")
	defer span.End()

	installInfo := &model.OsInstall{}
	if err := s.db.Table(model.OsInstall{}.TableName()).
		Where("id = ?", osInstallID).First(installInfo).Error; err != nil {
		return nil, err
	}

	return installInfo, nil
}

// GetInstallInfoByBusinessID 根据工单ID获取安装信息
// 安装信息按照自增id升序排序
func (s *installOSStore) GetInstallInfoByBusinessID(ctx context.Context, businessID string) ([]*model.OsInstall, error) {
	zap.L().Debug("GetInstallInfoByBusinessID Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallInfoByBusinessID", "Store")
	defer span.End()

	installInfos := make([]*model.OsInstall, 0)
	if err := s.db.Table(model.OsInstall{}.TableName()).
		Where("business_id = ?", businessID).
		Order("id ASC").
		Find(&installInfos).Error; err != nil {
		return nil, err
	}

	return installInfos, nil
}

func (s *installOSStore) GetInstallInfoBySNList(ctx context.Context, snList []string, businessId string) ([]*model.OsInstall, error) {
	zap.L().Debug("GetInstallInfoBySNList Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallInfoBySNList", "Store")
	defer span.End()

	installInfos := make([]*model.OsInstall, 0)
	tx := s.db.Table(model.OsInstall{}.TableName())

	if len(snList) > 0 {
		tx.Where("sn IN (?)", snList)
	}

	if businessId != "" {
		tx.Where("business_id = ?", businessId)
	}

	if err := tx.Find(&installInfos).Error; err != nil {
		return nil, err
	}

	return installInfos, nil
}

func (s *installOSStore) UpdateInstallTask(ctx context.Context, task *model.InstallTask) error {
	zap.L().Debug("UpdateInstallTask Function Called")
	span, _ := apm.StartSpan(ctx, "UpdateInstallTask", "Store")
	defer span.End()

	return s.db.Table(model.InstallTask{}.TableName()).Save(task).Error
}

func (s *installOSStore) GetInstallTaskByID(ctx context.Context, taskID int64) (*model.InstallTask, error) {
	zap.L().Debug("GetInstallTaskByID Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallTaskByID", "Store")
	defer span.End()

	installTask := &model.InstallTask{}
	if err := s.db.Table(model.InstallTask{}.TableName()).Where("id = ?", taskID).First(installTask).Error; err != nil {
		return nil, err
	}

	return installTask, nil
}

// GetInstallInfoBySN 根据sn获取安装信息
func (s *installOSStore) GetInstallInfoBySN(ctx context.Context, sn string) (*model.OsInstall, error) {
	zap.L().Debug("GetInstallInfo Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallInfo", "Store")
	defer span.End()

	installInfo := &model.OsInstall{}
	if err := s.db.Table(model.OsInstall{}.TableName()).
		Where("sn = ?", sn).
		Where("install_status NOT IN (?)", []uint8{share.InstallStatusCompleted, share.InstallStatusCancelled, share.InstallStatusFailed}).
		First(installInfo).Error; err != nil {
		return nil, err
	}

	return installInfo, nil
}

// CreateOsInstall 创建操作系统安装信息
func (s *installOSStore) CreateOsInstall(ctx context.Context, ins *model.OsInstall, tasks []*model.InstallTask) error {
	zap.L().Debug("CreateOsInstall Function Called")
	span, _ := apm.StartSpan(ctx, "CreateOsInstall", "Store")
	defer span.End()

	tx := s.db.Begin()
	defer tx.Rollback()

	if err := tx.Table(model.OsInstall{}.TableName()).Create(ins).Error; err != nil {
		zap.L().Error("创建操作系统安装信息失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		tx.Rollback()
		return err
	}

	// 更新install_id
	for _, task := range tasks {
		task.OsInstallID = ins.ID
	}

	if err := tx.Table(model.InstallTask{}.TableName()).Create(tasks).Error; err != nil {
		zap.L().Error("创建安装任务失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func (s *installOSStore) GetInstallTasks(ctx context.Context, osInstallID int64) ([]*model.InstallTask, error) {
	zap.L().Debug("GetInstallTasks Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallTasks", "Store")
	defer span.End()

	installTasks := make([]*model.InstallTask, 0)
	if err := s.db.Table(model.InstallTask{}.TableName()).Where("os_install_id = ?", osInstallID).Find(&installTasks).Error; err != nil {
		return nil, err
	}

	return installTasks, nil
}
