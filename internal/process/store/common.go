package store

import "git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"

type CommonStore interface {
	Transaction(fn func(txStore Factory) error) error
}

type commonStore struct {
	db *kdb.Korm
}

func newCommonStore(db *DataStore) CommonStore {
	return &commonStore{
		db: db.Db,
	}
}

// Transaction 事务, 暴露事务给外部使用, 屏蔽内部的tx
func (c *commonStore) Transaction(fn func(factory Factory) error) error {
	return c.db.Transaction(func(tx *kdb.Korm) error {
		factory := &DataStore{
			Db: tx,
		}
		return fn(factory)
	})
}
