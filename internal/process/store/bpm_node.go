package store

import (
	"errors"

	api "ks-knoc-server/internal/common/base/api/bpm"
	model "ks-knoc-server/internal/common/base/model/bpm"
)

const (
	processNodeTableName                = "process_node_details"
	processNodeFieldDefinitionTableName = "process_node_field_definitions"
	agileFieldMetaTableName             = "agile_process_field_definitions"

	nodePrefixOnrack    = "it_dc_onrack"
	nodePrefixReInstall = "it_dc_reinstall"
)

type BPMNodeStore interface {
	GetProcessNodeByNodeID(nodeID string) (*model.ProcessNode, error)
	GetProcessNodeFieldDefinitionByNodeID(nodeID string) ([]model.ProcessNodeFieldDefinition, error)
	GetAgileFieldMetaByProcessKey(processKey string) ([]model.AgileFieldMeta, error)

	GetITDCProcessNodeFieldDefinitionByOpType(opType string) ([]model.ProcessNodeFieldDefinition, error)
}

type bpmNodeStore struct {
	ds *DataStore
}

var _ BPMNodeStore = (*bpmNodeStore)(nil)

func newBpmNodeStore(ds *DataStore) *bpmNodeStore {
	return &bpmNodeStore{ds: ds}
}

// GetITDCProcessNodeFieldDefinitionByOpType 根据opType获取IT机房流程节点字段定义
// 这个写法目前感觉很不好，因为这不是通用的，这个是针对IT机房流程的，是针对具体业务流程的硬编码，后续需要优化
func (b *bpmNodeStore) GetITDCProcessNodeFieldDefinitionByOpType(opType string) ([]model.ProcessNodeFieldDefinition, error) {
	var fields []model.ProcessNodeFieldDefinition
	switch opType {
	case api.OnRack.String():
		if err := b.ds.Db.Table(processNodeFieldDefinitionTableName).
			Where("node_id LIKE ?", nodePrefixOnrack+"%").
			Find(&fields).Error; err != nil {
			return nil, err
		}
	case api.ReInstall.String():
		if err := b.ds.Db.Table(processNodeFieldDefinitionTableName).
			Where("node_id LIKE ?", nodePrefixReInstall+"%").
			Find(&fields).Error; err != nil {
			return nil, err
		}
	default:
		return nil, errors.New("unsupported opType")
	}
	return fields, nil
}

// GetProcessNodeByNodeID 根据nodeID查询当前的审批节点
func (b *bpmNodeStore) GetProcessNodeByNodeID(nodeID string) (*model.ProcessNode, error) {
	var node model.ProcessNode
	if result := b.ds.Db.Table(processNodeTableName).Where("node_id = ?", nodeID).First(&node); result.Error != nil {
		return nil, result.Error
	}
	return &node, nil
}

func (b *bpmNodeStore) GetProcessNodeFieldDefinitionByNodeID(nodeID string) ([]model.ProcessNodeFieldDefinition, error) {
	var fields []model.ProcessNodeFieldDefinition
	if result := b.ds.Db.Table(processNodeFieldDefinitionTableName).
		Where("node_id = ?", nodeID).
		Find(&fields); result.Error != nil {
		return nil, result.Error
	}
	return fields, nil
}

func (b *bpmNodeStore) GetAgileFieldMetaByProcessKey(processKey string) ([]model.AgileFieldMeta, error) {
	var fieldMeta []model.AgileFieldMeta
	if result := b.ds.Db.Table(agileFieldMetaTableName).Where("process_key = ?", processKey).Find(&fieldMeta); result.Error != nil {
		return nil, result.Error
	}
	return fieldMeta, nil
}
