package store

import (
	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
)

type Factory interface {
	BPMOrder() BPMOrderStore
	BPMProcess() BPMProcessStore
	BPMNode() BPMNodeStore
	BPMExecutionLog() BPMExecutionLogStore
	User() UserStore
	InstallOS() InstallOSStore
	Common() CommonStore
}

type DataStore struct {
	Db *kdb.Korm
}

var _ Factory = (*DataStore)(nil)

func (ds *DataStore) BPMExecutionLog() BPMExecutionLogStore { return newBpmExecutionLogStore(ds) }

func (ds *DataStore) Common() CommonStore { return newCommonStore(ds) }

func (ds *DataStore) InstallOS() InstallOSStore { return newInstallOSStore(ds) }

func (ds *DataStore) BPMOrder() BPMOrderStore { return newBpmOrderStore(ds) }

func (ds *DataStore) BPMProcess() BPMProcessStore { return newBpmProcessStore(ds) }

func (ds *DataStore) BPMNode() BPMNodeStore { return newBpmNodeStore(ds) }

func (ds *DataStore) User() UserStore { return newUserStore(ds) }
