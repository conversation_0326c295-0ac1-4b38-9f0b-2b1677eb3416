package server

import (
	"time"

	"ks-knoc-server/internal/process/options"
	"ks-knoc-server/internal/process/route"
	v1 "ks-knoc-server/internal/process/service/v1"
	"ks-knoc-server/internal/process/store"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server ...
type Server struct {
	Log *zap.Logger
	Web *gin.Engine
	Kdb *kdb.Korm
}

// NewServer 启动服务相关中间件初始化
func NewServer(opts *options.Options) (*Server, error) {
	var err error

	// 初始化logger
	logger, err := opts.LogsOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化数据库
	db, err := opts.MysqlOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化DataStore
	ds := &store.DataStore{
		Db: db,
	}

	// 监听kafka的变动
	go v1.InitBPMKafka(ds)

	// 生成路由的同时注入ds
	g := route.APIServerRouter(opts.WebOptions.Engine, ds)
	g.Use(ginzap.Ginzap(logger, time.RFC3339, true))
	g.Use(ginzap.RecoveryWithZap(logger, true))

	return &Server{Log: logger, Web: g, Kdb: db}, nil
}
