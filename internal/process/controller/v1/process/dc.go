package process

import (
	"fmt"
	"strings"

	bpmApi "ks-knoc-server/internal/common/base/api/bpm"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/internal/common/sso"
	"ks-knoc-server/pkg/page"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (ctrl *Controller) GetOrderAuditLog(c *gin.Context) {
	zap.L().Debug("GetOrderAuditLog Function Called")
	span, ctx := apm.StartSpan(c, "GetOrderAuditLog", "controller")
	defer span.End()

	businessId := c.Query("business_id")
	if businessId == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("business_id不能为空"), nil)
		return
	}

	result, err := ctrl.svc.Process().GetOrderAuditLog(ctx, businessId)
	if err != nil {
		zap.L().Error("GetOrderAuditLog Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

// CreateDCProcess 创建机房相关工单流程
func (ctrl *Controller) CreateDCProcess(c *gin.Context) {
	zap.L().Debug("CreateDCProcess Function Called")
	span, ctx := apm.StartSpan(c, "CreateProcess", "Controller")
	defer span.End()

	var dcReq bpmApi.DCRequest
	gin.EnableJsonDecoderUseNumber()
	if err := c.ShouldBindJSON(&dcReq); err != nil {
		errorMsg := errno.GetErrMsg(err)
		zap.L().Error("CreateDCProcess ShouldBindJSON Error: " + errorMsg)
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errorMsg), nil)
		return
	}

	// 发起人
	initiator := sso.GetLoginUserName(c)

	// 创建工单
	if err := ctrl.svc.Process().CreateDCProcess(ctx, initiator, dcReq); err != nil {
		zap.L().Error("创建工单失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 如果走到这里，说明参数校验都通过了，那么就返回成功
	core.SendResponse(c, nil, nil)
}

// GetOrderList 获取工单列表
func (ctrl *Controller) GetOrderList(c *gin.Context) {
	zap.L().Debug("GetOrderList Function Called")
	span, ctx := apm.StartSpan(c, "GetOrderList", "controller")
	defer span.End()

	orderType := c.Query("orderType")
	if orderType == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("orderType不能为空"), nil)
		return
	}

	// 新增搜索关键字
	keyword := c.Query("keyword")

	// 查看工单列表只能是这几种类型，todo表示待办，done表示已办，initiated表示我发起的，如果不是的话，则返回报错
	validOrderType := []string{"all", "todo", "done", "initiated"}
	exist := false
	for _, valid := range validOrderType {
		if strings.EqualFold(orderType, valid) {
			exist = true
			break
		}
	}
	if !exist {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("orderType不合法, 可选值为all, todo, done, initiated"), nil)
		return
	}

	pageNumber, pageSizeNumber, err := page.CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	// 获取当前登录人
	operator := sso.GetLoginUserName(c)

	zap.L().Debug("当前操作人: " + operator)

	// 获取工单列表
	result, err := ctrl.svc.Process().GetProcessList(ctx, orderType, keyword, operator, pageNumber, pageSizeNumber)
	if err != nil {
		zap.L().Error("GetOrderList Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, result)
}

// GetOrderDetail 获取工单详情
func (ctrl *Controller) GetOrderDetail(c *gin.Context) {
	zap.L().Debug("GetOrderDetail Function Called")
	span, ctx := apm.StartSpan(c, "GetOrderDetail", "controller")
	defer span.End()

	// 获取工单ID
	businessId := c.Query("business_id")
	if businessId == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("business_id不能为空"), nil)
		return
	}

	// 获取工单详情
	result, err := ctrl.svc.Process().GetProcessDetail(ctx, businessId)
	if err != nil {
		zap.L().Error("GetOrderDetail Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, result)
}

func (ctrl *Controller) RejectDCProcess(c *gin.Context) {
	zap.L().Debug("RejectDCProcess Function Called")
	span, ctx := apm.StartSpan(c, "RejectDCProcess", "controller")
	defer span.End()

	req := &bpmApi.ProcessPassOrRejectRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	// 拒绝工单必须要填写拒绝原因
	if req.Comments == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("拒绝工单必须要填写拒绝原因"), nil)
		return
	}

	result, err := ctrl.svc.Process().RejectDCProcess(ctx, sso.GetLoginUserName(c), req)
	if err != nil {
		zap.L().Error("RejectDCProcess Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (ctrl *Controller) PassDCProcess(c *gin.Context) {
	zap.L().Debug("PassDCProcess Function Called")
	span, ctx := apm.StartSpan(c, "PassDCProcess", "controller")
	defer span.End()

	// 接收用户请求
	req := new(bpmApi.ProcessPassRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().PassDCProcess(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("PassDCProcess Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (ctrl *Controller) GetOrderAuditRecord(c *gin.Context) {
	zap.L().Debug("GetOrderAuditRecord Function Called")
	span, ctx := apm.StartSpan(c, "GetOrderAuditRecord", "controller")
	defer span.End()

	businessId := c.Query("business_id")
	if businessId == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("business_id不能为空"), nil)
		return
	}

	result, err := ctrl.svc.Process().GetOrderAuditRecord(ctx, businessId)
	if err != nil {
		zap.L().Error("GetOrderAuditRecord Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (ctrl *Controller) ProcessBack(c *gin.Context) {
	zap.L().Debug("ProcessBack Function Called")
	span, ctx := apm.StartSpan(c, "ProcessBack", "controller")
	defer span.End()

	req := new(bpmApi.ProcessPassOrRejectRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().ProcessBack(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("ProcessBack Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (ctrl *Controller) FrontAddSign(c *gin.Context) {
	zap.L().Debug("FrontAddSign Function Called")
	span, ctx := apm.StartSpan(c, "FrontAddSign", "controller")
	defer span.End()

	req := new(bpmApi.ProcessAddOrShiftSignRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().FrontAddSign(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("FrontAddSign Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (ctrl *Controller) AfterAddSign(c *gin.Context) {
	zap.L().Debug("AfterAddSign Function Called")
	span, ctx := apm.StartSpan(c, "AfterAddSign", "controller")
	defer span.End()

	req := new(bpmApi.ProcessAddOrShiftSignRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().AfterAddSign(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("AfterAddSign Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (ctrl *Controller) ShiftSign(c *gin.Context) {
	zap.L().Debug("ShiftSign Function Called")
	span, ctx := apm.StartSpan(c, "ShiftSign", "controller")
	defer span.End()

	req := new(bpmApi.ProcessAddOrShiftSignRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().ShiftSign(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("ShiftSign Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (ctrl *Controller) Consult(c *gin.Context) {
	zap.L().Debug("Consult Function Called")
	span, ctx := apm.StartSpan(c, "Consult", "controller")
	defer span.End()

	req := new(bpmApi.ProcessAddOrShiftSignRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().Consult(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("Consult Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// Inform 知会
func (ctrl *Controller) Inform(c *gin.Context) {
	zap.L().Debug("Inform Function Called")
	span, ctx := apm.StartSpan(c, "Inform", "controller")
	defer span.End()

	req := new(bpmApi.ProcessInformRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().Inform(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("Inform Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// StartDCProcess 启动工单
func (ctrl *Controller) StartDCProcess(c *gin.Context) {
	zap.L().Debug("StartDCProcess Function Called")
	span, ctx := apm.StartSpan(c, "StartDCProcess", "controller")
	defer span.End()

	req := new(bpmApi.ProcessStartRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().StartDCProcess(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("StartDCProcess Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// CancelDCProcess 撤销工单
func (ctrl *Controller) CancelDCProcess(c *gin.Context) {
	zap.L().Debug("CancelDCProcess Function Called")
	span, ctx := apm.StartSpan(c, "CancelDCProcess", "controller")
	defer span.End()

	req := new(bpmApi.ProcessPassOrRejectRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(fmt.Sprintf("参数校验失败: %s", errno.GetErrMsg(err))), nil)
		return
	}

	if err := ctrl.svc.Process().CancelDCProcess(ctx, sso.GetLoginUserName(c), req); err != nil {
		zap.L().Error("CancelDCProcess Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (ctrl *Controller) GetOrderInfo(c *gin.Context) {
	zap.L().Debug("GetOrderInfo Function Called")
	span, ctx := apm.StartSpan(c, "GetOrderInfo", "controller")
	defer span.End()

	// 获取工单ID
	businessId := c.Query("business_id")
	if businessId == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("business_id不能为空"), nil)
		return
	}

	// 获取工单详情
	resp, err := ctrl.svc.Process().GetOrderInfo(ctx, businessId)
	if err != nil {
		zap.L().Error("GetOrderInfo Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

// GetOrderBase 获取工单基本信息
func (ctrl *Controller) GetOrderBase(c *gin.Context) {
	zap.L().Debug("GetOrderBase Function Called")
	span, ctx := apm.StartSpan(c, "GetOrderBase", "controller")
	defer span.End()

	businessId := c.Query("business_id")
	if businessId == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("business_id不能为空"), nil)
		return
	}

	result, err := ctrl.svc.Process().GetOrderBase(ctx, businessId)
	if err != nil {
		zap.L().Error("GetOrderBase Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (ctrl *Controller) GetInstallDeviceDetail(c *gin.Context) {
	zap.L().Debug("GetInstallDeviceDetail Function Called")
	span, ctx := apm.StartSpan(c, "GetInstallDeviceDetail", "controller")
	defer span.End()

	businessId := c.Query("business_id")
	if businessId == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("business_id不能为空"), nil)
		return
	}

	result, err := ctrl.svc.InstallOS().GetInstallDeviceDetail(ctx, businessId)
	if err != nil {
		zap.L().Error("GetInstallDeviceList Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}
