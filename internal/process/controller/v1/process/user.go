package process

import (
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (ctrl *Controller) FuzzySearchUser(c *gin.Context) {
	zap.L().Debug("FuzzySearchUser Function Called")
	span, ctx := apm.StartSpan(c, "FuzzySearchUser", "controller")
	defer span.End()

	// 创建一个map来存储参数
	params := make(map[string]string)

	// 这里目前只接受username或者name，前端根据传递的参数来判断
	username := c.Query("username")
	name := c.<PERSON>("name")

	// 查询主要以username为准，如果username为空，则以name为准
	if username != "" {
		params["username"] = username
	} else if name != "" {
		params["name"] = name
	} else {
		core.SendResponse(c, errno.ErrParameterRequired.Add("username 或 name 至少传递一个参数"), nil)
		return
	}

	result, err := ctrl.svc.User().FuzzySearchUser(ctx, params)
	if err != nil {
		zap.L().Error("FuzzySearchUser Error: ", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}
