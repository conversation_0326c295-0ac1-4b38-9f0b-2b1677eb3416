package process

import (
	"errors"
	"strings"

	api "ks-knoc-server/internal/common/base/api/process"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (ctrl *Controller) GetInstallInfo(c *gin.Context) {
	zap.L().Debug("GetInstallInfo Function Called")
	span, ctx := apm.StartSpan(c, "GetInstallInfo", "Controller")
	defer span.End()

	// 获取SN序列号
	sn := c.Query("sn")
	if strings.TrimSpace(sn) == "" {
		zap.L().Error("SN系列号为空")
		e := apm.CaptureError(ctx, errors.New("SN系列号为空"))
		e.Send()
		core.SendResponse(c, errors.New("SN系列号为空"), nil)
		return
	}

	// 获取安装信息
	installInfo, err := ctrl.svc.InstallOS().GetInstallInfo(ctx, sn)
	if err != nil {
		zap.L().Error("GetInstallInfo Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, installInfo)
}

func (ctrl *Controller) TaskCallback(c *gin.Context) {
	zap.L().Debug("TaskCallback Function Called")
	span, ctx := apm.StartSpan(c, "TaskCallback", "Controller")
	defer span.End()

	var req api.TaskCallBackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("TaskCallback BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("TaskCallback Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	undoTaskIDs, err := ctrl.svc.InstallOS().TaskCallBack(ctx, req)
	if err != nil {
		zap.L().Error("TaskCallback Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, undoTaskIDs)
}

func (ctrl *Controller) TaskInfo(c *gin.Context) {
	zap.L().Debug("TaskInfo Function Called")
	span, ctx := apm.StartSpan(c, "TaskInfo", "Controller")
	defer span.End()

	taskID := c.Param("task_id")
	if strings.TrimSpace(taskID) == "" {
		zap.L().Error("TaskID为空")
		e := apm.CaptureError(ctx, errors.New("TaskID为空"))
		e.Send()
		core.SendResponse(c, errors.New("TaskID为空"), nil)
		return
	}

	tid := utils.ToInt64(taskID)
	if tid == 0 {
		zap.L().Error("TaskID不合法")
		e := apm.CaptureError(ctx, errors.New("TaskID不合法"))
		e.Send()
		core.SendResponse(c, errors.New("TaskID不合法"), nil)
		return
	}

	taskInfo, err := ctrl.svc.InstallOS().GetTaskInfo(ctx, tid)
	if err != nil {
		zap.L().Error("GetTaskInfo Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, taskInfo)
}
