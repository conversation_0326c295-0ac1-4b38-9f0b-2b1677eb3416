package process

import (
	"ks-knoc-server/config"
	"ks-knoc-server/internal/process/app"
	"ks-knoc-server/internal/process/options"
)

// NewAPIServer 创建APIServer
func NewAPIServer() *app.App {
	opts := options.NewOptions()
	application := app.NewApp("Process API Server",
		config.ProjectName,
		app.WithOptions(opts),
		app.WithRunFunc(run(opts)),
	)

	return application
}

func run(opts *options.Options) app.RunFunc {
	return func(basename string) error {
		return nil
	}
}
