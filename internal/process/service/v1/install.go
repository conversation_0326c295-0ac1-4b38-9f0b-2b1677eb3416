package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	api "ks-knoc-server/internal/common/base/api/process"
	message "ks-knoc-server/internal/common/base/message/process"
	"ks-knoc-server/internal/common/base/model/bpm"
	share "ks-knoc-server/internal/common/base/share/bpm"
	"ks-knoc-server/internal/common/openapi"
	"ks-knoc-server/internal/process/store"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/mitchellh/mapstructure"
	"go.elastic.co/apm"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	AutoInstallActivityID = "it_dc_auto_install"
)

type InstallOSService interface {
	// GetInstallInfo 获取安装信息
	GetInstallInfo(ctx context.Context, sn string) (*message.InstallInfo, error)
	// TaskCallBack 任务回调
	TaskCallBack(ctx context.Context, req api.TaskCallBackRequest) ([]int64, error)
	// GetTaskInfo 获取任务信息
	GetTaskInfo(ctx context.Context, taskID int64) (*bpm.InstallTask, error)
	// GetInstallDeviceDetail 获取安装设备详情
	GetInstallDeviceDetail(ctx context.Context, businessId string) (*message.QueryInstallDetail, error)
}

type installOSService struct {
	store store.Factory
}

func newInstallOSService(store store.Factory) InstallOSService {
	return &installOSService{
		store: store,
	}
}

// GetInstallInfo 获取安装信息
func (s *installOSService) GetInstallInfo(ctx context.Context, sn string) (*message.InstallInfo, error) {
	zap.L().Debug("GetInstallInfo Function Called")
	span, ctx := apm.StartSpan(ctx, "GetInstallInfo", "Service")
	defer span.End()

	// 初始化响应数据
	msg := &message.InstallInfo{
		ServerSN: sn,
	}

	// 首先查询传递过来的sn是否在待安装的数据库里
	installInfo, err := s.store.InstallOS().GetInstallInfoBySN(ctx, sn)
	if err != nil {
		zap.L().Error("GetInstallInfo Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()

		msg.Message = err.Error()
		return msg, err
	}

	// 构建响应体
	msg.BusinessID = installInfo.BusinessID
	msg.Creator = installInfo.Creator
	msg.CreatedAt = installInfo.CreatedAt
	msg.UpdatedAt = installInfo.UpdatedAt

	installConfig := new(bpm.InstallConfig)
	if err := json.Unmarshal(installInfo.Config, installConfig); err != nil {
		zap.L().Error("Unmarshal InstallConfig Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return msg, err
	}

	msg.InstallConfig = message.InstallConfig{
		ConfigRaid: message.ConfigRaid{
			Enabled: installConfig.Raid.Enable,
			Info:    installConfig.Raid.Info,
		},
		ConfigBmc: message.ConfigBmc{
			BMCIP:      installConfig.BMC.BMCIP,
			BMCMask:    installConfig.BMC.BMCMask,
			BMCGateway: installConfig.BMC.BMCGW,
		},
		ConfigOS: message.ConfigOS{
			OSName:   installConfig.SysFamily.OsName,
			Hostname: installConfig.SysFamily.Hostname,
		},
		ConfigNetwork: message.ConfigNetwork{
			BondMode:   installConfig.Network.BondMode,
			SystemIP:   installConfig.Network.SystemIP,
			SystemMask: installConfig.Network.SystemMask,
			SystemDNS:  installConfig.Network.SystemDNS,
			SystemGW:   installConfig.Network.SystemGW,
		},
	}

	// 获取具体的tasks
	tasks, err := s.store.InstallOS().GetInstallTasks(ctx, installInfo.ID)
	if err != nil {
		zap.L().Error("GetInstallTasks Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return msg, err
	}

	msg.Tasks = make([]message.InstallTask, len(tasks))
	for i, task := range tasks {
		msg.Tasks[i] = message.InstallTask{
			TaskID:   task.ID,
			TaskName: task.TaskName,
		}
	}

	return msg, nil
}

// sendInstallSignalToBPM 发送安装信号到bpm, 推进流程节点向下一个节点推进
func (s *installOSService) sendInstallSignalToBPM(ctx context.Context, hasPending, hasFailed bool, order *bpm.OrderAbstract) error {
	// 如果存在pending状态的任务，那么就不需要往下走，因为要等待其他的任务执行完成
	if !hasPending {
		req := &openapi.SignalReceivedRequest{
			BusinessId: order.BusinessID,
			SignalName: AutoInstallActivityID,
		}

		variables := make(mapdata.MapData)
		if err := json.Unmarshal(order.ProcessVars, &variables); err != nil {
			zap.L().Error("解析工单流程变量失败", zap.Error(err))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return err
		}

		if hasFailed {
			// 如果所有任务都执行完毕，但是存在失败的case，此时说明整个工单是不成功的，即使有部分机器可能装成功了
			// 此时也需要人工介入，我们需要手动推动流程往下走，走到人工处理阶段
			zap.L().Info("任务执行失败，需要转至人工处理")

			// install_status为0表示自动装机阶段失败，此时下一阶段会自动跳转到人工处理阶段
			variables.Set("install_status", 0)
			req.Variables = variables
			resp, err := openapi.SignalReceived(req)
			if err != nil {
				zap.L().Error("调用bpm任务接收接口失败", zap.Error(err), zap.Any("resp", resp))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return err
			}
			zap.L().Debug("调用bpm任务接收接口成功, 推进流程节点向下一个节点推进", zap.Any("variables", variables))
		} else {
			zap.L().Info("所有任务执行成功，并且没有失败的case，此时就可以推进工单正常往下执行")

			// install_status为1表示自动装机阶段成功，此时下一阶段会自动跳转到人工处理阶段
			variables.Set("install_status", 1)
			req.Variables = variables
			resp, err := openapi.SignalReceived(req)
			if err != nil {
				zap.L().Error("调用bpm任务接收接口失败", zap.Error(err), zap.Any("resp", resp))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return err
			}

			zap.L().Debug("调用bpm任务接收接口成功, 推进流程节点向下一个节点推进", zap.Any("variables", variables))
		}
	}
	return nil
}

// checkPendingTask 检查当前安装记录中的所有任务是否都已完成
// 以及是否存在失败的单子，来确定当前工单是否应该向下一个节点流转
func (s *installOSService) checkPendingTask(ctx context.Context, osInstallID int64, order *bpm.OrderAbstract) ([]int64, error) {
	var (
		// 安装记录的映射关系，key是os_install_id，value是安装记录的指针
		osInstallMap = make(map[int64]*bpm.OsInstall)
		// 构建一个map，key为os_install_id，value为tasks的切片，便于后续快速查找
		allTasksMap = make(map[int64][]*bpm.InstallTask)
		// 用来保存还没有完成的tasks，key为os install的id，value为未完成的tasks的切片
		unCompletedTaskMap = make(map[int64][]*bpm.InstallTask)
		// 用来保存所有失败的任务，key为os install的id，value为失败的任务的切片
		failedTaskMap = make(map[int64][]*bpm.InstallTask)
		// 查询所有对应的tasks任务
		allTasks = make([]*bpm.InstallTask, 0)

		// 是否存在pending状态的任务
		hasPendingTask = false
		// 是否存在失败的任务
		hasFailedTask = false

		// 未完成任务id列表
		unCompletedTaskIDs = make([]int64, 0)
	)

	// 获取所有的安装记录
	ins, err := s.store.InstallOS().GetInstallInfoByBusinessID(ctx, order.BusinessID)
	if err != nil {
		zap.L().Error("获取安装记录失败", zap.Error(err), zap.String("business_id", order.BusinessID))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	// 构建osInstallMap同时初始化allTasksMap、unCompletedTaskMap、failedTaskMap
	for _, i := range ins {
		osInstallMap[i.ID] = i
		allTasksMap[i.ID] = make([]*bpm.InstallTask, 0)
		unCompletedTaskMap[i.ID] = make([]*bpm.InstallTask, 0)
		failedTaskMap[i.ID] = make([]*bpm.InstallTask, 0)
	}

	// 获取传入的这个osInstallID是否合法，即需要在ins中存在
	if _, ok := osInstallMap[osInstallID]; !ok {
		zap.L().Error("安装记录不存在, 请检查os_install_id是否正确", zap.Int64("os_install_id", osInstallID))
		return nil, errors.New("安装记录不存在, 请检查os_install_id是否正确")
	}

	// 按照install_id查询所有对应的tasks任务
	for _, i := range ins {
		tasks, err := s.store.InstallOS().GetInstallTasks(ctx, i.ID)
		if err != nil {
			zap.L().Error("获取安装任务失败", zap.Error(err), zap.Int64("os_install_id", i.ID))
			e := apm.CaptureError(ctx, err)
			e.Send()
			return nil, err
		}

		// 将查询到的tasks任务添加到allTasks中
		allTasks = append(allTasks, tasks...)
	}

	// 遍历所有task构建初始化的map
	for _, t := range allTasks {
		allTasksMap[t.OsInstallID] = append(allTasksMap[t.OsInstallID], t)
		if t.TaskStatus == share.TaskStatusPending {
			hasPendingTask = true
			unCompletedTaskMap[t.OsInstallID] = append(unCompletedTaskMap[t.OsInstallID], t)
		}
		if t.TaskStatus == share.TaskStatusFailed {
			hasFailedTask = true
			failedTaskMap[t.OsInstallID] = append(failedTaskMap[t.OsInstallID], t)
		}
	}

	// Part1: 更新安装记录中的状态
	// 判断对应的安装记录是否将状态修改为结束, 安装记录状态修改为结束的依据是：
	// 1. 所有tasks都已操作完成（不是pending状态）
	// 2. 不存在失败的任务
	currentPendingTasks := unCompletedTaskMap[osInstallID]
	currentFailedTasks := failedTaskMap[osInstallID]
	// 更新状态的前提是所有的task都执行完成
	if len(currentPendingTasks) == 0 {
		insObj := osInstallMap[osInstallID]
		var newStatus uint8
		if len(currentFailedTasks) > 0 {
			zap.L().Debug("安装已完成，但存在失败的任务，此时需要将安装记录状态修改为失败")
			newStatus = share.InstallStatusFailed
		} else {
			zap.L().Debug("安装已完成，且不存在失败的任务，此时需要将安装记录状态修改为成功")
			newStatus = share.InstallStatusCompleted
		}

		// 只有当状态确实发生变化时才更新数据库，避免唯一约束冲突
		if insObj.InstallStatus != newStatus {
			insObj.InstallStatus = newStatus
			// 更新数据库
			if err := s.store.InstallOS().UpdateInstallInfo(ctx, insObj); err != nil {
				zap.L().Error("更新安装记录状态失败", zap.Error(err), zap.Int64("os_install_id", osInstallID))
				e := apm.CaptureError(ctx, err)
				e.Send()
				return nil, err
			}
		} else {
			zap.L().Debug("安装记录状态无需更新",
				zap.Int64("os_install_id", osInstallID),
				zap.String("current_status", share.InstallStatusMap[insObj.InstallStatus]),
			)
		}
	}

	// 构建未完成的任务id列表
	for _, task := range currentPendingTasks {
		unCompletedTaskIDs = append(unCompletedTaskIDs, task.ID)
	}

	// Part2: 更新工单信息：所有任务都执行完毕，分情况进行处理
	if err := s.sendInstallSignalToBPM(ctx, hasPendingTask, hasFailedTask, order); err != nil {
		zap.L().Error("发送安装信号到bpm失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	return unCompletedTaskIDs, nil
}

// TaskCallBack 任务回调
func (s *installOSService) TaskCallBack(ctx context.Context, req api.TaskCallBackRequest) ([]int64, error) {
	zap.L().Debug("TaskCallBack Function Called")
	span, ctx := apm.StartSpan(ctx, "TaskCallBack", "Service")
	defer span.End()

	// 未完成的任务id列表
	undoTaskIDs := make([]int64, 0)

	// 首先查询到要更新的这个任务
	installTask, err := s.store.InstallOS().GetInstallTaskByID(ctx, req.TaskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zap.L().Info("任务不存在, 请检查任务ID是否正确", zap.Int64("task_id", req.TaskID))
			return undoTaskIDs, nil
		}

		zap.L().Error("获取任务信息失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return undoTaskIDs, err
	}

	// 不处理已经结束的任务, 如果任务已完成则直接返回并记录日志
	if installTask.TaskStatus == share.TaskStatusCompleted {
		zap.L().Info("Task任务已完成，无须处理", zap.String("task_name", installTask.TaskName), zap.Int64("task_id", req.TaskID))
		return undoTaskIDs, errors.New("Task任务已完成，无须处理")
	}

	// 查询该task所属的系统安装记录
	osInstall, err := s.store.InstallOS().GetInstallInfoById(ctx, installTask.OsInstallID)
	if err != nil {
		zap.L().Error("获取系统安装记录失败", zap.Error(err), zap.Int64("os_install_id", installTask.OsInstallID))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return undoTaskIDs, err
	}

	// 获取当前工单记录
	order, err := s.store.BPMOrder().GetOrderByBusinessID(osInstall.BusinessID)
	if err != nil {
		zap.L().Error("获取工单记录失败", zap.Error(err), zap.String("business_id", osInstall.BusinessID))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return undoTaskIDs, err
	}

	// 并不是所有的流程节点都可以处理任务回调，只有自动装机阶段才可以处理
	// 注意：虽然进入了接受任务节点，但是当前审批的node节点依然没变，所以这里需要判断activity_id
	if order.ActivityID != AutoInstallActivityID {
		zap.L().Info("当前流程节点不是自动装机阶段，无法更新任务信息", zap.String("activity_id", order.ActivityID))
		return undoTaskIDs, errors.New("当前流程节点不是自动装机阶段，无法更新任务信息")
	}

	// 更新task的其他字段
	installTask.TaskStatus = req.TaskStatus
	installTask.Message = req.Message
	installTask.StartTime = req.StartTime
	installTask.EndTime = req.EndTime

	// 如果要计算时间的话，那么start和end都不能为零值
	if req.StartTime != 0 && req.EndTime != 0 {
		// 如果起止时间都有值的话，这个时候还需要看起止时间是否合法
		if req.StartTime > req.EndTime {
			// 这里只记录日志，不返回错误，否则可能直接阻塞掉流程
			zap.L().Error("开始时间大于结束时间，无法计算耗时", zap.Int64("start_time", req.StartTime), zap.Int64("end_time", req.EndTime))
		}

		// 如果起止时间合法，计算耗时
		installTask.Duration = int32(req.EndTime - req.StartTime)
	}

	// 更新task的更新时间
	installTask.UpdatedAt = time.Now().Unix()

	// 更新task信息
	if err := s.store.InstallOS().UpdateInstallTask(ctx, installTask); err != nil {
		zap.L().Error("更新任务信息失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return undoTaskIDs, err
	}

	zap.L().Info("更新任务信息成功",
		zap.String("任务名称", installTask.TaskName),
		zap.String("耗时", fmt.Sprintf("%d秒", installTask.Duration)),
		zap.String("任务状态", share.TaskStatusMap[installTask.TaskStatus]),
		zap.String("信息", installTask.Message),
	)

	// 检查当前安装记录中的所有任务是否都已经完成
	tasks, err := s.checkPendingTask(ctx, installTask.OsInstallID, order)
	if err != nil {
		zap.L().Error("检查未完成任务失败", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return undoTaskIDs, err
	}

	// 将未完成的任务id列表添加到undoTaskIDs中
	undoTaskIDs = append(undoTaskIDs, tasks...)

	return undoTaskIDs, nil
}

func (s *installOSService) GetTaskInfo(ctx context.Context, taskID int64) (*bpm.InstallTask, error) {
	zap.L().Debug("GetTaskInfo Function Called")
	span, ctx := apm.StartSpan(ctx, "GetTaskInfo", "Service")
	defer span.End()

	installTask, err := s.store.InstallOS().GetInstallTaskByID(ctx, taskID)
	if err != nil {
		zap.L().Error("GetInstallTaskByID Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		return nil, err
	}

	return installTask, nil
}

// GetInstallDeviceDetail 获取安装设备详情
func (s *installOSService) GetInstallDeviceDetail(ctx context.Context, businessId string) (*message.QueryInstallDetail, error) {
	zap.L().Debug("GetInstallDeviceDetail Function Called")
	span, _ := apm.StartSpan(ctx, "GetInstallDeviceDetail", "Service")
	defer span.End()

	// 查询到当前的工单
	order, err := s.store.BPMOrder().GetOrderByBusinessID(businessId)
	if err != nil {
		zap.L().Error("查询工单失败", zap.Error(err), zap.String("business_id", businessId))
		return nil, err
	}

	// 查询工单详情
	detail, err := s.store.BPMOrder().GetOrderDetailByBusinessID(businessId)
	if err != nil {
		zap.L().Error("查询工单详情失败", zap.Error(err), zap.String("business_id", businessId))
		return nil, err
	}

	// 解析工单数据，获取设备信息
	detailData, err := detail.ParseBusinessData()
	if err != nil {
		zap.L().Error("解析工单数据失败", zap.Error(err))
		return nil, err
	}

	// 先把工单中的sn数据提取出来
	deviceSNs := make([]string, 0)

	// 用于保存device_data map
	detailDataMap := make(map[string]any)
	for _, data := range detailData {
		sn, ok := data["sn"]
		if !ok {
			zap.L().Error("工单数据中没有sn字段", zap.Any("data", data))
			return nil, errors.New("工单数据中没有sn字段")
		}
		deviceSNs = append(deviceSNs, utils.ToString(sn))
		detailDataMap[utils.ToString(sn)] = data
	}

	// 根据sn查询设备信息
	cmdbDevMap := make(map[string]v1.DeviceInfoResponse)
	devs, err := openapi.GetDeviceBySN(deviceSNs)
	if err != nil {
		zap.L().Error("获取设备信息失败", zap.Error(err), zap.Strings("sn", deviceSNs))
		return nil, err
	}

	for _, dev := range devs {
		cmdbDevMap[dev.MetaData.DeviceSN] = dev
	}

	if len(cmdbDevMap) != len(detailDataMap) {
		unmatchedSNs := make([]string, 0)
		for sn := range detailDataMap {
			if _, ok := cmdbDevMap[sn]; !ok {
				unmatchedSNs = append(unmatchedSNs, sn)
			}
		}

		zap.L().Error("查询设备信息异常", zap.Strings("未查询到的sn", unmatchedSNs))
		return nil, errors.New("查询设备信息异常")
	}

	// 初始化响应
	response := message.NewQueryInstallDetail()
	response.Devices = make(message.QueryInstallDeviceList, 0)

	// 设置工单级别信息
	response.Title = order.Title
	response.ProcessState = order.ProcessState.String()
	response.ProcessKey = order.ProcessKey
	response.BusinessID = order.BusinessID
	response.Initiator = fmt.Sprintf("%s (%s)", order.InitiatorName, order.InitiatorUserName)
	response.InitiatorUserName = order.InitiatorUserName

	// 首先拼凑设备自身的一些信息
	installDeviceMap := make(map[string]*message.QueryInstallDeviceInfo)
	for idx, sn := range deviceSNs {
		data, ok := cmdbDevMap[sn]
		if !ok {
			continue
		}

		deviceInfo := message.NewQueryInstallDeviceInfo()
		deviceInfo.Number = idx + 1      // 在这里设置设备序号，不依赖installInfo
		deviceInfo.DeviceType = "server" // 默认设置为服务器类型
		deviceInfo.DeviceSN = data.MetaData.DeviceSN
		deviceInfo.DeviceBrand = data.MetaData.Brand
		deviceInfo.DeviceModel = data.MetaData.Model
		deviceInfo.DeviceName = data.MetaData.Name
		deviceInfo.OfficeName = data.MetaData.Office
		deviceInfo.IdcName = data.MetaData.IDC
		deviceInfo.RackName = data.MetaData.Rack
		deviceInfo.Position = fmt.Sprintf("%d-%d", data.MetaData.Position, data.MetaData.Position)
		deviceInfo.Owner = data.MetaData.Owner
		deviceInfo.Fields = make(share.FieldTobeSupplementList, 0)

		// 将设备信息添加到map中
		installDeviceMap[sn] = deviceInfo

		// 将设备信息添加到响应中
		response.Devices = append(response.Devices, deviceInfo)
	}

	// 当前工单所处的节点是什么
	nodeID := strings.TrimSpace(order.TaskKey)

	// 获取当前工单节点对象
	var node *bpm.ProcessNode
	if nodeID != "" {
		nodeObj, err := s.store.BPMNode().GetProcessNodeByNodeID(nodeID)
		if err != nil {
			zap.L().Error("获取工单节点失败", zap.Error(err), zap.String("node_id", nodeID))
			return nil, err
		}
		node = nodeObj
	}

	// 获取当前流程类型中所有需要补充的字段的定义
	fieldsTobeFilled, err := s.store.BPMNode().GetITDCProcessNodeFieldDefinitionByOpType(detail.OpType.String())
	if err != nil {
		zap.L().Error("获取IT机房流程节点字段定义失败", zap.Error(err), zap.String("op_type", detail.OpType.String()))
		return nil, err
	}
	fieldsTobeFilledMap := make(map[string]bpm.ProcessNodeFieldDefinition)
	for _, field := range fieldsTobeFilled {
		fieldsTobeFilledMap[field.FieldName] = field
	}

	if len(fieldsTobeFilled) > 0 {
		for _, field := range fieldsTobeFilled {
			f := &share.FieldTobeSupplement{
				FieldName:        field.FieldName,
				FieldDisplayName: field.FieldDisplayName,
				FieldType:        bpm.FieldTypeMap[field.FieldType],
				Required:         field.Required,
				FieldSeq:         field.FieldSeq,
			}

			// 说明是当前节点需要补充的字段
			if node != nil && field.NodeID == node.NodeID {
				// 如果说当前节点虽然是要补充字段的节点，但是工单的ActivityID不为空，说明当前节点是接收任务节点
				// 即当前节点正在自动装机的节点，那么当前节点不需要填写字段
				if order.ActivityID == "" {
					f.ReadOnly = false
				} else {
					f.ReadOnly = true
				}
			} else {
				f.ReadOnly = true
			}

			// 如果是select类型字段，处理选项信息
			if field.FieldType == bpm.FieldSelect {
				fieldInfo := bpm.ProcessNodeFieldInfoSelect{}
				if err := json.Unmarshal(field.FieldInfo, &fieldInfo); err != nil {
					zap.L().Error("解析字段信息失败", zap.Error(err))
					return nil, err
				}

				// 设置字段属性
				selectField := make(map[string]any)
				selectField["request_url"] = fieldInfo.RequestURL
				selectField["method"] = fieldInfo.Method

				// 处理选项
				options := make([]map[string]any, 0)
				for _, opt := range fieldInfo.Options {
					options = append(options, map[string]any{
						"label": opt.Label,
						"value": opt.Value,
					})
				}
				selectField["options"] = options

				// 处理依赖关系
				selectField["rely_field"] = fieldInfo.RelyField

				// 处理参数
				params := make(map[string]any)
				if len(fieldInfo.Params) > 0 {
					for _, param := range fieldInfo.Params {
						params[param] = ""
					}
				}
				selectField["params"] = params

				f.Attributes = selectField
			}

			// 添加到设备的字段列表
			for sn, dev := range installDeviceMap {
				// 如果字段存在值的化要进行赋值以便在前端进行展示
				currentDeviceData := detailDataMap[sn]
				filledData := transferMap(currentDeviceData)
				if val, ok := filledData[f.FieldName]; ok {
					f.FieldValue = val
				}

				zap.L().Debug("添加字段信息", zap.Any("field", f))
				dev.Fields = append(dev.Fields, f)
			}

		}
	}

	// 查询是否有安装记录，如果有安装记录则展示装机进度
	// 这样不仅在自动装机阶段能看到进度，在后续阶段也能看到历史装机进度
	// 注意: 这里查询到的installInfo其实是可以为空的，因为可能还没有进入自动装机阶段
	installInfo, err := s.store.InstallOS().GetInstallInfoByBusinessID(ctx, businessId)
	if err != nil {
		zap.L().Error("查询安装记录异常", zap.Error(err))
		return response, nil
	}

	// 如果有安装记录，则展示装机进度
	if len(installInfo) > 0 {
		for _, install := range installInfo {
			sn := install.SN
			deviceInfo, ok := installDeviceMap[sn]
			if !ok {
				zap.L().Error("自动装机阶段未找到设备信息", zap.String("sn", sn))
				return nil, errors.New("自动装机阶段未找到设备信息")
			}

			deviceInfo.DeviceStatus = share.InstallStatusMap[install.InstallStatus]

			installTasks, err := s.store.InstallOS().GetInstallTasks(ctx, install.ID)
			if err != nil {
				zap.L().Error("获取安装任务失败", zap.Error(err), zap.Int64("os_install_id", install.ID))
				return nil, err
			}

			taskList := make(message.QueryInstallTaskList, 0)
			if len(installTasks) > 0 {
				for _, task := range installTasks {
					taskList = append(taskList, message.QueryInstallTaskDetail{
						TaskID:      task.ID,
						TaskName:    share.TaskNameMap[share.TaskType(task.TaskName)],
						TaskMessage: task.Message,
						TaskStatus:  share.TaskStatusMap[task.TaskStatus],
						CreatedAt:   task.CreatedAt,
						UpdatedAt:   task.UpdatedAt,
						Duration:    int64(task.Duration),
					})
				}

				// 按照任务顺序排序
				taskList.SortByTaskSeq()
			}

			deviceInfo.Tasks = taskList
		}
	}

	return response, nil
}

func transferMap(data any) map[string]any {
	var (
		installConfig  = bpm.InstallConfig{}
		deviceFieldMap = make(map[string]any)
	)

	// 先判断传递过来的数据的合法性
	dataMap, ok := data.(map[string]any)
	if !ok {
		zap.L().Error("data不是map[string]any", zap.Any("data", data))
		return deviceFieldMap
	}

	// 判断config是否存在
	config, ok := dataMap["config"]
	if !ok {
		return deviceFieldMap
	}

	// 把config反序列化到结构体
	if err := mapstructure.Decode(config, &installConfig); err != nil {
		zap.L().Error("反序列化config失败", zap.Error(err))
		return deviceFieldMap
	}

	deviceFieldMap["ip_address"] = installConfig.Network.SystemIP
	deviceFieldMap["ip_netmask"] = installConfig.Network.SystemMask
	deviceFieldMap["ip_gateway"] = installConfig.Network.SystemGW
	deviceFieldMap["bmc_ip_address"] = installConfig.BMC.BMCIP
	deviceFieldMap["bmc_netmask"] = installConfig.BMC.BMCMask
	deviceFieldMap["bmc_gateway"] = installConfig.BMC.BMCGW

	return deviceFieldMap
}
