package v1

import (
	"context"

	"ks-knoc-server/internal/common/openapi"
)

type UserService interface {
	FuzzySearchUser(ctx context.Context, params map[string]string) ([]openapi.UserInfo, error)
}

type userService struct {
}

func newUserService() UserService {
	return &userService{}
}

func (u *userService) FuzzySearchUser(ctx context.Context, params map[string]string) ([]openapi.UserInfo, error) {
	return openapi.FuzzySearchUser(params)
}
