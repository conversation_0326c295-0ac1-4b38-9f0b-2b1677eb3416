package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"

	"ks-knoc-server/internal/common/base/model/bpm"
	"ks-knoc-server/internal/common/openapi"
	"ks-knoc-server/internal/process/store"
	"ks-knoc-server/pkg/utils"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kafka"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

const (
	DefaultTaskQueueSize = 1000
	DefaultQueueTimeout  = 30 * 24 * time.Hour // 30天超时
	CleanupCheckInterval = 1 * time.Hour       // 每小时检查一次
)

// QueueInfo 队列信息，包含最后活跃时间
type QueueInfo struct {
	Queue      chan *bpm.Process
	LastActive time.Time
}

func generateEmptyTaskID(businessID, nodeID string) string {
	return fmt.Sprintf("empty_%s_%s_%d", businessID, nodeID, time.Now().Unix())
}

func generateEventID(businessID, assiginee string, currentTs int64) string {
	return fmt.Sprintf("event_%s_%s_%d", businessID, assiginee, currentTs)
}

func InitBPMKafka(ds *store.DataStore) {
	var (
		topic   = "it_knoc_process"
		groupID = "it_knoc_process_group"
	)

	// sync.Map零值可用，不需要进行初始化
	p := &BPMProcess{
		Topic:         topic,
		ConsumerGroup: groupID,
		dataStore:     ds,
		cleanupDone:   make(chan struct{}),
	}

	// 启动清理协程
	go p.startCleanupRoutine()

	consumer, err := kafka.NewKsKafkaConsumer(p.Topic, p.ConsumerGroup, p)
	if err != nil {
		fmt.Printf("创建ks consumer 失败，error：%v\n", err)
	}
	defer func() {
		// 停止清理协程
		close(p.cleanupDone)
		if err := consumer.Close(); err != nil {
			zap.L().Error("consumer 关闭失败, error: " + err.Error())
		}
	}()
	zap.L().Debug("启动consumer")
	consumer.Start()
	zap.L().Info("consumer 创建成功")
}

// BPMProcess BPM流程
type BPMProcess struct {
	Topic         string
	ConsumerGroup string
	dataStore     store.Factory

	TaskQueueMap sync.Map      // 用于存储工单ID和对应的QueueInfo
	cleanupDone  chan struct{} // 用于停止清理协程
}

// Consume 消费Kafka消息
func (k *BPMProcess) Consume(value []byte, ctx kafka.MessageContext) {
	zap.L().Debug("消息接收成功 Consume函数被调用")

	// 初始化一个message消息容器，用于接收kafka消息
	processMsg := new(bpm.Process)
	if err := json.Unmarshal(value, processMsg); err != nil {
		originalMsg := make(map[string]any)
		if err := json.Unmarshal(value, &originalMsg); err != nil {
			zap.L().Error("解析原始消息失败", zap.Error(err))
			return
		}
		zap.L().Error("消息格式错误", zap.Any("original_msg", originalMsg))
		return
	}

	// 获取工单的ID
	businessID := processMsg.BusinessID

	// 检查是否存在对应的QueueInfo
	queueInfoInterface, ok := k.TaskQueueMap.Load(businessID)
	if ok {
		queueInfo := queueInfoInterface.(*QueueInfo)
		// 更新最后活跃时间
		queueInfo.LastActive = time.Now()
		k.TaskQueueMap.Store(businessID, queueInfo)

		zap.L().Debug(fmt.Sprintf("工单ID: %s 在TaskQueue中存在，直接入队", businessID))
		queueInfo.Queue <- processMsg
	} else {
		// 如果工单的ID在TaskQueue中不存在，则创建一个channel并启动消费者协程
		ch := make(chan *bpm.Process, DefaultTaskQueueSize)
		queueInfo := &QueueInfo{
			Queue:      ch,
			LastActive: time.Now(),
		}
		k.TaskQueueMap.Store(businessID, queueInfo)

		// 启动该BusinessID的串行处理协程
		go k.processBusinessQueue(businessID, ch)

		// 将消息入队
		ch <- processMsg
		zap.L().Debug(fmt.Sprintf("工单ID: %s 创建新的TaskQueue并启动处理协程", businessID))
	}
}

// processBusinessQueue 串行处理某个BusinessID的消息队列
func (k *BPMProcess) processBusinessQueue(businessID string, queue chan *bpm.Process) {
	zap.L().Info(fmt.Sprintf("启动工单 %s 的消息处理协程", businessID))

	// 遍历channel，处理消息，只要channel不关闭，就一直阻塞，直到channel关闭
	for msg := range queue {
		zap.L().Debug(fmt.Sprintf("开始处理工单 %s 的消息，事件类型: %s", businessID, msg.EventType))

		// 实际处理消息的函数
		if err := k.DealProcessMsg(msg); err != nil {
			zap.L().Error(fmt.Sprintf("处理工单 %s 的消息失败，error: %v", businessID, err))
			// 这里可以根据需要决定是否重试或者记录到死信队列
		}

		zap.L().Debug(fmt.Sprintf("完成处理工单 %s 的消息，事件类型: %s", businessID, msg.EventType))
	}

	// 当channel关闭时，清理TaskQueue中的记录
	k.TaskQueueMap.Delete(businessID)
	zap.L().Info(fmt.Sprintf("工单 %s 的消息处理协程结束", businessID))
}

// closeBusinessQueue 关闭指定BusinessID的消息队列
func (k *BPMProcess) closeBusinessQueue(businessID string) {
	if queueInfoInterface, ok := k.TaskQueueMap.Load(businessID); ok {
		queueInfo := queueInfoInterface.(*QueueInfo)
		close(queueInfo.Queue)
		zap.L().Info(fmt.Sprintf("已关闭工单 %s 的消息队列", businessID))
	}
}

// startCleanupRoutine 启动清理协程，定期清理超时的队列
func (k *BPMProcess) startCleanupRoutine() {
	ticker := time.NewTicker(CleanupCheckInterval)
	defer ticker.Stop()

	zap.L().Info("启动队列清理协程",
		zap.Duration("检查间隔", CleanupCheckInterval),
		zap.Duration("超时时间", DefaultQueueTimeout))

	for {
		select {
		case <-ticker.C:
			k.cleanupTimeoutQueues()
		case <-k.cleanupDone:
			zap.L().Info("队列清理协程退出")
			return
		}
	}
}

// cleanupTimeoutQueues 清理超时的队列
func (k *BPMProcess) cleanupTimeoutQueues() {
	now := time.Now()
	timeoutQueues := make([]string, 0)

	// 遍历所有队列，找出超时的
	k.TaskQueueMap.Range(func(key, value any) bool {
		businessID := key.(string)
		queueInfo := value.(*QueueInfo)

		if now.Sub(queueInfo.LastActive) > DefaultQueueTimeout {
			timeoutQueues = append(timeoutQueues, businessID)
		}
		return true
	})

	// 清理超时的队列
	for _, businessID := range timeoutQueues {
		zap.L().Warn(fmt.Sprintf("工单 %s 超时未活跃，强制关闭队列", businessID))
		k.closeBusinessQueue(businessID)
	}

	if len(timeoutQueues) > 0 {
		zap.L().Info(fmt.Sprintf("本次清理了 %d 个超时队列", len(timeoutQueues)))
	}
}

// DealProcessMsg 处理Kafka消息
func (k *BPMProcess) DealProcessMsg(msg *bpm.Process) error {
	// 初始化一个上下文
	ctx := context.Background()

	// 获取本地时间戳，用于CreatedAt和UpdatedAt
	now := time.Now().Unix()

	// 根据不同的消息类型，进行不同的处理
	switch msg.EventType {
	case bpm.ProcessStart:
		zap.L().Debug("ProcessStart Event Received")

		// 首先需要看投递过来的到底是什么类型的流程，是快流程还是专业流程
		p, err := k.dataStore.BPMProcess().GetProcessByKey(ctx, msg.ProcessKey)
		if err != nil {
			zap.L().Error("获取流程信息失败", zap.Error(err))
			return err
		}

		// 根据流程分类的不同采取不同的操作
		switch p.ProcessCategory {
		case bpm.AgileProcessDesigner:
			// 快流程，发起端主要是在bpm上发起的，所以服务在接收到消息后，要在本地创建一条数据的记录
			// 获取快流程字段元数据
			fieldMeta, err := k.dataStore.BPMNode().GetAgileFieldMetaByProcessKey(msg.ProcessKey)
			if err != nil {
				zap.L().Error("获取快流程字段元数据失败", zap.Error(err))
				return err
			}

			// 快流程的内容在创建工单的第一步就可以确认，没有后续补内容的过程，所以这一步的话，我们直接调用openapi查询工单内容
			formCode := p.FormCode
			formContent, err := openapi.GetAgileFormContent(formCode, msg.BusinessID)
			if err != nil {
				zap.L().Error("获取快流程表单内容失败", zap.Error(err))
				return err
			}

			// 解析表单内容
			formElements := make([]bpm.AgileFormElementDefinition, 0)
			if err := json.Unmarshal([]byte(formContent.Result.ModelData), &formElements); err != nil {
				zap.L().Error("解析快流程表单内容失败", zap.Error(err))
				return err
			}

			// 把formElements攒成一个map，方便快速定位查找
			formElementMap := make(map[string]bpm.AgileFormElementDefinition)
			for _, element := range formElements {
				formElementMap[element.ID] = element
			}

			// 获取标题和内容
			var (
				formTitle         string
				agileOrderContent strings.Builder
			)

			// 遍历这个流程的所有form元素
			for _, field := range fieldMeta {
				if field.FormElementType == bpm.AgileFieldTypeTitle {
					formTitle = utils.ToString(formElementMap[field.FormElementID].Value)
					continue
				}
				if field.FormElementType == bpm.AgileFieldTypeContent {
					val := formElementMap[field.FormElementID].Value
					kind := reflect.TypeOf(val).Kind()
					switch kind {
					case reflect.String:
						agileOrderContent.WriteString(field.FormElementName + ": ")
						agileOrderContent.WriteString(utils.ToString(val))
						agileOrderContent.WriteString("\n")
					case reflect.Slice:
						// 对应的是快流程中的一个多选框，传过来的其实是一个列表
						agileOrderContent.WriteString(field.FormElementName + ": ")
						for idx, v := range val.([]interface{}) {
							agileOrderContent.WriteString(utils.ToString(v))
							if (idx + 1) < len(val.([]interface{})) {
								agileOrderContent.WriteString(",")
							}
						}
						agileOrderContent.WriteString("\n")
					default:
						return fmt.Errorf("不支持的快流程字段类型, 类型为: %s", kind.String())
					}
					continue
				}
			}

			o := new(bpm.OrderAbstract)
			o.Title = formTitle
			o.Abstract = agileOrderContent.String()
			o.ProcessKey = msg.ProcessKey
			o.ProcessName = msg.ProcessName
			o.ProcessCategory = bpm.AgileProcessDesigner
			o.ProcessState = bpm.Audit
			o.BusinessID = msg.BusinessID
			o.InitiatorUserName = msg.InitiatorUsername
			o.InitiatorName = msg.InitiatorName
			o.InitiatorID = msg.InitiatorID
			o.InitiatorOrg = msg.InitiatorOrg
			o.CreateTime = msg.CurrentTimeStamp
			o.UpdateTime = msg.CurrentTimeStamp
			o.Url = fmt.Sprintf("%s/b/%s", viper.GetString("bpm.baseurl"), msg.BusinessID)

			// 获取发起人的湘西信息
			initiator, err := k.dataStore.User().GetUserByNumber(msg.InitiatorID)
			if err != nil {
				zap.L().Error(err.Error())
				return err
			}

			o.InitiatorOrgName = initiator.DisplayName

			// 根据审批人工号获取审批人信息
			approvalUser, err := k.dataStore.User().GetUserByNumber(msg.Assignees)
			if err != nil {
				zap.L().Error(err.Error())
				return err
			}

			// 更新审批人信息
			o.ApprovalList = strings.Join([]string{approvalUser.Username}, ",")

			// 创建流程
			if err := k.dataStore.BPMOrder().CreateAgileOrder(o); err != nil {
				zap.L().Error(err.Error())
				return err
			}

			zap.L().Info(fmt.Sprintf("同步快流程成功，流程ID：%s", o.BusinessID))
			return nil

		case bpm.ProProcessDesigner:
			// 专业流程，在这一步是不需要手动在db创建order的，因为在发起的时候通过接口进行创建的。
			// 那么出现PROCESS_START有两种情况
			// 1. 正常发起流程
			// 2. 被拒绝后重新发起流程
			// 注意，这里仅仅是获取kafka消息，工单并不是在这里创建的，所以此时对应的businessID在数据库中是一定存在的
			o, err := k.dataStore.BPMOrder().GetOrderByBusinessID(msg.BusinessID)
			if err != nil {
				zap.L().Error("获取工单失败", zap.Error(err))
				return err
			}

			// 更新工单字段
			o.ProcessState = bpm.Audit
			o.UpdateTime = msg.CurrentTimeStamp
			o.TaskID = msg.TaskID
			o.TaskKey = msg.TaskDefinitionKey
			o.TaskName = msg.TaskName
			o.InitiatorID = msg.InitiatorID

			// 查询并补充发起人部门名称
			userInfoResp, err := k.dataStore.User().GetUserByNumber(msg.InitiatorID)
			if err != nil {
				zap.L().Error(err.Error())
				o.InitiatorOrgName = "未知部门"
			} else {
				// 获取发起人部门名称
				o.InitiatorOrgName = userInfoResp.DisplayName
				o.InitiatorUserName = userInfoResp.Username
				o.InitiatorName = userInfoResp.Name
				// 这里的DisplayName是部门全名称
				o.InitiatorOrg = userInfoResp.DisplayName
			}

			zap.L().Debug("发起人信息", zap.String("发起人工号", msg.InitiatorID),
				zap.String("发起人姓名", userInfoResp.Name),
				zap.String("发起人部门", userInfoResp.DisplayName))

			// 更新当前审批人
			approvalUser, err := k.dataStore.User().GetUserByNumber(msg.Assignees)
			if err != nil {
				zap.L().Error(err.Error())
				return err
			}
			o.ApprovalUser = approvalUser.Username

			// 更新审批时间
			o.UpdateTime = msg.CurrentTimeStamp

			// 更新工单
			if err = k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, o); err != nil {
				zap.L().Error(err.Error())
				return err
			}

			zap.L().Debug(fmt.Sprintf("更新工单成功，流程ID：%s", msg.BusinessID))

			// 记录执行日志
			auditLog := &bpm.ProcessExecutionLog{
				BusinessID: msg.BusinessID,
				ProcessKey: msg.ProcessKey,
				NodeID:     msg.TaskDefinitionKey,
				NodeName:   msg.TaskName,
				TaskID:     msg.TaskID,
				NodeType:   bpm.Application,
				Comments:   msg.Comments,
				Executor:   msg.InitiatorUsername,
				StartTime:  msg.CurrentTimeStamp,
				EndTime:    msg.CurrentTimeStamp,
				Status:     bpm.ProcessApplicationStatus.String(),
				CreatedAt:  now,
				UpdatedAt:  now,
				EventID:    generateEventID(msg.BusinessID, msg.InitiatorUsername, msg.CurrentTimeStamp),
			}

			if err := k.dataStore.BPMExecutionLog().CreateExecutionLog([]*bpm.ProcessExecutionLog{auditLog}); err != nil {
				zap.L().Error("记录执行日志失败", zap.Error(err))
				return err
			}

			zap.L().Debug("记录执行日志成功")

		default:
			// 不支持的流程类型
			return errors.New("不支持的流程类型")
		}

		return nil
	case bpm.SequenceFlowTaken:
		// 一般是经过连线，或者经过网关的时候，可以忽略
		zap.L().Debug(fmt.Sprintf("BusinessID: %s, 流程经过连线", msg.BusinessID))
	case bpm.TaskCreate:
		// 执行到某个任务节点，节点创建，节点创建并不涉及审批的动作，所以我们需要更新的内容主要有
		// 新的任务ID，新的任务key，新任务名称，新的审批人以及新的时间。
		zap.L().Debug(fmt.Sprintf("BusinessID: %s, 创建任务节点, 任务标识: %s, 任务名称: %s", msg.BusinessID, msg.TaskDefinitionKey, msg.TaskName))
		approvalUser := strings.Builder{}
		if len(msg.AssigneeInUsername) > 0 {
			for idx, assigneeUser := range msg.AssigneeInUsername {
				approvalUser.WriteString(assigneeUser)
				if idx < (len(msg.AssigneeInUsername) - 1) {
					approvalUser.WriteString(",")
				}
			}
		}
		updateMap := map[string]any{
			"task_id":       msg.TaskID,
			"task_key":      msg.TaskDefinitionKey,
			"task_name":     msg.TaskName,
			"process_state": msg.ProcessState,
			"update_time":   msg.CurrentTimeStamp,
			"approval_user": approvalUser.String(),
		}
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, updateMap); err != nil {
			return err
		}
		zap.L().Info(fmt.Sprintf("创建任务节点成功，流程ID：%s", msg.BusinessID))

		nodeType := bpm.ProcessNodeType("")
		if msg.ActivitiID == "" {
			nodeType = bpm.AuditEvent
		} else {
			nodeType = bpm.Task
		}

		// 记录执行日志
		auditLog := &bpm.ProcessExecutionLog{
			BusinessID:   msg.BusinessID,
			ProcessKey:   msg.ProcessKey,
			NodeID:       msg.TaskDefinitionKey,
			NodeName:     msg.TaskName,
			TaskID:       msg.TaskID,
			NodeType:     nodeType,
			Comments:     msg.Comments,
			Executor:     "",
			ApprovalList: strings.Join(msg.AssigneeInUsername, ","),
			StartTime:    msg.CurrentTimeStamp,
			EndTime:      msg.CurrentTimeStamp,
			Status:       bpm.ProcessAuditStatus.String(),
			CreatedAt:    now,
			UpdatedAt:    now,
			EventID:      generateEventID(msg.BusinessID, msg.InitiatorUsername, msg.CurrentTimeStamp),
		}

		if err := k.dataStore.BPMExecutionLog().CreateExecutionLog([]*bpm.ProcessExecutionLog{auditLog}); err != nil {
			zap.L().Error("记录执行日志失败", zap.Error(err))
			return err
		}

		zap.L().Debug("记录执行日志成功")
	case bpm.TaskComplete:
		// 当某个任务节点完成，节点完成意味着有用户进行了操作，可能是同意（通过审批，加签，征询，转签），拒绝
		zap.L().Info(fmt.Sprintf("BusinessID: %s, 任务节点结束, 任务标识: %s, 任务名称: %s", msg.BusinessID, msg.TaskDefinitionKey, msg.TaskName))
		// 查询出来操作的工单
		oData, err := k.dataStore.BPMOrder().GetOrderByBusinessID(msg.BusinessID)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		// 获取当前节点是谁审批的
		assigneeInUsername := msg.AssigneeInUsername[0]
		// 更新用户, 并接收新审批人
		latestApprovalList := oData.UpdateApprovalUserList(msg.AssigneeInUsername)
		// 初始化一个当前审批节点的状态
		taskStatus := ""

		// 判断当前task完成是通过还是被拒绝
		updateMap := make(map[string]any)
		switch msg.Operation {
		case bpm.Refuse:
			targetTaskKey, _ := msg.Variables.Get("targetTaskDefKey")
			zap.L().Info(fmt.Sprintf("BusinessID: %s, 节点 [%s] 被 [%s] 拒绝至 [%s]节点", msg.BusinessID, msg.TaskName, assigneeInUsername, targetTaskKey))
			refuseReason := ""
			switch msg.ProcessState {
			case bpm.Rejected:
				refuseReason = msg.Comments

				// 说明拒绝到发起节点了
				if targetTaskKey == "application" {
					// 更新任务相关字段为发起节点
					updateMap["approval_user"] = msg.InitiatorUsername
					updateMap["task_key"] = "application"
					updateMap["task_name"] = "提交节点"
					updateMap["task_id"] = "" // 发起节点没有真正的task_id
				}

			case bpm.Audit:
				comment, _ := msg.Variables.Get("comments")
				refuseReason = utils.ToString(comment)
			}
			zap.L().Info(fmt.Sprintf("拒绝原因: %s", refuseReason))
			updateMap["process_state"] = bpm.Rejected
			updateMap["approval_list"] = strings.Join(latestApprovalList, ",")
			updateMap["update_time"] = msg.CurrentTimeStamp

			taskStatus = bpm.ProcessRefusedStatus.String()
		case bpm.Passed:
			zap.L().Info(fmt.Sprintf("BusinessID: %s, 节点 [%s] 被 [%s] 通过", msg.BusinessID, msg.TaskName, assigneeInUsername))
			updateMap["approval_list"] = strings.Join(latestApprovalList, ",")
			updateMap["task_id"] = msg.TaskID
			updateMap["task_key"] = msg.TaskDefinitionKey
			updateMap["task_name"] = msg.TaskName
			updateMap["update_time"] = msg.CurrentTimeStamp

			taskStatus = bpm.ProcessPassedStatus.String()
		}
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, updateMap); err != nil {
			return err
		}
		zap.L().Debug(fmt.Sprintf("更新任务节点成功，流程ID：%s", msg.BusinessID))

		// 记录执行日志，更新的肯定是已经创建过的任务节点
		logs, err := k.dataStore.BPMExecutionLog().GetExecutionLogByTaskID(msg.TaskID)
		if err != nil {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}
		if len(logs) == 0 {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}

		log := logs[0]

		// 需要更新的内容就是
		log.Executor = assigneeInUsername
		log.EndTime = msg.CurrentTimeStamp
		log.UpdatedAt = now
		log.Status = taskStatus
		log.Comments = msg.Commments

		// 更新执行日志
		if err := k.dataStore.BPMExecutionLog().UpdateExecutionLog(log); err != nil {
			zap.L().Error("更新执行日志失败", zap.Error(err))
			return err
		}
		zap.L().Debug("更新执行日志成功")
		return nil

	case bpm.NotifyAction:
		// 2024-12-06：知会，目前通过接口调用的知会，不会发送kafka消息，对外的接口因为是给业务后端调用的，就没有通知
		// 通过页面点击知会按钮，会发送kafka消息；by: 流程小助手
		// TODO: 后面我可以试一试通过页面监听kafka消息的发送
	case bpm.TaskFrontAddSign, bpm.TaskAfterAddSign, bpm.SignShift, bpm.TaskConsult:
		// 前加签，后加签，转签
		zap.L().Debug(bpm.EventTypeMap[msg.EventType])
		o, err := k.dataStore.BPMOrder().GetOrderByBusinessID(msg.BusinessID)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		o.UpdateTime = msg.CurrentTimeStamp
		o.TaskID = msg.TaskID
		o.TaskKey = msg.TaskDefinitionKey
		o.TaskName = msg.TaskName

		// 获取新审批人是谁
		zap.L().Debug("获取新的审批人, 审批人工号为: " + msg.Assiginee)
		newApproval, err := k.dataStore.User().GetUserByNumber(msg.Assiginee)
		if err != nil {
			zap.L().Error("获取新审批人失败", zap.Error(err))
			return err
		}
		// 更新历史审批人列表
		o.ApprovalUser = newApproval.Username

		// 更新历史审批人
		zap.L().Debug("获取旧审批人, 审批人工号为: " + msg.OldAssiginee)
		oldApproval, err := k.dataStore.User().GetUserByNumber(msg.OldAssiginee)
		if err != nil {
			zap.L().Error("获取旧审批人失败", zap.Error(err))
			return err
		}

		// 更新历史审批人列表
		o.ApprovalList = strings.Join(o.UpdateApprovalUserList([]string{oldApproval.Username}), ",")

		// 日志打印审批意见, bpm遗留的bug，同时存在comments和commments两个字段，这里需要兼容
		comments := ""
		if msg.EventType == bpm.SignShift {
			comments = msg.Commments
			zap.L().Debug("审批意见为: " + comments)
		} else {
			comments = msg.Comments
			zap.L().Debug("审批意见为: " + comments)
		}

		// 更新工单
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, o); err != nil {
			return err
		}
		// 拼接nodeName
		nodeName := oldApproval.Name + "设置的"
		switch msg.EventType {
		case bpm.TaskFrontAddSign:
			nodeName += "前加签"
		case bpm.TaskAfterAddSign:
			nodeName += "后加签"
		case bpm.SignShift:
			nodeName += "转签"
		case bpm.TaskConsult:
			nodeName += "征询"
		}
		nodeName += "审批"

		// 先找到当前节点下所有的审批中的节点，然后更新这些旧节点的状态
		auditLogs, err := k.dataStore.BPMExecutionLog().GetExecutionLogByTaskID(msg.TaskID)
		if err != nil {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}

		auditLogEventMap := make(map[string]*bpm.ProcessExecutionLog)
		pendingAuditLogs := make([]*bpm.ProcessExecutionLog, 0)
		for _, log := range auditLogs {
			auditLogEventMap[log.EventID] = log
			if log.Status == bpm.ProcessAuditStatus.String() {
				pendingAuditLogs = append(pendingAuditLogs, log)
			}
		}

		eventID := generateEventID(msg.BusinessID, oldApproval.Username, msg.CurrentTimeStamp)
		if _, ok := auditLogEventMap[eventID]; !ok {
			// 如果当前节点下没有这个事件，则说明是新发起的节点，需要创建一个执行日志
			for _, log := range pendingAuditLogs {
				log.Executor = oldApproval.Username
				log.Status = bpm.ProcessPassedStatus.String()
				log.EndTime = msg.CurrentTimeStamp
				log.UpdatedAt = now
				log.Comments = comments
				if err := k.dataStore.BPMExecutionLog().UpdateExecutionLog(log); err != nil {
					zap.L().Error("更新执行日志失败", zap.Error(err))
					return err
				}
			}

			// 记录执行日志
			auditLog := &bpm.ProcessExecutionLog{
				BusinessID:   msg.BusinessID,
				ProcessKey:   msg.ProcessKey,
				NodeID:       msg.TaskDefinitionKey,
				NodeName:     nodeName,
				NodeType:     bpm.AuditEvent,
				Comments:     "", // 这个时候还没审批呢，不应该有审批意见
				Executor:     oldApproval.Username,
				StartTime:    msg.CurrentTimeStamp,
				EndTime:      msg.CurrentTimeStamp,
				Status:       bpm.ProcessAuditStatus.String(),
				CreatedAt:    now,
				UpdatedAt:    now,
				TaskID:       msg.TaskID,
				EventID:      generateEventID(msg.BusinessID, oldApproval.Username, msg.CurrentTimeStamp),
				ApprovalList: strings.Join([]string{newApproval.Username}, ","),
			}

			if err := k.dataStore.BPMExecutionLog().CreateExecutionLog([]*bpm.ProcessExecutionLog{auditLog}); err != nil {
				zap.L().Error("记录执行日志失败", zap.Error(err))
				return err
			}
		}

		return nil

	case bpm.ProcessRollBack:
		// 申请人退回流程
		o, err := k.dataStore.BPMOrder().GetOrderByBusinessID(msg.BusinessID)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		// 发起人撤回后，此时ProcessState会变为Rejected
		o.ProcessState = msg.ProcessState
		o.UpdateTime = msg.CurrentTimeStamp
		o.TaskID = msg.TaskID
		// 直接退回发起节点
		o.TaskKey = "application"
		// 退回以后工单审批人变为发起人,发起人此时可以选择废弃
		o.ApprovalUser = o.InitiatorUserName
		// 更新工单信息
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, o); err != nil {
			return err
		}
		zap.L().Debug(fmt.Sprintf("流程退回成功，流程ID：%s", msg.BusinessID))

		// 记录执行日志
		auditLogs, err := k.dataStore.BPMExecutionLog().GetExecutionLogByTaskID(msg.TaskID)
		if err != nil {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}

		if len(auditLogs) == 0 {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}

		auditLog := auditLogs[0]
		auditLog.Executor = o.InitiatorUserName
		auditLog.Status = bpm.ProcessRollbackStatus.String()
		auditLog.UpdatedAt = now
		auditLog.EndTime = msg.CurrentTimeStamp
		auditLog.Comments = msg.Comments
		auditLog.NodeID = "application"
		auditLog.NodeName = "提交节点"
		auditLog.NodeType = bpm.Application

		if err := k.dataStore.BPMExecutionLog().UpdateExecutionLog(auditLog); err != nil {
			zap.L().Error("记录执行日志失败", zap.Error(err))
			return err
		}
		return nil

	case bpm.ProcessEnd:
		// 当流程结束的时候，需要更新流程的状态
		switch msg.ProcessState {
		case bpm.End:
			// 流程正常结束
			zap.L().Info(fmt.Sprintf("BusinessID: %s, 流程结束", msg.BusinessID))
			// 更新流程字段信息, 清空所有任务信息, 这里要使用map, 因为
			// 当使用 struct 更新时，默认情况下GORM 只会更新非零值的字段
			updateMap := map[string]any{
				"task_id":       "",
				"task_key":      "",
				"task_name":     "",
				"update_time":   msg.CurrentTimeStamp,
				"process_state": bpm.End,
				"activity_id":   "",
				"approval_user": "", // 结束以后也不包含所谓的什么审批人了
			}

			// 更新bpm工单信息
			if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, updateMap); err != nil {
				return err
			}

			// 流程结束，关闭对应的消息队列
			k.closeBusinessQueue(msg.BusinessID)

			eventID := generateEventID(msg.BusinessID, msg.InitiatorUsername, msg.CurrentTimeStamp)
			auditLogs, err := k.dataStore.BPMExecutionLog().GetExecutionLogByEventID(eventID)
			if err != nil {
				zap.L().Error("获取执行日志失败", zap.Error(err))
				return err
			}

			if len(auditLogs) == 0 {
				// 记录执行日志
				auditLog := &bpm.ProcessExecutionLog{
					BusinessID: msg.BusinessID,
					ProcessKey: msg.ProcessKey,
					NodeID:     msg.TaskID,
					NodeName:   "流程结束",
					NodeType:   bpm.EndEvent,
					Comments:   "",
					Executor:   "",
					StartTime:  msg.CurrentTimeStamp,
					EndTime:    msg.CurrentTimeStamp,
					Status:     bpm.ProcessEndStatus.String(),
					CreatedAt:  now,
					UpdatedAt:  now,
					EventID:    eventID,
				}

				if err := k.dataStore.BPMExecutionLog().CreateExecutionLog([]*bpm.ProcessExecutionLog{auditLog}); err != nil {
					zap.L().Error("记录执行日志失败", zap.Error(err))
					return err
				}
				return nil
			}

		case bpm.Termination:
			// 流程终止, 快流程
			zap.L().Info(fmt.Sprintf("BusinessID: %s, 流程拒绝至结束", msg.BusinessID))
			// 流程终止，关闭对应的消息队列
			k.closeBusinessQueue(msg.BusinessID)

		case bpm.Rejected:
			// 流程终止，专业流程, 直接拒绝到结束
			zap.L().Info(fmt.Sprintf("BusinessID: %s, 流程拒绝至结束", msg.BusinessID))
			updateMap := map[string]any{
				"task_id":       "",
				"task_key":      "",
				"task_name":     "",
				"update_time":   msg.CurrentTimeStamp,
				"process_state": bpm.End,
				"activity_id":   "",
			}
			if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, updateMap); err != nil {
				return err
			}
			// 流程拒绝至结束，关闭对应的消息队列
			k.closeBusinessQueue(msg.BusinessID)

		default:
			return fmt.Errorf("不支持的流程状态: %s", msg.ProcessState)
		}

	case bpm.SignalEntityCreate:
		// 接收任务
		zap.L().Info(fmt.Sprintf("BusinessID: %s, 接收到任务: %s", msg.BusinessID, msg.ActivitiID))
		o, err := k.dataStore.BPMOrder().GetOrderByBusinessID(msg.BusinessID)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		// 当执行到三方任务的时候，会有一个任务的节点ID，这个ID是三方任务的ID
		o.ActivityID = msg.ActivitiID

		// 更新bpm工单信息
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, o); err != nil {
			return err
		}

		// 执行三方任务，异步执行，不阻塞
		go k.ProcessCallBackTask(msg.ActivitiID, o.BusinessID)

		// 记录执行日志
		node, err := k.dataStore.BPMNode().GetProcessNodeByNodeID(msg.ActivitiID)
		if err != nil {
			zap.L().Error(fmt.Sprintf("获取节点信息失败，节点ID: %s", msg.ActivitiID))
			return fmt.Errorf("获取节点信息失败，节点ID: %s", msg.ActivitiID)
		}

		auditLog := &bpm.ProcessExecutionLog{
			BusinessID: msg.BusinessID,
			ProcessKey: msg.ProcessKey,
			NodeID:     msg.ActivitiID,
			NodeName:   node.NodeName,
			NodeType:   bpm.Task,
			Comments:   msg.Comments,
			Executor:   "第三方系统",
			StartTime:  msg.CurrentTimeStamp,
			EndTime:    msg.CurrentTimeStamp,
			Status:     bpm.ProcessAuditStatus.String(),
			CreatedAt:  now,
			UpdatedAt:  now,
		}

		if err := k.dataStore.BPMExecutionLog().CreateExecutionLog([]*bpm.ProcessExecutionLog{auditLog}); err != nil {
			zap.L().Error("记录执行日志失败", zap.Error(err))
			return err
		}

		zap.L().Debug("记录执行日志成功")
		return nil

	case bpm.ActivitySignaled:
		// 任务通过
		zap.L().Info(fmt.Sprintf("BusinessID: %s, 任务通过", msg.BusinessID))

		// 任务通过以后，要把对应工单的activityID置空，表示当前order已不再处理三方任务执行阶段
		updateMap := map[string]any{
			"activity_id": "",
		}
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, updateMap); err != nil {
			zap.L().Error("更新工单信息失败", zap.Error(err))
			return err
		}

		// 比较蛋疼的是接收任务节点是没有taskID的
		auditLogs, err := k.dataStore.BPMExecutionLog().GetExecutionLogByActivitiID(msg.ActivitiID, msg.BusinessID)
		if err != nil {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}

		if len(auditLogs) == 0 {
			zap.L().Error("获取执行日志失败", zap.Error(err))
			return err
		}

		auditLog := auditLogs[0]
		auditLog.Status = bpm.ProcessPassedStatus.String()
		auditLog.EndTime = msg.CurrentTimeStamp
		auditLog.UpdatedAt = now

		if err := k.dataStore.BPMExecutionLog().UpdateExecutionLog(auditLog); err != nil {
			zap.L().Error("更新执行日志失败", zap.Error(err))
			return err
		}

		zap.L().Debug("更新执行日志成功")
		return nil

	case bpm.ProcessCancelled:
		// 流程作废
		zap.L().Info(fmt.Sprintf("BusinessID: %s, 流程作废", msg.BusinessID))
		o, err := k.dataStore.BPMOrder().GetOrderByBusinessID(msg.BusinessID)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}

		// 更新工单信息
		o.ProcessState = bpm.Termination
		o.UpdateTime = msg.CurrentTimeStamp
		o.ApprovalUser = "" // 作废以后也不包含所谓的什么审批人了
		o.ApprovalList = strings.Join(o.UpdateApprovalUserList([]string{o.InitiatorUserName}), ",")

		updateMap := map[string]any{
			"process_state": bpm.Termination,
			"update_time":   msg.CurrentTimeStamp,
			"approval_user": "",
			"approval_list": strings.Join(o.UpdateApprovalUserList([]string{o.InitiatorUserName}), ","),
		}
		if err := k.dataStore.BPMOrder().UpdateOrder(msg.BusinessID, updateMap); err != nil {
			return err
		}

		// 流程作废终止，终止的时候新增一个终止的节点
		auditLog := &bpm.ProcessExecutionLog{
			BusinessID: msg.BusinessID,
			ProcessKey: msg.ProcessKey,
			NodeID:     "application",
			NodeName:   "提交节点",
			NodeType:   bpm.Application,
			Comments:   msg.Cause,
			Executor:   o.InitiatorUserName,
			StartTime:  msg.CurrentTimeStamp,
			EndTime:    msg.CurrentTimeStamp,
			Status:     bpm.ProcessTerminatedStatus.String(),
			CreatedAt:  now,
			UpdatedAt:  now,
			TaskID:     generateEmptyTaskID(msg.BusinessID, "application"),
		}

		endAuditLog := &bpm.ProcessExecutionLog{
			BusinessID: msg.BusinessID,
			ProcessKey: msg.ProcessKey,
			NodeID:     "termination",
			NodeName:   "终止",
			NodeType:   bpm.EndEvent,
			Comments:   "",
			Executor:   o.InitiatorUserName,
			StartTime:  msg.CurrentTimeStamp,
			EndTime:    msg.CurrentTimeStamp,
			Status:     "",
			CreatedAt:  now + 1,
			UpdatedAt:  now + 1,
			TaskID:     generateEmptyTaskID(msg.BusinessID, "termination"),
		}

		if err := k.dataStore.BPMExecutionLog().CreateExecutionLog([]*bpm.ProcessExecutionLog{
			auditLog,
			endAuditLog,
		}); err != nil {
			zap.L().Error("记录执行日志失败", zap.Error(err))
			return err
		}

		zap.L().Debug("记录执行日志成功")

		// 流程作废，关闭对应的消息队列
		k.closeBusinessQueue(msg.BusinessID)
		return nil

	default:
		evt := strings.TrimSpace(string(msg.EventType))
		b, err := json.Marshal(msg)
		if err != nil {
			return errors.New("json序列化 Bpm Message失败")
		}

		zap.L().Error("不支持的消息类型",
			zap.String("eventType", func() string {
				if evt == "" {
					return "空"
				}
				return string(msg.EventType)
			}()), zap.String("msg", string(b)))
		return fmt.Errorf("不支持的消息类型：%s", msg.EventType)
	}

	return nil
}
