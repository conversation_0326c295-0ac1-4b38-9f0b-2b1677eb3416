package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	api "ks-knoc-server/internal/common/base/api/bpm"
	"ks-knoc-server/internal/common/base/model/bpm"
	share "ks-knoc-server/internal/common/base/share/bpm"
	"ks-knoc-server/internal/common/openapi"
	"ks-knoc-server/internal/process/store"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// buildReinstallTasks 构建安装任务
func (p *processService) buildReinstallTasks() []*bpm.InstallTask {
	installTasksData := make([]*bpm.InstallTask, 0)
	for _, task := range share.TaskSequence {
		installTasksData = append(installTasksData, &bpm.InstallTask{
			TaskName:   task.String(),
			TaskStatus: share.TaskStatusPending,
			Message:    "",
			StartTime:  0,
			EndTime:    0,
			Duration:   0,
		})
	}

	return installTasksData
}

func (p *processService) validateInstallStatus(deviceSN string, installing []*bpm.OsInstall) error {
	switch len(installing) {
	case 0:
		// 正常情况，可以继续安装
		return nil
	case 1:
		// 如果安装中的列表不为空，则代表该服务器正在安装队列中，不允许重复提交
		zap.L().Error("服务器正在安装队列中，不允许重复提交", zap.String("sn", deviceSN))
		return errors.New("服务器正在安装队列中，不允许重复提交, 设备SN: " + deviceSN)
	default:
		// 一台设备如果正在安装任务中，应该有且仅有一条才对，如果说超过一条，证明有脏数据，需要清理
		zap.L().Error("当前设备存在多条安装中的记录，请联系管理员检查", zap.String("sn", deviceSN))
		return errors.New("当前设备存在多条安装中的记录，请联系管理员检查")
	}
}

func (p *processService) reinstall(ctx context.Context,
	initiator string,
	req api.DCRequest,
	orderTitle string,
	orderAbstract *bpm.OrderAbstract,
	orderDetail *bpm.OrderDetail,
) error {
	zap.L().Debug("reinstall Service Called")
	span, _ := apm.StartSpan(ctx, "reinstall", "service")
	defer span.End()

	// 初始化业务数据和流程变量
	var (
		abstractInfo string
		now          = time.Now().Unix()
	)

	// 获取用户提交的安装配置，将用户提交的数据通过mapstructure转换为结构体
	data := api.NewDCReInstallRequest()
	if err := RequestCheck(req.DCRequest, &data); err != nil {
		zap.L().Error("参数校验失败", zap.Error(err))
		return err
	}

	// 构建一个安装配置映射，其中key为设备的sn，这样可以通过sn快速定位到那台设备的安装配置
	installRecordMap := make(map[string]*bpm.InstallConfig, 0)
	// 遍历用户提交过来的安装设备的信息，用于构造工单，工单详情，安装记录以及安装任务
	for _, dev := range data {
		zap.L().Debug("开始处理重装设备", zap.String("device_sn", dev.DeviceSN))

		// 校验参数
		if err := dev.Validate(); err != nil {
			zap.L().Error("参数校验失败", zap.Error(err))
			return err
		}
		// 检查要安装的服务器是否已经在install表中存在，如存在，则代表该服务器正在安装队列中
		// 且安装尚未结束，不允许提交。
		install, err := p.store.InstallOS().GetInstallInfoBySNList(ctx, []string{dev.DeviceSN}, "")
		if err != nil {
			zap.L().Error("获取安装信息失败", zap.Error(err))
			return err
		}

		// 安装信息存在，但有可能是历史信息，所以需要进一步判断
		if len(install) != 0 {
			// 获取安装中的列表
			installing := make([]*bpm.OsInstall, 0)
			for _, ins := range install {
				if ins.InstallStatus != share.InstallStatusCompleted &&
					ins.InstallStatus != share.InstallStatusFailed &&
					ins.InstallStatus != share.InstallStatusCancelled {
					installing = append(installing, ins)
				}
			}
			// 检查设备安装状态
			if err := p.validateInstallStatus(dev.DeviceSN, installing); err != nil {
				return err
			}
		}
		// 初始化一个安装的config
		conf := new(bpm.InstallConfig)

		// 根据raid组来设置安装的配置
		switch dev.RaidGroup {
		case api.Raid1:
			conf.Raid.Enable = true
			conf.Raid.Info.SystemRaidLevel = "raid1"
			conf.Raid.Info.DataRaidLevel = "raid1"
		case api.Raid5:
			conf.Raid.Enable = true
			conf.Raid.Info.SystemRaidLevel = "raid1"
			conf.Raid.Info.DataRaidLevel = "raid5"
		case api.Raid5AndHotSpare:
			conf.Raid.Enable = true
			conf.Raid.Info.SystemRaidLevel = "raid1"
			conf.Raid.Info.HotSpare = true
			conf.Raid.Info.HotSpareCount = 1
		default:
			conf.Raid.Enable = true
			conf.Raid.Info.SystemRaidLevel = "raid1"
			conf.Raid.Info.DataRaidLevel = "raid5"
		}

		// 配置BMC
		conf.BMC.BMCIP = dev.BMCIPAddress
		conf.BMC.BMCMask = dev.BMCNetMask
		conf.BMC.BMCGW = dev.BMCGateway

		// 配置系统
		conf.SysFamily.OsName = string(dev.SystemFamily)
		conf.SysFamily.Hostname = "" // 暂时留空，暂未支持

		// 配置网络
		conf.Network.BondMode = 4 // 默认先写死了, 按照默认就是bond mode4 模式
		conf.Network.SystemDNS = []string{"**************", "**************"}
		conf.Network.SystemGW = dev.IPGateway
		conf.Network.SystemIP = dev.IPAddress
		conf.Network.SystemMask = dev.IPNetMask

		// 追加到installRecordMap中, 用于后续的安装任务创建
		installRecordMap[dev.DeviceSN] = conf
	}

	// 构建摘要信息用于搜索
	for _, dt := range data {
		t := reflect.TypeOf(*dt)
		v := reflect.ValueOf(*dt)
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			jsonTag := field.Tag.Get("json")
			fieldValue := utils.ToString(v.Field(i).Interface())
			if fieldValue == "" {
				continue
			}
			// 构建摘要信息用于搜索
			abstractInfo += fmt.Sprintf("%s:%s;", api.DcReInstallDeviceFieldName[jsonTag], fieldValue)
		}
		abstractInfo += "\n"
	}

	// 设置摘要信息
	orderAbstract.Abstract = abstractInfo

	// 把流程变量序列化
	processVars := mapdata.MapData{
		"dc_type": api.ReInstall,
	}
	processVarsBytes, err := json.Marshal(processVars)
	if err != nil {
		zap.L().Error("序列化流程变量失败", zap.Error(err))
		return err
	}
	orderAbstract.ProcessVars = processVarsBytes
	// 构建install数据列表, 其中key为设备的sn，value为安装配置
	osInstallMap := make(map[string]*bpm.OsInstall, 0)

	// 构建install数据列表, 其中key为设备的sn，value为安装配置
	osInstallList := make([]*bpm.OsInstall, 0)

	// 再次遍历用户提交过来的安装设备的信息，用于构造install数据列表和安装任务列表
	for _, dev := range data {
		// 初始化配置bytes
		configRawMessage, err := json.Marshal(installRecordMap[dev.DeviceSN])
		if err != nil {
			zap.L().Error("序列化安装配置失败", zap.Error(err))
			return err
		}

		ins := &bpm.OsInstall{
			BusinessID:    orderDetail.BusinessID,
			SN:            dev.DeviceSN,
			Creator:       initiator,
			InstallStatus: share.InstallStatusPending,
			Config:        configRawMessage,
			CreatedAt:     now,
			UpdatedAt:     now,
		}

		osInstallMap[dev.DeviceSN] = ins
		osInstallList = append(osInstallList, ins)
	}

	// 构建工单详情, 工单详情中应该记录要保存的所有安装配置
	installRecordsRawMessage, err := json.Marshal(osInstallList)
	if err != nil {
		zap.L().Error("序列化工单数据失败", zap.Error(err))
		return err
	}
	orderDetail.BusinessData = installRecordsRawMessage
	// 将order以及order_detail插入数据库
	if err := p.store.BPMOrder().CreateProfessionalOrder(orderAbstract, orderDetail); err != nil {
		zap.L().Error("插入工单概览和工单详情失败", zap.Error(err))
		return err
	}

	zap.L().Info("工单插入数据库成功, 重装任务创建成功", zap.String("business_id", orderDetail.BusinessID))
	// 发起远程调用，调用openapi创建工单
	if _, err := openapi.ProcessStart(orderTitle, processKey, processPrefix, initiator, processVars); err != nil {
		zap.L().Error("发起流程失败", zap.Error(err))
		return err
	}

	zap.L().Info("发起流程调用OpenAPI成功",
		zap.String("工单标题", orderTitle),
		zap.String("发起人", initiator),
		zap.String("工单ID", orderDetail.BusinessID),
	)

	return nil
}

// initiateAutoReInstall 发起自动重装的任务，创建os_install以及以及install_tasks记录
func (p *processService) initiateAutoReInstall(ctx context.Context, businessId string) error {
	// 获取工单安装详情
	orderDetail, err := p.store.BPMOrder().GetOrderDetailByBusinessID(businessId)
	if err != nil {
		zap.L().Error("获取工单安装详情失败", zap.Error(err))
		return err
	}

	// 反序列化获取安装配置列表
	osInstallList := make([]*bpm.OsInstall, 0)
	if err := json.Unmarshal(orderDetail.BusinessData, &osInstallList); err != nil {
		zap.L().Error("反序列化工单安装详情失败", zap.Error(err))
		return err
	}

	// 遍历安装配置列表，创建os_install以及install_tasks记录
	txErr := p.store.Common().Transaction(func(factory store.Factory) error {
		for _, osInstall := range osInstallList {
			// 检查当前工单内是否已存在对应SN的安装记录，如果存在，说明是申请人复检拒绝的场景
			existingInstalls, err := factory.InstallOS().GetInstallInfoBySNList(ctx, []string{osInstall.SN}, businessId)
			if err != nil {
				zap.L().Error("查询当前工单已有安装记录失败", zap.Error(err), zap.String("sn", osInstall.SN), zap.String("business_id", businessId))
				return err
			}

			if len(existingInstalls) > 0 {
				// 存在历史记录，说明是申请人复检拒绝后重新进入自动装机节点
				// 需要将这些历史记录重置为初始状态，重新开始装机流程
				zap.L().Info("发现历史安装记录，准备重置为初始状态",
					zap.String("business_id", businessId),
					zap.String("sn", osInstall.SN),
					zap.Int("records_count", len(existingInstalls)))

				// 获取当前的时间
				currentTime := time.Now().Unix()
				for _, existingInstall := range existingInstalls {
					// 获取历史安装记录对应的任务
					existingTasks, err := factory.InstallOS().GetInstallTasks(ctx, existingInstall.ID)
					if err != nil {
						zap.L().Error("获取历史安装任务失败", zap.Error(err), zap.Int64("os_install_id", existingInstall.ID))
						return err
					}

					// 将历史任务状态重置为待执行状态
					for _, task := range existingTasks {
						task.TaskStatus = share.TaskStatusPending
						task.Message = "申请人复检拒绝，任务已重置为待执行状态"
						task.CreatedAt = currentTime
						task.UpdatedAt = currentTime
						if err := factory.InstallOS().UpdateInstallTask(ctx, task); err != nil {
							zap.L().Error("重置历史安装任务状态失败", zap.Error(err), zap.Int64("task_id", task.ID))
							return err
						}
					}

					// 将历史安装记录状态重置为等待中
					existingInstall.InstallStatus = share.InstallStatusPending
					existingInstall.UpdatedAt = time.Now().Unix()
					if err := factory.InstallOS().UpdateInstallInfo(ctx, existingInstall); err != nil {
						zap.L().Error("重置历史安装记录状态失败", zap.Error(err), zap.Int64("os_install_id", existingInstall.ID))
						return err
					}

					zap.L().Info("成功重置历史安装记录为初始状态",
						zap.String("business_id", businessId),
						zap.String("sn", osInstall.SN),
						zap.Int64("os_install_id", existingInstall.ID))
				}
				// 如果存在历史记录，直接重置即可，不需要创建新记录
				continue
			} else {
				// 不存在历史记录，正常创建新记录
				zap.L().Info("未发现历史记录，准备创建新的安装记录",
					zap.String("business_id", businessId),
					zap.String("sn", osInstall.SN))

				// 创建新的install_tasks记录
				tasks := p.buildReinstallTasks()

				// 创建新的os_install记录
				if err := factory.InstallOS().CreateOsInstall(ctx, osInstall, tasks); err != nil {
					zap.L().Error("创建os_install记录失败", zap.Error(err))
					return err
				}

				zap.L().Info("成功创建新的安装记录和任务",
					zap.String("business_id", businessId),
					zap.String("sn", osInstall.SN))
			}
		}

		return nil
	})
	// 事务执行失败会自动回滚，这里只记录日志
	if txErr != nil {
		zap.L().Error("处理os_install记录失败", zap.Error(txErr))
		return txErr
	}

	return nil
}
