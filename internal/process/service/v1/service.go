package v1

import (
	"ks-knoc-server/internal/process/store"
)

type Service interface {
	User() UserService
	Process() ProcessService
	InstallOS() InstallOSService
}

var _ Service = (*service)(nil)

type service struct {
	store store.Factory
}

// NewService ...
func NewService(store store.Factory) Service {
	return &service{
		store: store,
	}
}

func (s *service) InstallOS() InstallOSService {
	return newInstallOSService(s.store)
}

func (s *service) User() UserService {
	return newUserService()
}

func (s *service) Process() ProcessService {
	return newProcessService(s.store)
}
