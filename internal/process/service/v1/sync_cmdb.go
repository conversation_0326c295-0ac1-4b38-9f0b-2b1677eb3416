package v1

import (
	"encoding/json"
	"fmt"
	bpmApi "ks-knoc-server/internal/common/base/api/bpm"
	"ks-knoc-server/internal/common/kim"
	"ks-knoc-server/internal/common/openapi"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"
	"strings"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// syncCMDBData 同步cmdb数据
func (k *BPMProcess) syncCMDBData(businessID string) string {
	// 获取工单信息
	order, err := k.dataStore.BPMOrder().GetOrderByBusinessID(businessID)
	if err != nil {
		zap.L().Error(err.Error())
		return ""
	}

	orderSections := strings.Split(order.Title, "-")
	title := orderSections[len(orderSections)-1]
	orderType := fmt.Sprintf("%s-%s", orderSections[0], orderSections[1])

	// 填充模板结构体信息
	syncInfoInstance := new(syncInfo)
	syncInfoInstance.OrderType = orderType
	syncInfoInstance.Title = title
	syncInfoInstance.BusinessID = businessID
	syncInfoInstance.Initiator = order.InitiatorUserName

	// 获取工单的详细内容
	detail, err := k.dataStore.BPMOrder().GetOrderDetailByBusinessID(businessID)
	if err != nil {
		zap.L().Error(err.Error())
		syncInfoInstance.FailedReason = err.Error()
		msg, err := parseSyncFailed(syncInfoInstance)
		if err != nil {
			zap.L().Error(err.Error())
			return msg
		}
		return msg
	}

	// 需要解析数据，然后同步到cmdb，什么类型的操作需要同步数据，上架，下架，重装
	// 综合布线同步CMDB，设备操作暂时没有设计的那么复杂，是单纯的提供的文本框，所以这个也没办法录入。
	switch detail.OpType {
	case "on_rack":
		// 上架
		bizData := make([]*bpmApi.DCOnRackDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &bizData); err != nil {
			zap.L().Error(err.Error())
			syncInfoInstance.FailedReason = err.Error()
			msg, err := parseSyncFailed(syncInfoInstance)
			if err != nil {
				zap.L().Error(err.Error())
				return msg
			}
			return msg
		}

		// 初始化一个切片用来保存上架成功后的数据
		successData := make([]*onRackDataInfo, 0)

		// 遍历每一条上架的设备信息
		for _, dev := range bizData {
			// 查询设备是否存在
			cmdbDev, err := openapi.GetCMDBModelDataByFilter(map[string]interface{}{
				"model_code":             dev.DeviceType,
				"meta_data.universal_sn": dev.DeviceSN,
			})
			if err != nil {
				zap.L().Error(err.Error())
				syncInfoInstance.FailedReason = err.Error()
				msg, err := parseSyncFailed(syncInfoInstance)
				if err != nil {
					zap.L().Error(err.Error())
					return msg
				}
				return msg
			}
			// 要上架的设备有可能是已经存在于cmdb中的设备，比如旧设备出库再上架，这种时候需要先查询一下cmdb，看看设备是否存在
			if len(cmdbDev) > 1 {
				zap.L().Error(fmt.Sprintf("设备SN: %s, 存在多个设备，存在冲突，无法确定是哪个设备", dev.DeviceSN))
				syncInfoInstance.FailedReason = fmt.Sprintf("设备SN: %s, 存在多个设备，存在冲突，无法确定是哪个设备", dev.DeviceSN)
				msg, err := parseSyncFailed(syncInfoInstance)
				if err != nil {
					zap.L().Error(err.Error())
					return msg
				}
				return msg
			}

			// 有两种情况，一种是已经在cmdb中存在的数据，这种情况多见于旧设备出库再上架，这种时候需要先查询一下cmdb，看看设备是否存在
			// 另一种是设备不存在，这种情况下需要先创建设备信息，然后再上架
			if len(cmdbDev) == 1 {
				// 设备已经存在，需要更新设备信息
				existDev := cmdbDev[0]

				existDev.Data.Set(dev.DeviceType+"_remark", dev.ServiceInfo)
				existDev.Data.Set(dev.DeviceType+"_status", "已上线")
				existDev.Data.Set(dev.DeviceType+"_owner", dev.Owner)
				existDev.Data.Set(dev.DeviceType+"_start_u", dev.DevicePosition)

				// 服务器特有的信息，上架的时候，肯定这些信息是必填的
				if dev.DeviceType == "server" {
					existDev.Data.Set("server_os_family", dev.SystemFamily)
					existDev.Data.Set("server_internal_ip", dev.IPAddress)
					existDev.Data.Set("server_bmc", dev.BMCIPAddress)
				}

				// 更新设备信息
				if err := openapi.UpdateCMDBData(&openapi.UpdateCMDBDataRequest{
					ID:            existDev.ID,
					ModelCode:     existDev.ModelCode,
					Data:          existDev.Data,
					IdentifyName:  existDev.IdentifyName,
					IdentifyValue: existDev.IdentifyValue,
					InputType:     int(existDev.InPutType),
				}); err != nil {
					zap.L().Error(err.Error())
					syncInfoInstance.FailedReason = err.Error()
					msg, err := parseSyncFailed(syncInfoInstance)
					if err != nil {
						zap.L().Error(err.Error())
						return msg
					}
					return msg
				}

				successData = append(successData, &onRackDataInfo{
					Action:         "更新",
					Name:           dev.DeviceType,
					DeviceSN:       dev.DeviceSN,
					Owner:          dev.Owner,
					DeviceType:     dev.DeviceType,
					DeviceBrand:    dev.DeviceBrand,
					DeviceModel:    dev.DeviceModel,
					Office:         order.Office,
					IdcRoom:        dev.IdcRoom,
					DeviceRack:     dev.DeviceRack,
					DevicePosition: utils.ToString(dev.DevicePosition),
				})
			} else {
				// 设备不存在，需要创建设备
				devCode := fmt.Sprintf("%s_code", dev.DeviceType)
				devName := fmt.Sprintf("%s-%s-%s", dev.DeviceType, dev.DeviceBrand, dev.DeviceModel)
				devCodeValue := fmt.Sprintf("%s_%s_%d", dev.DeviceType, utils.ToPinyin(devName), time.Now().Unix())

				// 初始化数据
				deviceData := make(mapdata.MapData)
				deviceData.Set(dev.DeviceType+"_sn", strings.ToUpper(dev.DeviceSN))
				deviceData.Set(dev.DeviceType+"_name", devName)
				deviceData.Set(dev.DeviceType+"_code", devCodeValue)
				deviceData.Set(dev.DeviceType+"_brand", dev.DeviceBrand)
				deviceData.Set(dev.DeviceType+"_model", dev.DeviceModel)
				deviceData.Set(dev.DeviceType+"_remark", dev.ServiceInfo)
				deviceData.Set(dev.DeviceType+"_overdue_time", dev.OverdueTime)
				deviceData.Set(dev.DeviceType+"_owner", dev.Owner)
				deviceData.Set(dev.DeviceType+"_start_u", dev.DevicePosition)
				deviceData.Set(dev.DeviceType+"_power", dev.Power)
				deviceData.Set(dev.DeviceType+"_height", dev.DeviceHeight)

				// 处理一下上架位置
				var rackID string
				if dev.DeviceRack != "" {
					rackData, err := openapi.GetCMDBModelDataByFilter(map[string]interface{}{
						"model_code":     "rack",
						"data.rack_name": dev.DeviceRack,
					})
					if err != nil {
						zap.L().Error(err.Error())
						syncInfoInstance.FailedReason = err.Error()
						msg, err := parseSyncFailed(syncInfoInstance)
						if err != nil {
							zap.L().Error(err.Error())
							return msg
						}
						return msg
					}
					if len(rackData) == 1 {
						rackID = rackData[0].ID
					}
				}

				// 服务器特有的信息，上架的时候，肯定这些信息是必填的
				if dev.DeviceType == "server" {
					deviceData.Set("server_os_family", dev.SystemFamily)
					deviceData.Set("server_internal_ip", dev.IPAddress)
					deviceData.Set("server_bmc", dev.BMCIPAddress)
				}

				// 创建设备
				if err := openapi.CreateCMDBData(&openapi.CreateModelDataRequest{
					ModelCode:     dev.DeviceType,
					IdentifyName:  devCode,
					IdentifyValue: devCodeValue,
					InputType:     2,
					Data:          deviceData,
					ParentID:      rackID,
				}); err != nil {
					zap.L().Error(err.Error())
					syncInfoInstance.FailedReason = err.Error()
					msg, err := parseSyncFailed(syncInfoInstance)
					if err != nil {
						zap.L().Error(err.Error())
						return msg
					}
					return msg
				}
				zap.L().Info(fmt.Sprintf("创建设备成功，设备SN: %s, 设备名称: %s, 设备型号: %s", dev.DeviceSN, devName, dev.DeviceModel))
				successData = append(successData, &onRackDataInfo{
					Action:         "创建",
					Name:           devName,
					DeviceSN:       dev.DeviceSN,
					Owner:          dev.Owner,
					DeviceType:     dev.DeviceType,
					DeviceBrand:    dev.DeviceBrand,
					DeviceModel:    dev.DeviceModel,
					Office:         order.Office,
					IdcRoom:        dev.IdcRoom,
					DeviceRack:     dev.DeviceRack,
					DevicePosition: utils.ToString(dev.DevicePosition),
				})
			}
		}
		msg, err := parseSyncSuccess(syncInfoInstance, successData)
		if err != nil {
			zap.L().Error(err.Error())
			return msg
		}
		return msg
	case "off_rack":
		// 下架
		bizData := make([]*bpmApi.DCOffRackDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &bizData); err != nil {
			zap.L().Error(err.Error())
			syncInfoInstance.FailedReason = err.Error()
			msg, err := parseSyncFailed(syncInfoInstance)
			if err != nil {
				zap.L().Error(err.Error())
				return msg
			}
			return msg
		}
		// 下架的设备，需要做的是更新设备在cmdb中的一个状态，分两种情况
		// 1、归属IT的设备，清空一些设备的在线信息
		// 2、归属IDC以及归属个人的设备，可以直接删除
		// dev, err := openapi.GetCMDBModelDataByFilter(map[string]any{
		// })

		// if err := openapi.UpdateCMDBData(&openapi.UpdateCMDBDataRequest{
		// 	ID:            existDev.ID,
		// 	ModelCode:     existDev.ModelCode,
		// 	Data:          existDev.Data,
		// 	IdentifyName:  existDev.IdentifyName,
		// 	IdentifyValue: existDev.IdentifyValue,
		// }); err != nil {
		// 	zap.L().Error(err.Error())
		// 	syncInfoInstance.FailedReason = err.Error()
		// 	msg, err := parseSyncFailed(syncInfoInstance)
		// 	if err != nil {
		// 		zap.L().Error(err.Error())
		// 		return msg
		// 	}
		// }
		return ""
	case "reinstall":
		// 重装
		bizData := make([]*bpmApi.DCReInstallDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &bizData); err != nil {
			zap.L().Error(err.Error())
			syncInfoInstance.FailedReason = err.Error()
			msg, err := parseSyncFailed(syncInfoInstance)
			if err != nil {
				zap.L().Error(err.Error())
				return msg
			}
			return msg
		}
		zap.L().Debug("重装类型的工单", zap.Any("bizData", bizData))
		// 需要重装的设备，至少你前提得存在，所以需要先查询一下设备是否存在
		return ""
	case "operation":
		// 操作类型的工单
		bizData := make([]*bpmApi.DCOperationDevice, 0)
		if err := json.Unmarshal(detail.BusinessData, &bizData); err != nil {
			zap.L().Error(err.Error())
			syncInfoInstance.FailedReason = err.Error()
			msg, err := parseSyncFailed(syncInfoInstance)
			if err != nil {
				zap.L().Error(err.Error())
				return msg
			}
			return msg
		}
		zap.L().Debug("操作类型的工单", zap.Any("bizData", bizData))
		return ""
	default:
		zap.L().Error("不支持的操作类型", zap.String("opType", detail.OpType.String()))
		syncInfoInstance.FailedReason = fmt.Sprintf("不支持的操作类型: %s", detail.OpType)
		msg, err := parseSyncFailed(syncInfoInstance)
		if err != nil {
			zap.L().Error(err.Error())
			return msg
		}
		return msg
	}
}

// ProcessCallBackTask 执行三方任务
func (k *BPMProcess) ProcessCallBackTask(taskKey, businessID string) {
	fmt.Println("接收任务: ", taskKey)

	// 标记是否需要立即发送SignalReceived信号
	shouldSendSignalImmediately := true

	switch taskKey {
	case "it_dc_cmdb_sync":
		// 同步cmdb信息
		zap.L().Debug(fmt.Sprintf("同步cmdb信息, 工单ID: %s", businessID))

		// 初始化MarkDown内容为空字符串
		markDownContent := ""
		// 接收消息
		markDownContent = k.syncCMDBData(businessID)
		// 发送Kim消息同步执行是否成功，任何错误都只记录日志，不影响流程继续
		if err := k.sendKimMessage(markDownContent); err != nil {
			zap.L().Error("发送Kim消息失败", zap.Error(err))
		}
		// cmdb同步任务完成后立即发送SignalReceived
		shouldSendSignalImmediately = true

	case "it_dc_auto_install":
		// 自动装机节点处理 - 这里应该等待实际的装机任务执行完成
		// 不应该立即完成节点，应该等待PXE回调等实际任务的执行
		zap.L().Info(fmt.Sprintf("进入自动装机节点, 工单ID: %s", businessID))
		zap.L().Info("自动装机节点将等待实际装机任务的执行和回调")

		// 自动装机节点不立即发送SignalReceived，等待装机完成后的回调
		shouldSendSignalImmediately = false

	default:
		// 不支持的第三方服务暂时也别卡住，直接放行
		zap.L().Warn(fmt.Sprintf("不支持的三方任务: %s", taskKey))
		shouldSendSignalImmediately = true
	}

	// 根据任务类型决定是否立即发送SignalReceived信号
	if shouldSendSignalImmediately {
		if _, err := openapi.SignalReceived(&openapi.SignalReceivedRequest{
			BusinessId: businessID,
			SignalName: taskKey,
		}); err != nil {
			zap.L().Error(fmt.Sprintf("发送信号接收请求失败，error: %v", err))
		} else {
			zap.L().Info(fmt.Sprintf("任务 %s 执行完成，已发送SignalReceived信号", taskKey))
		}
	} else {
		zap.L().Info(fmt.Sprintf("任务 %s 不立即发送SignalReceived信号，等待后续回调", taskKey))
	}
}

// sendKimMessage 发送Kim消息，任何错误都只记录日志，不影响流程继续
func (k *BPMProcess) sendKimMessage(markDownContent string) error {
	content := kim.NewKimRobotMarkdown(markDownContent)
	baseUrl := viper.GetString("kim.robot.baseUrl")

	if baseUrl == "" {
		zap.L().Error("Kim机器人baseUrl为空")
		return fmt.Errorf("Kim机器人baseUrl为空")
	}

	robotUrl := fmt.Sprintf("%s/api/robot/send?key=a95cbb96-9ae4-471e-ac0f-b3f63cc781c2", baseUrl)
	req, err := kim.NewKimRobotRequest(robotUrl, content)
	if err != nil {
		zap.L().Error("创建Kim机器人请求失败", zap.Error(err), zap.String("robotUrl", robotUrl))
		return fmt.Errorf("创建Kim机器人请求失败: %w", err)
	}

	resp := new(kim.KimRobotResponse)
	if err := req.Do().Into(resp); err != nil {
		zap.L().Error("发送Kim机器人请求失败", zap.Error(err), zap.String("robotUrl", robotUrl))
		return fmt.Errorf("发送Kim机器人请求失败: %w", err)
	}

	zap.L().Info("Kim消息发送成功")
	return nil
}
