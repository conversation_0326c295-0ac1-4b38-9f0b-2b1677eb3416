package v1

import (
	"bytes"
	"html/template"
)

type syncInfo struct {
	OrderType    string
	FailedReason string
	BusinessID   string
	Title        string
	Initiator    string
}

const syncFailed = `### {{.OrderType}}
<font color='red'>CMDB同步失败</font>:

- 原因: {{.FailedReason}}
- 工单ID: {{.BusinessID}}
- 标题: {{.Title}}
- 发起人: {{.Initiator}}
`

func parseSyncFailed(syncInfo *syncInfo) (string, error) {
	tmpl, err := template.New("syncFailed").Parse(syncFailed)
	if err != nil {
		return "", err
	}
	buf := new(bytes.Buffer)
	if err := tmpl.Execute(buf, syncInfo); err != nil {
		return "", err
	}
	return buf.String(), nil
}

const syncSuccess = `### {{.OrderType}}
<font color='blue'>CMDB同步成功</font>

- 工单ID: {{.BusinessID}}
- 标题: {{.Title}}
- 发起人: {{.Initiator}}

| 操作 | 名称 | 序列号 | 负责人 | 设备类型 | 设备品牌 | 设备型号 | 职场 | 机房 | 机柜 | 机柜位 |
| :--: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
`

type onRackDataInfo struct {
	Name           string
	DeviceSN       string
	Owner          string
	DeviceType     string
	DeviceBrand    string
	DeviceModel    string
	Office         string
	IdcRoom        string
	DeviceRack     string
	DevicePosition string
	Action         string
}

const deviceTmpl = `| {{.Action}} | {{.Name}} | {{.DeviceSN}} |  {{.Owner}} | {{.DeviceType}} |  {{.DeviceBrand}} |  {{.DeviceModel}} | {{.Office}} |  {{.IdcRoom}} |  {{.DeviceRack}} | {{.DevicePosition}} | `

func parseSyncSuccess(syncInfoInstance *syncInfo, onRackDataInfo []*onRackDataInfo) (string, error) {
	tmpl, err := template.New("syncSuccess").Parse(syncSuccess)
	if err != nil {
		return "", err
	}
	buf := new(bytes.Buffer)
	if err := tmpl.Execute(buf, syncInfoInstance); err != nil {
		return "", err
	}
	// 添加数据
	for _, dev := range onRackDataInfo {
		dt, err := template.New("deviceTmpl").Parse(deviceTmpl)
		if err != nil {
			return "", err
		}
		deviceBuf := new(bytes.Buffer)
		if err := dt.Execute(deviceBuf, dev); err != nil {
			return "", err
		}
		tmplString := deviceBuf.String()
		buf.WriteString(tmplString)
		buf.WriteString("\n")
	}
	return buf.String(), nil
}
