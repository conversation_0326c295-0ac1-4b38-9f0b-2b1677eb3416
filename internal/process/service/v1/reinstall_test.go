package v1

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"testing"
	"time"

	"ks-knoc-server/config/knoc"
	model "ks-knoc-server/internal/common/base/model/bpm"
	share "ks-knoc-server/internal/common/base/share/bpm"
	"ks-knoc-server/internal/common/infra"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
	"github.com/olekukonko/tablewriter"
	"gorm.io/gorm"
)

const (
	orderAbstractTable = "bpm_order_abstract"
	installOSTable     = "os_install"
	installTaskTable   = "install_tasks"
)

var (
	db  *kdb.Korm
	err error
)

func initDependency() error {
	if err := knoc.InitConfig(); err != nil {
		return err
	}

	// 初始化数据库链接
	db, err = infra.NewKDBOptions().Init()
	if err != nil {
		return err
	}

	return nil
}

// clearScreen 清屏函数
func clearScreen() {
	cmd := exec.Command("clear")
	cmd.Stdout = os.Stdout
	cmd.Run()
}

// printHeader 打印标题头部
func printHeader(businessID string) {
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("# 重装工单实时监控 - 工单ID: %s\n", businessID)
	fmt.Printf("# 刷新时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println(strings.Repeat("=", 40))
}

func TestReinstallWorkflowDemo(t *testing.T) {
	// 解析命令行参数
	flag.Parse()

	// 初始化上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化依赖
	if err := initDependency(); err != nil {
		t.Fatalf("[Error] 初始化依赖失败: %v", err)
	}

	// 从环境变量或命令行参数获取工单ID，如果没有则跳过测试
	businessID := os.Getenv("BUSINESS_ID")
	if businessID == "" {
		t.Skip("环境变量 BUSINESS_ID 未设置，跳过演示测试。使用方法：BUSINESS_ID=your_order_id go test -v -run TestReinstallWorkflowDemo")
	}

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	// 首次清屏和显示
	clearScreen()
	printHeader(businessID)
	fmt.Println("[Info] 开始实时监控...")

	for {
		select {
		case <-ticker.C:
			// 清屏并重新显示
			clearScreen()
			printHeader(businessID)

			var order model.OrderAbstract
			if err := db.Table(orderAbstractTable).Where("business_id = ?", businessID).First(&order).Error; err != nil {
				t.Fatalf("[Error] 获取工单信息失败: %v", err)
			}

			if order.ProcessState == model.ProcessState(model.ProcessEnd) {
				fmt.Printf("\n[Info] 工单流程已结束\n")
				cancel()
				return
			}

			// 显示工单基本信息
			fmt.Println("\n[Info] 工单信息:")
			fmt.Printf("工单状态: %s\n", model.ProcessStateNameMap[order.ProcessState])
			fmt.Printf("当前节点: %s\n", order.TaskName)
			fmt.Printf("审批人员: %s\n", order.ApprovalUser)
			fmt.Printf("创建时间: %s\n", time.Unix(order.CreateTime, 0).Format("2006-01-02 15:04:05"))
			fmt.Printf("工单标题: %s\n", order.Title)

			// 获取重装任务信息
			var installOS model.OsInstall
			if err := db.Table(installOSTable).Where("business_id = ?", businessID).First(&installOS).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					fmt.Printf("\n[Info] 重装任务信息不存在\n")
					continue
				} else {
					t.Fatalf("[Error] 获取重装任务信息失败: %v", err)
				}
			}

			fmt.Printf("\n[Info] 重装任务信息: \n")
			fmt.Printf("重装设备SN: %s\n", installOS.SN)
			fmt.Printf("重装任务状态: %s\n", share.InstallStatusMap[installOS.InstallStatus])

			// 获取重装任务列表
			var installTasks []model.InstallTask
			if err := db.Table(installTaskTable).Where("os_install_id = ?", installOS.ID).Find(&installTasks).Error; err != nil {
				t.Fatalf("[Error] 获取重装任务列表失败: %v", err)
			}

			// 显示安装任务表格
			if len(installTasks) > 0 {
				fmt.Printf("\n[Info] 安装任务列表 (共%d个任务):\n", len(installTasks))

				table := tablewriter.NewWriter(os.Stdout)
				table.SetHeader([]string{"序号", "任务名称", "任务状态", "开始时间", "结束时间", "耗时(秒)"})

				// 设置表格样式
				table.SetBorder(true)
				table.SetCenterSeparator("|")
				table.SetColumnSeparator("|")
				table.SetRowSeparator("-")
				table.SetHeaderAlignment(tablewriter.ALIGN_CENTER)
				table.SetAlignment(tablewriter.ALIGN_LEFT)

				for i, task := range installTasks {
					// 计算耗时
					var duration string
					if task.EndTime > 0 && task.StartTime > 0 {
						duration = fmt.Sprintf("%.0f", float64(task.EndTime-task.StartTime))
					} else if task.StartTime > 0 {
						duration = fmt.Sprintf("%.0f", float64(time.Now().Unix()-task.StartTime))
					} else {
						duration = "-"
					}

					// 格式化时间
					startTime := "-"
					if task.StartTime > 0 {
						startTime = time.Unix(task.StartTime, 0).Format("15:04:05")
					}

					endTime := "-"
					if task.EndTime > 0 {
						endTime = time.Unix(task.EndTime, 0).Format("15:04:05")
					}

					// 获取任务显示名称
					taskDisplayName := share.TaskNameMap[share.TaskType(task.TaskName)]
					if taskDisplayName == "" {
						taskDisplayName = string(task.TaskName)
					}

					// 获取状态显示名称
					statusDisplayName := share.TaskStatusMap[task.TaskStatus]
					if statusDisplayName == "" {
						statusDisplayName = string(task.TaskStatus)
					}

					table.Append([]string{
						fmt.Sprintf("%d", i+1),
						taskDisplayName,
						statusDisplayName,
						startTime,
						endTime,
						duration,
					})
				}

				table.Render()
			} else {
				fmt.Println("\n[Info] 暂无安装任务")
			}

		case <-ctx.Done():
			clearScreen()
			fmt.Println("[Done] 监控已停止")
			return
		}
	}
}
