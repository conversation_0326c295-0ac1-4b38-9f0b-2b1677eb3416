package route

import (
	"net/http"

	"ks-knoc-server/internal/common/http/middleware"
	commonMiddleware "ks-knoc-server/internal/common/middleware"
	"ks-knoc-server/internal/common/sso"
	"ks-knoc-server/internal/process/controller/v1/check"
	"ks-knoc-server/internal/process/controller/v1/process"
	"ks-knoc-server/internal/process/store"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm/module/apmgin"
)

// APIServerRouter APIServer路由
func APIServerRouter(g *gin.Engine, db *store.DataStore, mw ...gin.HandlerFunc) *gin.Engine {
	// 应用中间件
	g.Use(apmgin.Middleware(g))
	g.Use(gin.Recovery())
	g.Use(middleware.NoCache)
	g.Use(commonMiddleware.CORSMiddleware)
	g.Use(middleware.Secure)
	g.Use(mw...)
	g.GET("/sso", sso.LoginValidate)

	g.NoRoute(func(c *gin.Context) {
		c.String(http.StatusNotFound, "The incorrect API route, Please contact the Kwai IT Developer.")
	})

	// 定义API相关路由
	api := g.Group("/api")
	api.Use(middleware.GetRequestDuration)
	{
		// 初始化API v1版本的路由
		v1 := api.Group("/v1")
		{
			processController := process.NewProcessController(db)

			// 定义工单相关路由
			processRoute := v1.Group("/process")
			// 创建工单, 只有这一个接口是给提交页调用的，其他的都是在权限中台后面调用的，权限中台自己实现了SSO
			processRoute.POST("/create", sso.LoginValidate, processController.CreateDCProcess)
			// 临时: 创建工单, 不使用SSO，仅用于本地测试
			processRoute.POST("/create_without_sso", processController.CreateDCProcess)
			// 启动工单
			processRoute.POST("/start", processController.StartDCProcess)
			// 驳回工单
			processRoute.POST("/reject", processController.RejectDCProcess)
			// 同意工单
			processRoute.POST("/pass", processController.PassDCProcess)
			// 撤销工单
			processRoute.POST("/cancel", processController.CancelDCProcess)
			// 获取工单列表
			processRoute.GET("/order_list", processController.GetOrderList)
			// 获取工单详情
			processRoute.GET("/order_detail", processController.GetOrderDetail)
			// 获取工单基本信息
			processRoute.GET("/order_base", processController.GetOrderBase)
			// 获取工单审批记录
			processRoute.GET("/order_audit_record", processController.GetOrderAuditRecord)
			// 获取工单审批记录
			processRoute.GET("/order_audit_log", processController.GetOrderAuditLog)
			// 申请人退回流程
			processRoute.POST("/process_back", processController.ProcessBack)
			// 前加签
			processRoute.POST("/front_add_sign", processController.FrontAddSign)
			// 后加签
			processRoute.POST("/after_add_sign", processController.AfterAddSign)
			// 转签
			processRoute.POST("/shift_sign", processController.ShiftSign)
			// 征询
			processRoute.POST("/consult", processController.Consult)
			// 知会
			processRoute.POST("/inform", processController.Inform)
			// 获取工单只读详情，主要用于BPM内嵌前端底座使用
			processRoute.GET("/info", processController.GetOrderInfo)

			// install重装类需求
			installRoute := processRoute.Group("/install")
			// 获取安装信息
			installRoute.GET("/info", processController.GetInstallInfo)
			// 获取安装设备列表
			installRoute.GET("/device/detail", processController.GetInstallDeviceDetail)
			// 安装任务回调
			installRoute.POST("/task_callback", processController.TaskCallback)
			// 获取安装任务信息
			installRoute.GET("/task/:task_id", processController.TaskInfo)
		}
	}

	// 定义服务的健康检查接口
	c := g.Group("/check")
	{
		c.GET("/health", check.HealthCheck)
	}

	return g
}
