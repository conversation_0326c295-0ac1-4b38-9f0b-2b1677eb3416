package audit

import (
	"bytes"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/auth"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w BodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}
func (w BodyLogWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// CmdbAuditLog 审计日志
// 通过注入的方式，将store保存到中间件中供审计中间件记录日志使用
func CmdbAuditLog(factory store.Factory) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 进入中间件
		zap.L().Debug("CmdbAuditLog Middleware Called")
		zap.L().Debug(c.ClientIP())
		zap.L().Debug(c.Request.Method)
		zap.L().Debug(auth.GetUsername(c))
		zap.L().Debug(c.Request.Header.Get("X-REAL-IP"))

		blw := &BodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		c.Next()

		// 返回中间件
		// responseBody := blw.body.Bytes()
		// resp := core.Response{}
		// if len(responseBody) > 0 {
		// 	if err := json.Unmarshal(responseBody, &resp); err != nil {
		// 		zap.L().Error(err.Error())
		// 		return
		// 	}
		// }

		// if resp.Code != 0 {
		// 	zap.L().Debug("请求失败，不记录审计日志")
		// 	return
		// }
		// zap.L().Debug("请求成功，记录审计日志")
		// auditLog := new(v1.AuditLog)
		// fmt.Println(auditLog)

		// if err := factory.Audit().SaveAuditLog(c, nil); err != nil {
		// 	zap.L().Error(err.Error())
		// }

		// zap.L().Debug("记录审计日志")
		// zap.L().Debug(c.Request.Method)
	}
}
