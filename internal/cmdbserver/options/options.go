package options

import (
	"fmt"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/http"
	"ks-knoc-server/internal/common/logger"
	"ks-knoc-server/pkg/json"

	"github.com/spf13/pflag"
)

var cfg = pflag.StringP("config", "c", "config.yaml", "Config file path.")

// Options 初始化依赖的选项
type Options struct {
	WebOptions   *http.WebOptions    `json:"http"`
	MySQLOptions *db.MySQLOptions    `json:"mysql"`
	LogsOptions  *logger.LogOptions  `json:"log"`
	RedisOptions *db.RedisOptions    `json:"redis"`
	MongoOptions *db.MongoOptions    `json:"mongo"`
	ES           *db.ESOptions       `json:"es"`
	Cache        *cache.CacheOptions `json:"cache"`
}

// NewOptions 配置文件以及依赖项初始化
func NewOptions() *Options {
	// 初始化配置文件
	pflag.Parse()
	fmt.Println("[Debug] config file path:", *cfg)
	if err := config.NewConf(*cfg); err != nil {
		panic(err)
	}

	o := Options{
		WebOptions:   http.NewWebOptions(),
		MySQLOptions: db.NewMySQLOptions(),
		LogsOptions:  logger.NewLogOptions(),
		RedisOptions: db.NewRedisOptions(),
		MongoOptions: db.NewMongoOptions(),
		ES:           db.NewESOptions(),
		Cache:        cache.NewCacheOptions(),
	}
	return &o
}

func (o *Options) String() string {
	data, _ := json.Marshal(o)
	return string(data)
}
