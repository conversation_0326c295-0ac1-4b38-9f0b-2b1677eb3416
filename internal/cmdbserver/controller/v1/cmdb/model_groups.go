package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	"ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/mitchellh/mapstructure"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// GetModelGroupList 获取模型分组列表
func (cm *CmdbController) GetModelGroupList(c *gin.Context) {
	zap.L().Debug("GetModelGroupList Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelGroupList", "GET")
	defer span.End()

	// 调用Service层的逻辑处理请求
	modelGroupList, err := cm.svc.ModelGroup().GetModelGroupList(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelGroupList)
}

// CreateModelGroup 创建模型分组
func (cm *CmdbController) CreateModelGroup(c *gin.Context) {
	zap.L().Debug("CreateModelGroup Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateModelGroup", "POST")
	defer span.End()

	var mg v1.ModelGroup
	if err := c.ShouldBindJSON(&mg); err != nil {
		zap.L().Error(errno.GetErrMsg(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	// 处理实际的创建模型分组的逻辑
	if err := mg.Validate(); err != nil {
		core.SendResponse(c, err, nil)
		return
	}
	modelGroupInstance, err := cm.svc.ModelGroup().CreateModelGroup(ctx, auth.GetUsername(c), &mg)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, modelGroupInstance)
}

// UpdateModelGroup 更新模型分组
func (cm *CmdbController) UpdateModelGroup(c *gin.Context) {
	zap.L().Debug("UpdateModelGroup Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateModelGroup", "PUT")
	defer span.End()

	var mg map[string]interface{}
	if err := c.Bind(&mg); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}
	m := v1.ModelGroup{}
	err := mapstructure.Decode(mg, &m)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	if err = binding.Validator.ValidateStruct(m); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err = cm.svc.ModelGroup().UpdateModelGroup(ctx, auth.GetUsername(c), mg)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}

// DeleteModelGroup 删除模型分组
func (cm *CmdbController) DeleteModelGroup(c *gin.Context) {
	zap.L().Debug("DeleteModelGroup Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelGroup", "DELETE")
	defer span.End()

	var mg v1.InstanceFilter
	if err := c.ShouldBindJSON(&mg); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err := cm.svc.ModelGroup().DeleteModelGroup(ctx, auth.GetUsername(c), &mg)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}
