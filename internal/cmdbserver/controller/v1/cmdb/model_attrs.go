package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"
)

// GetModelAttributeList 获取模型属性列表
func (cm *CmdbController) GetModelAttributeList(c *gin.Context) {
	zap.L().Debug("GetModelAttributeListController Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeList", "GET")
	defer span.End()

	// 1. 获取参数
	modelCode := c.Query("code")
	if modelCode == "" {
		core.SendResponse(c, errno.ErrParameterRequired, nil)
		return
	}
	nested := c.Query("nested")
	if nested == "true" {
		modelAttributeList, err := cm.svc.ModelAttr().GetNestedAttributeList(ctx, modelCode)
		if err != nil {
			zap.L().Error(err.Error())
			e := apm.CaptureError(ctx, err)
			e.Send()
			core.SendResponse(c, err, nil)
			return
		}
		if len(modelAttributeList) == 0 {
			core.SendResponse(c, errno.ErrDataNotExists.Add("模型属性数据为空"), nil)
			return
		}
		core.SendResponse(c, nil, modelAttributeList)
		return
	}
	// 2. 调用Service进行数据查询
	modelAttrList, err := cm.svc.ModelAttr().GetModelAttributeList(ctx, modelCode, bson.M{})
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	if len(modelAttrList) == 0 {
		core.SendResponse(c, errno.ErrDataNotExists.Add("模型属性数据为空"), nil)
		return
	}
	core.SendResponse(c, nil, modelAttrList)
}

// CreateModelAttribute 创建模型属性
func (cm *CmdbController) CreateModelAttribute(c *gin.Context) {
	zap.L().Debug("CreateModelAttributeController Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateModelAttributeController", "POST")
	defer span.End()

	// 1. 获取模型属性的参数，由于不同模型对应的参数不一样，因此，结构体也不一样，所以有两种实现方式
	// 第一种就是使用interface来接收，然后序列化再做判断。注意这里是不能使用断言的，断言的话，你会发现它其实是一个map类型
	// 第二种我就是直接干脆使用一个map来接收，然后再做序列化
	var attr map[string]any
	gin.EnableJsonDecoderUseNumber()
	if err := c.ShouldBindJSON(&attr); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	// 首先检测一下是否传递了type_name参数，如果没有传递直接返回报错即可
	attrTypeName := utils.ToString(attr["type_name"])
	if attrTypeName == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("type_name为必填字段, 且不能为空"), nil)
		return
	}

	// 首先得看看这个属性的类型，然后做一下字段的校验
	// 这里需要注意的是，如果你使用map来接收用户数据的话，那么在调用Bind方法的时候。
	// 设置的那些校验规则是无法生效的，因为它是一个map并不存在我们在结构体中定义的规则
	if err := v1.ModelAttrValidate(attrTypeName, attr); err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	// 2. 调用Service进行数据处理
	modelAttr, err := cm.svc.ModelAttr().CreateModelAttribute(ctx, auth.GetUsername(c), attr)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelAttr)
}

// DeleteModelAttribute 删除模型属性
func (cm *CmdbController) DeleteModelAttribute(c *gin.Context) {
	zap.L().Debug("DeleteModelAttributeController Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelAttribute", "DELETE")
	defer span.End()

	// 1. 获取参数
	var attrCode v1.InstanceFilter
	if err := c.ShouldBindJSON(&attrCode); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	// 2. 调用Service进行数据处理
	err := cm.svc.ModelAttr().DeleteModelAttribute(ctx, auth.GetUsername(c), &attrCode)
	if err != nil {
		zap.L().Error(errno.GetErrMsg(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}

// UpdateModelAttribute 更新模型属性
func (cm *CmdbController) UpdateModelAttribute(c *gin.Context) {
	zap.L().Debug("UpdateModelAttributeController Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateModelAttributeController", "PUT")
	defer span.End()

	// 这里与创建模型属性其实是类似的，由于不确定用户更新的属性类型，因此也需要用一个mapInterface来接收
	var attr map[string]any
	gin.EnableJsonDecoderUseNumber()
	if err := c.ShouldBindJSON(&attr); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}
	err := apiv1.ModelAttrValidate(attr)
	if err != nil {
		core.SendResponse(c, errno.ErrParameterInvalid.Add(errno.GetErrMsg(err)), nil)
		return
	}
	// 2. 调用Service进行数据处理
	err = cm.svc.ModelAttr().UpdateModelAttribute(ctx, auth.GetUsername(c), attr)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, attr)
		return
	}
	core.SendResponse(c, nil, nil)
}

// GetModelAttrRules 获取模型属性正则规则
func (cm *CmdbController) GetModelAttrRules(c *gin.Context) {
	zap.L().Debug("GetModelAttrRulesController Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelAttrRules", "GET")
	defer span.End()

	modelRules, err := cm.svc.ModelAttr().GetModelRules(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelRules)
}
