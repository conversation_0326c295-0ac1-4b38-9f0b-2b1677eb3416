package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// CreateModelDataSubOrdinateRelation 创建模型数据从属关系
func (cm *CmdbController) CreateModelDataSubOrdinateRelation(c *gin.Context) {
	zap.L().Debug("CreateModelDataSubOrdinateRelation Controller Called")
	span, ctx := apm.StartSpan(c, "CreateModelDataSubOrdinateRelationController", "POST")
	defer span.End()

	var dsr v1.DataRelRequest
	if err := c.ShouldBindJSON(&dsr); err != nil {
		zap.L().Error("CreateModelDataSubOrdinateRelation Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.ModelData().CreateModelDataSubOrdinateRelation(ctx, auth.GetUsername(c), &dsr); err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// CreateModelDataSubOrdinateRelationMove 转移至功能
func (cm *CmdbController) CreateModelDataSubOrdinateRelationMove(c *gin.Context) {
	zap.L().Debug("CreateModelDataSubOrdinateRelationMove Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateModelDataSubOrdinateRelationMoveController", "POST")
	defer span.End()

	var dsr v1.DataNTo1Request
	if err := c.ShouldBindJSON(&dsr); err != nil {
		zap.L().Error("CreateModelDataSubOrdinateRelationMove Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	resp, err := cm.svc.ModelData().CreateModelDataSubOrdinateRelationMove(ctx, auth.GetUsername(c), &dsr)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

// DeleteModelDataSubOrdinateRelation 删除模型数据从属关系
func (cm *CmdbController) DeleteModelDataSubOrdinateRelation(c *gin.Context) {
	zap.L().Debug("DeleteModelDataSubOrdinateRelation Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelDataSubOrdinateRelation", "DELETE")
	defer span.End()

	var IDFilter v1.IDFilter
	if err := c.ShouldBindJSON(&IDFilter); err != nil {
		zap.L().Error("DeleteModelDataSubOrdinateRelation Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.ModelData().DeleteModelDataSubOrdinateRelation(ctx, auth.GetUsername(c), &IDFilter); err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// CreateModelDataAssociateRelation 创建模型数据关联关系
func (cm *CmdbController) CreateModelDataAssociateRelation(c *gin.Context) {
	zap.L().Debug("CreateModelDataAssociateRelation Controller called")
	span, ctx := apm.StartSpan(c, "CreateModelDataAssociateRelationController", "POST")
	defer span.End()

	var data v1.DataRelRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	if err := cm.svc.ModelData().CreateModelDataAssociateRelation(ctx, auth.GetUsername(c), &data); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}

// DeleteModelDataAssociateRelation 删除模型数据关联关系
func (cm *CmdbController) DeleteModelDataAssociateRelation(c *gin.Context) {
	zap.L().Debug("DeleteModelDataAssociateRelation Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelDataAssociateRelationController", "DELETE")
	defer span.End()

	var data v1.DataRelRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err := cm.svc.ModelData().DeleteModelDataAssociateRelation(ctx, auth.GetUsername(c), &data)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// GetInstancesByRelCode 根据关系ID获取已经建立关系的实例
func (cm *CmdbController) GetInstancesByRelCode(c *gin.Context) {
	zap.L().Debug("GetInstancesByRelCode Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetInstancesByRelCodeController", "GET")
	defer span.End()

	relAttrID := c.Query("rel_attr_id")
	dataID := c.Query("data_id")

	if relAttrID == "" || dataID == "" {
		core.SendResponse(c, errno.ErrBind.Add("请传入关系属性ID和数据ID"), nil)
		return
	}

	// 初始化分页参数
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := cm.svc.ModelData().GetInstancesByRelCode(ctx, relAttrID, dataID, auth.GetUsername(c), page, pageSize)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetInstancesByRelCodeV2(c *gin.Context) {
	zap.L().Debug("GetInstancesByRelCodeV2 Controller called")
	span, ctx := apm.StartSpan(c, "GetInstancesByRelCodeV2Controller", "GET")
	defer span.End()

	relAttrID := c.Query("rel_attr_id")
	dataID := c.Query("data_id")

	if dataID == "" {
		core.SendResponse(c, errno.ErrBind.Add("请传入数据ID"), nil)
		return
	}

	// 初始化分页参数
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := cm.svc.ModelData().GetInstancesByRelCodeV2(ctx, relAttrID, dataID, auth.GetUsername(c), page, pageSize)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

// GetRelInstances 获取关系实例，根据关系ID查看，获取有哪些实例可以建立关系
func (cm *CmdbController) GetRelInstances(c *gin.Context) {
	zap.L().Debug("GetRelInstances Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetRelInstancesController", "GET")
	defer span.End()

	relAttrID := c.Query("rel_attr_id")
	dataID := c.Query("data_id")
	keyword := c.Query("keyword")
	field := c.Query("field")

	if relAttrID == "" {
		core.SendResponse(c, errno.ErrBind.Add("请传入关系属性ID"), nil)
		return
	}

	if dataID == "" {
		core.SendResponse(c, errno.ErrBind.Add("请传入数据ID"), nil)
		return
	}

	// 初始化分页参数
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := cm.svc.ModelData().GetRelInstances(ctx, relAttrID, dataID, keyword, field, page, pageSize)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetMoveInstances(c *gin.Context) {
	zap.L().Debug("GetMoveInstances Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetMoveInstancesController", "GET")
	defer span.End()

	var ari v1.AvailableRelInstances
	if err := c.ShouldBindJSON(&ari); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	// 初始化分页参数
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := cm.svc.ModelData().GetMoveInstances(ctx, ari, page, pageSize)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) UpdateModelDataRelation(c *gin.Context) {
	zap.L().Debug("UpdateModelDataRelation Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateModelDataRelationController", "PUT")
	defer span.End()

	var data v1.Data1To1Request
	if err := c.ShouldBindJSON(&data); err != nil {
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	err := cm.svc.ModelData().UpdateModelDataRelation(ctx, auth.GetUsername(c), &data)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
