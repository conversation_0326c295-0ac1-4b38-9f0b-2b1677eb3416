package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"
	"strings"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) OnRack(c *gin.Context) {
	zap.L().Debug("OnRack Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "OnRackController", "GET")
	defer span.End()

	gin.EnableJsonDecoderUseNumber()
	var onRackRequest v1.OnRackRequest
	if err := c.ShouldBindJSON(&onRackRequest); err != nil {
		zap.L().<PERSON>rror("OnRack Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err := cm.svc.Host().OnRack(ctx, auth.GetUsername(c), &onRackRequest)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) OffRack(c *gin.Context) {
	zap.L().Debug("OffRack Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "OffRackController", "GET")
	defer span.End()

	gin.EnableJsonDecoderUseNumber()
	var offRackRequest v1.OffRackRequest
	if err := c.ShouldBindJSON(&offRackRequest); err != nil {
		zap.L().Error("OffRack Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err := cm.svc.Host().OffRack(ctx, auth.GetUsername(c), &offRackRequest)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// AdjustUID
// TODO：调整UID，这个后续开发
func (cm *CmdbController) AdjustUID(c *gin.Context) {
	zap.L().Debug("AdjustUID Controller Called")
	ctx := c.Request.Context()
	span, _ := apm.StartSpan(ctx, "AdjustUIDController", "GET")
	defer span.End()

	core.SendResponse(c, nil, nil)
}

// RackSpare 检测可用的机柜位置
func (cm *CmdbController) RackSpare(c *gin.Context) {
	zap.L().Debug("RackSpare Controller Called")
	span, ctx := apm.StartSpan(c, "RackSpare", "controller")
	defer span.End()

	rackID := c.Query("rack_id")
	deviceHeight := c.Query("device_height")

	if rackID == "" {
		zap.L().Error("rack_id 不可以为空")
		core.SendResponse(c, errno.ErrParameterInvalid.Add("rack_id 不可以为空"), nil)
		return
	}

	// id的格式为 {rack_id}_{rack_name}
	rackIDSections := strings.Split(rackID, "_")
	if len(rackIDSections) != 2 {
		zap.L().Error("rack_id 格式不正确")
		core.SendResponse(c, errno.ErrParameterInvalid.Add("rack_id 格式不正确"), nil)
		return
	}
	rid := rackIDSections[0]

	height := utils.ToInt(deviceHeight)
	if height == 0 || height > 42 {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("device_height不合法"), nil)
		return
	}

	resp, err := cm.svc.Host().RackSpare(ctx, rid, height)
	if err != nil {
		zap.L().Error(errno.GetErrMsg(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}
