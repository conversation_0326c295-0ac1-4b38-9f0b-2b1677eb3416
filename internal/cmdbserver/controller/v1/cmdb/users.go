package cmdb

import (
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) GetUsersSearch(c *gin.Context) {
	zap.L().Debug("GetUsersSearch Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetUsersSearch", "GET")
	defer span.End()

	username := c.Query("username")
	if len(username) == 0 {
		core.SendResponse(c, errno.ErrParameterRequired.Add("username"), nil)
		return
	}

	res, err := cm.svc.Users().GetUsersSearch(ctx, username)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, res.Data)
}
