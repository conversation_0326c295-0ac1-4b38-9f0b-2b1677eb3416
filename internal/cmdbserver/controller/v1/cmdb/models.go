package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// CreateModel 创建模型
func (cm *CmdbController) CreateModel(c *gin.Context) {
	zap.L().Debug("CreateModelController Called")
	span, ctx := apm.StartSpan(c, "CreateModelController", "POST")
	defer span.End()

	var m v1.Model
	if err := c.ShouldBindJSON(&m); err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	if err := m.Validate(); err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	username := auth.GetUsername(c)
	newModel, err := cm.svc.Model().CreateModel(ctx, username, &m)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, newModel)
}

// GetModelList 获取模型列表，暂时没做分页，后面模型类型多了以后可以补充上
func (cm *CmdbController) GetModelList(c *gin.Context) {
	zap.L().Debug("GetModelListController Called")
	span, ctx := apm.StartSpan(c, "GetModelList", "GET")
	defer span.End()

	// 查看用户是否穿提了nested参数，如果传递了，则返回嵌套的结果，如果没有传递则返回模型列表即可
	nested := c.Query("nested")
	if nested == "true" {
		modelList, err := cm.svc.Model().GetNestedModelList(ctx)
		if err != nil {
			zap.L().Error(err.Error())
			e := apm.CaptureError(ctx, err)
			e.Send()
			core.SendResponse(c, errno.InternalServerError.Add(errno.GetErrMsg(err)), nil)
			return
		}
		core.SendResponse(c, nil, modelList)
	} else {
		modelList, err := cm.svc.Model().GetModelList(ctx)
		if err != nil {
			zap.L().Error(err.Error())
			e := apm.CaptureError(ctx, err)
			e.Send()
			core.SendResponse(c, err, nil)
			return
		}
		core.SendResponse(c, nil, modelList)
	}
}

func (cm *CmdbController) GetModelListPaginated(c *gin.Context) {
	zap.L().Debug("GetModelListPaginatedController Called")
	span, ctx := apm.StartSpan(c, "GetModelListPaginated", "GET")
	defer span.End()

	// 查看用户是否穿提了nested参数，如果传递了，则返回嵌套的结果，如果没有传递则返回模型列表即可
	nested := c.Query("nested")
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}
	if nested == "true" {
		modelList, err := cm.svc.Model().GetNestedModelListWithPagination(ctx, page, pageSize)
		if err != nil {
			zap.L().Error(err.Error())
			e := apm.CaptureError(ctx, err)
			e.Send()
			core.SendResponse(c, errno.InternalServerError.Add(errno.GetErrMsg(err)), nil)
			return
		}
		core.SendResponse(c, nil, modelList)
	} else {
		modelList, err := cm.svc.Model().GetModelListWithPagination(ctx, page, pageSize)
		if err != nil {
			zap.L().Error(err.Error())
			e := apm.CaptureError(ctx, err)
			e.Send()
			core.SendResponse(c, err, nil)
			return
		}
		core.SendResponse(c, nil, modelList)
	}
}

// GetModelByCode 根据模型的code获取模型的信息
func (cm *CmdbController) GetModelByCode(c *gin.Context) {
	zap.L().Debug("GetModelByCodeController Called")
	span, ctx := apm.StartSpan(c, "GetModelByCode", "GET")
	defer span.End()

	modelCode := c.Query("code")
	if modelCode == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("需要传入code参数"), nil)
		return
	}
	model, err := cm.svc.Model().GetModelByCode(ctx, modelCode)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, model)
}

// DeleteModel 删除模型
func (cm *CmdbController) DeleteModel(c *gin.Context) {
	zap.L().Debug("DeleteModelController Called")
	span, ctx := apm.StartSpan(c, "DeleteModel", "DELETE")
	defer span.End()

	var m *v1.InstanceFilter
	if err := c.ShouldBindJSON(&m); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err := cm.svc.Model().DeleteModel(ctx, auth.GetUsername(c), m)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}

// GetIcons 获取图标列表
func (cm *CmdbController) GetIcons(c *gin.Context) {
	zap.L().Debug("GetIconsController Called")
	span, ctx := apm.StartSpan(c, "GetIcons", "GET")
	defer span.End()

	icons, err := cm.svc.Model().GetIcons(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, icons)
}
