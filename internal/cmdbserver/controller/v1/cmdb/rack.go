package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) GetPhysicalRackDataView(c *gin.Context) {
	zap.L().Debug("GetPhysicalRackDataView Controller Called")
	span, ctx := apm.StartSpan(c, "GetPhysicalRackDataViewController", "GET")
	defer span.End()

	dataID := c.Query("data_id")
	if dataID == "" {
		zap.L().Error("要查询的机柜ID不能为空")
		core.SendResponse(c, errno.ErrParameterRequired.Add("要查询的机柜ID不能为空"), nil)
		return
	}
	resp, err := cm.svc.View().RackView(ctx, dataID)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()

		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetPhysicalIDCDataView(c *gin.Context) {
	zap.L().Debug("GetPhysicalIDCDataView Controller Called")
	ctx := c.Request.Context()
	span, _ := apm.StartSpan(ctx, "GetPhysicalIDCDataViewController", "GET")
	defer span.End()
}

func (cm *CmdbController) GetPhysicalRackDataViewInstance(c *gin.Context) {
	zap.L().Debug("GetPhysicalRackDataViewInstance Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetPhysicalRackDataViewInstanceController", "GET")
	defer span.End()

	dataID := c.Query("data_id")
	if dataID == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("要查询的机柜ID不能为空"), nil)
		return
	}

	page, pageSize, err := CheckPagination(c)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := cm.svc.View().RackViewInstance(ctx, dataID, page, pageSize)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()

		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) Placeholder(c *gin.Context) {
	zap.L().Debug("Placeholder Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "PlaceholderController", "POST")
	defer span.End()

	ph := apiv1.NewPlaceHolder()
	if err := c.ShouldBindJSON(ph); err != nil {
		errMsg := errno.GetErrMsg(err)
		zap.L().Error(errMsg)
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errMsg), nil)
		return
	}

	if !ph.TypeValid() {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("p_type类型错误"), nil)
		return
	}

	if err := cm.svc.Host().OnPlaceHolder(ctx, auth.GetUsername(c), ph); err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) RemovePlaceholder(c *gin.Context) {
	zap.L().Debug("RemovePlaceholder Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "RemovePlaceholderController", "DELETE")
	defer span.End()

	ph := apiv1.NewPlaceHolder()
	if err := c.ShouldBindJSON(ph); err != nil {
		errMsg := errno.GetErrMsg(err)
		zap.L().Error(errMsg)
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errMsg), nil)
		return
	}

	if !ph.TypeValid() {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("p_type类型错误"), nil)
		return
	}

	if err := cm.svc.Host().OffPlaceHolder(ctx, auth.GetUsername(c), ph); err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
