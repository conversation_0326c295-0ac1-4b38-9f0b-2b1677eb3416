package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// CreateModelSubOrdinateRelation 创建模型从属关系
func (cm *CmdbController) CreateModelSubOrdinateRelation(c *gin.Context) {
	zap.L().Info("CreateModelSubOrdinateRelation Controller Called")
	span, ctx := apm.StartSpan(c, "CreateModelSubOrdinateRelationController", "POST")
	defer span.End()

	mr := v1.NewModelSubordinateRelationRequest()
	if err := c.ShouldBindJSON(&mr); err != nil {
		zap.L().Error("CreateModelSubOrdinateRelation Controller", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	// 执行service
	data, err := cm.svc.ModelRelation().CreateModelSubOrdinateRelation(ctx, auth.GetUsername(c), mr)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	// 返回默认的数据
	core.SendResponse(c, nil, data)
}

// DeleteModelSubOrdinateRelation 删除模型从属关系
func (cm *CmdbController) DeleteModelSubOrdinateRelation(c *gin.Context) {
	zap.L().Debug("DeleteModelSubOrdinateRelation Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelSubOrdinateRelation", "DELETE")
	defer span.End()

	var instanceFilter v1.InstanceFilter
	if err := c.ShouldBindJSON(&instanceFilter); err != nil {
		zap.L().Error("DeleteModelSubOrdinateRelation Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	// 执行service
	err := cm.svc.ModelRelation().DeleteModelSubOrdinateRelation(ctx, auth.GetUsername(c), &instanceFilter)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	// 返回默认的数据
	core.SendResponse(c, nil, nil)
}

// CreateModelAssociateRelation 创建模型关联关系
func (cm *CmdbController) CreateModelAssociateRelation(c *gin.Context) {
	zap.L().Debug("CreateModelAssociateRelation Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateModelAssociateRelationController", "controller")
	defer span.End()

	var mar v1.ModelAssociateRelationRequest
	if err := c.ShouldBindJSON(&mar); err != nil {
		zap.L().Error("CreateModelAssociateRelation Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := mar.Validate(); err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	data, err := cm.svc.ModelRelation().CreateModelAssociateRelation(ctx, auth.GetUsername(c), &mar)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, data)
}

// DeleteModelAssociateRelation 删除模型关联关系
func (cm *CmdbController) DeleteModelAssociateRelation(c *gin.Context) {
	zap.L().Debug("DeleteModelAssociateRelation Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelAssociateRelation", "DELETE")
	defer span.End()

	var idFilter v1.IDFilter
	if err := c.ShouldBindJSON(&idFilter); err != nil {
		zap.L().Error("DeleteModelAssociateRelation Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	// 调用svc层的逻辑
	err := cm.svc.ModelRelation().DeleteModelAssociateRelation(ctx, auth.GetUsername(c), &idFilter)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// GetModelRelations 获取模型的关系，包含从属关系和关联关系
func (cm *CmdbController) GetModelRelations(c *gin.Context) {
	zap.L().Debug("GetModelRelations Controller called")
	span, ctx := apm.StartSpan(c, "GetModelRelationsController", "GET")
	defer span.End()

	// 获取模型的code参数
	modelCode := c.Query("code")
	if modelCode == "" {
		zap.L().Error("请传递模型的code参数")
		core.SendResponse(c, errno.ErrParameterRequired.Add("请传递模型的code参数"), nil)
		return
	}

	data, err := cm.svc.ModelRelation().GetModelRelations(ctx, modelCode)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, data)
}

// GetModelRelationTopo 获取模型关系的拓扑图
func (cm *CmdbController) GetModelRelationTopo(c *gin.Context) {
	zap.L().Debug("GetModelRelationTopo Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelRelationTopoController", "GET")
	defer span.End()

	resp, err := cm.svc.ModelRelation().GetModelRelationTopo(ctx)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) UpdateModelRelation(c *gin.Context) {
	zap.L().Debug("UpdateModelRelation Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateModelRelationController", "PUT")
	defer span.End()

	var req v1.UpdateModelRelationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UpdateModelRelation Controller", zap.Error(err))
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	err := cm.svc.ModelRelation().UpdateModelRelation(ctx, auth.GetUsername(c), &req)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
