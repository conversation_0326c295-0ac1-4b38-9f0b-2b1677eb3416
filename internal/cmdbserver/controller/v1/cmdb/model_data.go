package cmdb

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"ks-knoc-server/internal/common/auth"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/mapdata"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// DownloadData 下载数据，该接口主要用于下载数据Excel
func (cm *CmdbController) DownloadData(c *gin.Context) {
	zap.L().Debug("DownloadModelData called")
	span, _ := apm.StartSpan(c, "DownloadTemplate", "POST")
	defer span.End()

	var reqData apiv1.DownloadModelReq
	if err := c.ShouldBindJSON(&reqData); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	file, err := cm.svc.ModelTemplate().GetModelDataExcel(c, reqData.IDS, reqData.ExcludeIDs, reqData.ModelCode, reqData.Mode)
	if err != nil {
		if errors.Is(err, errno.ErrModelIndexParseError) {
			core.SendResponse(c, errno.ErrModelDataDownloadError.Add(errno.ErrModelIndexParseError.Message), nil)
		} else if errors.Is(err, errno.ErrModelNotFound) {
			core.SendResponse(c, errno.ErrModelDataDownloadError.Add(errno.ErrModelNotFound.Message), nil)
		} else if errors.Is(err, errno.ErrDataNotExists) {
			core.SendResponse(c, errno.ErrModelNotFound.Addf("无法查询到模型 '%s", reqData.ModelCode), nil)
		} else {
			core.SendResponse(c, errno.ErrModelDataDownloadError.Add(err.Error()), nil)
		}
		return
	}
	fileName := fmt.Sprintf("%s.xlsx", reqData.ModelCode)
	core.WriteExcel(c, fileName, file)
}

// DownloadTemplate 下载模板，该接口主要用于下载数据模板，用于批量导入数据使用
func (cm *CmdbController) DownloadTemplate(c *gin.Context) {
	zap.L().Debug("DownloadTemplate Controller called")
	span, _ := apm.StartSpan(c, "DownloadTemplate", "GET")
	defer span.End()
	modelCode, ok := c.GetQuery("code")
	if !ok || modelCode == "" {
		core.SendResponse(c, errno.ErrModelCodeQuery.Add("code为必填字段"), nil)
		return
	}
	file, err := cm.svc.ModelTemplate().GetModelTemplateExcel(c, modelCode)
	if err != nil {
		errMsg := errno.GetErrMsg(err)
		zap.L().Error(errMsg)
		core.SendResponse(c, errno.ErrExcelTemplateFile.Add(errMsg), nil)
		return
	}
	fileName := fmt.Sprintf("%s.xlsx", modelCode)
	core.WriteExcel(c, fileName, file)
}

// SearchModelData 搜索模型数据
func (cm *CmdbController) SearchModelData(c *gin.Context) {}

// checkSort 默认排序为desc，如果用户传入了以用户的为准
func checkSort(sort string) string {
	switch sort {
	case "":
		return "desc"
	case "asc":
		return "asc"
	case "desc":
		return "desc"
	default:
		return "desc"
	}
}

// GetModelDataList 获取模型数据列表
func (cm *CmdbController) GetModelDataList(c *gin.Context) {
	zap.L().Debug("GetModelDataListController called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelDataListController", "GET")
	defer span.End()

	// 获取要查询的模型
	code := c.Query("code")
	filterKey := c.Query("field")
	multi := c.Query("multi")
	sorting := c.Query("sort")

	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	sorting = checkSort(sorting)

	// 调用Service层的逻辑处理请求
	filter := bson.M{}
	if code != "" {
		filter["model_code"] = code
	}

	// 只有当filterKey和keyword都不为空的时候才会添加过滤条件
	if filterKey != "" {
		if multi == "true" {
			keyword := c.QueryArray("keyword")
			if len(keyword) != 0 {
				regexStr := make([]bson.M, 0)
				for _, k := range keyword {
					trimmedK := strings.TrimSpace(k)
					// 避免前端传递过来一个空行
					if trimmedK != "" {
						regexStr = append(regexStr, bson.M{
							"data." + filterKey: bson.M{
								"$regex":   ".*" + trimmedK + ".*",
								"$options": "i",
							},
						})
					}
				}

				filter["$or"] = regexStr
			}
		} else {
			keyword := c.Query("keyword")
			if keyword != "" {
				filter["data."+filterKey] = bson.M{
					"$regex":   ".*" + keyword + ".*",
					"$options": "i", // 匹配包含关键字的字符串，不区分大小写
				}
			}
		}
	}

	modelDataList, err := cm.svc.ModelData().GetModelDataList(ctx, filter, page, pageSize, sorting)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, modelDataList)
}

// GetModelDataListByID 根据id获取模型数据列表
func (cm *CmdbController) GetModelDataListByID(c *gin.Context) {
	zap.L().Debug("GetModelDataListByIDController called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelDataListByIDController", "GET")
	defer span.End()

	// 获取要查询的id列表
	ids := apiv1.IDsFilter{}
	if err := c.ShouldBindJSON(&ids); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	// 调用Service层的逻辑处理请求
	modelDataList, err := cm.svc.ModelData().GetModelDataByID(ctx, ids)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelDataList)
}

// CreateModelData 创建模型数据
func (cm *CmdbController) CreateModelData(c *gin.Context) {
	zap.L().Debug("CreateModelDataController called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateModelData", "POST")
	defer span.End()

	var data v1.ModelData
	if err := c.ShouldBindJSON(&data); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	// 调用Service层的逻辑处理请求
	zap.L().Debug("ModelData", zap.Any("data", data.GetDataInfo(true)))
	// 将数据都转换为字符串类型
	data.ToString()
	modelData, err := cm.svc.ModelData().CreateModelData(ctx, auth.GetUsername(c), &data)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, data)
		return
	}
	core.SendResponse(c, nil, modelData)
}

// UpdateModelData 更新模型数据
func (cm *CmdbController) UpdateModelData(c *gin.Context) {
	zap.L().Debug("UpdateModelDataController called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateModelData", "PUT")
	defer span.End()

	var data v1.ModelData
	if err := c.ShouldBindJSON(&data); err != nil {
		errMsg := errno.GetErrMsg(err)
		zap.L().Error(errMsg)
		core.SendResponse(c, errno.ErrBind.Add(errMsg), nil)
		return
	}
	// 处理用户提交上来的数据
	data.ToString()
	uData, err := cm.svc.ModelData().UpdateModelData(ctx, auth.GetUsername(c), &data)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, data)
		return
	}
	core.SendResponse(c, nil, uData)
}

// DeleteModelData 删除模型数据
func (cm *CmdbController) DeleteModelData(c *gin.Context) {
	zap.L().Debug("DeleteModelData Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelData", "DELETE")
	defer span.End()

	batch := c.Query("batch")
	if batch != "" {
		// TODO: 批量删除
		zap.L().Debug("批量删除, 待开发功能")
	}
	// 获取要删除的模型数据的ID
	var filter v1.IDFilter
	if err := c.ShouldBindJSON(&filter); err != nil {
		errMsg := errno.GetErrMsg(err)
		zap.L().Error(errMsg)
		core.SendResponse(c, errno.ErrBind.Add(errMsg), nil)
		return
	}

	if filter.ID == "" {
		core.SendResponse(c, errno.ErrBind.Add("ID不能为空"), nil)
		return
	}

	// 调用Service层的逻辑处理请求
	deletedData, err := cm.svc.ModelData().DeleteModelData(ctx, auth.GetUsername(c), &filter)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, deletedData)
		return
	}
	core.SendResponse(c, nil, deletedData)
}

// ImportData 导入数据
func (cm *CmdbController) ImportData(c *gin.Context) {
	zap.L().Debug("ImportData Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "ImportData", "POST")
	defer span.End()

	var data v1.ModelData
	if err := c.ShouldBindJSON(&data); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), data)
		return
	}
	// 调用Service层的逻辑处理请求
	data.ToString()
	importedData, err := cm.svc.ModelData().ImportData(ctx, auth.GetUsername(c), &data)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()

		// fix: 导入数据失败的时候将提交的data的唯一标识赋值给id字段，用于辅助前端做数据导入和统计使用
		dataCode := fmt.Sprintf("%s_code", data.ModelCode)
		if dataCode != "" {
			data.ID = data.Data[dataCode].(string)
		}

		core.SendResponse(c, err, data)
		return
	}
	core.SendResponse(c, nil, importedData)
}

// FuzzySearchModelData 模糊搜索模型数据
func (cm *CmdbController) FuzzySearchModelData(c *gin.Context) {
	zap.L().Debug("FuzzySearchModelData Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "FuzzySearchModelData", "GET")
	defer span.End()

	// TODO: 获取查询条件, 一期先根据keyword实现模糊查询，后续根据需求再扩展
	req := apiv1.NewFuzzySearchRequest()
	if err := c.ShouldBindJSON(req); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	// 修正数据格式
	req.Format()

	pageNumber, pageSizeNumber, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}
	// 如果keyword不传递的话，不直接返回报错，而是返回空数据
	if req.Keyword == "" {
		core.SendResponse(c, nil, nil)
		return
	}

	zap.L().Debug("User Post Keyword", zap.String("keyword", req.Keyword))

	// 调用Service层的逻辑处理请求
	response, err := cm.svc.ModelData().FuzzySearchModelData(ctx, req, pageNumber, pageSizeNumber)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	filter := mapdata.NewMapData()
	filter.Set("keyword", req.Keyword)
	if len(req.ModelCode) > 0 {
		filter.Set("model_code", req.ModelCode)
	} else {
		filter.Set("model_code", make([]string, 0))
	}
	filter.Set("model_group_code", req.ModelGroupCode)

	response.SearchFilter = filter

	core.SendResponse(c, nil, response)
}

// FuzzySearchSuggestedModelData 模糊搜索建议的模型数据
func (cm *CmdbController) FuzzySearchSuggestedModelData(c *gin.Context) {
	zap.L().Debug("FuzzySearchSuggestedModelData Controller called")
	ctx := c.Request.Context()
	span, _ := apm.StartSpan(ctx, "FuzzySearchSuggestedModelData", "GET")
	defer span.End()

	// TODO
}

// GetModelDataByFilter 根据特定条件获取模型数据
func (cm *CmdbController) GetModelDataByFilter(c *gin.Context) {
	zap.L().Debug("GetModelDataByFilter Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelDataByFilter", "GET")
	defer span.End()

	var filter map[string]interface{}
	if err := c.ShouldBindJSON(&filter); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	if len(filter) == 0 {
		core.SendResponse(c, errno.ErrBind.Add("filter不能为空"), nil)
		return
	}

	// 将filter转换为bson.M类型
	searchFilter := bson.M{}
	for k, v := range filter {
		searchFilter[k] = v
	}

	modelDataList, err := cm.svc.ModelData().GetModelDataByFilter(ctx, searchFilter)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelDataList)

}

func (cm *CmdbController) GlobalSearch(c *gin.Context) {
	zap.L().Debug("GlobalSearch Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GlobalSearch", "POST")
	defer span.End()

	var (
		r   any
		err error
	)

	pageNumber, pageSizeNumber, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	// 初始化一个空的filter用来保存用户传递过来的查询参数
	filter := make(mapdata.MapData)
	// 根据search_type的不同，选择不同的请求体
	searchType := c.Query("search_type")
	switch searchType {
	case "fuzzy":
		var (
			req  = &apiv1.GlobalSearchFuzzyRequest{}
			resp = &apiv1.GlobalSearchResponse{}
		)

		if err := c.ShouldBindJSON(req); err != nil {
			zap.L().Error(err.Error())
			core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
			return
		}
		resp, err = cm.svc.ModelData().GlobalSearchFuzzy(ctx, req, pageNumber, pageSizeNumber)
		filter.Set("keyword", req.Keyword)
		if len(req.ModelCode) > 0 {
			filter.Set("model_code", req.ModelCode)
		} else {
			filter.Set("model_code", make([]string, 0))
		}
		filter.Set("model_group_code", req.ModelGroupCode)
		resp.SearchFilter = filter
		r = resp
	case "batch":
		var (
			req  = &apiv1.GlobalSearchBatchRequest{}
			resp = &apiv1.GlobalBatchSearchResponse{}
		)
		if err := c.ShouldBindJSON(req); err != nil {
			zap.L().Error(err.Error())
			core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
			return
		}
		resp, err = cm.svc.ModelData().GlobalSearchBatch(ctx, req, pageNumber, pageSizeNumber)
		if err != nil {
			zap.L().Error(err.Error())
			core.SendResponse(c, err, nil)
			return
		}
		filter.Set("search_field", req.SearchField)
		if len(req.ModelCode) > 0 {
			filter.Set("model_code", req.ModelCode)
		} else {
			filter.Set("model_code", make([]string, 0))
		}
		filter.Set("model_group_code", req.ModelGroupCode)
		filter.Set("keywords", req.Keywords)
		resp.SearchFilter = filter
		r = resp
	case "combine":
		var (
			req  = &apiv1.GlobalSearchCombineRequest{}
			resp = &apiv1.GlobalSearchResponse{}
		)
		if err := c.ShouldBindJSON(req); err != nil {
			zap.L().Error(err.Error())
			core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
			return
		}
		resp, err = cm.svc.ModelData().GlobalSearchCombine(ctx, req, pageNumber, pageSizeNumber)
		if len(req.ModelCode) > 0 {
			filter.Set("model_code", req.ModelCode)
		} else {
			filter.Set("model_code", make([]string, 0))
		}
		filter.Set("model_group_code", req.ModelGroupCode)
		filter.Set("search_field", req.CombineFields)
		resp.SearchFilter = filter
		r = resp
	default:
		core.SendResponse(c, errno.ErrParameterInvalid.Add("search_type参数错误, 可选值为fuzzy, batch, combine"), nil)
		return
	}

	// 如果搜索失败，则返回错误
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	// 如果搜索成功，则返回搜索结果
	core.SendResponse(c, nil, r)
}

// GlobalSearchDownload 全局搜索下载
func (cm *CmdbController) GlobalSearchDownload(c *gin.Context) {
	zap.L().Debug("GlobalSearchDownload Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GlobalSearchDownload", "POST")
	defer span.End()

	req := &apiv1.GlobalSearchCombineRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	file, err := cm.svc.ModelData().GlobalSearchDownload(ctx, req)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}
	fileName := fmt.Sprintf("cmdb-export-%s.xlsx", time.Now().Format("2006-01-02"))
	zap.L().Debug("GlobalSearchDownload", zap.String("fileName", fileName))
	core.WriteExcel(c, fileName, file)
}

func (cm *CmdbController) Offline(c *gin.Context) {
	zap.L().Debug("Offline Controller called")
	span, ctx := apm.StartSpan(c, "OfflineController", "POST")
	defer span.End()

	var req apiv1.OfflineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}

	// 调用Service层的逻辑处理请求
	offlineData, err := cm.svc.ModelData().Offline(ctx, auth.GetUsername(c), &req)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, offlineData)
}
