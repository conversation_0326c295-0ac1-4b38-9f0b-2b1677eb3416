package cmdb

import (
	v1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) AgentReport(c *gin.Context) {
	zap.L().Debug("AgentReport Function Called")
	span, ctx := apm.StartSpan(c, "AgentReport", "POST")
	defer span.End()

	// 获取请求体
	var payload v1.Payload
	if err := c.ShouldBindJSON(&payload); err != nil {
		errMsg := errno.GetErrMsg(err)
		zap.L().Error(errMsg)
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errMsg), nil)
		return
	}

	if err := cm.svc.Agent().Report(ctx, "agent", &payload); err != nil {
		zap.L().Error("Failed to report", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.InternalServerError.Add(err.Error()), nil)
		return
	}

	core.SendResponse(c, nil, nil)
}
