package cmdb

import (
	"ks-knoc-server/internal/common/auth"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) AddRack(c *gin.Context) {

}

func (cm *CmdbController) GetRoomInfo(c *gin.Context) {
	zap.L().Debug("GetRoomInfo Controller called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetRoomInfoController", "GET")
	defer span.End()

	roomID := c.Query("id")
	if roomID == "" {
		core.SendResponse(c, errno.ErrParameterInvalid.Add("机房ID不能为空"), nil)
		return
	}

	resp, err := cm.svc.View().GetRoomInfo(ctx, auth.GetUsername(c), roomID)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}
