package cmdb

import (
	"ks-knoc-server/internal/common/auth"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"
)

// GetModelAttributeGroupList 获取模型分组列表
func (cm *CmdbController) GetModelAttributeGroupList(c *gin.Context) {
	zap.L().Debug("GetModelAttributeGroupList Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeGroupListController", "GET")
	defer span.End()

	modelCode := c.Query("code")
	if modelCode == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("需要传入模型的code"), nil)
		return
	}

	modelAttrGroupList, err := cm.svc.ModelAttrGroup().GetModelAttrGroupList(ctx, modelCode, bson.M{
		"relation": bson.M{"$ne": true},
	})
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelAttrGroupList)
}

// GetModelAttributeGroupByCode 根据code获取模型分组
func (cm *CmdbController) GetModelAttributeGroupByCode(c *gin.Context) {
	zap.L().Debug("GetModelAttributeGroupByCode Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeGroupByCode", "GET")
	defer span.End()

	code := c.Param("code")
	modelAttrGroup, err := cm.svc.ModelAttrGroup().GetModelAttrGroupByCode(ctx, code)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, modelAttrGroup)
}

// CreateModelAttributeGroup 创建模型分组
func (cm *CmdbController) CreateModelAttributeGroup(c *gin.Context) {
	zap.L().Debug("CreateModelAttributeGroup Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateModelAttributeGroup", "POST")
	defer span.End()

	var mag v1.ModelAttrGroup
	if err := c.ShouldBindJSON(&mag); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	// 设置默认值
	mag.SetGeneralDefaultVal()
	// 处理实际的创建模型分组的逻辑
	modelAttrGroupInstance, err := cm.svc.ModelAttrGroup().CreateModelAttrGroup(ctx, auth.GetUsername(c), &mag)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, modelAttrGroupInstance)
}

// UpdateModelAttributeGroup 更新模型分组
func (cm *CmdbController) UpdateModelAttributeGroup(c *gin.Context) {
	zap.L().Debug("UpdateModelAttributeGroup Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateModelAttributeGroup", "PUT")
	defer span.End()

	var mag map[string]interface{}
	gin.EnableJsonDecoderUseNumber()
	if err := c.ShouldBindJSON(&mag); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(err.Error()), nil)
		return
	}
	// 判断用户到底传没传递code，由于是用map接收的，因此无法使用默认的校验规则
	if _, ok := mag["code"]; !ok {
		core.SendResponse(c, errno.ErrBind.Add("code为必填字段"), nil)
		return
	}
	err := cm.svc.ModelAttrGroup().UpdateModelAttrGroup(ctx, auth.GetUsername(c), mag)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}

// DeleteModelAttributeGroup 删除模型分组
func (cm *CmdbController) DeleteModelAttributeGroup(c *gin.Context) {
	zap.L().Debug("DeleteModelAttributeGroup Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteModelAttributeGroup", "DELETE")
	defer span.End()

	var mag v1.InstanceFilter
	if err := c.ShouldBindJSON(&mag); err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}
	err := cm.svc.ModelAttrGroup().DeleteModelAttrGroup(ctx, auth.GetUsername(c), &mag)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, nil)
}
