package cmdb

import (
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) GetAuditRecordList(c *gin.Context) {
	zap.L().Debug("GetAuditRecordList Called")
	span, ctx := apm.StartSpan(c, "GetAuditRecordList", "GET")
	defer span.End()

	// 获取要查询的id列表
	request := new(apiv1.AuditRecordRequest)
	if err := c.ShouldBindJSON(&request); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	page, pageSize, err := CheckPagination(c)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	audits, err := cm.svc.Audit().GetAuditRecordList(ctx, *request, page, pageSize)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, audits)
}

func (cm *CmdbController) GetAuditRecordDetail(c *gin.Context) {
	zap.L().Debug("GetAuditRecordDetail Called")
	span, ctx := apm.StartSpan(c, "GetAuditRecordDetail", "GET")
	defer span.End()

	request := new(apiv1.AuditRecordRequestByID)
	if err := c.ShouldBindJSON(&request); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	page, pageSize, err := CheckPagination(c)
	if err != nil {
		core.SendResponse(c, err, nil)
		return
	}

	audit, err := cm.svc.Audit().GetAuditRecordDetail(ctx, *request, page, pageSize)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, audit)
}
