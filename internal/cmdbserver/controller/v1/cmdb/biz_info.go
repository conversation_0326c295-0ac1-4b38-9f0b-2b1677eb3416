package cmdb

import (
	"strings"

	apiV1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) GetITOffice(c *gin.Context) {
	zap.L().Debug("GetITOffice Controller Called")
	span, ctx := apm.StartSpan(c, "CreateModelDataSubOrdinateRelationController", "controller")
	defer span.End()

	result, err := cm.svc.Biz().GetITOffice(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetDeviceType(c *gin.Context) {
	zap.L().Debug("GetDeviceType Controller Called")

	type deviceType struct {
		Name string `json:"name"`
		Code string `json:"code"`
	}

	result := []deviceType{
		{Name: "服务器", Code: "server"},
		{Name: "存储", Code: "storage"},
		{Name: "安防设备", Code: "safety"},
		{Name: "交换机", Code: "switch"},
		{Name: "路由器", Code: "router"},
		{Name: "防火墙", Code: "firewall"},
		{Name: "负载均衡", Code: "loadbalancing"},
		{Name: "上网行为管理", Code: "audit"},
		{Name: "安全设备", Code: "secure"},
		{Name: "传输设备", Code: "transmission"},
		{Name: "无线控制器", Code: "wlc"},
		{Name: "流量分析", Code: "netflow"},
		{Name: "缓存设备", Code: "cache"},
	}
	core.SendResponse(c, nil, result)
}

// GetDcCascadeData 获取机房的级联数据, 职场 → 机房 → 机柜
func (cm *CmdbController) GetDcCascadeData(c *gin.Context) {
	zap.L().Debug("GetDcCascadeData Controller Called")
	span, ctx := apm.StartSpan(c, "GetDcCascadeData", "controller")
	defer span.End()

	officeCode := c.Query("office_code")
	if officeCode == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("office_code 不能为空"), nil)
		return
	}

	result, err := cm.svc.Biz().GetDcCascadeData(ctx, officeCode)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetDeviceBrandInfo(c *gin.Context) {
	zap.L().Debug("GetDeviceBrandInfo Controller Called")
	span, ctx := apm.StartSpan(c, "GetDeviceBrandInfo", "controller")
	defer span.End()

	deviceModelCode := c.Query("device_model_code")
	if deviceModelCode == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("device_model_code 不能为空"), nil)
		return
	}

	result, err := cm.svc.Biz().GetDeviceBrandInfo(ctx, deviceModelCode)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}
	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetDevicePosition(c *gin.Context) {
	zap.L().Debug("GetDeviceInfoBySn Controller Called")
	span, ctx := apm.StartSpan(c, "GetDeviceInfoBySn", "controller")
	defer span.End()

	sn := c.Query("sn")
	if sn == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("sn 不能为空"), nil)
		return
	}

	result, err := cm.svc.Biz().GetDevicePositionString(ctx, sn)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetDevicePositionInfo(c *gin.Context) {
	zap.L().Debug("GetDevicePositionInfo Controller Called")
	span, ctx := apm.StartSpan(c, "GetDevicePositionInfo", "controller")
	defer span.End()

	sn := c.Query("sn")
	if sn == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("sn 不能为空"), nil)
		return
	}

	result, err := cm.svc.Biz().GetDeviceInfo(ctx, sn)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetIdc(c *gin.Context) {
	zap.L().Debug("GetIdc Controller Called")
	span, ctx := apm.StartSpan(c, "GetIdc", "controller")
	defer span.End()

	officeID := c.Query("office_id")
	if officeID == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("office_id 不能为空"), nil)
		return
	}

	officeSections := strings.Split(officeID, "_")
	if len(officeSections) != 2 {
		core.SendResponse(c, errno.ErrParameterRequired.Add("office_id 格式不正确"), nil)
		return
	}

	result, err := cm.svc.Biz().GetIdcByOfficeCode(ctx, officeSections[0])
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetRack(c *gin.Context) {
	zap.L().Debug("GetRack Controller Called")
	span, ctx := apm.StartSpan(c, "GetRack", "controller")
	defer span.End()

	idcCode := c.Query("idc_code")
	if idcCode == "" {
		core.SendResponse(c, errno.ErrParameterRequired.Add("idc_code 不能为空"), nil)
		return
	}

	idcSections := strings.Split(idcCode, "_")
	if len(idcSections) != 2 {
		core.SendResponse(c, errno.ErrParameterRequired.Add("idc_code 格式不正确"), nil)
		return
	}

	result, err := cm.svc.Biz().GetRackByIDC(ctx, idcSections[0])
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetDeviceInfoList(c *gin.Context) {
	zap.L().Debug("GetDeviceInfoList Controller Called")
	span, ctx := apm.StartSpan(c, "GetDeviceInfoList", "controller")
	defer span.End()

	deviceList := new(apiV1.DeviceSNList)
	if err := c.ShouldBindJSON(deviceList); err != nil {
		core.SendResponse(c, errno.ErrParameterRequired.Add("sn 不能为空"), nil)
		return
	}

	result, err := cm.svc.Biz().GetDeviceInfoList(ctx, deviceList.SN)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) TestProbe(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "test probe",
	})
}

func (cm *CmdbController) GetOfficeList(c *gin.Context) {
	zap.L().Debug("GetOfficeList Controller Called")
	span, ctx := apm.StartSpan(c, "GetOfficeList", "controller")
	defer span.End()

	// 数据结构如下: 适用于简单的列表数据
	// [
	//        {
	//            "name": "Sao Paulo Corporate Towers",
	//            "code": "65a90546f899d8d189b1d90a"
	//        }
	// ]
	result, err := cm.svc.Biz().GetOfficeList(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetIdcList(c *gin.Context) {
	zap.L().Debug("GetIdcList Controller Called")
	span, ctx := apm.StartSpan(c, "GetIdcList", "controller")
	defer span.End()

	result, err := cm.svc.Biz().GetIdcList(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetManufacturers(c *gin.Context) {
	zap.L().Debug("GetManufacturers Controller Called")
	span, ctx := apm.StartSpan(c, "GetManufacturers", "controller")
	defer span.End()

	result, err := cm.svc.Biz().GetManufacturers(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}

func (cm *CmdbController) GetDeviceModels(c *gin.Context) {
	zap.L().Debug("GetDeviceModels Controller Called")
	span, ctx := apm.StartSpan(c, "GetDeviceModels", "controller")
	defer span.End()

	result, err := cm.svc.Biz().GetDeviceModels(ctx)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, result)
}
