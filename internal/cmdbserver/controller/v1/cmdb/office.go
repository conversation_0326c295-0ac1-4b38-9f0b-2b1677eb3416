package cmdb

import (
	"ks-knoc-server/internal/common/core"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// GetOffices 获取所有Office
func (cm *CmdbController) GetOffices(c *gin.Context) {
	zap.L().Debug("GetOffices Function Called")
	span, ctx := apm.StartSpan(c, "GetOffices", "controller")
	defer span.End()

	offices, err := cm.svc.Biz().GetOffices(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()

		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, offices)
}
