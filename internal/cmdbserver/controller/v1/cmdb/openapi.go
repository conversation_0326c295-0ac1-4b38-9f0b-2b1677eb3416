package cmdb

import (
	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"

	"ks-knoc-server/internal/common/auth"
	api "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
)

func (cm *CmdbController) OfflineDevice(c *gin.Context) {
	zap.L().Debug("OfflineDevice Function Called")
	span, ctx := apm.StartSpan(c, "OfflineDevice", "controller")
	defer span.End()

	var req api.OpenAPIOfflineDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("OfflineDevice BindJSON Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, nil, err)
		return
	}

	if err := req.Validate(); err != nil {
		zap.L().Error("OfflineDevice Validate Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, nil, err)
		return
	}

	username := auth.GetUsername(c)
	if err := cm.svc.Biz().OfflineDevice(ctx, username, &req); err != nil {
		zap.L().Error("OfflineDevice Error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, nil, err)
		return
	}

	core.SendResponse(c, nil, nil)
}
