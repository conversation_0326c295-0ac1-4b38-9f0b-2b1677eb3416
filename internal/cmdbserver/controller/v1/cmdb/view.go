package cmdb

import (
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (cm *CmdbController) GetHostTreeDataView(c *gin.Context) {
	zap.L().Debug("GetHostTreeDataView Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetHostTreeDataViewController", "controller")
	defer span.End()

	resp, err := cm.svc.View().HostTreeDataView(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetHostDataView(c *gin.Context) {
	zap.L().Debug("GetHostDataView Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetHostDataViewController", "controller")
	defer span.End()

	// 获取树节点的ID
	dataID := c.Query("data_id")
	if dataID == "" {
		zap.L().Error("data_id 不可以为空")
		core.SendResponse(c, errno.ErrParameterInvalid.Add("data_id 不可以为空"), nil)
		return
	}

	// 过滤条件有模型分类以及关键字过滤，其中code目前我们支持了多选。
	code := c.QueryArray("code")
	filterKey := c.Query("field")
	keyword := c.Query("keyword")

	filter := bson.M{}

	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	resp, err := cm.svc.View().HostDataView(ctx, dataID, keyword, filterKey, filter, page, pageSize, code)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) PhysicalTreeDataView(c *gin.Context) {
	zap.L().Debug("PhysicalTreeDataView Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetPhysicalTreeDataViewController", "controller")
	defer span.End()

	resp, err := cm.svc.View().PhysicalTreeDataView(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetNetworkTreeDataView(c *gin.Context) {
	zap.L().Debug("GetNetworkTreeDataView Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetNetworkTreeDataViewController", "controller")
	defer span.End()

	resp, err := cm.svc.View().GetNetworkTreeDataView(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetStoreroomTreeDataView(c *gin.Context) {
	zap.L().Debug("GetStoreroomTreeDataView Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetStoreroomTreeDataView", "controller")
	defer span.End()

	resp, err := cm.svc.View().GetStoreroomTreeDataView(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetBizTreeDataView(c *gin.Context) {
	zap.L().Debug("GetBizTreeDataView Controller Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetBizTreeDataView", "controller")
	defer span.End()

	resp, err := cm.svc.View().GetBizTreeDataView(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}

func (cm *CmdbController) GetBizDataView(c *gin.Context) {
	zap.L().Debug("GetBizDataView Controller Called")
	span, ctx := apm.StartSpan(c, "GetBizDataView", "controller")
	defer span.End()

	resp, err := cm.svc.View().GetBizDataView(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}
