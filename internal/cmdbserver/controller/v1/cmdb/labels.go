package cmdb

import (
	"strings"

	"ks-knoc-server/internal/common/auth"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/core"
	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (cm *CmdbController) CreateLabel(c *gin.Context) {
	zap.L().Debug("CreateLabel Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "CreateLabel", "controller")
	defer span.End()

	var (
		err error
		req = &v1.CreateLabelRequest{}
	)

	if err := c.ShouldBindJSON(req); err != nil {
		zap.L().Error("CreateLabel ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	// 去除Key两侧的空格
	req.LabelKey = strings.TrimSpace(req.LabelKey)

	// 校验一下key的合法性
	if err := req.Validate(); err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, errno.ErrParameterInvalid.Add(err.Error()), nil)
		return
	}

	if err := cm.svc.Labels().CreateLabel(ctx, req); err != nil {
		zap.L().Error("CreateLabel error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, err, nil)
}

func (cm *CmdbController) GetLabels(c *gin.Context) {
	zap.L().Debug("GetAllLabels Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "GetAllLabels", "controller")
	defer span.End()

	// 处理一下key参数
	keyArr := make([]string, 0)
	searchKeys := strings.TrimSpace(c.Query("keys"))
	if searchKeys != "" {
		keys := strings.Split(searchKeys, ",")
		for _, key := range keys {
			keyArr = append(keyArr, strings.TrimSpace(key))
		}
	}

	// 处理分页
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	// 调用service查询label标签信息
	labels, err := cm.svc.Labels().GetLabels(ctx, keyArr, page, pageSize)
	if err != nil {
		zap.L().Error("GetLabels error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, err, labels)
}

func (cm *CmdbController) DeleteLabel(c *gin.Context) {
	zap.L().Debug("DeleteLabel Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "DeleteLabel", "controller")
	defer span.End()

	labelID := c.Param("label_id")
	if labelID == "" {
		zap.L().Error("labelID is empty")
		core.SendResponse(c, errno.ErrParameterInvalid.Add("labelID为空"), nil)
		return
	}

	if err := cm.svc.Labels().DeleteLabel(ctx, auth.GetUsername(c), labelID); err != nil {
		zap.L().Error("DeleteLabel error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) UpdateLabelKey(c *gin.Context) {
	zap.L().Debug("UpdateLabelKey Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateLabelKey", "controller")
	defer span.End()

	labelKeyID := c.Param("key_id")
	if labelKeyID == "" {
		zap.L().Error("labelKeyID is empty")
		core.SendResponse(c, errno.ErrParameterInvalid.Add("labelKeyID为空"), nil)
		return
	}

	var req v1.UpdateLabelKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UpdateLabelKey ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.Labels().UpdateLabelKey(ctx, labelKeyID, &req); err != nil {
		zap.L().Error("UpdateLabelKey error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) UpdateLabelValue(c *gin.Context) {
	zap.L().Debug("UpdateLabelValue Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UpdateLabelValue", "controller")
	defer span.End()

	labelValueID := c.Param("value_id")
	if labelValueID == "" {
		zap.L().Error("labelValueID is empty")
		core.SendResponse(c, errno.ErrParameterInvalid.Add("labelValueID为空"), nil)
		return
	}

	var req v1.UpdateLabelValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UpdateLabelValue ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.Labels().UpdateLabelValue(ctx, labelValueID, &req); err != nil {
		zap.L().Error("UpdateLabelValue error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) BindingLabels(c *gin.Context) {
	zap.L().Debug("BindingLabels Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "BindingLabels", "controller")
	defer span.End()

	var req v1.LabelBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("BindingLabels ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.Labels().BindingLabels(ctx, auth.GetUsername(c), &req); err != nil {
		zap.L().Error("BindingLabels error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) UpdateBindingLabels(c *gin.Context) {
	zap.L().Debug("UnBindingLabels Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UnBindingLabels", "controller")
	defer span.End()

	var req v1.LabelUnBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UnBindingLabels ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.Labels().UpdateBindingLabels(ctx, auth.GetUsername(c), &req); err != nil {
		zap.L().Error("UnBindingLabels error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

// UnBindingLabels 将标签从数据上解绑
func (cm *CmdbController) UnBindingLabels(c *gin.Context) {
	zap.L().Debug("UnBindingLabels Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "UnBindingLabels", "controller")
	defer span.End()

	var req v1.LabelUnBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("UnBindingLabels ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	if err := cm.svc.Labels().UnBindingLabels(ctx, auth.GetUsername(c), &req); err != nil {
		zap.L().Error("UnBindingLabels error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, nil)
}

func (cm *CmdbController) SearchLabeledData(c *gin.Context) {
	zap.L().Debug("SearchLabeledData Function Called")
	ctx := c.Request.Context()
	span, ctx := apm.StartSpan(ctx, "SearchLabeledData", "controller")
	defer span.End()

	// 处理分页
	page, pageSize, err := CheckPagination(c)
	if err != nil {
		zap.L().Error(err.Error())
		core.SendResponse(c, err, nil)
		return
	}

	var req v1.SearchLabeledDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		zap.L().Error("SearchLabeledData ShouldBindJSON error", zap.Error(err))
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, errno.ErrBind.Add(errno.GetErrMsg(err)), nil)
		return
	}

	resp, err := cm.svc.Labels().SearchLabeledData(ctx, &req, page, pageSize)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		core.SendResponse(c, err, nil)
		return
	}

	core.SendResponse(c, nil, resp)
}
