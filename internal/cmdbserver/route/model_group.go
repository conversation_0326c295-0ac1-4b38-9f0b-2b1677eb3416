package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterModelGroupRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 获取模型分组，并返回模型分组列表
	v1.GET("/model_groups", controller.GetModelGroupList)
	// 创建模型分组
	v1.POST("/model_groups", controller.CreateModelGroup)
	// 更新模型分组
	v1.PUT("/model_groups", controller.UpdateModelGroup)
	// 删除模型分组
	v1.DELETE("/model_groups", controller.DeleteModelGroup)
}
