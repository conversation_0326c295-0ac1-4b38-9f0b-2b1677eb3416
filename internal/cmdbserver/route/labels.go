package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterLabelRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	v1.GET("/labels", controller.GetLabels)
	v1.POST("/labels", controller.CreateLabel)
	v1.DELETE("/labels/:label_id", controller.DeleteLabel)
	v1.PUT("/label_key/:key_id", controller.UpdateLabelKey)
	v1.PUT("/label_value/:value_id", controller.UpdateLabelValue)
	v1.POST("/labels/binding", controller.BindingLabels)
	v1.POST("/labels/unbinding", controller.UnBindingLabels)
	v1.POST("/labels/search_data", controller.SearchLabeledData)
}
