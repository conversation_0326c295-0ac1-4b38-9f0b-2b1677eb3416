package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterModelAttrGroupRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 获取模型属性分组列表
	v1.GET("/model_attr_groups", controller.GetModelAttributeGroupList)
	// 根据模型Code获取模型属性分组列表
	v1.GET("/get_model_attr_group_by_code", controller.GetModelAttributeGroupByCode)
	// 创建模型属性分组
	v1.POST("/model_attr_groups", controller.CreateModelAttributeGroup)
	// 更新模型属性分组
	v1.PUT("/model_attr_groups", controller.UpdateModelAttributeGroup)
	// 删除模型属性分组
	v1.DELETE("/model_attr_groups", controller.DeleteModelAttributeGroup)
}
