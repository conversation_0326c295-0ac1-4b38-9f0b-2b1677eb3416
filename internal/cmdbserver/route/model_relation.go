package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterModelRelationRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 创建模型从属关系
	v1.POST("/model_relation/subordinate", controller.CreateModelSubOrdinateRelation)
	// 删除模型从属关系
	v1.DELETE("/model_relation/subordinate", controller.DeleteModelSubOrdinateRelation)
	// 创建模型关联关系
	v1.POST("/model_relation/associate", controller.CreateModelAssociateRelation)
	// 删除模型关联关系
	v1.DELETE("/model_relation/associate", controller.DeleteModelAssociateRelation)
	// 获取模型的关系（包含模型的从属关系[父模型关系+子模型关系] + 关联关系）
	v1.GET("/model_relations", controller.GetModelRelations)
	// 更新模型关系，只更新关系描述
	v1.PUT("/model_relations", controller.UpdateModelRelation)
	// 获取模型关系拓扑
	v1.GET("/model_relation/topo", controller.GetModelRelationTopo)
}
