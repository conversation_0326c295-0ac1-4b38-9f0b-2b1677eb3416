package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterModelRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 获取模型列表
	v1.GET("/models", controller.GetModelList)
	// 获取模型列表（分页）
	v1.GET("/models/paginated", controller.GetModelListPaginated)
	// 创建模型
	v1.POST("/models", controller.CreateModel)
	// 删除模型
	v1.DELETE("/models", controller.DeleteModel)
	// 根据模型Code获取模型
	v1.GET("/get_model_by_code", controller.GetModelByCode)
	// 获取模型的Icon图标
	v1.GET("/icons", controller.GetIcons)
}
