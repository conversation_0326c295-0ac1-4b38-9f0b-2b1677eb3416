package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterDataRelationRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 添加数据之间的从属关系
	v1.POST("/model_relation/subordinate/data", controller.CreateModelDataSubOrdinateRelation)
	// 转移至功能
	v1.POST("/model_relation/subordinate/data/move", controller.CreateModelDataSubOrdinateRelationMove)
	// 删除数据之间的从属关系
	v1.DELETE("/model_relation/subordinate/data", controller.DeleteModelDataSubOrdinateRelation)
	// 添加数据之间的关联关系
	v1.POST("/model_relation/associate/data", controller.CreateModelDataAssociateRelation)
	// 删除数据之间的关联关系
	v1.DELETE("/model_relation/associate/data", controller.DeleteModelDataAssociateRelation)
	// 根据关系字段Code获取实例
	v1.GET("/model_relation/get_instances_by_rel_code", controller.GetInstancesByRelCode)
	// 获取关系的实例列表，v2版本，所有的关系都可以展示
	v1.GET("/model_relation/get_instances_by_rel_code_v2", controller.GetInstancesByRelCodeV2)
	// 获取可以建立关系的数据实例列表
	v1.GET("/model_relation/get_rel_instances", controller.GetRelInstances)
	// 获取可转移至的数据实例列表
	v1.POST("/model_relation/get_move_instances", controller.GetMoveInstances)
	// 更新数据之间的关系信息（仅能更新关系描述）
	v1.PUT("/model_relation/data", controller.UpdateModelDataRelation)
}
