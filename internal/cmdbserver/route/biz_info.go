package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterBizInfoRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 2025-01-08 当前这部分接口主要给工单管理使用，因为环境隔离性的问题，部分接口响应格式做了处理，比如office获取的不是id而是{id}_{name}
	// 后续如果服务之间打通的话，不应该存在这种问题，所以目前这个接口属于一种过度的方案，下面的一组接口是通用的接口。
	v1.GET("/office", controller.GetITOffice)
	v1.GET("/idc", controller.GetIdc)
	v1.GET("/rack", controller.GetRack)
	v1.GET("/device/type", controller.GetDeviceType)
	v1.GET("/device/brand_info", controller.GetDeviceBrandInfo)
	v1.GET("/dc/cascade", controller.GetDcCascadeData)
	v1.GET("/device/position", controller.GetDevicePosition)
	v1.GET("/device/position/info", controller.GetDevicePositionInfo)
	v1.POST("/device/infos", controller.GetDeviceInfoList)
	v1.GET("/probe", controller.TestProbe)

	// 2025-01-08 通用接口
	v1.GET("/office_list", controller.GetOfficeList)
	v1.GET("/idc_list", controller.GetIdcList)
	v1.GET("/manufacturers", controller.GetManufacturers)
	v1.GET("/device_models", controller.GetDeviceModels)

	// 2025-04-15 新增
	v1.GET("/offices", controller.GetOffices)
	v1.POST("/device/offline", controller.OfflineDevice)
}
