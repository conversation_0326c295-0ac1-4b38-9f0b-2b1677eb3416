package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterModelDataRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 获取模型数据列表
	v1.GET("/model_datas", controller.GetModelDataList)
	// 根据id获取模型数据列表
	v1.POST("/model_data_by_ids", controller.GetModelDataListByID)
	// 创建模型数据
	v1.POST("/model_datas", controller.CreateModelData)
	// 更新模型数据
	v1.PUT("/model_datas", controller.UpdateModelData)
	// 删除模型数据
	v1.DELETE("/model_datas", controller.DeleteModelData)
	// 模型信息数据下载
	v1.POST("/model_data/download", controller.DownloadData)
	// 数据模板的下载
	v1.GET("/template", controller.DownloadTemplate)
	// 模型数据的导入
	v1.POST("/model_data/import", controller.ImportData)
	// TODO: 检索功能
	v1.GET("/search", controller.SearchModelData)
	// 模糊搜索功能
	v1.POST("/fuzzy_search", controller.FuzzySearchModelData)
	// TODO: 模糊搜索推荐功能
	v1.GET("/fuzzy_search/suggested", controller.FuzzySearchSuggestedModelData)
	// 根据特定条件获取模型数据，内部调用
	v1.POST("/model_data_by_filter", controller.GetModelDataByFilter)
	// 全局搜索, 2025-01-03
	v1.POST("/global_search", controller.GlobalSearch)
	// 全局搜索下载, 2025-01-06
	v1.POST("/global_search/download", controller.GlobalSearchDownload)
	// 下线
	v1.POST("/model_data/offline", controller.Offline)
}
