package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterDataViewRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 物理视图
	v1.GET("/data_view/physical/rack", controller.GetPhysicalRackDataView)                  // 物理视图-机柜视图
	v1.GET("/data_view/physical/rack/instance", controller.GetPhysicalRackDataViewInstance) // 物理视图-机柜视图-实例
	v1.GET("/data_view/physical/idc", controller.GetPhysicalIDCDataView)                    // 物理视图-机房视图
	v1.GET("/data_view/physical/tree", controller.PhysicalTreeDataView)                     // 物理视图-树形视图
	v1.POST("/data_view/physical/on_rack", controller.OnRack)                               // 上架
	v1.POST("/data_view/physical/placeholder", controller.Placeholder)                      // 向机柜放置占位物品
	v1.POST("/data_view/physical/off_rack", controller.OffRack)                             // 下架
	v1.DELETE("/data_view/physical/placeholder", controller.RemovePlaceholder)              // 从机柜移除占位物品
	v1.POST("/data_view/physical/uid", controller.AdjustUID)                                // 调整UID灯
	v1.GET("/data_view/physical/room", controller.GetRoomInfo)                              // 获取机房信息
	v1.GET("/data_view/physical/rack_spare", controller.RackSpare)                          // 根据机柜ID获取可使用的位置

	// 主机视图
	v1.GET("/data_view/host/tree", controller.GetHostTreeDataView) // 主机视图-树形视图
	v1.GET("/data_view/host/instance", controller.GetHostDataView) // 主机视图-列表视图

	// 网络视图
	v1.GET("/data_view/network/tree", controller.GetNetworkTreeDataView)     // 网络视图-树形视图
	v1.GET("/data_view/storeroom/tree", controller.GetStoreroomTreeDataView) // 仓库视图-树形视图

	// 业务视图
	v1.GET("/data_view/biz/tree", controller.GetBizTreeDataView) // 业务视图-列表视图
	v1.GET("/data_view/biz/instance", controller.GetBizDataView)

	// TODO: 还没做，本来是为了给机房视图添置机柜用的。具体方案待定
	v1.POST("/data_view/physical/room/addRack", controller.AddRack)
}
