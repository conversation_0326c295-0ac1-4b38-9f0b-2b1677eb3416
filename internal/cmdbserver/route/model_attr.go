package route

import (
	"ks-knoc-server/internal/cmdbserver/controller/v1/cmdb"

	"github.com/gin-gonic/gin"
)

func RegisterModelAttrRoutes(v1 *gin.RouterGroup, controller *cmdb.CmdbController) {
	// 获取模型属性列表
	v1.GET("/model_attrs", controller.GetModelAttributeList)
	// 创建模型属性
	v1.POST("/model_attrs", controller.CreateModelAttribute)
	// 更新模型属性
	v1.PUT("/model_attrs", controller.UpdateModelAttribute)
	// 删除模型属性
	v1.DELETE("/model_attrs", controller.DeleteModelAttribute)
	// 通用的模型属性正则规则
	v1.GET("/model_attr_rules", controller.GetModelAttrRules)
}
