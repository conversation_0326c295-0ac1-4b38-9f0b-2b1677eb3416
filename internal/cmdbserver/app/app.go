package app

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"ks-knoc-server/internal/cmdbserver/store"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/cmdbserver/options"
	"ks-knoc-server/internal/cmdbserver/server"
	"ks-knoc-server/pkg/shutdown"
)

// App ...
type App struct {
	basename    string
	name        string
	description string
	options     *options.Options
	runFunc     RunFunc
}

// RunFunc ...
type RunFunc func(basename string) error

// Option ...
type Option func(*App)

// WithOptions ...
func WithOptions(opt *options.Options) Option {
	return func(a *App) {
		a.options = opt
		a.options.LogsOptions.FileRename(config.APIName)
	}
}

// WithRunFunc ...
func WithRunFunc(run RunFunc) Option {
	return func(a *App) {
		a.runFunc = run
	}
}

// NewApp ...
func NewApp(name, basename string, opts ...Option) *App {
	a := &App{
		name:     name,
		basename: basename,
	}

	for _, o := range opts {
		o(a)
	}
	return a
}

func (a *App) initBuildCache(srv *server.Server) (err error) {
	// 刷新缓存
	ctx := context.Background()

	attrs := make([]v1.CommonModelAttribute, 0)
	err = srv.Mgo.GetCollection("model_attr").
		Find(ctx, bson.M{}).
		Select(bson.M{}).
		All(&attrs)

	if err != nil {
		return err
	}

	jsonRaw, err := json.Marshal(attrs)
	if err != nil {
		return err
	}

	// 刷新模型模型属性列表的缓存
	_ = srv.Cache.Set(store.ModelAttrList.String(), jsonRaw)

	// 刷新模型列表的缓存
	models := make([]v1.Model, 0)
	err = srv.Mgo.GetCollection("model").
		Find(ctx, bson.M{}).
		Select(bson.M{}).
		All(&models)

	if err != nil {
		return err
	}

	modelJsonRaw, err := json.Marshal(models)
	if err != nil {
		return err
	}

	_ = srv.Cache.Set(store.ModelList.String(), modelJsonRaw)

	return
}

// Run 启动服务
func (a *App) Run() {
	var err error

	srv, err := server.NewServer(a.options)
	if err != nil {
		panic(err)
	}

	// 首次启动初始化一些常用的cache
	// err = a.initBuildCache(srv)
	// if err != nil {
	// 	zap.L().Error("首次启动初始化Cache失败", zap.Error(err))
	// }

	// 程序意外关闭之前，将内存中的日志flush到磁盘
	defer func() {
		_ = srv.Log.Sync()
	}()

	// 程序停止的时候，关闭mongo的连接
	defer func() {
		if err = srv.Mgo.Close(); err != nil {
			srv.Log.Error("MongoDB Close Error", zap.Error(err))
		}
	}()

	// 程序意外停止的时候清理cache
	defer func() {
		_ = srv.Cache.Close()
	}()

	srv.Log.Info("CmdbServer Start Succeed.")

	a.options.WebOptions.Server.Handler = srv.Web
	if err = a.options.WebOptions.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		srv.Log.Fatal("http server startup err", zap.Error(err))
	}

	shutdown.NewHook().Close(
		func() {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			if err = a.options.WebOptions.Shutdown(ctx); err != nil {
				srv.Log.Error("server shutdown err", zap.Error(err))
			}
		},
	)
}
