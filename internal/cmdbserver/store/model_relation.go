package store

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type ModelRelationStore interface {
	CreateModelAssociateRelation(ctx context.Context, operator string, data *v1.AssociateRelations) error
	GetModelAssociateRelationByCustomFilter(ctx context.Context, filter bson.M) (*v1.AssociateRelations, error)
	GetModelRelationByID(ctx context.Context, operator string, idFilter string) (*v1.AssociateRelations, error)
	GetModelRelationByFilter(ctx context.Context, filter bson.M) (*v1.AssociateRelations, error)
	DeleteModelAssociateRelation(ctx context.Context, operator, relID string) error
	UpdateModelRelation(ctx context.Context, relType v1.RelationType, relID, relDesc string) (err error)
	UpdateModelRelationLink(ctx context.Context, link *v1.AssociateRelations) error
}

type modelRelationStore struct {
	mgo *db.MongoOptions
}

func newModelRelationStore(ds *DataStore) *modelRelationStore {
	return &modelRelationStore{
		mgo: ds.Mgo,
	}
}

var _ ModelRelationStore = (*modelRelationStore)(nil)

func (m *modelRelationStore) CreateModelAssociateRelation(ctx context.Context, operator string, data *v1.AssociateRelations) error {
	zap.L().Debug("CreateModelAssociateRelation Store Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelAssociateRelation", "store")
	defer span.End()

	_, err := m.mgo.GetCollection("model_relation").InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (m *modelRelationStore) GetModelAssociateRelationByCustomFilter(ctx context.Context, filter bson.M) (*v1.AssociateRelations, error) {
	zap.L().Debug("GetModelAssociateRelation Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAssociateRelation", "store")
	defer span.End()

	rel := &v1.AssociateRelations{}
	err := m.mgo.GetCollection("model_relation").Find(ctx, filter).One(rel)
	if err != nil {
		return nil, err
	}
	return rel, nil
}

func (m *modelRelationStore) GetModelRelationByID(ctx context.Context, operator string, idFilter string) (*v1.AssociateRelations, error) {
	zap.L().Debug("GetModelRelationByID Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelRelationByIDStore", "store")
	defer span.End()

	rel := v1.NewAssociateRelations()
	err := m.mgo.GetCollection("model_relation").Find(ctx, bson.M{"_id": idFilter}).One(rel)
	if err != nil {
		zap.L().Error("GetModelRelation Store Failed", zap.Error(err))
		return nil, err
	}
	return rel, nil
}

func (m *modelRelationStore) GetModelRelationByFilter(ctx context.Context, filter bson.M) (*v1.AssociateRelations, error) {
	zap.L().Debug("GetModelRelationByFilter Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelRelationByFilterStore", "store")
	defer span.End()

	rel := v1.NewAssociateRelations()
	if err := m.mgo.GetCollection("model_relation").Find(ctx, filter).One(rel); err != nil {
		zap.L().Error("查询模型关系失败", zap.Error(err))
		return nil, err
	}
	return rel, nil
}

func (m *modelRelationStore) DeleteModelAssociateRelation(ctx context.Context, operator, relID string) error {
	zap.L().Debug("DeleteModelAssociateRelation Store Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelAssociateRelation", "store")
	defer span.End()

	err := m.mgo.GetCollection("model_relation").RemoveId(ctx, relID)
	if err != nil {
		return err
	}
	return nil
}

// UpdateModelRelation 更新模型关系，目前仅支持更新关系的描述信息
func (m *modelRelationStore) UpdateModelRelation(ctx context.Context, relType v1.RelationType, relID, relDesc string) (err error) {
	zap.L().Debug("UpdateModelRelation Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelRelationStore", "store")
	defer span.End()

	switch relType {
	case v1.Subordinate:
		if err = m.mgo.GetCollection("model_attr").UpdateOne(ctx,
			bson.M{"_id": relID},
			bson.M{"$set": bson.M{"attrs.rel_to_desc": relDesc}},
		); err != nil {
			return err
		}
	case v1.Associate:
		if err = m.mgo.GetCollection("model_attr").UpdateOne(ctx,
			bson.M{"_id": relID},
			bson.M{"$set": bson.M{"attrs.rel_desc": relDesc}},
		); err != nil {
			return err
		}
	default:
		zap.L().Error("关系类型不合法")
		return errno.ErrModelRelationType.Add("关系类型不合法")
	}

	return
}

func (m *modelRelationStore) UpdateModelRelationLink(ctx context.Context, link *v1.AssociateRelations) error {
	return nil
}
