package store

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	kjson "ks-knoc-server/pkg/json"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *modelAttributesStore) SetModelAttributesCache(ctx context.Context) ([]byte, error) {
	var (
		err error
		mal = make([]v1.CommonModelAttribute, 0)
	)

	err = m.mgo.GetCollection("model_attr").
		Find(ctx, bson.M{}).
		Sort("create_at").
		All(&mal)
	if err != nil {
		return nil, err
	}

	// refresh cache
	raw, err := kjson.Marshal(mal)

	// 序列化失败就失败了，不要影响正常的业务逻辑，顶多就是没缓存了，但是还能查询数据库
	if err != nil {
		return nil, err
	}

	err = m.cache.Set(ModelAttrList.String(), raw)
	if err != nil {
		return nil, err
	}

	zap.L().Debug("Set ModelAttributes Cache Success")
	return raw, nil
}
