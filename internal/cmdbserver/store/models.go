package store

import (
	"context"
	"errors"
	"fmt"

	"ks-knoc-server/internal/common/cache"

	"github.com/olivere/elastic/v7"
	"github.com/qiniu/qmgo"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"gorm.io/gorm"

	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"
	kjson "ks-knoc-server/pkg/json"
	"ks-knoc-server/pkg/utils"
)

type ModelStore interface {
	// CreateModel 创建模型
	CreateModel(ctx context.Context, operator string, model *v1.Model) (*v1.Model, error)
	// GetAllModelList 获取所有的模型列表，不带分页的
	GetAllModelList(ctx context.Context) ([]v1.Model, error)
	// GetModelMapping 获取模型映射，key为modelCode，value为model的object
	GetModelMapping(ctx context.Context) (map[string]v1.Model, error)
	// GetModelName 获取模型名称
	GetModelName(ctx context.Context, code string) (string, error)
	// GetModelListByFilter 获取模型列表
	GetModelListByFilter(ctx context.Context, filter bson.M) ([]v1.Model, error)
	GetModelListWithPagination(ctx context.Context, filter bson.M, page, pageSize int64) (*apiv1.PaginatedModelResponse, error)
	// DeleteModel 删除模型
	DeleteModel(ctx context.Context, operator string, model *v1.InstanceFilter) error
	// GetModelByCode 根据Code获取Model模型信息，Code为模型的唯一标识
	GetModelByCode(ctx context.Context, code string) (*v1.Model, error)
	// GetIcons 获取模型的图标
	GetIcons(ctx context.Context) ([]v1.ModelIcon, error)
	// UpdateModel 更新模型
	UpdateModel(ctx context.Context, model *v1.Model) error
	// SetModelCache 设置模型缓存
	SetModelCache(ctx context.Context) ([]byte, error)
	// DeleteModelCache 删除模型缓存
	DeleteModelCache(ctx context.Context) error
	// RefreshModelCache 刷新模型缓存
	RefreshModelCache(ctx context.Context) error
	// ResetModelParentCode 重置模型的父级Code，置为空字符串
	ResetModelParentCode(ctx context.Context, modelCode string) error
}

var _ ModelStore = (*modelStore)(nil)

type modelStore struct {
	mgo   *db.MongoOptions
	db    *gorm.DB
	cache cache.Cache
	es    *elastic.Client
}

func newModelStore(ds *DataStore) *modelStore {
	return &modelStore{
		mgo:   ds.Mgo,
		db:    ds.Db,
		cache: ds.Cache,
		es:    ds.ES,
	}
}

// CreateModel 创建模型
func (m *modelStore) CreateModel(ctx context.Context, operator string, model *v1.Model) (*v1.Model, error) {
	insertRes := new(qmgo.InsertOneResult)
	model.SetGeneralDefaultVal()
	// 1. 模型一定要归属一个模型分组下，因此模型分组为必填项，如果模型分组不许存在，不存在则无法进行添加
	// 这里返回的err类型为errors.errorString
	mg := v1.ModelGroup{}
	if err := m.mgo.GetCollection("model_group").Find(ctx, bson.M{"code": model.ModelGroup}).One(&mg); err != nil {
		return nil, errno.ErrDataNotExists.Addf("模型分组code '%s' 不存在", model.ModelGroup)
	}
	// 2. 如果存在的话，那么就可以添加模型了
	// 在这里建立一个事物，创建模型的时候，应该连带创建模型的基础属性分组，系统属性分组，关系属性分组以及基础属性，要么都成功，要么都失败
	s, err := m.mgo.GetClient().Session()
	if err != nil {
		return nil, err
	}
	defer s.EndSession(ctx) // 事务结束
	callback := func(sessionCtx context.Context) (interface{}, error) {
		// 2.1 创建模型
		if insertRes, err = m.mgo.GetCollection("model").InsertOne(sessionCtx, model); err != nil {
			if mongo.IsDuplicateKeyError(err) {
				return nil, errno.ErrDuplicateKey.Add(err.Error())
			}
			return nil, err
		}
		// 2.2 创建模型的基础属性分组，并分别插入基础属性分组，关系属性分组，系统属性分组
		basic, relation, system := v1.GenerateDefaultModelAttrGroup(model.Code)
		if _, err = m.mgo.GetCollection("model_attr_group").InsertOne(sessionCtx, basic); err != nil {
			return nil, err
		}
		if _, err = m.mgo.GetCollection("model_attr_group").InsertOne(sessionCtx, relation); err != nil {
			return nil, err
		}
		if _, err := m.mgo.GetCollection("model_attr_group").InsertOne(sessionCtx, system); err != nil {
			return nil, err
		}
		// 2.3 创建基本的模型属性
		name, code := v1.GenerateDefaultModelAttrs(model.Code)
		if _, err = m.mgo.GetCollection("model_attr").InsertOne(sessionCtx, name); err != nil {
			return nil, err
		}
		if _, err = m.mgo.GetCollection("model_attr").InsertOne(sessionCtx, code); err != nil {
			return nil, err
		}

		// 2.4 创建模型初始字段的索引
		if err := m.UpdateESMapping(ctx, "model_data", name.Code); err != nil {
			return nil, err
		}
		if err := m.UpdateESMapping(ctx, "model_data", code.Code); err != nil {
			return nil, err
		}

		return nil, nil
	}
	if _, err = s.StartTransaction(ctx, callback); err != nil {
		return nil, err
	}

	model.ID = utils.ToString(insertRes.InsertedID)
	_ = m.cache.Delete("model_list")
	return model, nil
}

// UpdateESMapping 更新ES的Mapping
// TODO: 这里和model_attr中的逻辑重复了，需要想办法把这个操作抽离成单独的操作，要解决一个store层横向调用的问题。
func (m *modelStore) UpdateESMapping(ctx context.Context, index, attrName string) error {
	zap.L().Debug("UpdateESMapping Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateESMappingStore", "store")
	defer span.End()

	resp, err := m.es.PutMapping().
		Index(index).
		BodyString(fmt.Sprintf(v1.ModelAttrESTmpl, attrName)).
		Do(ctx)

	if err != nil {
		zap.L().Error("更新Mapping异常", zap.Error(err))
		return err
	}

	if !resp.Acknowledged {
		zap.L().Error("更新Mapping失败", zap.Any("response", resp))
		return errno.ErrPutESMapping.Add("更新Mapping失败")
	}
	return nil
}

// GetAllModelList 获取所有模型列表
// 2023-09-27 添加缓存后，接口响应速度从20ms左右优化到5ms左右
func (m *modelStore) GetAllModelList(ctx context.Context) ([]v1.Model, error) {
	var (
		ml        = make([]v1.Model, 0)
		bytesData = make([]byte, 0)
		err       error
	)

	bytesData, err = m.cache.Get(ModelList.String())
	if err != nil {
		zap.L().Error("无法从cache中获取模型列表的信息，尝试从数据库中获取", zap.Error(err))
		raw, err := m.SetModelCache(ctx)
		if err != nil {
			zap.L().Error("设置模型缓存失败", zap.Error(err))
			return nil, err
		}
		// 将拿到的数据赋值给bytesData
		bytesData = raw
	}
	// 拿到了缓存的数据，反序列化
	err = kjson.Unmarshal(bytesData, &ml)
	if err != nil {
		zap.L().Error("反序列化model_list缓存失败", zap.Error(err))
		return ml, nil
	}
	return ml, nil
}

// GetModelMapping 获取模型映射
// example:
//
//	{
//	    "model_code1": v1.Model
//	}
func (m *modelStore) GetModelMapping(ctx context.Context) (map[string]v1.Model, error) {
	modelList, err := m.GetAllModelList(ctx)
	if err != nil {
		return nil, err
	}
	modelMapping := make(map[string]v1.Model)
	for _, model := range modelList {
		modelMapping[model.Code] = model
	}
	return modelMapping, nil
}

func (m *modelStore) GetModelName(ctx context.Context, code string) (string, error) {
	modelMap, err := m.GetModelMapping(ctx)
	if err != nil {
		return "", err
	}

	model, ok := modelMap[code]
	if !ok {
		return "", errno.ErrModelCodeNotExists.Addf("模型code %s 不存在", code)
	}

	return model.Name, nil
}

// GetModelListByFilter 获取模型列表
func (m *modelStore) GetModelListByFilter(ctx context.Context, filter bson.M) ([]v1.Model, error) {
	zap.L().Debug("GetModelListByFilter store called")

	var ml []v1.Model
	err := m.mgo.GetCollection("model").Find(ctx, filter).All(&ml)
	if err != nil {
		return nil, err
	}

	return ml, nil
}

// GetModelListWithPagination 获取模型列表
func (m *modelStore) GetModelListWithPagination(ctx context.Context, filter bson.M, page, pageSize int64) (*apiv1.PaginatedModelResponse, error) {
	var ml []v1.Model
	err := m.mgo.GetCollection("model").
		Find(ctx, filter).
		Skip((page - 1) * pageSize).
		Limit(pageSize).
		All(&ml)
	if err != nil {
		return nil, err
	}
	// 查询count
	total, err := m.mgo.GetCollection("model").Find(ctx, filter).Count()
	if err != nil {
		zap.L().Error("获取模型列表总数失败", zap.Error(err))
		return nil, err
	}
	pages := (total / pageSize) + 1
	mr := &apiv1.PaginatedModelResponse{
		Total:       total,
		Pages:       pages,
		PageSize:    pageSize,
		CurrentPage: page,
		Data:        ml,
	}
	return mr, nil
}

// DeleteModel 删除模型
func (m *modelStore) DeleteModel(ctx context.Context, operator string, model *v1.InstanceFilter) error {
	// 删除模型操作，除了删除模型本身，还需要删除模型的属性分组和属性，涉及到跨越多个集合的操作，因此需要使用事物
	s, err := m.mgo.GetClient().Session()
	if err != nil {
		return err
	}
	defer s.EndSession(ctx) // 事物结束
	// 删除这三个关联的属性和数据，要么都成功，要么都不成功，否则会有数据不一致的问题，脏数据的产生
	callback := func(sessionCtx context.Context) (interface{}, error) {
		// 1. 删除模型的属性分组
		if _, err := m.mgo.GetCollection("model_attr_group").RemoveAll(sessionCtx, bson.M{"model_code": model.Code}); err != nil {
			return nil, err
		}
		// 2. 删除模型的属性
		if _, err := m.mgo.GetCollection("model_attr").RemoveAll(sessionCtx, bson.M{"model_code": model.Code}); err != nil {
			return nil, err
		}
		// 3. 删除模型（模型code唯一，所以用Remove就可以，因为Remove默认至多删除一个doc）
		if err := m.mgo.GetCollection("model").Remove(sessionCtx, bson.M{"code": model.Code}); err != nil {
			return nil, err
		}
		return nil, nil
	}
	if _, err = s.StartTransaction(ctx, callback); err != nil {
		return err
	}

	_ = m.cache.Delete("model_list")
	return nil
}

// GetModelByCode 根据Code获取Model模型信息，Code为模型的唯一标识
func (m *modelStore) GetModelByCode(ctx context.Context, code string) (*v1.Model, error) {
	zap.L().Debug("GetModelByCode store called")
	span, ctx := apm.StartSpan(ctx, "GetModelByCodeStore", "GET")
	defer span.End()

	modelInfo := v1.Model{}
	err := m.mgo.GetCollection("model").Find(ctx, bson.M{"code": code}).One(&modelInfo)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("无法查询到模型 '%s", code)
		}
		return nil, err
	}
	// 获取模型下的数据的数量
	cnt, err := m.mgo.GetCollection("model_data").Find(ctx, bson.M{"model_code": code}).Count()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("无法查询到模型 '%s", code)
		}
		return nil, errno.ErrDataNotExists.Addf(err.Error())
	}
	// 更新模型的数据数量
	modelInfo.Total = cnt
	return &modelInfo, nil
}

// GetIcons 获取icon列表
func (m *modelStore) GetIcons(ctx context.Context) ([]v1.ModelIcon, error) {
	// 获取icon列表
	var icons []v1.ModelIcon
	err := m.db.Table("model_icons").Find(&icons).Error
	if err != nil {
		return nil, err
	}
	return icons, nil
}

func (m *modelStore) UpdateModel(ctx context.Context, model *v1.Model) error {
	zap.L().Debug("UpdateModel store called")

	// 目前只允许更新模型的父级和children字段
	if err := m.mgo.GetCollection("model").UpdateOne(ctx,
		bson.M{"code": model.Code},
		bson.M{"$set": bson.M{"parent_code": model.ParentCode, "children_code": model.ChildrenCode}},
	); err != nil {
		return err
	}

	// 此时缓存数据已经不准了，要删除缓存，有就删除，没有这个key就不管了
	_ = m.cache.Delete("model_list")

	return nil
}

// ResetModelParentCode 重置模型的父级Code，置为空字符串
func (m *modelStore) ResetModelParentCode(ctx context.Context, modelCode string) error {
	zap.L().Debug("ResetModelParentCode store called")
	span, ctx := apm.StartSpan(ctx, "ResetModelParentCodeStore", "GET")
	defer span.End()

	var err error

	if err = m.mgo.GetCollection("model").UpdateOne(ctx,
		bson.M{"code": modelCode},
		bson.M{"$set": bson.M{"parent_code": ""}},
	); err != nil {
		return err
	}

	return nil
}
