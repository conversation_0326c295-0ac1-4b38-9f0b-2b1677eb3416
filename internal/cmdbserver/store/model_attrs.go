package store

import (
	"context"
	"encoding/json"
	"fmt"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/clock"
	kjson "ks-knoc-server/pkg/json"
	"ks-knoc-server/pkg/utils"

	"github.com/olivere/elastic/v7"
	opts "github.com/qiniu/qmgo/options"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ModelAttributesStore interface {
	// CreateModelAttributes 创建模型属性
	CreateModelAttributes(ctx context.Context, operator string, modelAttr any) (*v1.CommonModelAttribute, error)
	// UpdateModelAttributes 更新模型属性
	UpdateModelAttributes(ctx context.Context, attr map[string]any) error
	// GetModelAttributesByCustomFilter 根据自定义的filter获取模型属性
	GetModelAttributesByCustomFilter(ctx context.Context, filter, selectFilter bson.M, opts ...opts.FindOptions) ([]v1.CommonModelAttribute, error)
	// GetModelAttributeByID 根据ID获取模型属性
	GetModelAttributeByID(ctx context.Context, id string) (*v1.CommonModelAttribute, error)
	// GetSingleModelAttributeByCustomFilter 根据自定义的filter获取单个模型属性
	GetSingleModelAttributeByCustomFilter(ctx context.Context, filter, selectFilter bson.M) (*v1.CommonModelAttribute, error)
	GetOneModelAttrMapByCustomFilter(ctx context.Context, filter, selectParam bson.M) (map[string]any, error)
	// GetModelAttrMap 获取所有模型属性的map，其中key为指定的key，value为属性的结构体
	GetModelAttrMap(ctx context.Context, key string) (map[string]v1.CommonModelAttribute, error)
	// GetModelAttrList 获取所有模型属性列表
	GetModelAttrList(ctx context.Context) ([]v1.CommonModelAttribute, error)
	// GetModelAttributeCodeList 获取模型属性code列表
	GetModelAttributeCodeList(ctx context.Context) ([]string, error)
	// GetModelAttributeList 根据model_code获取模型属性列表
	GetModelAttributeList(ctx context.Context, code string, selectParam bson.M) ([]map[string]interface{}, error)
	GetModelAttributeList2(ctx context.Context, code string, selectParam bson.M, filter bson.M) ([]v1.CommonModelAttribute, error)
	// DeleteModelAttributes 根据code删除模型属性
	DeleteModelAttributes(ctx context.Context, operator string, attrCode *v1.InstanceFilter) error
	// GetModelRules 获取模型规则
	GetModelRules(ctx context.Context) ([]v1.ModelRules, error)
	// SetModelAttributesCache 设置模型属性缓存
	SetModelAttributesCache(ctx context.Context) ([]byte, error)
	// UpdateESMapping 更新ES的mapping
	UpdateESMapping(ctx context.Context, index, attrName string) error
	// GetModelAttrMapping 获取模型属性映射
	GetModelAttrMapping(ctx context.Context) (map[string]v1.CommonModelAttribute, error)
}

type modelAttributesStore struct {
	db    *gorm.DB
	mgo   *db.MongoOptions
	cache cache.Cache
	es    *elastic.Client
}

func newModelAttributesStore(ds *DataStore) *modelAttributesStore {
	return &modelAttributesStore{
		mgo:   ds.Mgo,
		db:    ds.Db,
		cache: ds.Cache,
		es:    ds.ES,
	}
}

func (m *modelAttributesStore) GetModelRules(ctx context.Context) ([]v1.ModelRules, error) {
	zap.L().Debug("GetModelRules Store Called")
	span, _ := apm.StartSpan(ctx, "GetModelRules", "store")
	defer span.End()

	var rules []v1.ModelRules
	err := m.db.Table("model_attr_rules").Find(&rules).Error
	if err != nil {
		zap.L().Error("GetModelRules Error", zap.Error(err))
		return nil, err
	}
	return rules, nil
}

// DeleteModelAttributes 根据code删除模型属性
func (m *modelAttributesStore) DeleteModelAttributes(ctx context.Context, operator string, attrCode *v1.InstanceFilter) error {
	zap.L().Debug("DeleteModelAttributesStore Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelAttributes", "store")
	defer span.End()

	// 删除模型属性
	if err := m.mgo.GetCollection("model_attr").Remove(ctx, bson.M{"code": attrCode.Code}); err != nil {
		return err
	}

	// 在store层刷新cache缓存，删除老的，再重新生成一遍
	if err := m.refreshAttrListCache(ctx); err != nil {
		zap.L().Error("refreshAttrCodeListCache Error", zap.Error(err))
	}
	return nil
}

// UpdateModelAttributes 更新模型属性
func (m *modelAttributesStore) UpdateModelAttributes(ctx context.Context, attr map[string]interface{}) error {
	zap.L().Debug("UpdateModelAttributes Store Called")
	// 首先把约定禁止更新的字段剔除，防止用户通过接口修改这些字段
	attrCode := attr["code"].(string)
	delete(attr, "code")
	delete(attr, "model_code")
	delete(attr, "type_name")
	// 遍历用户提交上来的数据，然后把每个字段作为一个要更新的项目存在updateData中去
	var updateData []v1.UpdateAttrTmpl
	for k, v := range attr {
		updateData = append(updateData, v1.UpdateAttrTmpl{
			Field: k,
			Value: v,
		})
	}
	// 构造一个更新的数据结构，key即为要更新的字段，value即为要更新的值
	updatedData := bson.M{}
	for _, data := range updateData {
		updatedData[data.Field] = data.Value
	}
	// 更新update_at
	updatedData["update_at"] = clock.NowInNano()
	// 写入MongoDB
	if err := m.mgo.GetCollection("model_attr").
		UpdateOne(
			ctx,
			bson.M{"code": attrCode},
			bson.M{"$set": updatedData}); err != nil {
		return err
	}
	return nil
}

// GetModelAttributesByCustomFilter 根据自定义的filter获取模型属性，返回模型数组
func (m *modelAttributesStore) GetModelAttributesByCustomFilter(ctx context.Context, filter, selectParam bson.M, opts ...opts.FindOptions) ([]v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttributesByCustomFilter Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttributesByCustomFilterStore", "store")
	defer span.End()

	var batch []v1.CommonModelAttribute
	err := m.mgo.GetCollection("model_attr").
		Find(ctx, filter, opts...).
		Sort("-create_at"). // 默认按照创建时间倒序排列
		Select(selectParam).
		All(&batch)
	if err != nil {
		return nil, err
	}
	return batch, nil
}

func (m *modelAttributesStore) GetModelAttributeByID(ctx context.Context, id string) (*v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttributeByID Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeByIDStore", "store")
	defer span.End()

	var attr v1.CommonModelAttribute
	err := m.mgo.GetCollection("model_attr").
		Find(ctx, bson.M{"_id": id}).
		One(&attr)
	if err != nil {
		return nil, err
	}
	return &attr, nil
}

// GetModelAttributeCodeList 查询所有模型属性的code列表
func (m *modelAttributesStore) GetModelAttributeCodeList(ctx context.Context) ([]string, error) {
	zap.L().Debug("GetModelAttributeCodeList Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeCodeListStore", "store")
	defer span.End()

	var (
		batch = make([]v1.CommonModelAttribute, 0)
		attrs = make([]string, 0)
		raw   = make([]byte, 0)
		err   error
	)

	// 1、先查询缓存
	raw, err = m.cache.Get(ModelAttrList.String())
	if err != nil {
		zap.L().Error("GetModelAttributeCodeList From Cache Failed", zap.Error(err))
		// 2、缓存没有，查询数据库同时更新缓存
		raw, err = m.SetModelAttributesCache(ctx)
		if err != nil {
			return nil, err
		}

		// 反序列化
		if err = json.Unmarshal(raw, &batch); err != nil {
			return nil, err
		}

		for _, attr := range batch {
			attrs = append(attrs, attr.Code)
		}

		return attrs, nil
	} else {
		// 3、缓存有，直接返回
		if err = json.Unmarshal(raw, &batch); err != nil {
			return nil, err
		}
		for _, attr := range batch {
			attrs = append(attrs, attr.Code)
		}
		return attrs, nil
	}
}

func (m *modelAttributesStore) GetModelAttrList(ctx context.Context) ([]v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttrList Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttrListStore", "store")
	defer span.End()

	var (
		cacheData = make([]byte, 0)
		err       error
	)

	// 从缓存中查询数据
	cacheData, err = m.cache.Get(ModelAttrList.String())
	if err != nil {
		zap.L().Error("GetModelAttrList From Cache Failed", zap.Error(err))
		// 无缓存，查询数据库更新
		cacheData, err = m.SetModelAttributesCache(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		zap.L().Debug("GetModelAttrList From Cache Success")
	}

	attrs := make([]v1.CommonModelAttribute, 0)
	if err = json.Unmarshal(cacheData, &attrs); err != nil {
		return nil, err
	}

	return attrs, nil
}

// GetModelAttrMap 获取所有模型属性的map，其中key为指定的key，value为属性的结构体
func (m *modelAttributesStore) GetModelAttrMap(ctx context.Context, key string) (map[string]v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttrMap Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttrMapStore", "store")
	defer span.End()

	var (
		cacheData = make([]byte, 0)
		err       error
	)

	// 从缓存中查询数据
	cacheData, err = m.cache.Get(ModelAttrList.String())
	if err != nil {
		zap.L().Error("GetModelAttrMap From Cache Failed", zap.Error(err))
		// 无缓存，查询数据库更新
		cacheData, err = m.SetModelAttributesCache(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		zap.L().Debug("GetModelAttrMap From Cache Success")
	}

	var attrs []v1.CommonModelAttribute
	if err = json.Unmarshal(cacheData, &attrs); err != nil {
		return nil, err
	}

	mData := make(map[string]v1.CommonModelAttribute)
	switch key {
	case "code":
		for _, attr := range attrs {
			mData[attr.Code] = attr
		}
	case "id":
		for _, attr := range attrs {
			mData[attr.ID] = attr
		}
	default:
		return nil, errno.ErrParameterInvalid.Add("key 只能是code或者id")
	}

	return mData, nil
}

// GetSingleModelAttributeByCustomFilter 根据自定义的filter获取单个模型属性，仅返回单个属性，因此按需使用
// 如果需要返回的属性不唯一，需要使用GetModelAttributesByCustomFilter方法，这个返回的是数组
func (m *modelAttributesStore) GetSingleModelAttributeByCustomFilter(ctx context.Context, filter, selectParam bson.M) (*v1.CommonModelAttribute, error) {
	zap.L().Debug("GetSingleModelAttributeByCustomFilter Store Called")
	span, ctx := apm.StartSpan(ctx, "GetSingleModelAttributeByCustomFilterStore", "store")
	defer span.End()

	attr := &v1.CommonModelAttribute{}
	err := m.mgo.GetCollection("model_attr").
		Find(ctx, filter).
		Select(selectParam).
		One(attr)
	if err != nil {
		return nil, err
	}
	return attr, nil
}

func (m *modelAttributesStore) GetOneModelAttrMapByCustomFilter(ctx context.Context, filter, selectParam bson.M) (map[string]interface{}, error) {
	zap.L().Debug("GetOneModelAttrMapByCustomFilter Store Called")
	span, ctx := apm.StartSpan(ctx, "GetOneModelAttrMapByCustomFilterStore", "store")
	defer span.End()

	var attr map[string]interface{}
	err := m.mgo.GetCollection("model_attr").
		Find(ctx, filter).
		Select(selectParam).
		One(&attr)
	if err != nil {
		return nil, err
	}
	return attr, nil
}

// GetModelAttributeList 根据model_code获取模型属性列表
func (m *modelAttributesStore) GetModelAttributeList(ctx context.Context, code string, selectParam bson.M) ([]map[string]interface{}, error) {
	zap.L().Debug("GetModelAttributeListStore Called")
	batch := make([]map[string]interface{}, 0)
	// 先把除了关联关系字段之外的所有字段拿出来
	commonFilter := bson.M{
		"model_code":     code,
		"attrs.rel_type": bson.M{"$ne": 2},
	}
	err := m.mgo.GetCollection("model_attr").
		Find(ctx, commonFilter).
		Select(selectParam). // 将_id设置为0，即可以不返回_id字段
		All(&batch)
	if err != nil {
		return nil, err
	}
	return batch, nil
}

// GetModelAttributeList2 根据model_code获取模型属性列表
func (m *modelAttributesStore) GetModelAttributeList2(ctx context.Context, code string, selectParam, filter bson.M) ([]v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttributeListStore Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeList2Store", "store")
	defer span.End()

	batch := make([]v1.CommonModelAttribute, 0)
	// 先把除了关联关系字段之外的所有字段拿出来
	commonFilter := bson.M{
		"model_code":     code,
		"attrs.rel_type": bson.M{"$ne": 2},
	}
	for k, v := range filter {
		commonFilter[k] = v
	}
	err := m.mgo.GetCollection("model_attr").
		Find(ctx, commonFilter).
		Select(selectParam). // 将_id设置为0，即可以不返回_id字段
		Sort("create_at").
		All(&batch)
	if err != nil {
		return nil, err
	}
	return batch, nil
}

// GetModelAttrMapping 获取模型属性映射，不含关系字段
// key → attr.Code
// value → v1.CommonModelAttribute
func (m *modelAttributesStore) GetModelAttrMapping(ctx context.Context) (map[string]v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttrMapping Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttrMappingStore", "store")
	defer span.End()

	// 初始化一个空的attr map
	modelAttrMapping := make(map[string]v1.CommonModelAttribute)

	// 优先查询缓存
	raw, err := m.cache.Get(ModelAttrMapping.String())
	if err != nil {
		zap.L().Info("从缓存中获取模型属性映射失败", zap.Error(err))

		// 重新从db中查询返回，并同时更新缓存
		attrs := make([]v1.CommonModelAttribute, 0)
		if err := m.mgo.GetCollection("model_attr").
			Find(ctx, bson.M{"type_name": bson.M{"$ne": "relationship"}}).
			All(&attrs); err != nil {
			zap.L().Error("GetModelAttrMapping Failed", zap.Error(err))
			return nil, err
		}

		for _, attr := range attrs {
			if _, ok := modelAttrMapping[attr.Code]; !ok {
				modelAttrMapping[attr.Code] = attr
			} else {
				zap.L().Error("查询到重复的模型属性",
					zap.String("model_code", attr.ModelCode),
					zap.String("code", attr.Code))
			}
		}

		b, err := kjson.Marshal(modelAttrMapping)
		if err != nil {
			zap.L().Error("Marshal ModelAttrMapping Failed", zap.Error(err))
			return nil, err
		}

		if err := m.cache.Set(ModelAttrMapping.String(), b); err != nil {
			zap.L().Error("Set ModelAttrMapping Cache Failed", zap.Error(err))
			return nil, err
		}

		zap.L().Debug("Set ModelAttrMapping Cache Success")
		return modelAttrMapping, nil
	} else {
		// 可以从缓存中获取到
		if err := kjson.Unmarshal(raw, &modelAttrMapping); err != nil {
			zap.L().Error("Unmarshal ModelAttrMapping Failed", zap.Error(err))
			return nil, err
		}
		return modelAttrMapping, nil
	}
}

// CreateModelAttributes 创建模型属性
func (m *modelAttributesStore) CreateModelAttributes(ctx context.Context, operator string, attrs any) (*v1.CommonModelAttribute, error) {
	zap.L().Debug("CreateModelAttribute Store Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelAttributeStore", "store")
	defer span.End()

	insertRes, err := m.mgo.GetCollection(ModelAttrCollection).InsertOne(ctx, attrs)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			zap.L().Debug("CreateModelAttributeStore Failed, Error: " + err.Error())
			return nil, errno.ErrDuplicateKey.Add(err.Error())
		}
		zap.L().Debug("CreateModelAttributeStore Failed, Error: " + err.Error())
		return nil, err
	}

	// 创建成功后更新cache，同样也是先删除一遍然后再更新一遍
	if err := m.refreshAttrListCache(ctx); err != nil {
		zap.L().Error("refreshAttrListCache Error", zap.Error(err))
	}

	var commonModelAttr *v1.CommonModelAttribute
	bytes, err := json.Marshal(attrs)
	if err != nil {
		zap.L().Error("Marshal CommonModelAttribute Failed", zap.Error(err))
		return nil, err
	}
	if err := json.Unmarshal(bytes, &commonModelAttr); err != nil {
		zap.L().Error("Unmarshal CommonModelAttribute Failed", zap.Error(err))
		return nil, err
	}
	commonModelAttr.ID = utils.ToString(insertRes.InsertedID)

	// audit log
	// ma, err := convert.StructToMap(attrs)
	// if err != nil {
	// 	zap.L().Error("Convert Struct to map failed", zap.Error(err))
	// 	return nil, err
	// }

	// ma["id"] = utils.ToString(insertRes.InsertedID)

	// auditLog, err := audit.GenerateAuditLog(ctx, nil, ma, nil, v12.ActionCreate, v12.ModelAttributeRes, operator, insertRes.InsertedID.(string), "")
	// if err != nil {
	// 	return nil, err
	// }
	//
	// if err = audit.SaveAuditLog(ctx, m.mgo, auditLog); err != nil {
	// 	return nil, err
	// }

	// 目前先不对关系字段做索引
	if commonModelAttr.TypeName != "relationship" {
		// 更新Elasticsearch的mapping
		if err := m.UpdateESMapping(ctx, "model_data", commonModelAttr.Code); err != nil {
			zap.L().Error("更新ES Mapping失败", zap.Error(err))
			return nil, err
		}
	}

	return commonModelAttr, nil
}

func (m *modelAttributesStore) UpdateESMapping(ctx context.Context, index, attrName string) error {
	zap.L().Debug("UpdateESMapping Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateESMappingStore", "store")
	defer span.End()

	resp, err := m.es.PutMapping().
		Index(index).
		BodyString(fmt.Sprintf(v1.ModelAttrESTmpl, attrName)).
		Do(ctx)

	if err != nil {
		zap.L().Error("更新Mapping异常", zap.Error(err))
		return err
	}

	if !resp.Acknowledged {
		zap.L().Error("更新Mapping失败", zap.Any("response", resp))
		return errno.ErrPutESMapping.Add("更新Mapping失败")
	}
	return nil
}

// refreshAttrListCache 刷新模型属性列表的缓存
func (m *modelAttributesStore) refreshAttrListCache(ctx context.Context) (err error) {
	zap.L().Debug("refreshAttrListCache Store Called")
	span, ctx := apm.StartSpan(ctx, "refreshAttrListCacheStore", "store")
	defer span.End()

	// 先删除一遍缓存的，然后再重新查询一遍
	_ = m.cache.Delete(ModelAttrList.String())

	// 重新查询后更新缓存
	_, err = m.SetModelAttributesCache(ctx)
	if err != nil {
		return err
	}

	return
}
