package store

import (
	"context"

	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"
)

type ModelAttrGroupStore interface {
	// CreateModelAttrGroup 创建模型属性分组
	CreateModelAttrGroup(ctx context.Context, operator string, mag *v1.ModelAttrGroup) (*v1.ModelAttrGroup, error)
	// GetModelAttrGroupList 获取模型属性分组列表
	GetModelAttrGroupList(ctx context.Context, modelCode string, filter bson.M) ([]v1.ModelAttrGroup, error)
	// GetModelAttrGroupByCode 根据code获取模型属性分组，至多返回一个属性分组
	GetModelAttrGroupByCode(ctx context.Context, code string) (*v1.ModelAttrGroup, error)
	// GetModelAttrGroupByFilter 根据filter获取模型属性分组
	GetModelAttrGroupByFilter(ctx context.Context, filter bson.M) ([]*v1.ModelAttrGroup, error)
	// UpdateModelAttrGroup 更新模型属性分组
	UpdateModelAttrGroup(ctx context.Context, operator string, magInstance *v1.ModelAttrGroup) error
	// DeleteModelAttrGroup 删除模型属性分组
	DeleteModelAttrGroup(ctx context.Context, operator string, mag *v1.InstanceFilter) error
}

type modelAttrGroupStore struct {
	mgo *db.MongoOptions
}

func newModelAttrGroupStore(ds *DataStore) ModelAttrGroupStore {
	return &modelAttrGroupStore{
		mgo: ds.Mgo,
	}
}

var _ ModelAttrGroupStore = (*modelAttrGroupStore)(nil)

// CreateModelAttrGroup 创建模型属性分组
func (m *modelAttrGroupStore) CreateModelAttrGroup(ctx context.Context, operator string, mag *v1.ModelAttrGroup) (*v1.ModelAttrGroup, error) {
	zap.L().Debug("CreateModelAttrGroup Store Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelAttrGroupStore", "Store")
	defer span.End()

	// 如果设置了，以设置的为主，如果没设置则自动生成
	if mag.Code == "" {
		mag.Code = mag.AutoSetCode(mag.Name)
	}
	res, err := m.mgo.GetCollection("model_attr_group").InsertOne(ctx, mag)
	if err != nil {
		return nil, err
	}

	mag.ID = utils.ToString(res.InsertedID)

	return mag, nil
}

// GetModelAttrGroupList 获取模型属性分组列表
func (m *modelAttrGroupStore) GetModelAttrGroupList(ctx context.Context, modelCode string, filter bson.M) ([]v1.ModelAttrGroup, error) {
	zap.L().Debug("GetModelAttrGroupList Store Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttrGroupListStore", "Store")
	defer span.End()

	var batch []v1.ModelAttrGroup
	filter["model_code"] = modelCode
	err := m.mgo.GetCollection("model_attr_group").
		Find(ctx, filter).
		Sort("create_at").
		All(&batch)
	if err != nil {
		return nil, err
	}
	return batch, nil
}

// GetModelAttrGroupByCode 根据code获取模型属性分组，至多返回一个属性分组
func (m *modelAttrGroupStore) GetModelAttrGroupByCode(ctx context.Context, code string) (*v1.ModelAttrGroup, error) {
	var mag v1.ModelAttrGroup
	err := m.mgo.GetCollection("model_attr_group").Find(ctx, bson.M{"code": code}).One(&mag)
	if err != nil {
		return nil, err
	}
	return &mag, nil
}

func (m *modelAttrGroupStore) GetModelAttrGroupByFilter(ctx context.Context, filter bson.M) ([]*v1.ModelAttrGroup, error) {
	var mags []*v1.ModelAttrGroup
	if err := m.mgo.GetCollection("model_attr_group").Find(ctx, filter).All(&mags); err != nil {
		return nil, err
	}
	return mags, nil
}

// UpdateModelAttrGroup 更新模型属性分组
func (m *modelAttrGroupStore) UpdateModelAttrGroup(ctx context.Context, operator string, magInstance *v1.ModelAttrGroup) error {

	src := make(map[string]interface{})
	src["name"] = magInstance.Name

	if err := m.mgo.GetCollection("model_attr_group").
		UpdateOne(
			ctx,
			bson.M{"code": magInstance.Code},
			bson.M{"$set": bson.M{
				"name":      magInstance.Name,
				"update_at": magInstance.UpdateAt,
			}},
		); err != nil {
		return errno.ErrDataNotExists.Add(err.Error())
	}

	return nil
}

// DeleteModelAttrGroup 删除模型属性分组
func (m *modelAttrGroupStore) DeleteModelAttrGroup(ctx context.Context, operator string, mag *v1.InstanceFilter) error {
	err := m.mgo.GetCollection("model_attr_group").Remove(ctx, bson.M{"code": mag.Code})
	if err != nil {
		return err
	}
	return nil
}
