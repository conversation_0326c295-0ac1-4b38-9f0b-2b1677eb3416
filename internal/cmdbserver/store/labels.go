package store

import (
	"context"
	"errors"
	"fmt"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"
	kjson "ks-knoc-server/pkg/json"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const (
	CacheLabelKeyPrefix   = "cmdb_label_key"
	CacheLabelValuePrefix = "cmdb_label_value"
)

type LabelStore interface {
	// LabelExists 查看某个label是否存在
	// 返回值第一个参数是是否存在，第二个参数是已经存在的kv键值对，第三个返回值是error
	LabelExists(ctx context.Context, req *v1.CreateLabelRequest) (bool, []string, error)

	// CreateLabelKey 创建labelKey
	CreateLabel<PERSON>ey(ctx context.Context, name, describe string) (string, error)
	// GetLabelKeys 基于key名称获取label的key列表, 支持分页操作
	GetLabelKeys(ctx context.Context, keys []string, page, pageSize int64) ([]*v1.LabelKey, error)
	// GetLabelKeysCount 基于key名称获取label的key数量, 支持分页操作
	GetLabelKeysCount(ctx context.Context, keys []string) (int64, error)
	// GetSpecifiedLabelKeyByName 根据key的名称获取指定的labelKey
	GetSpecifiedLabelKeyByName(ctx context.Context, key string) (*v1.LabelKey, error)
	// GetSpecifiedLabelKeyByID 根据id获取指定的labelKey, 注意这里的id是label_key这个collection集合中的数据id
	GetSpecifiedLabelKeyByID(ctx context.Context, kid string) (*v1.LabelKey, error)
	// DeleteLabelKeyByID 根据LabelKey的id删除对应的label_key
	DeleteLabelKeyByID(ctx context.Context, kid string) error
	// UpdateLabelKey 更新LabelKey信息
	UpdateLabelKey(ctx context.Context, keyID string, req *v1.UpdateLabelKeyRequest) error

	// CreateLabelValue 创建labelValue
	CreateLabelValue(ctx context.Context, keyID, value string) error
	// GetLabelValuesByKeyIDs 根据key的id列表获取labelValue列表
	GetLabelValuesByKeyIDs(ctx context.Context, kids []string) ([]*v1.LabelValue, error)
	// GetLabelValue 根据key的id和value值获取labelValue
	GetLabelValue(ctx context.Context, keyID, value string) (*v1.LabelValue, error)
	// GetLabelValueByID 根据id获取labelValue, 注意这里的id是label_value这个collection集合中的数据id
	GetLabelValueByID(ctx context.Context, vid []string) ([]*v1.LabelValue, error)
	// UpdateLabelValue 根据id更新labelValue信息，目前只允许更新labelValue的value
	UpdateLabelValue(ctx context.Context, valueID string, req *v1.UpdateLabelValueRequest) error
	// DeleteLabel 删除label, 其实删除的是label_value中的数据，因为删除一个标签，可能对应的key下还有其他的value
	DeleteLabel(ctx context.Context, labelID string) error
}

type labelStore struct {
	mgo   *db.MongoOptions
	cache cache.Cache
}

func newLabelStore(ds *DataStore) *labelStore {
	return &labelStore{mgo: ds.Mgo, cache: ds.Cache}
}

var _ LabelStore = (*labelStore)(nil)

func (s *labelStore) UpdateLabelCache(rType, action, lid string, data any) error {
	// rType资源类型目前只有key和value两种
	var cacheKey string
	switch rType {
	case "key":
		cacheKey = fmt.Sprintf("%s:%s", CacheLabelKeyPrefix, lid)
	case "value":
		cacheKey = fmt.Sprintf("%s:%s", CacheLabelValuePrefix, lid)
	default:
		return errno.InternalServerError.Add("不支持的label资源类型")
	}

	switch action {
	case "create", "update":
		b, err := kjson.Marshal(data)
		if err != nil {
			return err
		}
		if err := s.cache.Set(cacheKey, b); err != nil {
			return err
		}
	case "delete":
		if err := s.cache.Delete(cacheKey); err != nil {
			return err
		}
	default:
		return errno.InternalServerError.Add("不支持的label操作类型")
	}

	return nil
}

func (s *labelStore) CreateLabelKey(ctx context.Context, name, describe string) (string, error) {
	span, ctx := apm.StartSpan(ctx, "CreateLabelKey", "store")
	defer span.End()

	var insertedID string

	data := &v1.LabelKey{
		Name:     name,
		Describe: describe,
		CreateAt: time.Now().Unix(),
		UpdateAt: time.Now().Unix(),
	}

	result, err := s.mgo.GetCollection(v1.LabelKeyColName).InsertOne(ctx, data)
	if err != nil {
		return "", err
	}

	data.ID = utils.ToString(result.InsertedID)
	insertedID = data.ID
	return insertedID, nil
}

func (s *labelStore) GetLabelKeys(ctx context.Context, keys []string, page, pageSize int64) ([]*v1.LabelKey, error) {
	span, ctx := apm.StartSpan(ctx, "GetLabelKeys", "store")
	defer span.End()

	var (
		labelKeys = make([]*v1.LabelKey, 0)
		filter    = bson.M{}
	)

	if len(keys) > 0 {
		filter = bson.M{"name": bson.M{"$in": keys}}
	}

	if err := s.mgo.GetCollection(v1.LabelKeyColName).
		Find(ctx, filter).
		Sort("-create_at").
		Skip((page - 1) * pageSize).
		Limit(pageSize).
		All(&labelKeys); err != nil {
		return nil, err
	}
	return labelKeys, nil
}

func (s *labelStore) GetSpecifiedLabelKeyByName(ctx context.Context, key string) (*v1.LabelKey, error) {
	span, ctx := apm.StartSpan(ctx, "GetSpecifiedLabelKey", "store")
	defer span.End()

	var labelKey v1.LabelKey
	if err := s.mgo.GetCollection(v1.LabelKeyColName).Find(ctx, bson.M{"name": key}).One(&labelKey); err != nil {
		return nil, err
	}
	return &labelKey, nil
}

func (s *labelStore) GetSpecifiedLabelKeyByID(ctx context.Context, kid string) (*v1.LabelKey, error) {
	span, ctx := apm.StartSpan(ctx, "GetSpecifiedLabelKeyByID", "store")
	defer span.End()

	var labelKey v1.LabelKey
	if err := s.mgo.GetCollection(v1.LabelKeyColName).Find(ctx, bson.M{"_id": kid}).One(&labelKey); err != nil {
		return nil, err
	}
	return &labelKey, nil
}

func (s *labelStore) GetLabelKeysCount(ctx context.Context, keys []string) (int64, error) {
	span, ctx := apm.StartSpan(ctx, "GetLabelKeysCount", "store")
	defer span.End()

	filter := bson.M{}

	if len(keys) > 0 {
		filter = bson.M{"name": bson.M{"$in": keys}}
	}

	cnt, err := s.mgo.GetCollection(v1.LabelKeyColName).Find(ctx, filter).Count()
	if err != nil {
		return 0, err
	}
	return cnt, nil
}

func (s *labelStore) UpdateLabelKey(ctx context.Context, keyID string, req *v1.UpdateLabelKeyRequest) error {
	span, ctx := apm.StartSpan(ctx, "UpdateLabelKey", "store")
	defer span.End()

	var labelKey v1.LabelKey
	if err := s.mgo.GetCollection(v1.LabelKeyColName).Find(ctx, bson.M{"_id": keyID}).One(&labelKey); err != nil {
		return err
	}

	now := time.Now().Unix()

	labelKey.Name = req.Name
	labelKey.Describe = req.Describe
	labelKey.UpdateAt = now
	if err := s.mgo.GetCollection(v1.LabelKeyColName).UpdateId(ctx, keyID, bson.M{"$set": labelKey}); err != nil {
		return err
	}

	if err := s.UpdateLabelCache("key", "update", keyID, labelKey); err != nil {
		return err
	}
	return nil
}

func (s *labelStore) GetLabelValuesByKeyIDs(ctx context.Context, kids []string) ([]*v1.LabelValue, error) {
	span, ctx := apm.StartSpan(ctx, "GetLabelValues", "store")
	defer span.End()

	labelValues := make([]*v1.LabelValue, 0)
	if err := s.mgo.GetCollection(v1.LabelValueColName).Find(ctx, bson.M{
		"key_id": bson.M{"$in": kids},
	}).All(&labelValues); err != nil {
		return nil, err
	}

	return labelValues, nil
}

func (s *labelStore) GetLabelValueByID(ctx context.Context, vid []string) ([]*v1.LabelValue, error) {
	span, ctx := apm.StartSpan(ctx, "GetLabelValueByID", "store")
	defer span.End()

	labelValues := make([]*v1.LabelValue, 0)
	if err := s.mgo.GetCollection(v1.LabelValueColName).
		Find(ctx, bson.M{"_id": bson.M{"$in": vid}}).All(&labelValues); err != nil {
		return nil, err
	}
	return labelValues, nil
}

func (s *labelStore) GetLabelValue(ctx context.Context, keyID, value string) (*v1.LabelValue, error) {
	zap.L().Debug("GetLabelValue Store Called")
	span, ctx := apm.StartSpan(ctx, "GetLabelValue", "store")
	defer span.End()

	var lv v1.LabelValue
	if err := s.mgo.GetCollection(v1.LabelValueColName).Find(ctx, bson.M{"key_id": keyID, "value": value}).One(&lv); err != nil {
		return nil, err
	}
	return &lv, nil
}

func (s *labelStore) CreateLabelValue(ctx context.Context, keyID, value string) error {
	span, ctx := apm.StartSpan(ctx, "CreateLabelValue", "store")
	defer span.End()

	if _, err := s.mgo.GetCollection(v1.LabelValueColName).InsertOne(ctx, &v1.LabelValue{
		KeyID:    keyID,
		Value:    value,
		CreateAt: time.Now().Unix(),
		UpdateAt: time.Now().Unix(),
	}); err != nil {
		return err
	}
	return nil
}

func (s *labelStore) DeleteLabelKeyByID(ctx context.Context, kid string) error {
	span, ctx := apm.StartSpan(ctx, "DeleteLabelKeyByID", "store")
	defer span.End()

	t, err := s.mgo.StartTransaction()
	if err != nil {
		return err
	}

	t.Wrapper(func(sessionCtx context.Context) error {
		if err := s.mgo.GetCollection(v1.LabelKeyColName).Remove(sessionCtx, bson.M{"_id": kid}); err != nil {
			return err
		}

		if err := s.UpdateLabelCache("key", "delete", kid, nil); err != nil {
			return err
		}

		return nil
	})

	if err := t.Do(ctx); err != nil {
		return err
	}

	return nil
}

func (s *labelStore) LabelExists(ctx context.Context, req *v1.CreateLabelRequest) (bool, []string, error) {
	span, ctx := apm.StartSpan(ctx, "LabelExists", "store")
	defer span.End()

	// 首先看看labelKey能不能查到
	k, err := s.GetSpecifiedLabelKeyByName(ctx, req.LabelKey)

	// 这里查不到数据的这个Error我认为是正常的情况
	if err != nil {
		// 如果说错误不为空，但是错误是mongo.ErrNoDocuments，说明labelKey不存在，这个时候，就不用往下查了，直接返回不存在即可，err为nil
		if errors.Is(err, mongo.ErrNoDocuments) {
			return false, nil, nil
		}
		// 此时虽然返回也是false，但是err不为空，说明查询过程中出现了其他问题，需要返回err
		return false, nil, err
	}

	// 如果可以获取到labelKey, 再看看对应的labelValue能不能查到，对应的labelValue是一个数组
	labelValues, err := s.GetLabelValuesByKeyIDs(ctx, []string{k.ID})
	if err != nil {
		return false, nil, err
	}

	lvs := make([]string, 0)
	for _, v := range labelValues {
		lvs = append(lvs, v.Value)
	}

	existLabels := make([]string, 0)
	for _, v := range req.LabelValues {
		if utils.FindStrInStringSlice(lvs, v) {
			existLabels = append(existLabels, fmt.Sprintf(`%s=%s`, k.Name, v))
		}
	}

	return len(existLabels) > 0, existLabels, nil
}

func (s *labelStore) DeleteLabel(ctx context.Context, labelID string) error {
	span, ctx := apm.StartSpan(ctx, "DeleteLabel", "store")
	defer span.End()

	if err := s.mgo.GetCollection(v1.LabelValueColName).Remove(ctx, bson.M{"_id": labelID}); err != nil {
		return err
	}
	return nil
}

func (s *labelStore) UpdateLabelValue(ctx context.Context, valueID string, req *v1.UpdateLabelValueRequest) error {
	zap.L().Debug("UpdateLabelValue Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateLabelValue", "store")
	defer span.End()

	if err := s.mgo.GetCollection(v1.LabelValueColName).UpdateId(ctx, valueID, bson.M{"$set": bson.M{
		"value":     req.Name,
		"update_at": time.Now().Unix(),
	}}); err != nil {
		return err
	}
	return nil
}
