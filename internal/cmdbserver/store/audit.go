package store

import (
	"context"

	"ks-knoc-server/internal/cmdbserver/constants"
	"ks-knoc-server/internal/common/audit"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"

	"github.com/olivere/elastic/v7"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type AuditStore interface {
	// GetAuditRecordList 获取审计日志列表
	GetAuditRecordList(ctx context.Context, request apiv1.AuditRecordRequest, page, pageSize int64) (*v1.AuditRecordResponse, error)
	// SaveAuditLog 保存审计日志
	SaveAuditLog(ctx context.Context, auditLog *audit.AuditLog) error
	// GetAuditRecordDetail 获取审计日志详情
	GetAuditRecordDetail(ctx context.Context, request apiv1.AuditRecordRequestByID, page, pageSize int64) (*v1.AuditRecordResponse, error)
}

type auditStore struct {
	mgo *db.MongoOptions
	es  *elastic.Client
}

func newAuditStore(ds *DataStore) *auditStore {
	return &auditStore{
		mgo: ds.Mgo,
		es:  ds.ES,
	}
}

func (m *auditStore) GetAuditRecordList(ctx context.Context, request apiv1.AuditRecordRequest, page, pageSize int64) (*v1.AuditRecordResponse, error) {
	zap.L().Debug("GetAuditRecordList Store called")

	span, _ := apm.StartSpan(ctx, "GetAuditRecordList", "sql")
	defer span.End()

	var audits []v1.Audit
	coll := m.mgo.GetCollection(constants.TableNameAuditLog)
	filter := bson.M{}

	// 支持不选资源类型
	all := false
	for _, v := range request.ResourceTypes {
		if v == "all" {
			all = true
			break
		}
	}

	if !all {
		filter["resource_type"] = bson.M{"$in": request.ResourceTypes}
	}

	// 时间过滤
	if request.StartTime != 0 && request.EndTime != 0 {
		filter["ts"] = bson.M{"$gte": request.StartTime, "$lte": request.EndTime}
	}

	// 模糊搜索
	if request.Keyword != "" {
		filter["operation"] = bson.M{"$regex": request.Keyword, "$options": "i"}
	}

	if request.UniqueId != "" {
		filter["unique_id"] = bson.M{"$eq": request.UniqueId}
	}

	if request.ModelCode != "" {
		filter["model_code"] = bson.M{"$eq": request.ModelCode}
	}

	if err := coll.
		Find(ctx, filter).
		Sort("-ts").
		Skip((page - 1) * pageSize).
		Limit(pageSize).All(&audits); err != nil {
		return nil, err
	}

	total, err := coll.Find(ctx, filter).Count()
	if err != nil {
		return nil, err
	}
	return &v1.AuditRecordResponse{
		Total:       total,
		Pages:       page,
		PageSize:    pageSize,
		CurrentPage: page,
		Data:        audits,
	}, nil
}

func (m *auditStore) GetAuditRecordDetail(ctx context.Context, request apiv1.AuditRecordRequestByID, page, pageSize int64) (*v1.AuditRecordResponse, error) {
	zap.L().Debug("GetAuditRecordDetail Store called")
	span, _ := apm.StartSpan(ctx, "GetAuditRecordDetail", "sql")
	defer span.End()

	coll := m.mgo.GetCollection(constants.TableNameAuditLog)
	filter := bson.M{}
	filter["unique_id"] = request.UniqueId
	filter["resource_type"] = audit.ResourceDetailRes

	// 时间过滤
	if request.StartTime != 0 && request.EndTime != 0 {
		filter["ts"] = bson.M{"$gte": request.StartTime, "$lte": request.EndTime}
	}

	// 模糊搜索
	if request.Keyword != "" {
		filter["operation"] = bson.M{"$regex": request.Keyword, "$options": "i"}
	}

	var audits []v1.Audit
	if err := coll.
		Find(ctx, filter).
		Sort("-ts").
		Skip((page - 1) * pageSize).
		Limit(pageSize).All(&audits); err != nil {
		return nil, err
	}

	total, err := coll.Find(ctx, filter).Count()
	if err != nil {
		return nil, err
	}

	return &v1.AuditRecordResponse{
		Total:       total,
		Pages:       page,
		PageSize:    pageSize,
		CurrentPage: page,
		Data:        audits,
	}, nil
}

func (m *auditStore) SaveAuditLog(ctx context.Context, auditLog *audit.AuditLog) error {
	span, ctx := apm.StartSpan(ctx, "saveAuditLog", "sql")
	defer span.End()

	if _, err := m.mgo.GetCollection(constants.TableNameAuditLog).InsertOne(ctx, auditLog); err != nil {
		return err
	}

	return nil
}
