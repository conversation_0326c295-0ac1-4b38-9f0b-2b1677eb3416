package store

import (
	"ks-knoc-server/internal/common/db"

	"github.com/olivere/elastic/v7"
)

type CommonStore interface {
	StartTransaction() (*db.MongoTransaction, error)
}

type commonStore struct {
	mgo *db.MongoOptions
	es  *elastic.Client
}

var _ CommonStore = (*commonStore)(nil)

func newCommonStore(ds *DataStore) *commonStore {
	return &commonStore{
		mgo: ds.Mgo,
		es:  ds.ES,
	}
}

// StartTransaction 开启事务
func (c *commonStore) StartTransaction() (*db.MongoTransaction, error) {
	t, err := c.mgo.StartTransaction()
	if err != nil {
		return nil, err
	}
	return t, nil
}
