package store

type Key string

func (k Key) String() string {
	return string(k)
}

const (
	ModelAttrList     Key = "model_attr_list"
	ModelAttrMapping  Key = "model_attr_mapping"
	ModelList         Key = "model_list"
	ModelRelationList Key = "model_relation_list"
)

const (
	ModelCollection             = "model"
	ModelGroupCollection        = "model_group"
	ModelAttrCollection         = "model_attr"
	ModelAttrGroupCollection    = "model_attr_group"
	ModelDataCollection         = "model_data"
	ModelDataTreePathCollection = "model_data_treepath"
	ModelRelationCollection     = "model_relation"
	LabelKeyCollection          = "label_key"
	LabelValueCollection        = "label_value"
	AuditLogCollection          = "audit_logs"
)
