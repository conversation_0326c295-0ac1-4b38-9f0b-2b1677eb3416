package store

import (
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/zabbix"

	"github.com/olivere/elastic/v7"
	"gorm.io/gorm"

	"ks-knoc-server/internal/common/db"
)

// Factory 类似Service这一部分的逻辑，还是套娃Factory中定义了一组初始化的方法
// 每一个方法返回的的都是一个接口的定义，每一个接口的定义里囊括了操作模型的CURD
type Factory interface {
	ModelGroup() ModelGroupStore
	Model() ModelStore
	ModelAttributes() ModelAttributesStore
	ModelAttrGroup() ModelAttrGroupStore
	ModelData() ModelDataStore
	ModelRelation() ModelRelationStore
	Audit() AuditStore
	View() ViewStore
	Common() CommonStore
	Labels() LabelStore
}

type DataStore struct {
	Db    *gorm.DB
	Mgo   *db.MongoOptions
	ES    *elastic.Client
	Cache cache.Cache
	Asynq *zabbix.AsynqClient
}

var _ Factory = (*DataStore)(nil)

func (ds *DataStore) ModelGroup() ModelGroupStore { return newModelGroupStore(ds) }

func (ds *DataStore) Model() ModelStore {
	return newModelStore(ds)
}

func (ds *DataStore) ModelAttributes() ModelAttributesStore { return newModelAttributesStore(ds) }

func (ds *DataStore) ModelAttrGroup() ModelAttrGroupStore {
	return newModelAttrGroupStore(ds)
}

func (ds *DataStore) ModelData() ModelDataStore {
	return newModelDataStore(ds)
}

func (ds *DataStore) Audit() AuditStore {
	return newAuditStore(ds)
}

func (ds *DataStore) ModelRelation() ModelRelationStore {
	return newModelRelationStore(ds)
}

func (ds *DataStore) View() ViewStore {
	return newViewStore(ds)
}

func (ds *DataStore) Common() CommonStore {
	return newCommonStore(ds)
}

func (ds *DataStore) Labels() LabelStore {
	return newLabelStore(ds)
}
