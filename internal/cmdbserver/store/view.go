package store

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ViewStore interface {
	GetHostDataView(ctx context.Context, filter bson.M) (v1.DataTreePathList, error)
}

type viewStore struct {
	mgo   *db.MongoOptions
	db    *gorm.DB
	cache cache.Cache
}

var _ ViewStore = (*viewStore)(nil)

func newViewStore(ds *DataStore) *viewStore {
	return &viewStore{
		mgo:   ds.Mgo,
		db:    ds.Db,
		cache: ds.Cache,
	}
}

func (vs *viewStore) GetHostDataView(ctx context.Context, filter bson.M) (v1.DataTreePathList, error) {
	zap.L().Debug("GetHostDataView Store Called")
	span, ctx := apm.StartSpan(ctx, "GetHostDataViewStore", "store")
	defer span.End()

	var paths []*v1.DataTreePath
	err := vs.mgo.GetCollection("model_data_treepath").
		Find(ctx, filter).
		Sort("created_at").
		All(&paths)

	if err != nil {
		return nil, err
	}

	return paths, nil
}
