package store

import (
	"context"
	"errors"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/clock"
	kjson "ks-knoc-server/pkg/json"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ModelGroupStore interface {
	// CreateModelGroup 创建模型分组
	CreateModelGroup(ctx context.Context, operator string, modelGroup *v1.ModelGroup) (*v1.ModelGroup, error)
	// GetModelGroupList 获取模型分组列表
	GetModelGroupList(ctx context.Context) ([]v1.ModelGroup, error)
	// GetModelGroupListWithPagination 获取模型分组列表，带分页的
	GetModelGroupListWithPagination(ctx context.Context, page, pageSize int64) ([]v1.ModelGroup, error)
	// GetModelGroup 获取指定的模型分组
	GetModelGroup(ctx context.Context, code string) (*v1.ModelGroup, error)
	// GetModelGroupByFilter 根据filter获取指定的模型分组
	GetModelGroupByFilter(ctx context.Context, filter bson.M) (*v1.ModelGroup, error)
	// UpdateModelGroup 更新模型分组
	UpdateModelGroup(ctx context.Context, operator string, modelGroup map[string]interface{}, srcModelGroup *v1.ModelGroup) error
	// DeleteModelGroup 删除模型分组
	DeleteModelGroup(ctx context.Context, operator string, modelGroup *v1.InstanceFilter) error
	// Count 获取模型分组的数量
	Count(ctx context.Context) (int64, error)
}

var _ ModelGroupStore = (*modelGroupStore)(nil)

type modelGroupStore struct {
	mgo   *db.MongoOptions
	cache cache.Cache
}

func newModelGroupStore(ds *DataStore) *modelGroupStore {
	return &modelGroupStore{
		mgo:   ds.Mgo,
		cache: ds.Cache,
	}
}

// CreateModelGroup 创建模型分组
func (m *modelGroupStore) CreateModelGroup(ctx context.Context, operator string, modelGroup *v1.ModelGroup) (*v1.ModelGroup, error) {
	// 返回的Result, 是一个InsertOneResult类型的指针，这个struct中包含了一个InsertedID的字段
	// 字段的类型是一个interface类型的切片，也就是说存什么都行，经过实际的测试，发现内部存储的对象是
	// primitive.ObjectID类型，primitive.ObjectID本身是一个type为byte类型的数组，长度为12
	// 定义：type ObjectID [12]byte
	modelGroup.SetGeneralDefaultVal()
	var err error
	if err = m.mgo.GetCollection("model_group").Find(ctx, bson.M{"code": modelGroup.Code}).One(&v1.ModelGroup{}); err == nil {
		return nil, errno.ErrDuplicateKey.Add("模型分组唯一标识不可重复")
	}
	if err = m.mgo.GetCollection("model_group").Find(ctx, bson.M{"name": modelGroup.Name}).One(&v1.ModelGroup{}); err == nil {
		return nil, errno.ErrDuplicateKey.Add("模型分组名称不可重复")
	}

	insertRes, err := m.mgo.GetCollection("model_group").InsertOne(ctx, modelGroup)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return nil, errno.ErrDuplicateKey.Add(err.Error())
		}
		return nil, err
	}

	modelGroup.ID = utils.ToString(insertRes.InsertedID)

	// dst := make(map[string]interface{})
	// dst["name"] = modelGroup.Name
	// auditLog, err := metadata.GenerateAuditLog(ctx, nil, dst, nil, metadata.ActionCreate, metadata.ModelGroupRes, operator, insertRes.InsertedID.(string), modelGroup.Code)
	// if err != nil {
	// 	return nil, err
	// }
	//
	// if err = metadata.SaveAuditLog(ctx, m.mgo, auditLog); err != nil {
	// 	return nil, err
	// }

	// 清空cache
	_ = m.cache.Delete("model_group_list")

	return modelGroup, nil
}

// GetModelGroupList 获取模型分组列表
// 2023-09-27 加了cache以后，接口的响应速度快了10多毫秒，平均响应时间3~5ms左右，优化前位15~20ms左右
// 当前的策略为获取modelGroupList的时候，将这个list缓存起来，如果模型分组有创建，更新，删除操作时，将缓存清空
// 考虑到目前的场景是查询操作大于变更操作的，因此目前该策略是可行的。
func (m *modelGroupStore) GetModelGroupList(ctx context.Context) ([]v1.ModelGroup, error) {
	zap.L().Debug("GetModelGroupList Store Called")
	var batch []v1.ModelGroup

	// 首先从缓存中获取数据
	cacheData, err := m.cache.Get("model_group_list")
	// 对应的cache key没有找到，穿透到db中获取，这里不要返回报错，只记录日志即可
	if err != nil {
		zap.L().Info("ModelGroupList cache not found, get from db")
		err = m.mgo.GetCollection("model_group").
			Find(ctx, bson.M{}).
			Sort("create_at").
			All(&batch)
		if err != nil {
			return nil, err
		}
		// 刷新缓存
		var entry []byte
		entry, err = kjson.Marshal(batch)
		if err != nil {
			return nil, err
		}
		err = m.cache.Set("model_group_list", entry)
		if err != nil {
			return nil, err
		}
		return batch, nil
	}
	// 如果没有报错，证明从缓存中拿到了数据，直接返回即可
	zap.L().Info("ModelGroupList cache found")
	err = kjson.Unmarshal(cacheData, &batch)
	if err != nil {
		return nil, err
	}
	return batch, nil
}

func (m *modelGroupStore) GetModelGroupListWithPagination(ctx context.Context, page, pageSize int64) ([]v1.ModelGroup, error) {
	zap.L().Debug("GetModelGroupList Store Called")
	var batch []v1.ModelGroup
	err := m.mgo.GetCollection("model_group").
		Find(ctx, bson.M{}).
		Skip((page - 1) * pageSize).
		Limit(pageSize).
		All(&batch)
	if err != nil {
		return nil, err
	}
	return batch, nil
}

// GetModelGroup 根据code获取指定的模型分组
func (m *modelGroupStore) GetModelGroup(ctx context.Context, code string) (*v1.ModelGroup, error) {
	zap.L().Debug("GetModelGroup Store Called")
	var modelGroup v1.ModelGroup
	err := m.mgo.GetCollection("model_group").Find(ctx, bson.M{"code": code}).One(&modelGroup)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("模型分组'%s'不存在", code)
		}
		return nil, err
	}
	return &modelGroup, nil
}

// GetModelGroupByFilter 基于过滤条件查询模型分组
func (m *modelGroupStore) GetModelGroupByFilter(ctx context.Context, filter bson.M) (*v1.ModelGroup, error) {
	zap.L().Debug("GetModelGroupByFilter Store Called")

	var modelGroup v1.ModelGroup
	err := m.mgo.GetCollection("model_group").Find(ctx, filter).One(&modelGroup)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return &modelGroup, errno.ErrDataNotExists.Add("模型分组不存在")
		}
		return &modelGroup, err
	}
	return &modelGroup, nil
}

// UpdateModelGroup 更新模型分组
func (m *modelGroupStore) UpdateModelGroup(ctx context.Context, operator string, modelGroup map[string]interface{}, srcModelGroup *v1.ModelGroup) error {
	zap.L().Debug("UpdateModelGroup Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelGroup", "store")
	defer span.End()
	// 记录updated_at更新时间
	modelGroup["update_at"] = clock.NowInNano()
	// 更新模型数据
	// TODO: 这里有一个bug，如果更新的时候，传递了模型分组不相关的字段，也会被写入到数据库
	// 虽然说查询的时候并不会查询出来，但是有可能造成数据有很多的垃圾数据。
	err := m.mgo.GetCollection("model_group").
		UpdateOne(
			ctx,
			bson.M{"code": modelGroup["code"].(string)},
			bson.M{"$set": modelGroup},
		)
	if err != nil {
		return err
	}

	// 清空cache
	_ = m.cache.Delete("model_group_list")
	return nil
}

// DeleteModelGroup 删除模型分组
func (m *modelGroupStore) DeleteModelGroup(ctx context.Context, operator string, modelGroup *v1.InstanceFilter) error {
	zap.L().Debug("DeleteModelGroup Store Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelGroupStore", "store")
	defer span.End()

	err := m.mgo.GetCollection("model_group").Remove(ctx, bson.M{"code": modelGroup.Code})
	if err != nil {
		return err
	}

	// // audit log
	// src := make(map[string]interface{})
	// src["name"] = modelGroup.Name
	// auditLog, err := metadata.GenerateAuditLog(ctx, src, nil, nil, metadata.ActionDelete, metadata.ModelGroupRes, operator, "", modelGroup.Code)
	// if err != nil {
	// 	return err
	// }
	//
	// if err = metadata.SaveAuditLog(ctx, m.mgo, auditLog); err != nil {
	// 	return err
	// }

	// 清空cache
	_ = m.cache.Delete("model_group_list")
	return nil
}

func (m *modelGroupStore) Count(ctx context.Context) (int64, error) {
	zap.L().Debug("Get ModelGroup Count Store Called")
	return m.mgo.GetCollection("model_group").Find(ctx, bson.M{}).Count()
}
