package store

import (
	"context"
	"errors"
	"fmt"
	"time"

	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/internal/common/zabbix"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/clock"
	"ks-knoc-server/pkg/json"
	"ks-knoc-server/pkg/mapdata"

	"github.com/olivere/elastic/v7"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const (
	ModelDataIndex = "model_data"
)

type ModelDataStore interface {
	// CreateModelData 创建模型数据
	CreateModelData(ctx context.Context, operator string, modelData *v1.ModelData, modelAttrs []v1.CommonModelAttribute, meta mapdata.MapData) (*v1.ModelData, error)
	// GetModelDataList 根据模型code，获取模型数据列表
	GetModelDataList(ctx context.Context, filter bson.M, page, pageSize int64, sorting string) ([]v1.ModelData, int64, error)
	// GetModelDataCount 根据模型code，获取模型数据的总数
	GetModelDataCount(ctx context.Context, modelCodeList []string) ([]v1.ModelDataCount, error)
	// GetModelDataListByIDs 根据模型数据的ID列表，获取模型数据列表
	GetModelDataListByIDs(ctx context.Context, ids apiv1.IDsFilter) (*v1.ModelDataResponseWithOutPagination, error)
	// UpdateModelData 更新模型数据
	UpdateModelData(ctx context.Context, update *v1.UpdateModelDataStoreRequest) (*v1.ModelData, error)
	// DeleteModelData 删除模型数据
	DeleteModelData(ctx context.Context, operator, dataID string, oldData *v1.ModelData, modelAttrs []v1.CommonModelAttribute, modelName string) error
	// GetModelDataByCustomFilter 根据自定义的过滤条件，获取模型数据列表
	GetModelDataByCustomFilter(ctx context.Context, filter bson.M, projection bson.M) ([]*v1.ModelData, error)
	// GetModelDataByCode 根据模型数据code，获取模型数据
	GetModelDataByCode(ctx context.Context, code string) (*v1.ModelData, error)
	// GetModelDataByID 根据模型数据ID，获取模型数据
	GetModelDataByID(ctx context.Context, dataID string) (*v1.ModelData, error)
	// GetModelDataByModelCode 根据模型code，获取模型数据列表
	GetModelDataByModelCode(ctx context.Context, modelCode string) ([]v1.ModelData, error)
	// FuzzySearchData 模糊搜索模型数据
	FuzzySearchData(ctx context.Context, qs *elastic.SearchSource) (*elastic.SearchResult, error)

	// GetDataImportLock 获取数据导入锁
	GetDataImportLock(ctx context.Context, modelCode string)
	// ReleaseDataImportLock 释放数据导入锁
	ReleaseDataImportLock(ctx context.Context, modelCode string)

	// AddDataAssociate 更新模型数据的关联数据
	AddDataAssociate(ctx context.Context, data *v1.ModelData, targetIDs []string, relID, relDesc string) error
	// DelDataAssociate 删除模型数据的关联数据
	DelDataAssociate(ctx context.Context, data *v1.ModelData, targetIDs []string) error
	// UpdateModelDataAssociation 更新模型数据的关联关系
	UpdateModelDataAssociation(ctx context.Context, operator string, data *v1.ModelData) error
	// CreateDataTreePath 创建数据树路径
	CreateDataTreePath(ctx context.Context, operator string, path *v1.DataTreePath) error
	// GetDataPath 根据数据ID获取数据路径信息
	GetDataPath(ctx context.Context, dataID string) ([]v1.DataTreePath, error)
	// GetDataPathByFilter 根据过滤条件获取数据路径信息
	GetDataPathByFilter(ctx context.Context, filter bson.M) ([]v1.DataTreePath, error)
	// DeleteDataPath 删除数据路径信息
	DeleteDataPath(ctx context.Context, parentID, childID string) error
	// DeleteDataPathByFilter 根据过滤条件删除数据路径信息
	DeleteDataPathByFilter(ctx context.Context, filter bson.M) (int64, error)
	// GetDataPathCount 根据过滤条件获取数据路径信息的数量
	GetDataPathCount(ctx context.Context, filter bson.M) (int64, error)
	// GetDataListByPipeLine 根据管道聚合查询数据列表
	GetDataListByPipeLine(ctx context.Context, pipeline []bson.D) ([]v1.ModelData, error)
	// UpdateDataLabels 更新数据标签
	UpdateDataLabels(ctx context.Context, operator string, data []*v1.ModelData) error
	// GetAggregatedData 聚合查询数据
	GetAggregatedData(ctx context.Context, pipeline any) ([]bson.M, error)
	// GetLabelAggregateData 聚合查询数据标签
	GetLabelAggregateData(ctx context.Context, pipeline any) ([]v1.SearchAggregatedDataWithLabelResponse, error)
	// SyncToZabbix 同步数据到zabbix
	SyncToZabbix(hostname, ip, action string) error
	// BoolQuerySearch 布尔查询搜索
	BoolQuerySearch(ctx context.Context, q *elastic.BoolQuery) (*elastic.SearchResult, error)
	// QuerySource 基于Source查询数据
	QuerySource(ctx context.Context, qs *elastic.SearchSource) (*elastic.SearchResult, error)
}

var _ ModelDataStore = (*modelDataStore)(nil)

type modelDataStore struct {
	mgo   *db.MongoOptions
	es    *elastic.Client
	cache cache.Cache
	asynq *zabbix.AsynqClient
}

func newModelDataStore(ds *DataStore) *modelDataStore {
	return &modelDataStore{
		mgo:   ds.Mgo,
		es:    ds.ES,
		cache: ds.Cache,
		asynq: ds.Asynq,
	}
}

func (m *modelDataStore) SyncToZabbix(hostname, ip, action string) error {
	if err := m.asynq.SubmitZabbixSyncTask(hostname, ip, action); err != nil {
		return err
	}
	return nil
}

// CreateModelData 创建模型数据
func (m *modelDataStore) CreateModelData(ctx context.Context,
	operator string,
	modelData *v1.ModelData,
	modelAttrs []v1.CommonModelAttribute,
	meta mapdata.MapData) (*v1.ModelData, error) {

	zap.L().Debug("CreateModelDataStore called")
	modelData.InitTime()
	// 初始化一下从属关系字段，默认为空字符串
	modelData.ParentID = ""
	// 需要初始化一下关联关系的这个切片，即使数据没有关联关系，也不应该为nil，而是一个空切片
	modelData.AssociateInstances = make([]v1.AssociateInstance, 0)
	insertRes, err := m.mgo.GetCollection("model_data").InsertOne(ctx, modelData)
	if err != nil {
		return nil, err
	}
	// 回写ID
	modelData.ID = insertRes.InsertedID.(string)
	// TODO: 2023-09-25 先使用同步写的方式，写入到ES，写入到es先不作为硬性错误返回，只记录日志
	// 后面看看性能如何，如果性能不好，再考虑异步写入的方式
	searchData := v1.NewSearchData()
	err = searchData.InjectData(modelData)
	if err != nil {
		return nil, err
	}

	// 初始化元数据信息以及labels标签信息，元数据信息一般是数据的一些附属信息，labels一般是附加标签，用于数据过滤以及筛选的时候使用
	// 因为目前还没有用到labels，所以先放在这里。
	labels := make(mapdata.MapData)

	searchData.Meta = meta
	searchData.Labels = labels

	// 刷新info信息
	searchData.Data["info"] = modelData.GetDataInfo(false)
	_, err = m.es.Index().
		Index("model_data").
		Id(modelData.ID).
		BodyJson(searchData).
		Do(ctx)
	if err != nil {
		zap.L().Error(fmt.Sprintf("Sync to ES Failed: data id %s, data code %s", modelData.ID, modelData.IdentifyValue), zap.Error(err))
	}
	zap.L().Debug(fmt.Sprintf("Sync to ES Success: data id %s, data code %s", modelData.ID, modelData.IdentifyValue))

	return modelData, nil
}

// GetModelDataList 根据模型code，获取模型数据列表，返回模型数据列表，以及数据的总数量
func (m *modelDataStore) GetModelDataList(ctx context.Context, filter bson.M, page, pageSize int64, sorting string) ([]v1.ModelData, int64, error) {
	zap.L().Debug("GetModelDataListStore called")
	span, ctx := apm.StartSpan(ctx, "GetModelDataListStore", "store")
	defer span.End()

	var (
		err        error
		sortFilter string
	)

	if sorting == "desc" {
		sortFilter = "-created_at"
	} else {
		sortFilter = "created_at"
	}

	// 1. 首先获取数据列表
	var modelDataList []v1.ModelData
	err = m.mgo.GetCollection("model_data").
		Find(ctx, filter).
		Sort(sortFilter, "-_id").
		Skip((page - 1) * pageSize).
		Limit(pageSize).
		All(&modelDataList)
	if err != nil {
		return nil, 0, err
	}

	// 2. 查询count
	total, err := m.mgo.GetCollection("model_data").
		Find(ctx, filter).
		Count()
	if err != nil {
		return nil, 0, err
	}

	return modelDataList, total, nil
}

// GetModelDataCount 根据模型code，获取模型数据的数量
func (m *modelDataStore) GetModelDataCount(ctx context.Context, modelCodeList []string) ([]v1.ModelDataCount, error) {
	zap.L().Debug("GetModelDataCountStore called")

	// bson.D是primitive.D的别名，而primitive.D是[]primitive.E的别名，因此bson.D是有序的
	matchStage := bson.D{
		bson.E{Key: "$match", Value: bson.D{
			bson.E{Key: "model_code", Value: bson.D{
				bson.E{Key: "$in", Value: modelCodeList},
			}},
		}},
	}
	groupStage := bson.D{
		bson.E{Key: "$group", Value: bson.D{
			bson.E{Key: "_id", Value: "$model_code"},
			bson.E{Key: "count", Value: bson.D{bson.E{Key: "$sum", Value: 1}}},
		}},
	}
	// 比如我传递过来三个model，model1，model2，model3，如果只有model1有数据，那么这里result只会有model1的count数据，model2和model3的count数据会丢失
	// mongodb本身可以通过$lookup实现表连接，但是目前了解还不深入，后续再研究，先简单的使用代码实现
	var result []v1.ModelDataCount
	err := m.mgo.GetCollection("model_data").
		Aggregate(ctx, mongo.Pipeline{matchStage, groupStage}).
		All(&result)
	if err != nil {
		return nil, err
	}
	// 这里先通过手动的方式把没有统计到的model_code的count数据补充上，补充的count数据为0，后面慢慢研究联表
	countNotEqZero := make([]string, 0)
	for _, r := range result {
		countNotEqZero = append(countNotEqZero, r.Code)
	}
	for _, code := range modelCodeList {
		if !array.InArray(code, countNotEqZero) {
			result = append(result, v1.ModelDataCount{
				Code:  code,
				Count: 0,
			})
		}
	}
	return result, nil
}

// GetModelDataListByIDs 接收一个模型数据的ID列表，返回对应的模型数据
func (m *modelDataStore) GetModelDataListByIDs(ctx context.Context, ids apiv1.IDsFilter) (*v1.ModelDataResponseWithOutPagination, error) {
	zap.L().Debug("GetModelDataListByIDs Store called")
	var modelDataList []v1.ModelData
	err := m.mgo.GetCollection("model_data").
		Find(ctx, bson.M{"_id": bson.M{"$in": ids.IDS}}).
		Sort("created_at").
		All(&modelDataList)
	if err != nil {
		return nil, err
	}
	total, err := m.mgo.GetCollection("model_data").Find(ctx, bson.M{"_id": bson.M{"$in": ids.IDS}}).Count()
	if err != nil {
		return nil, err
	}
	return &v1.ModelDataResponseWithOutPagination{
		Total: total,
		Data:  modelDataList,
	}, nil
}

// UpdateModelData 更新模型数据
func (m *modelDataStore) UpdateModelData(ctx context.Context, update *v1.UpdateModelDataStoreRequest) (*v1.ModelData, error) {
	zap.L().Debug("UpdateModelData Store called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelData", "store")
	defer span.End()

	// 定义一个setter，用于更新data数据, 需要优先判断modelData是不是nil，因为允许不更新data数据
	setter := bson.M{}
	if update.ModelData != nil {
		// 更新data数据
		for dataKey, dataValue := range update.ModelData.Data {
			setter["data."+dataKey] = dataValue
		}
		// 更新metadata数据数据
		for metaKey, metaValue := range update.ModelData.MetaData {
			setter["meta_data."+metaKey] = metaValue
		}
	}
	// 判断是否要更新父级数据
	if update.UpdateParent {
		setter["parent_id"] = update.ParentID
		setter["parent_desc"] = update.ParentDesc
	}

	// 判断是否需要更新数据
	if len(setter) == 0 {
		zap.L().Info("没有需要更新的数据")
		return nil, nil
	}

	// 更新时间戳
	setter["update_at"] = clock.NowInNano()
	// 更新数据
	if err := m.mgo.GetCollection("model_data").
		UpdateOne(
			ctx,
			bson.M{"_id": update.Id},
			bson.M{"$set": setter}); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Add("要更新的数据不存在")
		}
		return nil, err
	}
	// TODO: 这个方法有一点呆，更新完了以后还得查一次，看看mongodb官方驱动是否有一些更新的options可以设置hook获取更新后的数据
	// 2023-11-24 这里重新查了一遍以后，可能是觉得之前提交的数据里面飞data.Data的部分会有不一致的问题，导致刷新到es中的数据有问题，
	// 所以又查了一遍，以数据库中的为准。
	updatedData := &v1.ModelData{}
	if err := m.mgo.GetCollection("model_data").
		Find(ctx, bson.M{"_id": update.Id}).
		One(updatedData); err != nil {
		return nil, err
	}

	_ = m.UpdateESData(ctx, updatedData)
	return updatedData, nil
}

// DeleteModelData 删除模型数据
func (m *modelDataStore) DeleteModelData(ctx context.Context, operator, dataID string, oldData *v1.ModelData, modelAttrs []v1.CommonModelAttribute, modelName string) error {
	zap.L().Debug("DeleteModelData Store called")
	// 删除数据
	if err := m.mgo.GetCollection("model_data").Remove(ctx, bson.M{"_id": dataID}); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataDelete.Add("数据ID " + dataID + " 不存在")
		}
		return errno.ErrDataDelete.Add(err.Error())
	}

	// TODO: 同步删除ES中的数据
	deletedResponse, err := m.es.Delete().Index("model_data").Id(dataID).Do(ctx)
	if err != nil {
		zap.L().Error(fmt.Sprintf("Delete ModelData Sync to ES Failed: data id %s", dataID), zap.Error(err))
	}
	zap.L().Debug(fmt.Sprintf("删除数据同步ES成功, 数据ID: %s, deleted: %s", dataID, deletedResponse.Result))

	// 借助dst 传递模型名称
	oldData.Data["model_name"] = modelName
	// audit log
	// auditLog, err := metadata.GenerateAuditLog(ctx, oldData.Data, nil, modelAttrs, metadata.ActionDelete, metadata.ResourceDetailRes, operator, oldData.ID, oldData.ModelCode)
	// if err != nil {
	// 	return err
	// }
	//
	// if err = metadata.SaveAuditLog(ctx, m.mgo, auditLog); err != nil {
	// 	return err
	// }

	return nil
}

// GetModelDataByCustomFilter 根据自定义的过滤条件，获取模型数据列表
func (m *modelDataStore) GetModelDataByCustomFilter(ctx context.Context, filter bson.M, projection bson.M) ([]*v1.ModelData, error) {
	zap.L().Debug("GetModelDataByCustomFilterStore called")
	span, ctx := apm.StartSpan(ctx, "GetModelDataByCustomFilterStore", "store")
	defer span.End()

	var modelDataList []*v1.ModelData
	err := m.mgo.GetCollection("model_data").Find(ctx, filter).Select(projection).All(&modelDataList)
	if err != nil {
		return nil, err
	}
	return modelDataList, nil
}

// GetModelDataByCode 根据模型数据code获取模型数据，返回单一的数据
func (m *modelDataStore) GetModelDataByCode(ctx context.Context, code string) (*v1.ModelData, error) {
	zap.L().Debug("GetModelDataByCodeStore called")
	var modelData v1.ModelData
	err := m.mgo.GetCollection("model_data").Find(ctx, bson.M{"identify_value": code}).One(&modelData)
	if err != nil {
		return nil, err
	}
	return &modelData, nil
}

// GetModelDataByID 根据模型数据ID返回单一数据
func (m *modelDataStore) GetModelDataByID(ctx context.Context, dataID string) (*v1.ModelData, error) {
	zap.L().Debug("GetModelDataByCodeStore called")
	var modelData v1.ModelData
	err := m.mgo.GetCollection("model_data").Find(ctx, bson.M{"_id": dataID}).One(&modelData)
	if err != nil {
		return nil, err
	}
	return &modelData, nil
}

// GetModelDataByModelCode 根据模型code，获取模型数据列表
func (m *modelDataStore) GetModelDataByModelCode(ctx context.Context, modelCode string) ([]v1.ModelData, error) {
	zap.L().Debug("GetModelDataByModelCode Store Called")
	var modelData []v1.ModelData
	err := m.mgo.GetCollection("model_data").Find(ctx, bson.M{"model_code": modelCode}).All(&modelData)
	if err != nil {
		return nil, err
	}
	return modelData, nil
}

// FuzzySearchData 模糊搜索模型数据
func (m *modelDataStore) FuzzySearchData(ctx context.Context, qs *elastic.SearchSource) (*elastic.SearchResult, error) {
	zap.L().Debug("FuzzySearchModelData Store Called")

	// 执行查询
	s, err := m.es.Search().
		Index(ModelDataIndex). // 配置Search Index
		Source(qs).
		// 配置排序字段，默认按照创建时间倒排
		Sort("created_at", false).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// QuerySource 基于Source查询数据
func (m *modelDataStore) QuerySource(ctx context.Context, qs *elastic.SearchSource) (*elastic.SearchResult, error) {
	zap.L().Debug("QuerySource Store Called")
	span, ctx := apm.StartSpan(ctx, "QuerySourceStore", "store")
	defer span.End()

	s, err := m.es.Search().Index(ModelDataIndex).Source(qs).Do(ctx)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (m *modelDataStore) BoolQuerySearch(ctx context.Context, q *elastic.BoolQuery) (*elastic.SearchResult, error) {
	zap.L().Debug("BoolQuerySearch Store Called")
	span, ctx := apm.StartSpan(ctx, "BoolQuerySearchStore", "store")
	defer span.End()

	s, err := m.es.Search().Index("model_data").Query(q).Do(ctx)
	if err != nil {
		return nil, err
	}
	return s, nil
}

func (m *modelDataStore) GetDataImportLock(ctx context.Context, modelCode string) {
	zap.L().Debug("GetDataImportLock Store Called")

	var (
		err error
		key string = fmt.Sprintf("import:%s", modelCode)
	)

	_, err = m.cache.Get(key)

	// 不存在，说明可以导入
	if err != nil {
		_ = m.cache.Set(key, []byte{})
		return
	}

	// 有数据在使用，等待
	for {
		_, err = m.cache.Get(key)
		time.Sleep(5 * time.Millisecond)
		if err != nil {
			break
		}
	}
}

func (m *modelDataStore) ReleaseDataImportLock(ctx context.Context, modelCode string) {
	zap.L().Debug("ReleaseDataImportLock Store Called")
	_ = m.cache.Delete(fmt.Sprintf("import:%s", modelCode))
}

// UpdateESData 更新ES中的数据
func (m *modelDataStore) UpdateESData(ctx context.Context, modelData *v1.ModelData) error {
	zap.L().Debug("UpdateESData Store Called")

	var err error

	// 直接将新的数据刷新进去就可以了
	searchData := v1.NewSearchData()
	// Tip: 这里先别删哦，听话
	// 先把es中查出来，然后把变更的内容更新进去, 原因是searchData中有一个meta信息，直接覆盖就无需做额外的mongodb的查询了
	ret, err := m.es.Get().Index("model_data").Id(modelData.ID).Do(ctx)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(ret.Source, searchData); err != nil {
		return err
	}
	if err := searchData.InjectData(modelData); err != nil {
		return err
	}
	searchData.Data["info"] = modelData.GetDataInfo(false)

	// 直接刷新到ES中
	_, err = m.es.Update().Index("model_data").Id(modelData.ID).Doc(searchData).Do(ctx)
	if err != nil {
		zap.L().Error(fmt.Sprintf("同步ES失败: DataID %s, DataCode %s", modelData.ID, modelData.IdentifyValue), zap.Error(err))
		return err
	}
	zap.L().Debug(fmt.Sprintf("同步ES成功: DataID %s, DataCode %s", modelData.ID, modelData.IdentifyValue))
	return nil
}

func (m *modelDataStore) AddDataAssociate(ctx context.Context, data *v1.ModelData, targetIDs []string, relID, relDesc string) error {
	zap.L().Debug("AddDataAssociate Store Called")
	span, ctx := apm.StartSpan(ctx, "AddDataAssociateStore", "store")
	defer span.End()

	// 把现在的关联关系数据放到一个map中，方便后面的比较和去重
	dataSet := make(mapdata.MapData)
	for _, ass := range data.AssociateInstances {
		dataSet.Set(ass.InstanceID, struct{}{})
	}

	assInstances := make([]v1.AssociateInstance, 0)
	for _, targetID := range targetIDs {
		if _, exist := dataSet.Get(targetID); !exist {
			assInstances = append(assInstances, v1.AssociateInstance{
				InstanceID:   targetID,
				RelationID:   relID,
				RelationDesc: relDesc,
			})
		} else {
			zap.L().Error("资源关联关系已存在")
			return errno.ErrModelDataAssociateRelationExists.Add("资源关联关系已存在")
		}
	}

	err := m.mgo.GetCollection("model_data").UpdateOne(
		ctx,
		bson.M{"identify_value": data.IdentifyValue},
		bson.M{"$set": bson.M{"associate_instances": append(data.AssociateInstances, assInstances...)}},
	)

	if err != nil {
		zap.L().Error(err.Error())
		return err
	}
	return nil
}

// DelDataAssociate 删除数据之间的关联关系
func (m *modelDataStore) DelDataAssociate(ctx context.Context, data *v1.ModelData, targetIDs []string) error {
	zap.L().Debug("DelDataAssociate Store Called")
	span, ctx := apm.StartSpan(ctx, "DelDataAssociateStore", "store")
	defer span.End()

	// 重构一个关联关系对象的切片
	latestInstances := make([]v1.AssociateInstance, 0)
	for _, ass := range data.AssociateInstances {
		// 不在targetIDs中的就是要保留的，因为在targetIDs中的是要删除的
		if !array.InArray(ass.InstanceID, targetIDs) {
			latestInstances = append(latestInstances, ass)
		}
	}

	// 刷新data中的关联关系字段即可。
	err := m.mgo.GetCollection("model_data").UpdateOne(
		ctx,
		bson.M{"identify_value": data.IdentifyValue},
		bson.M{"$set": bson.M{"associate_instances": latestInstances}},
	)
	if err != nil {
		zap.L().Error(err.Error())
		return err
	}
	return nil
}

func (m *modelDataStore) UpdateModelDataAssociation(ctx context.Context, operator string, data *v1.ModelData) error {
	zap.L().Debug("UpdateModelDataAssociation Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelDataAssociationStore", "store")
	defer span.End()

	err := m.mgo.GetCollection("model_data").UpdateOne(
		ctx,
		bson.M{"identify_value": data.IdentifyValue},
		bson.M{"$set": bson.M{"associate_instances": data.AssociateInstances}},
	)
	if err != nil {
		return err
	}
	return nil
}

func (m *modelDataStore) CreateDataTreePath(ctx context.Context, operator string, path *v1.DataTreePath) error {
	zap.L().Debug("CreateDataTreePath Store Called")
	span, ctx := apm.StartSpan(ctx, "CreateDataTreePathStore", "store")
	defer span.End()

	if _, err := m.mgo.GetCollection("model_data_treepath").InsertOne(ctx, path); err != nil {
		return err
	}
	return nil
}

func (m *modelDataStore) GetDataPath(ctx context.Context, dataID string) ([]v1.DataTreePath, error) {
	zap.L().Debug("GetDataPath Store Called")
	span, ctx := apm.StartSpan(ctx, "GetDataPathStore", "store")
	defer span.End()

	var paths []v1.DataTreePath
	// 查询的是以传入的dataID为父亲的路径数据
	if err := m.mgo.GetCollection("model_data_treepath").Find(ctx, bson.M{"parent_id": dataID}).All(&paths); err != nil {
		return nil, err
	}
	return paths, nil
}

// GetDataPathByFilter 根据过滤条件获取数据路径
func (m *modelDataStore) GetDataPathByFilter(ctx context.Context, filter bson.M) ([]v1.DataTreePath, error) {
	zap.L().Debug("GetDataPathByFilter Store Called")
	span, ctx := apm.StartSpan(ctx, "GetDataPathByFilterStore", "store")
	defer span.End()

	var paths []v1.DataTreePath
	if err := m.mgo.GetCollection("model_data_treepath").Find(ctx, filter).All(&paths); err != nil {
		return nil, err
	}
	return paths, nil
}

func (m *modelDataStore) DeleteDataPath(ctx context.Context, parentID, childID string) error {
	zap.L().Debug("DeleteDataPath Store Called")
	span, ctx := apm.StartSpan(ctx, "DeleteDataPathStore", "store")
	defer span.End()

	if err := m.mgo.GetCollection("model_data_treepath").Remove(ctx, bson.M{"parent_id": parentID, "child_id": childID}); err != nil {
		return err
	}
	return nil
}

func (m *modelDataStore) DeleteDataPathByFilter(ctx context.Context, filter bson.M) (int64, error) {
	zap.L().Debug("DeleteDataPathByFilter Store Called")
	span, ctx := apm.StartSpan(ctx, "DeleteDataPathByFilterStore", "store")
	defer span.End()

	cnt, err := m.mgo.GetCollection("model_data_treepath").RemoveAll(ctx, filter)
	if err != nil {
		return 0, err
	}
	return cnt.DeletedCount, nil
}

func (m *modelDataStore) GetDataPathCount(ctx context.Context, filter bson.M) (int64, error) {
	zap.L().Debug("GetDataPathCount Store Called")
	span, ctx := apm.StartSpan(ctx, "GetDataPathCountStore", "store")
	defer span.End()

	cnt, err := m.mgo.GetCollection("model_data_treepath").Find(ctx, filter).Count()
	if err != nil {
		return 0, err
	}
	return cnt, nil
}

// GetDataListByPipeLine 通过管道聚合获取数据列表
func (m *modelDataStore) GetDataListByPipeLine(ctx context.Context, pipeline []bson.D) ([]v1.ModelData, error) {
	zap.L().Debug("GetDataListByPipeLine Store Called")
	span, ctx := apm.StartSpan(ctx, "GetDataListByPipeLineStore", "store")
	defer span.End()

	var dataList []v1.ModelData
	var dataList1 []any
	if err := m.mgo.GetCollection("model_data").Aggregate(ctx, pipeline).All(&dataList1); err != nil {
		return nil, err
	}
	return dataList, nil
}

func (m *modelDataStore) UpdateDataLabels(ctx context.Context, operator string, data []*v1.ModelData) error {
	zap.L().Debug("UpdateDataBatch Store Called")
	span, ctx := apm.StartSpan(ctx, "UpdateDataBatchStore", "store")
	defer span.End()

	// 批量操作，UpdateAll适合批量将数据更新为某个固定的值，而当前情况则是每个数据需要更新为不同的值，所以使用Bulk方法
	now := time.Now().Unix()
	bulk := m.mgo.GetCollection("model_data").Bulk()
	for _, d := range data {
		bulk.UpdateOne(bson.M{"_id": d.ID}, bson.M{
			"$set": bson.M{
				"label_id_list": d.LabelIDList,
				"update_at":     now,
			}})
	}

	_, err := bulk.Run(ctx)
	if err != nil {
		return err
	}
	return nil
}

// GetAggregatedData 聚合查询数据, 因为这里仅接受一个pipeline，所以实际要返回的内容是不确定的，故使用一个[]bson.M来接收结果
func (m *modelDataStore) GetAggregatedData(ctx context.Context, pipeline any) ([]bson.M, error) {
	zap.L().Debug("GetAggregatedData Store Called")
	span, ctx := apm.StartSpan(ctx, "GetAggregatedDataStore", "store")
	defer span.End()

	var result []bson.M
	if err := m.mgo.GetCollection("model_data").Aggregate(ctx, pipeline).All(&result); err != nil {
		return nil, err
	}
	return result, nil
}

// GetLabelAggregateData 聚合查询数据, 因为这里仅接受一个pipeline，所以实际要返回的内容是不确定的，故使用一个[]bson.M来接收结果
func (m *modelDataStore) GetLabelAggregateData(ctx context.Context, pipeline any) ([]v1.SearchAggregatedDataWithLabelResponse, error) {
	zap.L().Debug("GetAggregatedData Store Called")
	span, ctx := apm.StartSpan(ctx, "GetAggregatedDataStore", "store")
	defer span.End()

	var result []v1.SearchAggregatedDataWithLabelResponse
	if err := m.mgo.GetCollection("model_data").Aggregate(ctx, pipeline).All(&result); err != nil {
		return nil, err
	}
	return result, nil
}
