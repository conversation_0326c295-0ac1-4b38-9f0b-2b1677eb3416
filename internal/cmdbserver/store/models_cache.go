package store

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	kjson "ks-knoc-server/pkg/json"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func (m *modelStore) SetModelCache(ctx context.Context) ([]byte, error) {

	var (
		err error
		ml  = make([]v1.Model, 0)
	)

	err = m.mgo.GetCollection("model").
		Find(ctx, bson.M{}).
		Sort("create_at").
		All(&ml)
	if err != nil {
		return nil, err
	}

	// refresh cache
	raw, err := kjson.Marshal(ml)

	// 序列化失败就失败了，不要影响正常的业务逻辑，顶多就是没缓存了，但是还能查询数据库
	if err != nil {
		return nil, err
	}

	err = m.cache.Set(ModelList.String(), raw)
	if err != nil {
		return nil, err
	}

	zap.L().Debug("Set Model cache Success")
	return raw, nil
}

func (m *modelStore) DeleteModelCache(ctx context.Context) error {
	zap.L().Debug("delete model cache")
	return m.cache.Delete(ModelList.String())
}

func (m *modelStore) RefreshModelCache(ctx context.Context) error {
	zap.L().Debug("refresh model cache")

	_ = m.DeleteModelCache(ctx)
	_, err := m.SetModelCache(ctx)
	return err
}
