package server

import (
	"time"

	svcV1 "ks-knoc-server/internal/cmdbserver/service/v1"
	"ks-knoc-server/internal/common/cache"
	"ks-knoc-server/internal/common/validate"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"ks-knoc-server/internal/cmdbserver/options"
	"ks-knoc-server/internal/cmdbserver/route"
	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/zabbix"
)

// Server ...
type Server struct {
	DB         *gorm.DB
	Log        *zap.Logger
	Web        *gin.Engine
	Mgo        *db.MongoOptions
	ES         *elastic.Client
	Cache      cache.Cache
	TaskClient *zabbix.AsynqClient
}

// NewServer 启动服务相关中间件初始化
func NewServer(opts *options.Options) (*Server, error) {
	var err error

	// 初始化logger
	logger, err := opts.LogsOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化Mysql
	mysqlHandler, err := opts.MySQLOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化MongoDB
	mgo, err := opts.MongoOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化服务的校验规则
	err = validate.InitValidator()
	if err != nil {
		return nil, err
	}

	// 初始化ES
	es, err := opts.ES.Init()
	if err != nil {
		return nil, err
	}

	// 初始化Redis
	rdb, err := opts.RedisOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化Cache
	ca, err := opts.Cache.Init(rdb)
	if err != nil {
		return nil, err
	}

	// 初始化asynq的client
	asynqClient := zabbix.InitAsynqClient()

	// 初始化DataStore
	ds := &store.DataStore{Db: mysqlHandler, Mgo: mgo, ES: es, Cache: ca, Asynq: asynqClient}

	// 初始化部分业务数据到缓存
	err = svcV1.InitCMDBCache(ds)
	if err != nil {
		return nil, err
	}

	// 生成路由的同时注入ds
	g := route.APIServerRouter(opts.WebOptions.Engine, ds)
	g.Use(ginzap.Ginzap(logger, time.RFC3339, true))
	g.Use(ginzap.RecoveryWithZap(logger, true))

	return &Server{
		Log:        logger,
		DB:         mysqlHandler,
		Mgo:        mgo,
		Web:        g,
		ES:         es,
		Cache:      ca,
		TaskClient: asynqClient,
	}, nil
}
