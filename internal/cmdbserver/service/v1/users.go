package v1

import (
	"context"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/itsdk/service/openapi"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

type UsersService interface {
	GetUsersSearch(ctx context.Context, username string) (*openapi.IHRUsersSearchResponse, error)
}

type usersService struct {
	store store.Factory
}

var _ UsersService = (*usersService)(nil)

func newUsersService(srv *service) *usersService {
	return &usersService{store: srv.store}
}

func (as *usersService) GetUsersSearch(ctx context.Context, username string) (*openapi.IHRUsersSearchResponse, error) {
	zap.L().Debug("GetUsersSearch Service Called")
	span, _ := apm.StartSpan(ctx, "GetUsersSearch", "service")
	defer span.End()

	sdk, err := openapi.NewOpenSDK()
	if err != nil {
		return nil, err
	}

	return sdk.GetIHRUsersSearch(username)

}
