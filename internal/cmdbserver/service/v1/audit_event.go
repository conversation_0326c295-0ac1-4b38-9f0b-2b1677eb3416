package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.uber.org/zap"
)

var (
	ErrUnknownResourceType = errors.New("unknown resource type")
	ErrUnknownActionType   = errors.New("unknown action type")
)

const (
	SourceMode = "src"
	DestMode   = "dest"
	BothMode   = "both"
)

type AuditEvent struct {
	store    store.Factory
	auditLog *audit.AuditLog
}

// getModelObject 获取模型对象
// src: 源资源
// dst: 目标资源
// mode: 模式，可选值为SourceMode, DestMode, BothMode
// 返回值: 源资源模型, 目标资源模型, 错误
func (a *AuditEvent) getModelObject(ctx context.Context, src, dst mapdata.MapData) (*v1.Model, error) {
	modelCode := ""
	if _, ok := src.Get("model_code"); ok {
		modelCode = utils.ToString(src["model_code"])
	} else {
		modelCode = utils.ToString(dst["model_code"])
	}

	// 获取模型映射
	modelMapping, err := a.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error("获取模型映射失败", zap.Error(err))
		return nil, err
	}

	model, ok := modelMapping[modelCode]
	if !ok {
		zap.L().Error("获取模型失败", zap.String("modelCode", modelCode))
		return nil, errors.New("获取模型失败")
	}

	return &model, nil
}

// parseData 解析数据, 将src和dst转换成map[string]any
func (a *AuditEvent) parseData(src, dst any) (map[string]any, map[string]any, error) {
	srcBytes, err := json.Marshal(src)
	if err != nil {
		return nil, nil, err
	}
	dstBytes, err := json.Marshal(dst)
	if err != nil {
		return nil, nil, err
	}
	srcMap := make(map[string]any)
	dstMap := make(map[string]any)
	if err := json.Unmarshal(srcBytes, &srcMap); err != nil {
		return nil, nil, err
	}
	if err := json.Unmarshal(dstBytes, &dstMap); err != nil {
		return nil, nil, err
	}
	return srcMap, dstMap, nil
}

func (a *AuditEvent) convertToDataMap(data any) (mapdata.MapData, error) {
	// 虽然接收的是any，但是实际上要不是mapdata.MapData，要不就是map[string]any
	if data == nil {
		zap.L().Error("data is nil")
		return mapdata.MapData{}, errors.New("data is nil")
	}

	v := reflect.ValueOf(data)
	switch v.Type() {
	case reflect.TypeOf(mapdata.MapData{}):
		return data.(mapdata.MapData), nil
	case reflect.TypeOf(map[string]any{}):
		return mapdata.NewFromInterface(data)
	default:
		zap.L().Error("data is not a mapdata.MapData or map[string]any", zap.String("data_type", v.Type().String()))
		return mapdata.MapData{}, errors.New("data is not a mapdata.MapData or map[string]any")
	}
}

// Generate 生成审计日志
func (a *AuditEvent) Generate(ctx context.Context, evt audit.EventType, resourceType audit.ResourceType, actionType audit.ActionType,
	operator, uniqueId string, src, dst any) error {
	span, _ := apm.StartSpan(ctx, "GenerateAuditLog", "service")
	defer span.End()

	// 解析数据转换成mapdata.MapData
	srcMap, dstMap, err := a.parseData(src, dst)
	if err != nil {
		zap.L().Error("解析数据失败", zap.Error(err))
		return err
	}

	var opInfo strings.Builder

	// 变更前后数据都为空，说明传参有问题，不应该进一步生成审计日志
	if len(srcMap) == 0 && len(dstMap) == 0 {
		return errors.New("变更前和变更后的数据均为空，非法数据，无法生成审计日志")
	}

	// 构造模型字段信息映射
	modelAttrNameMapping, err := a.store.ModelAttributes().GetModelAttrMapping(ctx)
	if err != nil {
		zap.L().Error("获取模型属性字段映射失败", zap.Error(err))
		return err
	}

	switch actionType {
	case audit.ActionCreate:
		switch resourceType {
		case audit.ModelRes, audit.ModelGroupRes:
			// 创建模型，模型分组，模型属性，模型属性分组
			// 针对如上的资源类型，我只需要知道创建什么资源，名称是什么就可以
			// Example: 创建了模型: 测试模型
			if len(dstMap) == 0 {
				return errors.New("资源数据为空")
			}
			name, exist := dstMap["name"]
			if !exist {
				zap.L().Error("获取资源名称失")
				return errors.New("获取资源名称失败")
			}
			opInfo.WriteString("创建了")
			opInfo.WriteString(audit.ResourceTypeMap[resourceType])
			opInfo.WriteString(": ")
			opInfo.WriteString(utils.ToString(name))

		case audit.ModelAttributesGroupRes:
			// 创建模型属性分组
			// 示例：创建了 [服务器] 下的属性分组: 测试模型属性分组
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}
			opInfo.WriteString("创建了 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 下的属性分组: ")

			if len(dstMap) == 0 {
				return errors.New("资源数据为空")
			}
			name, exist := dstMap["name"]
			if !exist {
				zap.L().Error("获取资源名称失")
				return errors.New("获取资源名称失败")
			}
			opInfo.WriteString(utils.ToString(name))

		case audit.ModelAttributeRes:
			// 创建模型字段示例
			// 模型[服务器]下新增字段: 测试字段, 类型: string
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}
			opInfo.WriteString("模型 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 下新增字段: ")
			opInfo.WriteString(utils.ToString(dstMap["name"]))
			opInfo.WriteString(", 类型: ")
			attrType := utils.ToString(dstMap["type_name"])
			attrType = v1.ModelAttributeTypeMapping[attrType]
			opInfo.WriteString(attrType)

		case audit.ResourceDetailRes:
			// 记录创建资源审计日志，其中src为nil，dst为创建后的内容
			// 创建数据资源，需要展示数据资源的所有内容
			// 创建了 [服务器] 数据
			// 名称: 测试服务器
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("创建了 [")
			opInfo.WriteString(model.Name + "] \n")

			// 获取对应的data数据字段
			modelData, ok := dstMap["data"]
			if !ok {
				zap.L().Error("获取数据资源的data字段失败")
				return errors.New("获取数据资源的data字段失败")
			}
			modelDataMap, ok := modelData.(map[string]any)
			if !ok {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			// 获取一下唯一标识字段，这个字段不需要展示在审计日志中
			identifyField := fmt.Sprintf("%s_code", model.Code)
			for k := range modelDataMap {
				if k == identifyField {
					continue
				}
				attrObj, ok := modelAttrNameMapping[k]
				// 如果对应的字段查询不到的话，那就查询不到了，有可能是data中存在旧字段，但是实际上model的属性已经删掉了，data中有脏数据存在
				// 因此针对这种情况只打印日志就可以了。
				if !ok {
					zap.L().Debug("无法查询到对应的模型属性字段名", zap.String("field_code", k))
					continue
				}
				attrName := attrObj.Name
				// 过滤掉空的值
				attrValue := strings.TrimSpace(utils.ToString(modelDataMap[k]))
				if attrValue == "" {
					continue
				}
				opInfo.WriteString(fmt.Sprintf("%s: %s\n", attrName, attrValue))
			}

		case audit.ResourceRelationshipSubRes:
			// 创建数据关系
			// 示例：
			// 创建从属资源关系: 测试服务器 -> 测试模型
			nameField := fmt.Sprintf("%s_name", utils.ToString(dstMap["model_code"]))
			// 获取对应的data数据字段
			modelData, ok := dstMap["data"]
			if !ok {
				zap.L().Error("获取数据资源的data字段失败")
				return errors.New("获取数据资源的data字段失败")
			}
			modelDataMap, ok := modelData.(map[string]any)
			if !ok {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}
			sourceName, ok := modelDataMap[nameField]
			if !ok {
				zap.L().Error("获取数据资源的name字段失败")
				return errors.New("获取数据资源的name字段失败")
			}

			opInfo.WriteString("创建从属关系: \n")

			// 获取源资源模型
			sourceModel, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取源资源模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("[" + sourceModel.Name + "]: ")
			opInfo.WriteString(utils.ToString(sourceName))
			opInfo.WriteString(" -> ")

			// 获取目标资源
			targetDataId, ok := dstMap["parent_id"]
			if !ok {
				zap.L().Error("获取目标资源失败")
				return errors.New("获取目标资源失败")
			}

			if utils.ToString(targetDataId) == "" {
				zap.L().Error("父级资源id为空")
				return errors.New("父级资源id为空")
			}

			// 查询数据库获取资源
			targetData, err := a.store.ModelData().GetModelDataByID(ctx, utils.ToString(targetDataId))
			if err != nil {
				zap.L().Error("获取目标资源失败", zap.Error(err))
				return err
			}

			targetModel, err := a.store.Model().GetModelByCode(ctx, targetData.ModelCode)
			if err != nil {
				zap.L().Error("获取目标资源模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("[" + targetModel.Name + "]: ")
			opInfo.WriteString(targetData.Name())

		case audit.ModelRelationshipSubRes:
			// 创建模型主从关系
			// 示例：
			// 创建主从关系: 子 -> 父
			opInfo.WriteString("创建主从关系: ")
			attrs := dstMap["attrs"]
			attrMap, err := a.convertToDataMap(attrs)
			if err != nil {
				zap.L().Error("转换模型属性失败", zap.Error(err))
				return err
			}
			from := utils.ToString(attrMap["rel_from"])
			to := utils.ToString(attrMap["rel_to"])
			opInfo.WriteString(from)
			opInfo.WriteString(" -> ")
			opInfo.WriteString(to)
		default:
			zap.L().Error("未知资源类型", zap.String("resourceType", resourceType.String()))
			return ErrUnknownResourceType
		}

	case audit.ActionDelete:
		switch resourceType {
		case audit.ModelRes, audit.ModelGroupRes:
			// 删除模型，模型分组，模型属性，模型属性分组
			// 示例：删除了模型: 测试模型
			opInfo.WriteString("删除了")
			opInfo.WriteString(audit.ResourceTypeMap[resourceType])
			opInfo.WriteString(": ")
			opInfo.WriteString(utils.ToString(srcMap["name"]))

		case audit.ModelAttributesGroupRes:
			// 删除模型属性分组
			// 示例：删除了 [服务器] 下的属性分组: 测试模型属性分组
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}
			opInfo.WriteString("删除了 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 下的属性分组: ")
			opInfo.WriteString(utils.ToString(srcMap["name"]))

		case audit.ModelAttributeRes:
			// 删除模型字段示例
			// 删除了模型[服务器]下的字段: 测试字段
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("删除了模型[")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("]下的字段: ")
			opInfo.WriteString(utils.ToString(srcMap["name"]))

		case audit.ResourceDetailRes:
			// 删除数据
			// 示例：
			// 删除了 [服务器] 数据
			// 名称: 测试服务器
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("删除了 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 数据")
			opInfo.WriteString("\n")

			// 获取对应的data数据字段
			srcData, ok := srcMap["data"]
			if !ok {
				zap.L().Error("获取数据资源的data字段失败")
				return errors.New("获取数据资源的data字段失败")
			}
			modelDataMap, err := mapdata.NewFromInterface(srcData)
			if err != nil {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			identifyField := fmt.Sprintf("%s_code", model.Code)
			for k := range modelDataMap {
				if k == identifyField {
					continue
				}
				attrObj, ok := modelAttrNameMapping[k]
				// 如果对应的字段查询不到的话，那就查询不到了，有可能是data中存在旧字段，但是实际上model的属性已经删掉了，data中有脏数据存在
				// 因此针对这种情况只打印日志就可以了。
				if !ok {
					zap.L().Debug("无法查询到对应的模型属性字段名", zap.String("field_code", k))
					continue
				}
				attrName := attrObj.Name
				// 过滤掉空的值
				attrValue := strings.TrimSpace(utils.ToString(modelDataMap[k]))
				if attrValue == "" {
					continue
				}
				opInfo.WriteString(fmt.Sprintf("%s: %s\n", attrName, attrValue))
			}

		case audit.ResourceRelationshipSubRes:
			// 删除数据关系, 示例如下：
			// 删除了模型[服务器]下[测试服务器]的从属关系
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("删除了模型 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 下 [")

			// 获取对应的data数据字段
			modelData, ok := srcMap["data"]
			if !ok {
				zap.L().Error("获取数据资源的data字段失败")
				return errors.New("获取数据资源的data字段失败")
			}
			modelDataMap, err := a.convertToDataMap(modelData)
			if err != nil {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			name, _ := modelDataMap.Get(fmt.Sprintf("%s_name", model.Code))
			opInfo.WriteString(utils.ToString(name))
			opInfo.WriteString("] 的从属关系")

		default:
			zap.L().Error("unknown resource type", zap.String("resourceType", resourceType.String()))
			return ErrUnknownResourceType
		}

	case audit.ActionUpdate:
		switch resourceType {
		case audit.ModelGroupRes:
			// 更新模型分组, 模型属性分组，分组类只允许变更名字
			// 示例:
			// 更新了模型分组: 测试模型分组 → 测试模型分组2
			opInfo.WriteString("更新了")
			opInfo.WriteString(audit.ResourceTypeMap[resourceType])
			opInfo.WriteString(": ")
			opInfo.WriteString(utils.ToString(srcMap["name"]))
			opInfo.WriteString(" → ")
			opInfo.WriteString(utils.ToString(dstMap["name"]))

		case audit.ModelAttributesGroupRes:
			// 更新模型属性分组
			// 示例：更新了 [服务器] 下的属性分组: 测试模型属性分组 → 测试模型属性分组2
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("更新了 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 下的属性分组名称: ")
			opInfo.WriteString(utils.ToString(srcMap["name"]))
			opInfo.WriteString(" → ")
			opInfo.WriteString(utils.ToString(dstMap["name"]))
		case audit.ResourceDetailRes:
			// 更新数据
			// 示例:
			// 更新了 [服务器] 模型的数据: [测试服务器]
			// 名称: 测试服务器 → 测试服务器2
			// 内网IP: *********** → [空]
			emptyField := "[空]"
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString("更新了 [")
			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 模型的数据: [")

			// 对比新老数据字段
			srcData, ok := srcMap["data"]
			if !ok {
				zap.L().Error("获取数据资源的data字段失败")
				return errors.New("获取数据资源的data字段失败")
			}

			srcDataMap, err := a.convertToDataMap(srcData)
			if err != nil {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			dstData, ok := dstMap["data"]
			if !ok {
				zap.L().Error("获取数据资源的data字段失败")
				return errors.New("获取数据资源的data字段失败")
			}

			dstDataMap, err := a.convertToDataMap(dstData)
			if err != nil {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			// 构建名称字段
			nameField := fmt.Sprintf("%s_name", model.Code)
			srcName := srcDataMap[nameField]
			opInfo.WriteString(utils.ToString(srcName) + "] \n")

			// 获取srcDataMap与dstDataMap的区别
			// less: 源数据中不存在，目标数据中存在, 表示在新的数据中新增了字段
			// changes: 源数据与目标数据不一致，表示发生了变化的数据
			_, less, changes, err := srcDataMap.Different(dstDataMap)
			if err != nil {
				zap.L().Error("获取数据资源之间的区别失败", zap.Error(err))
				return err
			}

			// 新数据中新增的字段
			for k := range less {
				targetValue := utils.ToString(less[k])
				attrObj, ok := modelAttrNameMapping[k]
				if !ok {
					zap.L().Debug("无法查询到对应的模型属性字段名", zap.String("field_code", k))
					continue
				}
				attrName := attrObj.Name
				opInfo.WriteString(attrName)
				opInfo.WriteString(": ")
				opInfo.WriteString(emptyField)
				opInfo.WriteString(" -> ")
				opInfo.WriteString(targetValue)
				opInfo.WriteString("\n")
			}

			// 发生变化的数据
			for k := range changes {
				sourceValue := strings.TrimSpace(utils.ToString(srcDataMap[k]))
				targetValue := strings.TrimSpace(utils.ToString(dstDataMap[k]))

				// 如果源数据与目标数据一致，则不记录，说明并没有发生变化
				if sourceValue == targetValue {
					continue
				}

				// 如果目标数据为空，则设置为[空]
				if targetValue == "" {
					targetValue = emptyField
				}

				attrObj, ok := modelAttrNameMapping[k]
				if !ok {
					zap.L().Debug("无法查询到对应的模型属性字段名", zap.String("field_code", k))
					continue
				}
				attrName := attrObj.Name
				opInfo.WriteString(attrName)
				opInfo.WriteString(": ")
				opInfo.WriteString(sourceValue)
				opInfo.WriteString(" -> ")
				opInfo.WriteString(targetValue)
				opInfo.WriteString("\n")
			}

		case audit.ResourceRelationshipSubRes:
			// 更新已建立的资源从属关系的说明
			// 示例：
			// 更新 [虚拟机] 模型的数据 [测试虚拟机] 的从属关系描述:
			// 旧描述: 测试从属关系旧
			// 新描述: 测试从属关系新
			opInfo.WriteString("更新 [")
			model, err := a.getModelObject(ctx, srcMap, dstMap)
			if err != nil {
				zap.L().Error("获取模型失败", zap.Error(err))
				return err
			}

			opInfo.WriteString(model.Name)
			opInfo.WriteString("] 模型的数据 [")

			// 拿到源目的数据，变更的内容其实就是parent_desc字段
			srcMapData, err := a.convertToDataMap(srcMap)
			if err != nil {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			dstMapData, err := a.convertToDataMap(dstMap)
			if err != nil {
				zap.L().Error("数据资源中的data字段不是mapdata.MapData类型")
				return errors.New("数据资源中的data字段不是mapdata.MapData类型")
			}

			dstData, err := a.convertToDataMap(dstMapData["data"])
			if err != nil {
				zap.L().Error("数据转换至mapdata.MapData类型失败", zap.Error(err))
				return err
			}
			nameField := fmt.Sprintf("%s_name", model.Code)
			opInfo.WriteString(utils.ToString(dstData[nameField]))
			opInfo.WriteString("] 的从属关系描述: \n")
			opInfo.WriteString("旧描述: ")
			oldDesc := utils.ToString(srcMapData["parent_desc"])
			if oldDesc == "" {
				oldDesc = "[空]"
			}
			opInfo.WriteString(oldDesc)
			opInfo.WriteString("\n")
			opInfo.WriteString("新描述: ")
			newDesc := utils.ToString(dstMapData["parent_desc"])
			if newDesc == "" {
				newDesc = "[空]"
			}
			opInfo.WriteString(newDesc)
			opInfo.WriteString("\n")

		default:
			zap.L().Error("未知的资源类型", zap.String("resourceType", resourceType.String()))
			return ErrUnknownResourceType
		}
	default:
		zap.L().Error("未知的操作类型", zap.String("actionType", actionType.String()))
		return ErrUnknownActionType
	}

	// 构造auditLog
	a.auditLog = audit.NewAuditLog(evt, resourceType, actionType, operator, uniqueId)
	a.auditLog.Operation = opInfo.String()

	return nil
}

func (a *AuditEvent) Save(ctx context.Context) error {
	span, _ := apm.StartSpan(ctx, "SaveAuditLog", "sql")
	defer span.End()

	// 调用store层插入数据
	if err := a.store.Audit().SaveAuditLog(ctx, a.auditLog); err != nil {
		return err
	}

	return nil
}

func GenerateAuditLog(ctx context.Context, src, dst map[string]any, modelAttrs []v1.CommonModelAttribute,
	actionType audit.ActionType, resourceType audit.ResourceType, operator, uniqueId, modelCode string) (*audit.AuditLog, error) {
	span, _ := apm.StartSpan(ctx, "GenerateAuditLog", "service")
	defer span.End()

	// 构造模型属性的map，方便匹配字段名
	modelNameMap := make(map[string]string, len(modelAttrs))
	if len(modelAttrs) > 0 {
		for _, item := range modelAttrs {
			modelNameMap[item.Code] = item.Name
		}
	}

	auditLog := new(audit.AuditLog)
	auditLog.ResourceType = resourceType
	auditLog.Action = actionType
	auditLog.Operator = operator
	auditLog.UniqueId = uniqueId
	auditLog.Ts = time.Now().Unix()
	var operation strings.Builder

	switch actionType {
	case audit.ActionCreate:
		switch resourceType {
		case audit.ModelRes, audit.ModelGroupRes, audit.ModelAttributeRes, audit.ModelAttributesGroupRes:
			if modelName, ok := dst["name"]; ok {
				auditLog.Operation = utils.ToString(modelName)
				break
			} else {
				return auditLog, errors.New("get dst map name failed！")
			}

		case audit.ResourceDetailRes:
			modelName, ok := dst["model_name"]
			if !ok {
				return auditLog, errors.New("get dst map model_name failed！")
			}
			delete(dst, "model_name")

			operation.WriteString(fmt.Sprintf("新增了%s下的\n", utils.ToString(modelName)))
			for k := range dst {
				operation.WriteString(fmt.Sprintf("%s: %s -> %s\n", utils.ToString(modelNameMap[k]), "", utils.ToString(dst[k])))
			}
			auditLog.Operation = operation.String()
		}

	case audit.ActionDelete:
		switch resourceType {
		case audit.ModelRes, audit.ModelGroupRes, audit.ModelAttributesGroupRes:
			if modelName, ok := src["name"]; ok {
				auditLog.Operation = utils.ToString(modelName.(string))
				break
			}
			return auditLog, errors.New("get dst map name failed！")

		case audit.ModelAttributeRes:
			attrName, ok := src["name"]
			if !ok {
				return auditLog, errors.New("get dst map name failed！")
			}
			model, ok := src["model"]
			if !ok {
				return auditLog, errors.New("get dst map name failed！")
			}
			auditLog.Operation = "【" + utils.ToString(model.(string)) + "】模型的【" + utils.ToString(attrName.(string)) + "】字段已经被删除"

		case audit.ResourceDetailRes:
			// 删除数据
			modelName, ok := src["model_name"]
			if !ok {
				return auditLog, errors.New("get src map model_name failed！")
			}
			// 去掉model_name
			delete(src, "model_name")

			operation.WriteString(fmt.Sprintf("删除了%s下的%s\n", utils.ToString(modelName), utils.ToString(src["name"])))
			// 这里不需要比较直接记录数据本来的内容就可以
			for k := range src {
				operation.WriteString(fmt.Sprintf("%s: %s\n", utils.ToString(modelNameMap[k]), utils.ToString(src[k])))
			}
			auditLog.Operation = operation.String()
		}

	case audit.ActionUpdate:
		switch resourceType {
		case audit.ModelGroupRes, audit.ModelAttributesGroupRes:
			auditLog.Operation = "从【" + utils.ToString(src["name"].(string)) + "】" + "变更为【" + utils.ToString(dst["name"].(string)) + "】"
		case audit.ResourceDetailRes:
			modelName, ok := dst["model_name"]
			if !ok {
				return auditLog, errors.New("get dst map model_name failed！")
			}
			delete(dst, "model_name")
			operation.WriteString(fmt.Sprintf("修改了%s\n", utils.ToString(modelName)))
			for k := range dst {
				if src[k] != dst[k] {
					operation.WriteString(fmt.Sprintf("%s: %s -> %s\n", utils.ToString(modelNameMap[k]), utils.ToString(src[k]), utils.ToString(dst[k])))
				}
			}
			auditLog.Operation = operation.String()
		}
	}

	return auditLog, nil
}

func SaveAuditLog(ctx context.Context, store store.Factory, auditLog audit.AuditLog) error {
	span, _ := apm.StartSpan(ctx, "saveAuditLog", "sql")
	defer span.End()

	if err := store.Audit().SaveAuditLog(ctx, &auditLog); err != nil {
		return err
	}

	return nil
}
