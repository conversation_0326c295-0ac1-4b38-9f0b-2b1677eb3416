package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/convert"
	"ks-knoc-server/pkg/utils"

	"github.com/mohae/deepcopy"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type HostService interface {
	// OnRack 上架
	OnRack(ctx context.Context, operator string, orr *v1.OnRackRequest) error
	// OffRack 下架
	OffRack(ctx context.Context, operator string, orr *v1.OffRackRequest) error
	// OnPlaceHolder 上架占位设备
	OnPlaceHolder(ctx context.Context, operator string, holder *apiv1.PlaceHolder) error
	// OffPlaceHolder 下架占位设备
	OffPlaceHolder(ctx context.Context, operator string, holder *apiv1.PlaceHolder) error
	// RackSpare 获取机柜的空闲U位
	RackSpare(ctx context.Context, rackID string, devHeight int) ([]apiv1.CommonSelectIntValue, error)
}

type hostService struct {
	store store.Factory
}

var _ HostService = (*hostService)(nil)

func newHostService(srv *service) HostService {
	return &hostService{
		store: srv.store,
	}
}

// OnRack 设备上架
func (h *hostService) OnRack(ctx context.Context, operator string, orr *v1.OnRackRequest) error {
	zap.L().Debug("OnRack Service Called")
	span, ctx := apm.StartSpan(ctx, "OnRackService", "service")
	defer span.End()

	// 1. 检测设备的ID是否合法
	deviceData, err := h.store.ModelData().GetModelDataByID(ctx, orr.DeviceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("设备ID不存在", zap.String("device_id", orr.DeviceID))
			return errno.ErrDataNotExists.Add("设备ID " + orr.DeviceID + "不存在")
		}
		zap.L().Error("获取设备数据失败", zap.Error(err))
		return err
	}
	zap.L().Debug("上架设备", zap.String("device_name", deviceData.Name()))

	// 设备数据信息
	deviceDataInfo := fmt.Sprintf("设备 [%s] ", deviceData.Name())
	device := v1.Dev(*deviceData)

	// 2. 并且设备从属关系的父亲必须是Rack，才可以执行上架的操作，否则报错
	rel, err := h.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{
		"model_code":     device.ModelCode, // 设备模型代码
		"type_name":      "relationship",   // 属性类型必须是关系类型
		"attrs.rel_type": 1,                // 从属关系类型必须是1，即从属关系；
	}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("设备模型没有配置从属关系", zap.String("device_id", deviceData.ID))
			return errno.ErrModelRelationNotExist.Add(deviceDataInfo + "对应的模型没有配置从属关系")
		}
		zap.L().Error("获取设备从属关系失败", zap.Error(err))
		return err
	}

	// 校验设备模型与机柜模型是否存在从属关系
	if utils.ToString(rel.Attrs["rel_to"]) != "rack" {
		zap.L().Error("设备模型与机柜模型不存在从属关系", zap.String("device_id", deviceData.ID))
		return errno.ErrModelRelationNotExist.Add(deviceDataInfo + "所属的模型与机柜模型不存在从属关系")
	}

	// 3. 如果设备合法的话，检查一下要上架的机柜是否是合法的，首先就需要确认机柜的ID所对应的数据是否存在
	r, err := h.store.ModelData().GetModelDataByID(ctx, orr.RackID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("机柜ID不存在", zap.String("rack_id", orr.RackID))
			return errno.ErrDataNotExists.Add("机柜ID" + orr.RackID + "不存在")
		}
		zap.L().Error("获取机柜数据失败", zap.Error(err))
		return err
	}
	zap.L().Debug("获取机柜数据成功", zap.String("rack_name", r.Name()))
	zap.L().Debug("上架U位", zap.Int("start_u", orr.StartU))

	rackInfo := fmt.Sprintf("机柜 [%s] ", r.Name())

	// 4. 数据存在并不代表这个数据就是机柜，还需要确认一下这个数据是否是机柜模型的数据
	rack, err := v1.ToRackData(*r)
	if err != nil {
		zap.L().Error("rack_id对应的数据并不是机柜模型", zap.String("rack_id", orr.RackID), zap.String("model_code", r.ModelCode))
		return err
	}

	// 5. 检查设备的父机柜ID是否为空，如果为空的话，报错
	if device.ParentID == "" {
		zap.L().Error("设备父机柜ID为空", zap.String("device_id", deviceData.ID))
		return errno.ErrDataParentNotSet.Addf("%s的父机柜ID为空，请先设置设备的父机柜ID", deviceDataInfo)
	}

	// 6. 检查设备的父机柜ID是否与提交的机柜ID一致，如果不一致的话，报错，这里必须要保证设备的父机柜ID与提交的机柜ID一致，否则就说明关系不对
	if device.ParentID != orr.RackID {
		zap.L().Error("设备父机柜ID与提交的机柜ID不一致",
			zap.String("device_id", deviceData.ID),
			zap.String("parent_id", device.ParentID),
			zap.String("rack_id", orr.RackID))
		return errno.ErrModelDataMisMatch.Addf("%s的父机柜ID与您提交的机柜ID不一致，请确保您的机柜ID正确", deviceDataInfo)
	}

	// 7. 上述均为数据合法性校验，走到这里说明数据都是合法的，并且这一条数据已经和机柜建立了从属关系，接下来就看一下设备是否设置了高度
	height := device.Height()

	// 如果原始高度未设置，并且提交的height也是0的话说明有问题，不满足条件
	// 1、原始高度有值，提交的高度为0，因为我更新的时候，可以不提交高度，这个时候以原始高度为准
	// 2、原始高度为0，提交的高度大于0，直接更新height
	// 3、原始高度和提交的高度大于0，以最新的为准
	if height == 0 && orr.Height == 0 {
		zap.L().Error("设备高度为空", zap.String("device_id", deviceData.ID))
		return errno.ErrDeviceHeightEmpty.Addf("%s的高度为空，请先设置设备的高度", deviceDataInfo)
	}

	// 只有更新的高度不为0的时候，才更新原始的height，但是这个高度不可以是无限制的，高度应该符合一定的规则
	if orr.Height > 0 {
		if orr.Height >= rack.Height() {
			return errno.ErrDeviceHeightInvalid.Add(deviceDataInfo + "的高度超过了机柜的最大高度，请确保您的设备高度正确")
		}

		height = orr.Height
		deviceData.Data[fmt.Sprintf("%s_height", device.ModelCode)] = height
	}

	zap.L().Debug("设备高度", zap.Int("height", height))

	// 8. 设备设置了高度，那么就需要检查一下机柜上是否有空余的位置。
	spare, err := checkRackSpare(ctx, r, orr.StartU, height, h.store, device)
	if err != nil {
		zap.L().Error("检测空余位置失败", zap.Error(err))
		return err
	}

	if !spare {
		zap.L().Info("机柜空间不足",
			zap.String("rack_name", rackInfo),
			zap.Int("start_u", orr.StartU),
			zap.Int("height", height))
		return errno.ErrRackPositionConflict.Addf("%s的起始U位置已被占用，请确保您的机柜空间足够", rackInfo)
	}

	// 计算结束U位
	endU := orr.StartU + height - 1
	if endU > rack.Height() {
		zap.L().Error("设备高度超过了机柜的最大高度", zap.String("device_id", deviceData.ID), zap.Int("height", height), zap.Int("rack_height", rack.Height()))
		return errno.ErrRackPositionError.Addf("%s的高度超过了机柜的最大高度，请确保您的设备高度正确", deviceDataInfo)
	}

	zap.L().Debug("机柜空间充足",
		zap.String("rack_name", rackInfo),
		zap.Int("start_u", orr.StartU),
		zap.Int("end_u", endU),
		zap.Int("height", height))

	// set device start u
	dataCopy := deepcopy.Copy(deviceData)
	oldData := dataCopy.(*v1.ModelData)
	device.Data[fmt.Sprintf("%s_start_u", device.ModelCode)] = orr.StartU

	// 更新设备U位
	if _, err = h.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
		Id:           deviceData.ID,
		ModelData:    deviceData,
		UpdateParent: false,
	}); err != nil {
		zap.L().Error("更新设备U位失败", zap.Error(err))
		return err
	}

	// 审计日志
	au := &AuditEvent{store: h.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ResourceDetailRes,
		audit.ActionUpdate,
		operator,
		oldData.ID,
		oldData,
		deviceData,
	); err != nil {
		zap.L().Error("生成审计日志失败", zap.Error(err))
		return err
	}
	if err := au.Save(ctx); err != nil {
		zap.L().Error("保存审计日志失败", zap.Error(err))
		return err
	}

	return nil
}

// OffRack 下架, 机柜中的设备下架，目前只清除U位信息
func (h *hostService) OffRack(ctx context.Context, operator string, orr *v1.OffRackRequest) error {
	zap.L().Debug("OffRack Service Called")
	span, ctx := apm.StartSpan(ctx, "OffRackService", "service")
	defer span.End()

	// check the device id is valid
	deviceData, err := h.store.ModelData().GetModelDataByID(ctx, orr.DeviceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Add("设备ID " + orr.DeviceID + "不存在")
		}
		return err
	}

	field := fmt.Sprintf("%s_start_u", deviceData.ModelCode)
	if _, exist := deviceData.Data[field]; !exist {
		return errno.ErrDataNotExists.Add("设备ID " + orr.DeviceID + "没有配置起始U位字段")
	}

	// clean device start u
	oldData := deepcopy.Copy(deviceData).(*v1.ModelData)
	deviceData.Data[field] = ""

	// get model attr of device model
	attrs, err := h.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{"model_code": deviceData.ModelCode}, bson.M{})
	if err != nil {
		return err
	}

	if len(attrs) == 0 {
		return errno.ErrModelDoNotHaveAttr.Add("设备ID" + orr.DeviceID + "对应的模型没有配置属性，数据异常，请联系管理员")
	}

	modelName, err := h.store.Model().GetModelName(ctx, deviceData.ModelCode)
	if err != nil {
		return err
	}

	// update data
	if _, err := h.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
		Id:           deviceData.ID,
		ModelData:    deviceData,
		UpdateParent: false,
	}); err != nil {
		return err
	}

	// audit log
	src, err := convert.StructToMap(oldData.Data)
	if err != nil {
		return err
	}

	dst, err := convert.StructToMap(deviceData.Data)
	if err != nil {
		return err
	}

	// 借助dst 传递模型名称
	dst["model_name"] = modelName

	auditLog, err := GenerateAuditLog(ctx, src, dst, attrs, audit.ActionUpdate, audit.ResourceDetailRes, operator, oldData.ID, oldData.ModelCode)
	if err != nil {
		return err
	}
	if err = SaveAuditLog(ctx, h.store, *auditLog); err != nil {
		return err
	}

	return nil
}

func (h *hostService) OnPlaceHolder(ctx context.Context, operator string, holder *apiv1.PlaceHolder) error {
	zap.L().Debug("OnPlaceHolder Service Called")
	span, ctx := apm.StartSpan(ctx, "OnPlaceHolderService", "service")
	defer span.End()

	// 先看看机柜在不在
	r, err := h.store.ModelData().GetModelDataByID(ctx, holder.RackID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Add("机柜ID" + holder.RackID + "不存在")
		}
	}

	// 看看到底是不是机柜的modeldata
	rack, err := v1.ToRackData(*r)
	if err != nil {
		return err
	}

	// 检测高度是否异常
	if holder.Height >= rack.Height() {
		return errno.ErrDeviceHeightInvalid.Add("占位设备高度超过了机柜的最大高度，请确保您的设备高度正确")
	}

	// 根据用户提交的数据初始化一个占位设备
	phDev, err := v1.NewPlaceHolder(holder.StartU, holder.Height)
	if err != nil {
		return err
	}

	// 避免这个占位设备的高度超过了机柜的最大高度
	if phDev.EndU() > rack.Height() {
		return errno.ErrRackPositionError.Add("占位设备的高度超过了机柜的最大高度，请确保您的设备高度正确")
	}

	// 备注一些信息
	phDev.Name = holder.Name
	phDev.Comment = holder.Comment
	phDev.PType = holder.PType

	// 看看还有没有地方放这个占位的设备
	spare, err := checkRackSpare(ctx, r, holder.StartU, holder.Height, h.store, phDev)
	if err != nil {
		return err
	}

	// 对应的位置已经占用上了
	rackInfo := fmt.Sprintf("机柜 [%s] ", r.Name())
	if !spare {
		return errno.ErrRackPositionConflict.Add(rackInfo + "的起始U位置已被占用，请确保您的机柜空间足够")
	}

	// 保存一份旧的数据
	oldRackInfo := deepcopy.Copy(r).(*v1.ModelData)

	// 如果说没有占用的话，那么就可以更新
	placeHolders, exist := r.Data.Get(RackPlaceHolder)
	if !exist || placeHolders == "" {
		// 如果说不存在这个字段的话，说明之前没有初始化过，我们自己初始化一下
		phList := make([]*v1.PlaceHolder, 0)
		phList = append(phList, phDev)

		// 注意序列化
		serializedData, err := json.Marshal(phList)
		if err != nil {
			return err
		}
		r.Data.Set(RackPlaceHolder, string(serializedData))
	} else {
		// 存在字段的话，就先把它反序列化出来
		var phList []*v1.PlaceHolder
		if err := json.Unmarshal([]byte(utils.ToString(placeHolders)), &phList); err != nil {
			return err
		}

		// 追加进去
		phList = append(phList, phDev)
		serializedData, err := json.Marshal(phList)
		if err != nil {
			return err
		}
		r.Data.Set(RackPlaceHolder, string(serializedData))
	}

	// get model attr of device model
	attrs, err := h.store.ModelAttributes().GetModelAttributesByCustomFilter(
		ctx,
		bson.M{"model_code": r.ModelCode},
		bson.M{})
	if err != nil {
		return err
	}

	if len(attrs) == 0 {
		return errno.ErrModelDoNotHaveAttr.Add(rackInfo + "对应的模型没有配置属性")
	}

	modelMap, _ := h.store.Model().GetModelMapping(ctx)
	rackModel := modelMap[r.ModelCode]

	// 回写数据库
	if _, err := h.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
		Id:           r.ID,
		ModelData:    r,
		UpdateParent: false,
	}); err != nil {
		zap.L().Error("更新模型数据失败", zap.Error(err))
		return err
	}

	// audit log
	src, err := convert.StructToMap(oldRackInfo.Data)
	if err != nil {
		return err
	}

	dst, err := convert.StructToMap(r.Data)
	if err != nil {
		return err
	}

	// 借助dst 传递模型名称
	dst["model_name"] = rackModel.Name

	auditLog, err := GenerateAuditLog(ctx, src, dst, attrs, audit.ActionUpdate, audit.ResourceDetailRes, operator, oldRackInfo.ID, oldRackInfo.ModelCode)
	if err != nil {
		return err
	}
	if err = SaveAuditLog(ctx, h.store, *auditLog); err != nil {
		return err
	}

	return nil
}

func (h *hostService) OffPlaceHolder(ctx context.Context, operator string, holder *apiv1.PlaceHolder) error {
	zap.L().Debug("OffPlaceHolder Service Called")
	span, ctx := apm.StartSpan(ctx, "OffPlaceHolderService", "service")
	defer span.End()

	// 先看看机柜在不在
	r, err := h.store.ModelData().GetModelDataByID(ctx, holder.RackID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Add("机柜ID" + holder.RackID + "不存在")
		}
	}

	// 备份一个老数据
	oldRackInfo := deepcopy.Copy(r).(*v1.ModelData)

	// 看看到底是不是机柜的model data
	if _, err := v1.ToRackData(*r); err != nil {
		return err
	}

	// 查一下所有的占位设备
	placeHolders, exist := r.Data.Get(RackPlaceHolder)
	if !exist {
		return errno.ErrDataNotExists.Add("机柜ID" + holder.RackID + "没有占位设备")
	}

	var phList []*v1.PlaceHolder
	if err := json.Unmarshal([]byte(utils.ToString(placeHolders)), &phList); err != nil {
		return err
	}

	// 找到对应的占位设备, 因为占位设备并没有唯一的ID，只是一个占位的标识，这就要求名称，p_type，起始U位，高度都一样
	phExist := false
	updated := make([]*v1.PlaceHolder, 0)
	for _, ph := range phList {
		if (ph.PType == holder.PType) && (ph.Start == holder.StartU) && (ph.DeviceHeight == holder.Height) &&
			(ph.Name == holder.Name) {
			phExist = true
			continue
		}
		updated = append(updated, ph)
	}

	if phExist {
		if len(updated) == 0 {
			r.Data.Set(RackPlaceHolder, "[]")
		} else {
			serializedData, err := json.Marshal(updated)
			if err != nil {
				return err
			}
			r.Data.Set(RackPlaceHolder, string(serializedData))
		}

		// get model attr of device model
		attrs, err := h.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{"model_code": r.ModelCode}, bson.M{})
		if err != nil {
			return err
		}

		rackInfo := fmt.Sprintf("机柜 [%s] ", r.Name())
		if len(attrs) == 0 {
			return errno.ErrModelDoNotHaveAttr.Add(rackInfo + "对应的模型没有配置属性")
		}

		modelMap, _ := h.store.Model().GetModelMapping(ctx)
		rackModel := modelMap[r.ModelCode]

		// 回写到数据库
		if _, err := h.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
			Id:           r.ID,
			ModelData:    r,
			UpdateParent: false,
		}); err != nil {
			zap.L().Error("更新模型数据失败", zap.Error(err))
			return err
		}

		// audit log
		src, err := convert.StructToMap(oldRackInfo.Data)
		if err != nil {
			return err
		}

		dst, err := convert.StructToMap(r.Data)
		if err != nil {
			return err
		}

		// 借助dst 传递模型名称
		dst["model_name"] = rackModel.Name

		auditLog, err := GenerateAuditLog(ctx, src, dst, attrs, audit.ActionUpdate, audit.ResourceDetailRes, operator, oldRackInfo.ID, oldRackInfo.ModelCode)
		if err != nil {
			return err
		}
		if err = SaveAuditLog(ctx, h.store, *auditLog); err != nil {
			return err
		}
	} else {
		return errno.ErrDataNotExists.Add("机柜ID" + holder.RackID + "中找不到要删除的占位设备")
	}

	return nil
}

func (h *hostService) RackSpare(ctx context.Context, rackID string, devHeight int) ([]apiv1.CommonSelectIntValue, error) {
	zap.L().Debug("RackSpare Service Called")
	span, ctx := apm.StartSpan(ctx, "RackSpareService", "service")
	defer span.End()

	// 检测机柜ID是否合法
	rd, err := h.store.ModelData().GetModelDataByID(ctx, rackID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("rack id " + rackID + " not exist")
			return nil, errno.ErrDataNotExists.Add("机柜ID" + rackID + "不存在")
		}
		zap.L().Error(err.Error())
		return nil, err
	}
	rack, err := v1.ToRackData(*rd)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 获取机柜占位设备
	deviceList := make([]v1.Device, 0)
	dataList, err := h.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"parent_id": rack.ID}, nil)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 获取机柜占位符
	placeHolders := make([]v1.PlaceHolder, 0)
	phs, ok := rack.Data.Get(RackPlaceHolder)
	if ok && phs != "" {
		if err := json.Unmarshal([]byte(utils.ToString(phs)), &placeHolders); err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}
	}

	// 计算占位信息
	for _, data := range dataList {
		deviceList = append(deviceList, v1.Dev(*data))
	}

	for _, ph := range placeHolders {
		deviceList = append(deviceList, ph)
	}

	rackRange := make([][2]int, 0)
	for _, device := range deviceList {
		r, valid := device.Range()
		zap.L().Debug("rack range information", zap.Any("r", r), zap.Any("deviceID", device.DataID()))
		if valid {
			rackRange = append(rackRange, r)
		}
	}

	// 针对占用区域进行排序
	sort.Slice(rackRange, func(i, j int) bool {
		return rackRange[i][0] < rackRange[j][0]
	})

	// Debug 打点，输出机柜其实位置的range数组
	zap.L().Debug("rack range information", zap.Any("rackRange", rackRange))

	// 初始化一个二维切片用于保存可以放置设备的区间
	var available [][]int

	// 记录已经占用的U位，用map保存是为了高效快速的查询
	occupiedMap := make(map[int]bool)

	// 机柜的总高度
	totalHeight := rack.Height()

	// 便利机柜占用区间，将区间内的U位都记录到occupiedMap中
	for _, interval := range rackRange {
		for i := interval[0]; i <= interval[1]; i++ {
			occupiedMap[i] = true
		}
	}
	// 起始从1开始算，假设机柜U位为42，假设机柜全部都是空的，做多也就能放到39U(39,40,41,42)
	for i := 1; i <= totalHeight-devHeight+1; i++ {
		canPlace := true
		for j := 0; j < devHeight; j++ {
			if occupiedMap[i+j] {
				canPlace = false
				break
			}
		}
		if canPlace {
			available = append(available, []int{i, i + devHeight - 1})
		}
	}

	// 返回可用区间的起始位置
	startPos := make([]apiv1.CommonSelectIntValue, 0)
	for _, interval := range available {
		startPos = append(startPos, apiv1.CommonSelectIntValue{
			Name: fmt.Sprintf("%d", interval[0]),
			Code: interval[0],
		})
	}

	return startPos, nil
}
