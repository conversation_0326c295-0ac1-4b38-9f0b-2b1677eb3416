package v1

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// 缓存项结构
type cacheItem struct {
	data      []v1.DataTreePath
	timestamp time.Time
}

// 后代数据缓存
type descendantsCache struct {
	cache   map[string]*cacheItem
	mu      sync.RWMutex
	ttl     time.Duration // 缓存过期时间
	maxSize int           // 最大缓存条目数
}

// 创建新的缓存实例
func newDescendantsCache(ttl time.Duration, maxSize int) *descendantsCache {
	return &descendantsCache{
		cache:   make(map[string]*cacheItem),
		ttl:     ttl,
		maxSize: maxSize,
	}
}

// 获取缓存数据
func (dc *descendantsCache) get(key string) ([]v1.DataTreePath, bool) {
	dc.mu.RLock()
	defer dc.mu.RUnlock()

	item, exists := dc.cache[key]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Since(item.timestamp) > dc.ttl {
		return nil, false
	}

	return item.data, true
}

// 设置缓存数据
func (dc *descendantsCache) set(key string, data []v1.DataTreePath) {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 如果缓存已满，清理过期数据
	if len(dc.cache) >= dc.maxSize {
		dc.cleanExpired()
	}

	// 如果还是满的，删除最老的数据
	if len(dc.cache) >= dc.maxSize {
		dc.evictOldest()
	}

	dc.cache[key] = &cacheItem{
		data:      data,
		timestamp: time.Now(),
	}
}

// 清理过期数据
func (dc *descendantsCache) cleanExpired() {
	now := time.Now()
	for key, item := range dc.cache {
		if now.Sub(item.timestamp) > dc.ttl {
			delete(dc.cache, key)
		}
	}
}

// 删除最老的数据
func (dc *descendantsCache) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, item := range dc.cache {
		if oldestKey == "" || item.timestamp.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.timestamp
		}
	}

	if oldestKey != "" {
		delete(dc.cache, oldestKey)
	}
}

// 清空缓存
func (dc *descendantsCache) clear() {
	dc.mu.Lock()
	defer dc.mu.Unlock()
	dc.cache = make(map[string]*cacheItem)
}

// 删除特定key的缓存
func (dc *descendantsCache) delete(key string) {
	dc.mu.Lock()
	defer dc.mu.Unlock()
	delete(dc.cache, key)
}

// 获取缓存统计信息
func (dc *descendantsCache) stats() map[string]interface{} {
	dc.mu.RLock()
	defer dc.mu.RUnlock()

	return map[string]interface{}{
		"size":     len(dc.cache),
		"max_size": dc.maxSize,
		"ttl":      dc.ttl.String(),
	}
}

// 批量删除缓存（根据前缀）
func (dc *descendantsCache) deleteByPrefix(prefix string) int {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	deleted := 0
	for key := range dc.cache {
		if strings.HasPrefix(key, prefix) {
			delete(dc.cache, key)
			deleted++
		}
	}
	return deleted
}

// ClearDescendantsCache 清空后代数据缓存
func (mds *modelDataService) ClearDescendantsCache() {
	mds.descendantsCache.clear()
	zap.L().Info("已清空后代数据缓存")
}

// GetDescendantsCacheStats 获取后代数据缓存统计信息
func (mds *modelDataService) GetDescendantsCacheStats() map[string]interface{} {
	return mds.descendantsCache.stats()
}

// InvalidateDescendantsCacheByPrefix 根据前缀批量清理缓存
func (mds *modelDataService) InvalidateDescendantsCacheByPrefix(prefix string) int {
	deleted := mds.descendantsCache.deleteByPrefix(prefix)
	zap.L().Info("根据前缀批量清理缓存", zap.String("prefix", prefix), zap.Int("deleted", deleted))
	return deleted
}

// getDescendants 获取子孙后代的数据id
func (mds *modelDataService) getDescendants(ctx context.Context, dataID string) ([]string, error) {
	ids := make([]string, 0)
	paths, err := mds.getDescendantsPath(ctx, dataID)
	if err != nil {
		return ids, err
	}

	if len(paths) == 0 {
		return ids, err
	}

	for _, path := range paths {
		ids = append(ids, path.ChildID)
	}

	return ids, nil
}

func (mds *modelDataService) getDescendantsPath(ctx context.Context, dataID string) ([]v1.DataTreePath, error) {
	// 先尝试从缓存获取
	if cached, found := mds.descendantsCache.get(dataID); found {
		zap.L().Debug("从缓存获取后代数据", zap.String("dataID", dataID), zap.Int("count", len(cached)))
		return cached, nil
	}

	var (
		paths = make([]v1.DataTreePath, 0)
		err   error
	)
	paths, err = mds.store.ModelData().GetDataPathByFilter(ctx, bson.M{"parent_id": dataID})
	if err != nil {
		return paths, err
	}

	// 将结果存入缓存
	mds.descendantsCache.set(dataID, paths)
	zap.L().Debug("从数据库获取后代数据并缓存", zap.String("dataID", dataID), zap.Int("count", len(paths)))

	return paths, nil
}

func (mds *modelDataService) getAncestors(ctx context.Context, dataID string) ([]string, error) {
	ids := make([]string, 0)
	paths, err := mds.getAncestorsPath(ctx, dataID)
	if err != nil {
		return ids, err
	}

	if len(paths) == 0 {
		return ids, err
	}

	for _, path := range paths {
		ids = append(ids, path.ParentID)
	}

	return ids, nil
}

func (mds *modelDataService) getAncestorsPath(ctx context.Context, dataID string) ([]v1.DataTreePath, error) {
	var (
		paths  = make([]v1.DataTreePath, 0)
		filter = bson.M{"child_id": dataID}
		err    error
	)

	paths, err = mds.store.ModelData().GetDataPathByFilter(ctx, filter)
	if err != nil {
		return paths, err
	}

	if len(paths) == 0 {
		return paths, err
	}

	return paths, nil
}

// getAncestorDataObjs 迭代方式获取所有父节点数据（非递归，性能更好）
func (mds *modelDataService) getAncestorDataObjs(ctx context.Context, data *v1.ModelData) ([]*v1.ModelData, error) {
	parents := make([]*v1.ModelData, 0)
	currentData := data

	for currentData.ParentID != "" {
		// 获取父节点
		parent, err := mds.store.ModelData().GetModelDataByID(ctx, currentData.ParentID)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				// 父节点不存在，结束循环
				break
			}
			return nil, err
		}

		// 将父节点加入结果
		parents = append(parents, parent)

		// 继续向上查找
		currentData = parent
	}

	return parents, nil
}

func (mds *modelDataService) getDescendantDataObjs(ctx context.Context, data *v1.ModelData) ([]*v1.ModelData, error) {
	descendants := make([]*v1.ModelData, 0)

	// 使用队列进行广度优先遍历
	queue := []*v1.ModelData{data}
	visited := make(map[string]bool) // 防止循环引用
	visited[data.ID] = true

	for len(queue) > 0 {
		// 取出队列头部元素
		current := queue[0]
		queue = queue[1:]

		// 查找当前节点的直接子节点
		children, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"parent_id": current.ID}, nil)
		if err != nil {
			return nil, err
		}

		// 将子节点加入结果和队列
		for _, child := range children {
			// 防止循环引用
			if !visited[child.ID] {
				visited[child.ID] = true
				descendants = append(descendants, child)
				queue = append(queue, child)
			}
		}
	}

	return descendants, nil
}

func (mds *modelDataService) getDataTreePathV2(ctx context.Context, data *v1.ModelData) ([]*v1.DataTreePath, error) {
	paths := make([]*v1.DataTreePath, 0)

	// 使用递归方式获取所有父节点
	ancestors, err := mds.getAncestorDataObjs(ctx, data)
	if err != nil {
		return nil, err
	}

	// 获取所有子代
	descendants, err := mds.getDescendantDataObjs(ctx, data)
	if err != nil {
		return nil, err
	}

	// 根据ancestors和descendants构建完整的矩阵路径
	// 1. 所有祖先节点到当前节点的路径
	for i, ancestor := range ancestors {
		paths = append(paths, &v1.DataTreePath{
			ParentID:       ancestor.ID,
			ParentName:     ancestor.Name(),
			ChildID:        data.ID,
			ChildName:      data.Name(),
			ChildModelCode: data.ModelCode,
			Depth:          i + 1, // 深度从1开始
		})
	}

	// 2. 当前节点到所有后代节点的路径
	for _, descendant := range descendants {
		paths = append(paths, &v1.DataTreePath{
			ParentID:       data.ID,
			ParentName:     data.Name(),
			ChildID:        descendant.ID,
			ChildName:      descendant.Name(),
			ChildModelCode: descendant.ModelCode,
			Depth:          1, // 直接子节点深度为1，间接子节点需要计算实际深度
		})
	}

	// 3. 所有祖先节点到所有后代节点的路径（通过当前节点传递）
	for i, ancestor := range ancestors {
		for _, descendant := range descendants {
			paths = append(paths, &v1.DataTreePath{
				ParentID:       ancestor.ID,
				ParentName:     ancestor.Name(),
				ChildID:        descendant.ID,
				ChildName:      descendant.Name(),
				ChildModelCode: descendant.ModelCode,
				Depth:          i + 2, // 祖先到当前节点的深度 + 当前节点到后代的深度
			})
		}
	}

	zap.L().Debug("构建矩阵路径完成",
		zap.Int("ancestors", len(ancestors)),
		zap.Int("descendants", len(descendants)),
		zap.Int("total_paths", len(paths)))

	return paths, nil
}

// getDataTreePath 获取数据路径，使用闭包表的设计方式
func (mds *modelDataService) getDataTreePath(
	ctx context.Context,
	p []*v1.DataTreePath,
	d *v1.ModelData,
	currentPath *v1.DataTreePath,
	OrgSons []v1.DataTreePath) ([]*v1.DataTreePath, error) {

	// 构造一个path路径，父亲指向d的parent，孩子指向OrgID
	path := v1.NewDataTreePath()
	path.ParentID = d.ParentID
	path.ChildID = d.ID
	path.ChildName = d.Name()
	path.ChildModelCode = d.ModelCode

	// 校验一下这条数据是否已经存在了
	exist, err := mds.DataPathExists(ctx, path.ParentID, path.ChildID)
	if err != nil {
		return nil, err
	}

	// 如果不存在的话，那么说明就是需要进行添加的路径，深度+1，加到p中，p即一开始传入的initialDataPath
	if !exist {
		if currentPath != nil {
			path.Depth = currentPath.Depth + 1
		} else {
			path.Depth += 1
		}

		p = append(p, path)
	}

	// 获取当前节点的所有子孙后代
	sonPaths := make([]*v1.DataTreePath, 0)
	if len(OrgSons) > 0 {
		for _, son := range OrgSons {
			subPath := v1.NewDataTreePath()
			subPath.ParentID = d.ParentID
			subPath.ChildID = son.ChildID
			subPath.ChildModelCode = son.ChildModelCode
			subPath.ChildName = son.ChildName
			subPath.Depth += son.Depth + 1

			// 子孙节点也要进行校验
			exist, err := mds.DataPathExists(ctx, subPath.ParentID, subPath.ChildID)
			if err != nil {
				return nil, err
			}

			if !exist {
				sonPaths = append(sonPaths, subPath)
			}
		}
	}

	// 获取当前节点的父亲节点对应的ModelData对象
	parent, err := mds.store.ModelData().GetModelDataByID(ctx, d.ParentID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Add("父数据不存在")
		}
		return nil, err
	}

	path.ParentName = parent.Name()
	for _, sonPath := range sonPaths {
		sonPath.ParentName = parent.Name()
	}

	p = append(p, sonPaths...)

	// 递归出口：判断一下传递过来的数据的父亲ID是否为空，如果为空的话，证明到根了。直接返回就可以了
	if parent.ParentID == "" {
		return p, nil
	}

	// 获取当前节点d的子孙后代
	OrgSons, err = mds.getDescendantsPath(ctx, parent.ID)
	if err != nil {
		return nil, err
	}

	// 注意这个是偶部分子路径还没有录入到数据库中，所以这个时候要把p中的子路径也加到OrgSons中
	for _, s := range p {
		if s.ParentID == parent.ID {
			OrgSons = append(OrgSons, *s)
		}
	}

	// 当前的父亲节点仍然有父级数据，继续递归
	return mds.getDataTreePath(ctx, p, parent, path, OrgSons)
}

// GetDataTreePath 获取数据路径，使用闭包表的设计方式
func (mds *modelDataService) GetDataTreePath(ctx context.Context, data *v1.ModelData) ([]*v1.DataTreePath, error) {
	zap.L().Debug("GetDataTreePath service called")
	span, ctx := apm.StartSpan(ctx, "GetDataTreePathService", "service")
	defer span.End()

	// 首次调用的时候，必须保证data的parentID不为空
	if data.ParentID == "" {
		return nil, errno.ErrDataParentNotSet.Add("调用获取路径的时候，必须保证数据的父亲ID不为空")
	}

	// 通过路径表，查询一下当前数据的后代数据
	children, err := mds.store.ModelData().GetDataPathByFilter(ctx, bson.M{"parent_id": data.ID})
	if err != nil {
		return nil, err
	}

	// 初始化一个空的数据路径的切片
	initialDataPath := make([]*v1.DataTreePath, 0)
	pathResult, err := mds.getDataTreePath(ctx, initialDataPath, data, nil, children)

	if err != nil {
		return nil, err
	}

	return pathResult, nil
}

// updateDataTreePath 更新数据路径，不存在的则进行创建，存在的则跳过
// 接收一个DataTreePath的指针的切片，遍历每一个tree path并进行查询
// 如果可以查询到数据，证明这个tree path已经存在，直接跳过
// 如果对应的tree path不存在，则创建这条tree path
func (mds *modelDataService) updateDataTreePath(ctx context.Context, operator string, p []*v1.DataTreePath) error {
	if len(p) == 0 {
		return nil
	}

	// 收集需要清理缓存的parentID
	parentIDsToInvalidate := make(map[string]bool)

	for _, path := range p {
		paths, err := mds.store.ModelData().GetDataPathByFilter(ctx, bson.M{
			"parent_id": path.ParentID,
			"child_id":  path.ChildID,
		})

		if err != nil {
			zap.L().Error(err.Error())
			return err
		}

		if len(paths) > 0 {
			zap.L().Info("数据路径已经存在")
			continue
		}

		if err := mds.store.ModelData().CreateDataTreePath(ctx, operator, path); err != nil {
			zap.L().Error(err.Error())
			return err
		}

		// 标记需要清理缓存的parentID
		parentIDsToInvalidate[path.ParentID] = true
	}

	// 清理相关缓存
	for parentID := range parentIDsToInvalidate {
		mds.descendantsCache.delete(parentID)
		zap.L().Debug("清理后代数据缓存", zap.String("parentID", parentID))
	}

	return nil
}

func (mds *modelDataService) DataPathExists(ctx context.Context, parentID, childID string) (bool, error) {
	paths, err := mds.store.ModelData().GetDataPathByFilter(ctx, bson.M{
		"parent_id": parentID,
		"child_id":  childID,
	})

	if err != nil {
		return false, err
	}

	return len(paths) > 0, nil
}

func (mds *modelDataService) getDeletedDataPath(ctx context.Context, dataID string) ([]v1.DataTreePath, error) {
	sons, err := mds.getDescendants(ctx, dataID)
	if err != nil {
		return nil, err
	}

	sons = append(sons, dataID)

	// 获取当前节点的所有父节点
	parents, err := mds.getAncestors(ctx, dataID)
	if err != nil {
		return nil, err
	}

	toBeDeletedPath := make([]v1.DataTreePath, 0)
	for _, p := range parents {
		for _, s := range sons {
			toBeDeletedPath = append(toBeDeletedPath, v1.DataTreePath{
				ParentID: p,
				ChildID:  s,
			})
		}
	}

	return toBeDeletedPath, nil
}

func (mds *modelDataService) DeleteDataPath(ctx context.Context, operator, dataID string) error {
	zap.L().Debug("DeleteDataPath service called")
	span, ctx := apm.StartSpan(ctx, "DeleteDataPathService", "service")
	defer span.End()

	// 获取当前数据的所有子孙后代
	toBeDeletedPath, err := mds.getDeletedDataPath(ctx, dataID)
	if err != nil {
		return err
	}

	if len(toBeDeletedPath) == 0 {
		return nil
	}

	// 收集需要清理缓存的parentID
	parentIDsToInvalidate := make(map[string]bool)
	filter := make([]bson.M, 0)
	for _, p := range toBeDeletedPath {
		filter = append(filter, bson.M{
			"parent_id": p.ParentID,
			"child_id":  p.ChildID,
		})
		parentIDsToInvalidate[p.ParentID] = true
	}

	cnt, err := mds.store.ModelData().DeleteDataPathByFilter(ctx, bson.M{"$or": filter})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Debug("数据路径不存在，无需删除")
			return nil
		}
		zap.L().Error(err.Error())
		return err
	}

	if utils.ToInt(cnt) != len(filter) {
		zap.L().Error("要删除的数据路径数量与实际删除的数量不一致，可能存在脏数据，请联系管理员")
		return errno.InternalServerError.Add("要删除的数据路径数量与实际删除的数量不一致，可能存在脏数据，请联系管理员")
	}

	// 清理相关缓存
	for parentID := range parentIDsToInvalidate {
		mds.descendantsCache.delete(parentID)
		zap.L().Debug("删除数据路径后清理缓存", zap.String("parentID", parentID))
	}

	return nil
}
