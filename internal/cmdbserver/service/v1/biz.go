package v1

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	"ks-knoc-server/internal/cmdbserver/store"
	api "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	message "ks-knoc-server/internal/common/base/message/cmdbserver"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type BizService interface {
	GetITOffice(ctx context.Context) ([]*api.OfficeListResponse, error)
	GetDcCascadeData(ctx context.Context, officeCode string) ([]*v1.TreeNode, error)
	GetDeviceBrandInfo(ctx context.Context, deviceModelCode string) ([]v1.SelectDataTree, error)
	GetDeviceInfo(ctx context.Context, sn string) (*api.DevicePositionResponse, error)
	GetDevicePositionString(ctx context.Context, sn string) (string, error)
	GetIdcByOfficeCode(ctx context.Context, officeCode string) ([]*api.CommonSelectStringValue, error)
	GetRackByIDC(ctx context.Context, idcCode string) ([]*api.CommonSelectStringValue, error)
	GetDeviceInfoList(ctx context.Context, snList []string) ([]*api.DeviceInfoResponse, error)

	// 通用部分接口
	GetOfficeList(ctx context.Context) ([]*api.CommonSelectStringValue, error)
	GetIdcList(ctx context.Context) ([]*api.CommonSelectStringValue, error)
	GetManufacturers(ctx context.Context) ([]*api.CommonSelectStringValue, error)
	GetDeviceModels(ctx context.Context) ([]*api.CommonSelectStringValue, error)

	// 2025-04-15 新增
	GetOffices(ctx context.Context) (message.QueryOfficeListMessage, error)
	OfflineDevice(ctx context.Context, operator string, req *api.OpenAPIOfflineDeviceRequest) error
}

type bizService struct {
	store store.Factory
}

var _ BizService = (*bizService)(nil)

func newBizService(s *service) *bizService {
	return &bizService{
		store: s.store,
	}
}

// resetDeviceInfo 根据不同类型的设备清理设备的信息
func (b *bizService) resetDeviceInfo(d *v1.ModelData) error {
	switch d.ModelCode {
	case "server":
	case "switch":
	case "router":
	case "firewall":
	case "loadbalancing":
	case "audit":
	case "secure":
	case "transmission":
	case "wlc":
	case "netflow":
	case "storage":
	default:
		return errors.New("不支持的设备类型")
	}
	return nil
}

// OfflineDevice 下线设备, 提交了设备下线申请后，调用该逻辑进行下线处理
func (b *bizService) OfflineDevice(ctx context.Context, operator string, req *api.OpenAPIOfflineDeviceRequest) error {
	zap.L().Debug("OfflineDevice Service Called")
	span, ctx := apm.StartSpan(ctx, "OfflineDevice", "service")
	defer span.End()

	// 根据sn查询到所有的设备，然后进行下线处理
	snList := req.DeviceSN
	dataList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"meta_data.universal_sn": bson.M{
			"$in": snList,
		},
	}, nil)
	if err != nil {
		zap.L().Error("获取设备列表失败", zap.Error(err))
		return err
	}

	// 如果设备列表为空，则直接返回
	if len(dataList) == 0 {
		return errors.New("无法查询到要下架的设备")
	}

	// 下线要做的事情是
	// 1. 清理设备的从属关系，主要是和机柜之间的关系
	// 2. 将设备一些字段信息清空
	// 3. 将设备状态设置为下线
	// 4. 将设备下线时间写入到数据库
	// 5. 返回结果
	for _, d := range dataList {
		// 将一些字段的信息清空
		if err := b.resetDeviceInfo(d); err != nil {
			zap.L().Error("清理设备信息失败",
				zap.Error(err),
				zap.String("model_code", d.ModelCode))
			continue
		}

		// 将设备状态设置为下线
		d.Data[fmt.Sprintf("%s_status", d.ModelCode)] = "已下线"
		d.MetaData["universal_status"] = "已下线"

		// 更新设备信息
		if _, err := b.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
			ModelData:    d,
			UpdateParent: true,
			ParentID:     "", // 设备下线需要清理从属关系，所以需要传入空字符串
			ParentDesc:   "", // 设备下线需要清理从属关系，所以需要传入空字符串
		}); err != nil {
			zap.L().Error("更新设备信息失败", zap.Error(err))
			return err
		}
	}

	return nil
}

// GetOffices 获取所有职场
func (b *bizService) GetOffices(ctx context.Context) (message.QueryOfficeListMessage, error) {
	zap.L().Debug("GetOffices Service Called")
	span, ctx := apm.StartSpan(ctx, "GetOffices", "service")
	defer span.End()

	officeList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "office",
	}, nil)
	if err != nil {
		zap.L().Error("获取职场列表失败", zap.Error(err))
		return nil, err
	}

	resp := message.NewQueryOfficeListMessage()
	for _, data := range officeList {
		resp.Add(&message.QueryOfficeMessage{
			ID:          data.ID,
			Code:        utils.ToString(data.Data["office_code_name"]), // 职场编码
			Name:        utils.ToString(data.Data["office_name"]),      // 职场名称
			Description: utils.ToString(data.Data["office_remark"]),    // 职场备注
			Status:      utils.ToString(data.Data["office_status"]),    // 职场状态
			OfficeType:  utils.ToString(data.Data["office_type"]),      // 职场类型
			Address:     utils.ToString(data.Data["office_address"]),   // 职场地址
			HostByIDC:   utils.ToBool(data.Data["office_host_by_idc"]), // 职场是否托管机房
			CreateTime:  utils.ToString(data.CreateAt),                 // 创建时间
			UpdateTime:  utils.ToString(data.UpdateAt),                 // 更新时间
		})
	}

	return resp, nil
}

func (b *bizService) GetITOffice(ctx context.Context) ([]*api.OfficeListResponse, error) {
	zap.L().Debug("GetITOffice Service Called")
	span, ctx := apm.StartSpan(ctx, "GetITOffice", "service")
	defer span.End()

	dataList, err := b.store.ModelData().GetModelDataByCustomFilter(
		ctx,
		bson.M{"model_code": "office"},
		bson.M{"id": 1, "data": 1, "model_code": 1})
	if err != nil {
		return nil, err
	}

	officeList := make([]*api.OfficeListResponse, 0)
	for _, data := range dataList {
		code := fmt.Sprintf("%s_%s", data.ID, data.Name())
		officeList = append(officeList, &api.OfficeListResponse{
			Name: data.Name(),
			Code: code,
		})
	}
	return officeList, nil
}

func (b *bizService) GetDcCascadeData(ctx context.Context, officeCode string) ([]*v1.TreeNode, error) {
	zap.L().Debug("GetDcCascadeData Service Called")
	span, ctx := apm.StartSpan(ctx, "GetDcCascadeData", "service")
	defer span.End()

	var (
		idcMap       = make(map[string]*v1.TreeNode)
		physicalRoot = v1.NewTreeNode()
	)

	physicalRoot.Key = "physical"
	physicalRoot.Title = "物理视图"
	physicalRoot.NodeType = v1.NodeTypeRoot

	// 首先根据code获取职场信息
	office, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code":            "office",
		"data.office_code_name": officeCode,
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	if len(office) == 0 {
		return nil, errno.ErrDataNotExists.Add(fmt.Sprintf("职场 %s 不存在", officeCode))
	}

	// 拿机房
	idcList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "idc",
		"parent_id":  office[0].ID,
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, idc := range idcList {
		idcNode := &v1.TreeNode{
			Key:      idc.ID,
			Title:    idc.Data[fmt.Sprintf("%s_name", idc.ModelCode)].(string),
			NodeType: v1.NodeTypeIDC,
			Children: make([]*v1.TreeNode, 0),
		}

		physicalRoot.Children = append(physicalRoot.Children, idcNode)
		idcMap[idc.ID] = idcNode
	}

	// 拿机柜
	rackList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"model_code": "rack"}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, rack := range rackList {
		rackNode := v1.TreeNode{
			Key:      rack.ID,
			Title:    rack.Data[fmt.Sprintf("%s_name", rack.ModelCode)].(string),
			NodeType: v1.NodeTypeRack,
			Children: make([]*v1.TreeNode, 0),
		}

		if idc, ok := idcMap[rack.ParentID]; ok {
			idc.Children = append(idc.Children, &rackNode)
		}
	}

	// 给机柜排序
	for _, idc := range idcMap {
		sort.Sort(idc.Children)
	}

	resp := make([]*v1.TreeNode, 0)

	for _, physical := range physicalRoot.Children {
		resp = append(resp, physical)
	}

	// 首先拿到所有的
	return resp, nil
}

func (b *bizService) GetDeviceBrandInfo(ctx context.Context, deviceModelCode string) ([]v1.SelectDataTree, error) {
	zap.L().Debug("GetDeviceBrandInfo Service Called")
	span, ctx := apm.StartSpan(ctx, "GetDeviceBrandInfo", "service")
	defer span.End()

	// 对应模型设备的型号
	attrs, err := b.store.ModelAttributes().GetModelAttributesByCustomFilter(
		ctx, bson.M{"code": fmt.Sprintf("%s_model", deviceModelCode)}, nil)

	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	if len(attrs) != 1 {
		return nil, errno.ErrModelAttrNotExist.Add(fmt.Sprintf("类型 %s 不存在品牌字段", deviceModelCode))
	}

	attr := attrs[0]
	modelField, err := attr.ToSelectField()
	if err != nil {
		return nil, err
	}

	// TODO: 2024-12-10 目前没有品牌管理，在数据库中，我也不是很好查询，因为涉及到查询cmdb，但是其他模块和这个服务有网络隔离
	// 所以临时的解决方案，是把对应要保存的数据带在id里。拼出来这样一个比较奇葩的结构
	var resp []v1.SelectDataTree
	dt := modelField.Attrs.ChainData.DataTree
	for _, tree := range dt {
		sdt := new(v1.SelectDataTree)
		newID := fmt.Sprintf("%s@#%s", tree.ID, tree.Name)
		sdt.ID = newID
		sdt.Name = tree.Name
		sdt.Children = make([]v1.SelectOption, 0)
		// 遍历子节点
		for _, child := range tree.Children {
			so := v1.SelectOption{
				ID:   fmt.Sprintf("%s@#%s", child.ID, child.Name),
				Name: child.Name,
			}
			sdt.Children = append(sdt.Children, so)
		}
		resp = append(resp, *sdt)
	}

	return resp, nil
}

// GetDeviceInfo 获取设备信息, 如果设备查不到才抛出错误，其他关联信息如机柜，机房等查不到则不认为是错误
func (b *bizService) GetDeviceInfo(ctx context.Context, sn string) (*api.DevicePositionResponse, error) {
	dataList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"meta_data.universal_sn": bson.M{
		"$regex":   ".*" + sn + ".*",
		"$options": "i",
	}}, nil)
	if err != nil {
		return nil, err
	}

	if len(dataList) > 1 {
		zap.L().Warn("根据SN查询设备信息返回多条数据，数据可能存在不准确的情况")
		return nil, errno.ErrDataInvalid.Add(fmt.Sprintf("系统内存在多条设备 %s 的信息", sn))
	}

	if len(dataList) == 0 {
		return nil, errno.ErrDataNotExists.Add(fmt.Sprintf("系统内不存在设备 %s", sn))
	}

	data := dataList[0]

	mp, err := b.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 初始化设备位置信息
	pos := api.DevicePositionResponse{}
	pos.DeviceSN = sn

	// 查询数据所属模型的中文名
	modelDisplayName := mp[data.ModelCode].Name
	pos.DeviceType = modelDisplayName

	// 如果没有parent信息，说明并没有和机柜建立关联，直接返回即可
	if data.ParentID == "" {
		return &pos, nil
	}

	rackStartU, exist := data.Data[fmt.Sprintf("%s_start_u", data.ModelCode)]
	if !exist {
		return &pos, nil
	}

	pos.StartU = utils.ToInt(rackStartU)

	rackHeight, exist := data.Data[fmt.Sprintf("%s_height", data.ModelCode)]
	if !exist {
		return &pos, nil
	}
	pos.DeviceHeight = utils.ToInt(rackHeight)

	// 获取机柜信息
	rackData, err := b.store.ModelData().GetModelDataByID(ctx, data.ParentID)
	if err != nil {
		zap.L().Error("查询设备机柜信息失败", zap.Error(err))
		return &pos, nil
	}

	pos.Rack = rackData.Name()

	// 机柜没配置所属机房
	if rackData.ParentID == "" {
		return &pos, nil
	}

	// 获取机房信息
	idc, err := b.store.ModelData().GetModelDataByID(ctx, rackData.ParentID)
	if err != nil {
		zap.L().Error("查询设备机房信息失败", zap.Error(err))
		return &pos, nil
	}
	pos.IDC = idc.Name()
	if idc.ParentID == "" {
		return &pos, nil
	}

	// 获取职场信息
	office, err := b.store.ModelData().GetModelDataByID(ctx, idc.ParentID)
	if err != nil {
		zap.L().Error("查询设备职场信息失败", zap.Error(err))
		return &pos, nil
	}
	pos.Office = office.Name()
	if office.ParentID == "" {
		return &pos, nil
	}

	// 获取城市信息
	city, err := b.store.ModelData().GetModelDataByID(ctx, office.ParentID)
	if err != nil {
		zap.L().Error("查询设备城市信息失败", zap.Error(err))
		return &pos, nil
	}
	pos.Area = city.Name()

	return &pos, nil
}

// GetDevicePositionString 获取设备机架位信息, 返回字符串
func (b *bizService) GetDevicePositionString(ctx context.Context, sn string) (string, error) {
	dataList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"meta_data.universal_sn": bson.M{
		"$regex":   ".*" + sn + ".*",
		"$options": "i",
	}}, nil)
	if err != nil {
		return "", err
	}

	if len(dataList) > 1 {
		zap.L().Warn("根据SN查询设备信息返回多条数据，数据可能存在不准确的情况")
		return "", errno.ErrDataInvalid.Add(fmt.Sprintf("系统内存在多条设备 %s 的信息", sn))
	}

	if len(dataList) == 0 {
		return "", errno.ErrDataNotExists.Add(fmt.Sprintf("系统内不存在设备 %s", sn))
	}

	data := dataList[0]

	mp, err := b.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return "", err
	}

	// 初始化设备位置信息
	pos := &api.DevicePositionResponse{}
	pos.DeviceSN = sn

	// 查询数据所属模型的中文名
	modelDisplayName := mp[data.ModelCode].Name
	pos.DeviceType = modelDisplayName

	// 如果没有parent信息，说明并没有和机柜建立关联，直接返回即可
	if data.ParentID == "" {
		return "", errno.ErrDataNotExists.Add(fmt.Sprintf("sn为 [%s] 的设备未设置机架位信息, 请联系管理员处理", sn))
	}

	// 如果说有的话，查询对应的机柜信息
	rackStartU, exist := data.Data[fmt.Sprintf("%s_start_u", data.ModelCode)]
	if !exist {
		return "", errno.ErrDataInvalid.Add(fmt.Sprintf("sn为 [%s] 的设备未设置机架位信息, 请联系管理员处理", sn))
	}
	pos.StartU = utils.ToInt(rackStartU)
	if pos.StartU == 0 {
		return "", errno.ErrDataInvalid.Add(fmt.Sprintf("sn为 [%s] 的设备未设置机架位信息, 请联系管理员处理", sn))
	}

	rackHeight, exist := data.Data[fmt.Sprintf("%s_height", data.ModelCode)]
	if !exist {
		return "", errno.ErrDataInvalid.Add(fmt.Sprintf("sn为 [%s] 的设备未设置设备高度, 请联系管理员处理", sn))
	}
	pos.DeviceHeight = utils.ToInt(rackHeight)
	if pos.DeviceHeight == 0 {
		return "", errno.ErrDataInvalid.Add(fmt.Sprintf("sn为 [%s] 的设备未设置设备高度, 请联系管理员处理", sn))
	}

	// 获取机柜信息
	rackData, err := b.store.ModelData().GetModelDataByID(ctx, data.ParentID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.L().Warn("根据机柜ID查询机柜信息失败", zap.String("device_id", data.ID), zap.String("sn", sn))
		return "", errno.ErrDataNotExists.Add("无法查到对应的机柜信息")
	}
	if err != nil {
		zap.L().Error("查询设备机柜信息失败", zap.Error(err))
		return "", err
	}
	pos.Rack = rackData.Name()

	// 机柜没配置所属机房
	if rackData.ParentID == "" {
		return pos.RackInfo(), nil
	}

	// 获取机房信息
	idc, err := b.store.ModelData().GetModelDataByID(ctx, rackData.ParentID)
	if err != nil {
		zap.L().Error("查询设备机房信息失败", zap.Error(err))
		return pos.RackInfo(), err
	}
	pos.IDC = idc.Name()
	if idc.ParentID == "" {
		return pos.IDCInfo(), err
	}

	// 获取职场信息
	office, err := b.store.ModelData().GetModelDataByID(ctx, idc.ParentID)
	if err != nil {
		zap.L().Error("查询设备职场信息失败", zap.Error(err))
		return pos.IDCInfo(), err
	}
	pos.Office = office.Name()
	if office.ParentID == "" {
		return pos.OfficeInfo(), err
	}

	// 获取城市信息
	city, err := b.store.ModelData().GetModelDataByID(ctx, office.ParentID)
	if err != nil {
		zap.L().Error("查询设备城市信息失败", zap.Error(err))
		return pos.OfficeInfo(), err
	}
	pos.Area = city.Name()

	return pos.AreaInfo(), nil
}

func (b *bizService) GetIdcByOfficeCode(ctx context.Context, officeCode string) ([]*api.CommonSelectStringValue, error) {
	zap.L().Debug("GetIdcByOfficeCode Service Called")
	span, ctx := apm.StartSpan(ctx, "GetIdcByOfficeCode", "service")
	defer span.End()

	// 根据职场ID查询机房信息
	idcList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "idc",
		"parent_id":  officeCode,
	}, nil)
	if err != nil {
		zap.L().Error("查询机房信息失败", zap.Error(err))
		return nil, err
	}

	// 初始化机房response
	idcResp := make([]*api.CommonSelectStringValue, 0)
	for _, idc := range idcList {
		idcResp = append(idcResp, &api.CommonSelectStringValue{
			Name: idc.Name(),
			Code: fmt.Sprintf("%s_%s", idc.ID, idc.Name()),
		})
	}

	return idcResp, nil
}

func (b *bizService) GetRackByIDC(ctx context.Context, idcCode string) ([]*api.CommonSelectStringValue, error) {
	zap.L().Debug("GetRackByIDC Service Called")
	span, ctx := apm.StartSpan(ctx, "GetRackByIDC", "service")
	defer span.End()

	// 根据机房ID查询机柜信息
	rackList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "rack",
		"parent_id":  idcCode,
	}, nil)
	if err != nil {
		zap.L().Error("查询机柜信息失败", zap.Error(err))
		return nil, err
	}

	// 初始化机柜response
	rackResp := make([]*api.CommonSelectStringValue, 0)
	for _, rack := range rackList {
		rackResp = append(rackResp, &api.CommonSelectStringValue{
			Name: rack.Name(),
			Code: fmt.Sprintf("%s_%s", rack.ID, rack.Name()),
		})
	}

	return rackResp, nil
}

func (b *bizService) GetDeviceInfoList(ctx context.Context, snList []string) ([]*api.DeviceInfoResponse, error) {
	zap.L().Debug("GetDeviceInfoList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetDeviceInfoList", "service")
	defer span.End()

	// 初始化response
	resp := make([]*api.DeviceInfoResponse, 0)

	// 查询设备信息
	dataList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"meta_data.universal_sn": bson.M{"$in": snList}}, nil)
	if err != nil {
		// 如果查询不到设备信息，记录一下日志
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Warn("查询设备信息失败", zap.Error(err))
		}
		zap.L().Error("查询设备信息失败", zap.Error(err))
		return nil, err
	}

	// 如果设备信息为空的话，那么直接返回
	if len(dataList) == 0 {
		for _, sn := range snList {
			resp = append(resp, &api.DeviceInfoResponse{
				DeviceSN: sn,
				Status:   "invalid",
				Error: struct {
					Code    int    `json:"code"`
					Message string `json:"message"`
				}{
					Code:    errno.ErrDCIMDeviceNotExists.Code,
					Message: errno.ErrDCIMDeviceNotExists.ZhCN,
				},
				MetaData: struct {
					Name       string `json:"name"`
					DeviceType string `json:"device_type"`
					DeviceSN   string `json:"device_sn"`
					Brand      string `json:"brand"`
					Model      string `json:"model"`
					Office     string `json:"office"`
					IDC        string `json:"idc"`
					Rack       string `json:"rack"`
					Owner      string `json:"owner"`
					Position   int    `json:"position"`
				}{},
			})
		}
		return resp, nil
	}

	if len(dataList) > len(snList) {
		zap.L().Error("正常情况下不应该出现这种问题，请联系管理员排查")
		return nil, errno.InternalServerError.Add("查询到的设备数量大于请求数量, 请联系管理员")
	}

	// 这种情况下只能是缺设备的情况
	if len(dataList) != len(snList) {
		zap.L().Warn("查询设备信息返回数据数量与请求数量不一致", zap.Int("request_count", len(snList)), zap.Int("response_count", len(dataList)))
		diff := make(mapdata.MapData)
		for _, data := range dataList {
			dev, err := v1.ToDev(*data)
			if err != nil {
				zap.L().Error(err.Error())
				return nil, err
			}
			diff.Set(dev.SN(), data)
		}

		// 把缺失的设备信息添加到response中，只不过只填写sn，其他的都为空，目前判定这个设备是否存在的标志就是name是不是为空。
		for _, sn := range snList {
			if _, ok := diff[sn]; !ok {
				resp = append(resp, &api.DeviceInfoResponse{
					DeviceSN: sn,
					Status:   "invalid",
					Error: struct {
						Code    int    `json:"code"`
						Message string `json:"message"`
					}{
						Code:    errno.ErrDCIMDeviceNotExists.Code,
						Message: errno.ErrDCIMDeviceNotExists.ZhCN,
					},
					MetaData: struct {
						Name       string `json:"name"`
						DeviceType string `json:"device_type"`
						DeviceSN   string `json:"device_sn"`
						Brand      string `json:"brand"`
						Model      string `json:"model"`
						Office     string `json:"office"`
						IDC        string `json:"idc"`
						Rack       string `json:"rack"`
						Owner      string `json:"owner"`
						Position   int    `json:"position"`
					}{},
				})
			}
		}
	}

	// 根据有效设备填充response信息
	for _, data := range dataList {
		dev, err := v1.ToDev(*data)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}
		// 初始化response
		dataResp := &api.DeviceInfoResponse{}
		dataResp.DeviceSN = dev.SN()

		// 先把数据库中的数据填充到response中
		resp = append(resp, dataResp)

		// 初始化MetaData
		dataResp.MetaData = struct {
			Name       string `json:"name"`
			DeviceType string `json:"device_type"`
			DeviceSN   string `json:"device_sn"`
			Brand      string `json:"brand"`
			Model      string `json:"model"`
			Office     string `json:"office"`
			IDC        string `json:"idc"`
			Rack       string `json:"rack"`
			Owner      string `json:"owner"`
			Position   int    `json:"position"`
		}{
			Name:       dev.Name(),
			DeviceType: data.ModelCode,
			DeviceSN:   dev.SN(),
			Brand:      dev.Brand(),
			Model:      dev.Model(),
			Position:   dev.StartU(),
			Owner:      dev.Owner(),
		}

		// 先获取一下设备机柜的信息
		rackID := data.ParentID
		if rackID == "" {
			// 说明设备并没有和机柜建立关联
			zap.L().Error("设备信息中parent_id为空")

			dataResp.Status = "invalid"
			dataResp.Error = struct {
				Code    int    `json:"code"`
				Message string `json:"message"`
			}{
				Code:    errno.ErrDCIMDeviceNotOnRack.Code,
				Message: errno.ErrDCIMDeviceNotOnRack.ZhCN,
			}
			dataResp.MetaData.Rack = ""
			dataResp.MetaData.IDC = ""
			dataResp.MetaData.Office = ""
			continue
		}

		dataResp.Status = "valid"
		dataResp.Error = struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}{
			Code:    0,
			Message: "",
		}

		// 如果rackID不是空的话，那么查询rack信息
		rackData, err := b.store.ModelData().GetModelDataByID(ctx, rackID)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		dataResp.MetaData.Rack = rackData.Name()

		// 看一下机柜所属的机房有没有配置
		idcID := rackData.ParentID
		if idcID == "" {
			zap.L().Error("机柜信息中parent_id为空")
			dataResp.MetaData.IDC = ""
			dataResp.MetaData.Office = ""
			continue
		}

		// 如果idcID不是空的话，那么查询idc信息
		idcData, err := b.store.ModelData().GetModelDataByID(ctx, idcID)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		dataResp.MetaData.IDC = idcData.Name()

		// 看一下idc所属的职场有没有配置
		officeID := idcData.ParentID
		if officeID == "" {
			zap.L().Error("机房信息中parent_id为空")
			dataResp.MetaData.Office = ""
			continue
		}

		// 如果officeID不是空的话，那么查询office信息
		officeData, err := b.store.ModelData().GetModelDataByID(ctx, officeID)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		dataResp.MetaData.Office = officeData.Name()
	}

	return resp, nil
}

func (b *bizService) GetOfficeList(ctx context.Context) ([]*api.CommonSelectStringValue, error) {
	zap.L().Debug("GetOfficeList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetOfficeList", "service")
	defer span.End()

	officeList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "office",
	}, nil)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	if len(officeList) == 0 {
		return nil, nil
	}

	resp := make([]*api.CommonSelectStringValue, len(officeList))
	for i := 0; i < len(officeList); i++ {
		office := officeList[i]
		resp[i] = &api.CommonSelectStringValue{
			Name: office.Name(),
			Code: office.ID,
		}
	}

	return resp, nil
}

func (b *bizService) GetIdcList(ctx context.Context) ([]*api.CommonSelectStringValue, error) {
	zap.L().Debug("GetIdcList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetIdcList", "service")
	defer span.End()

	idcList, err := b.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "idc",
	}, nil)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	if len(idcList) == 0 {
		return nil, nil
	}

	resp := make([]*api.CommonSelectStringValue, len(idcList))
	for i := 0; i < len(idcList); i++ {
		idc := idcList[i]
		resp[i] = &api.CommonSelectStringValue{
			Name: idc.Name(),
			Code: idc.ID,
		}
	}

	return resp, nil
}

func (b *bizService) GetManufacturers(ctx context.Context) ([]*api.CommonSelectStringValue, error) {
	zap.L().Debug("GetManufacturers Service Called")
	span, ctx := apm.StartSpan(ctx, "GetManufacturers", "service")
	defer span.End()

	attrs, err := b.store.ModelAttributes().GetModelAttrMap(ctx, "code")
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	allValidOptions := make(mapdata.MapData)
	for _, attr := range attrs {
		brandFieldCode := fmt.Sprintf("%s_brand", attr.ModelCode)
		if attr.Code != brandFieldCode {
			continue
		}

		// 所有的品牌类型肯定都是select类型的字段
		selectAttr := attrs[brandFieldCode]
		selectField, err := selectAttr.ToSelectField()
		if err != nil {
			zap.L().Error(err.Error())
			continue
		}

		for _, o := range selectField.Attrs.Options {
			v := strings.TrimSpace(o.Name)
			if _, ok := allValidOptions[v]; !ok {
				allValidOptions[v] = struct{}{}
			}
		}
	}

	resp := make([]*api.CommonSelectStringValue, 0)
	for k := range allValidOptions {
		resp = append(resp, &api.CommonSelectStringValue{
			Name: k,
			Code: k,
		})
	}

	return resp, nil
}

func (b *bizService) GetDeviceModels(ctx context.Context) ([]*api.CommonSelectStringValue, error) {
	zap.L().Debug("GetDeviceModels Service Called")
	span, ctx := apm.StartSpan(ctx, "GetDeviceModels", "service")
	defer span.End()

	attrs, err := b.store.ModelAttributes().GetModelAttrMap(ctx, "code")
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	allValidOptions := make(mapdata.MapData)
	for _, attr := range attrs {
		modelFieldCode := fmt.Sprintf("%s_model", attr.ModelCode)
		if attr.Code != modelFieldCode {
			continue
		}

		selectAttr := attrs[modelFieldCode]
		selectField, err := selectAttr.ToSelectField()
		if err != nil {
			zap.L().Error(err.Error())
			continue
		}

		for _, o := range selectField.Attrs.Options {
			v := strings.TrimSpace(o.Name)
			if _, ok := allValidOptions[v]; !ok {
				allValidOptions[v] = struct{}{}
			}
		}
	}

	resp := make([]*api.CommonSelectStringValue, 0)
	for k := range allValidOptions {
		resp = append(resp, &api.CommonSelectStringValue{
			Name: k,
			Code: k,
		})
	}

	return resp, nil
}
