package v1

import (
	"context"
	"regexp"
	"sort"
	"strings"

	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	kjson "ks-knoc-server/pkg/json"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
)

// AggregationInfo ElasticSearch 聚合信息
type AggregationInfo struct {
	Buckets []struct {
		Key      string `json:"key"`
		DocCount int64  `json:"doc_count"`
	} `json:"buckets"`
}

// SearchAggregationInfo 搜索聚合信息
func (mds *modelDataService) SearchAggregationInfo(ctx context.Context, searchFields []string, keyword string) (apiv1.NestedModelList, []string, error) {
	var (
		aggregationQuery = elastic.NewBoolQuery()
		err              error
	)

	// 去除特殊字符
	escapedKeyword := regexp.QuoteMeta(keyword)

	// 把搜索字段作为聚合条件，注意添加到Query中去
	for _, field := range searchFields {
		// 索引keyword类型的field
		aggregationQuery.Should(elastic.NewRegexpQuery("data."+field+".keyword", ".*"+escapedKeyword+".*").CaseInsensitive(true))
		// 索引text类型的field
		// aggregationQuery.Should(elastic.NewRegexpQuery("data."+field, ".*"+escapedKeyword+".*").CaseInsensitive(true))
	}

	// 设置聚合，集合字段为model_code，注意对应Index的Mapping的model_code字段的类型必须为keyword不可以为text，否则无法聚合
	// 所以闲着没事别删除index，如果有需要清空里面的数据就可以了，也不要重建mapping。
	// 在这里我设置了一个bucket Size，默认的BucketSize只有10个大小，这就意味着超过10个的聚合结果将被截断，所以需要设置一个足够大的值
	// 如果说在返回结果中的sum_other_doc_count字段中，你发现了它的值是大于0的，那么就代表着你的聚合结果被截断了，你需要增大bucket Size的值
	// 目前BucketSize已经很大了，再大我觉得在页面上也无法很好的展示出来了, 注意，这里的size和query的size是两码事，这是bucket的size
	// query中的size是限制返回匹配到的数据的条数，我们在下面设置size为0，表示不返回具体的数据，只返回聚合信息
	aggregations := elastic.NewTermsAggregation().Field("meta.model_code.keyword").Size(1000)

	// 这个查询仅查询聚合信息，不查询具体数据。不管有没有传递modelsFilter过滤条件，都需要返回所有与keyword匹配的模型，因此需要分开查询
	queryAggregationInfo := elastic.NewSearchSource().Aggregation("model_code_agg", aggregations).Query(aggregationQuery).Size(0)

	// 记录query到日志中
	err = SaveESQueryTOLog(queryAggregationInfo)
	if err != nil {
		return nil, nil, err
	}

	// 先查询聚合数据
	aggInfoResult, err := mds.store.ModelData().FuzzySearchData(ctx, queryAggregationInfo)
	if err != nil {
		return nil, nil, err
	}

	// 获取聚合的信息用于构建搜索的模型以及模型分组的tab
	aggInfo := string(aggInfoResult.Aggregations["model_code_agg"])

	// 解析聚合信息
	var agg AggregationInfo
	err = kjson.Unmarshal([]byte(aggInfo), &agg)
	if err != nil {
		return nil, nil, err
	}

	// 构建搜索的模型以及模型分组的tab，nest是一个map，其中key为模型分组的code，value为模型分组的信息，children嵌套了模型信息的数组
	nest := make(map[string]*apiv1.ModelWithNested)

	// 构建要返回的数据，先处理一下聚合的数据，构建模型列表
	modelList := make([]string, 0)
	for _, bucket := range agg.Buckets {
		modelList = append(modelList, bucket.Key)
		// 其实有可能查到的数据是过期的，这里直接草率的返回error还不太好，直接不能搜索了，所以先只记录一下日志
		var m *v1.Model
		m, err = mds.store.Model().GetModelByCode(ctx, bucket.Key)
		if err != nil {
			zap.L().Error("模糊搜索查询模型失败", zap.Error(err))
			continue
		}
		// 更新搜索到的数据的数量
		m.Total = bucket.DocCount

		// 查询这个模型所属的模型分组
		var modelGroup *v1.ModelGroup
		modelGroup, err = mds.store.ModelGroup().GetModelGroup(ctx, m.ModelGroup)
		if err != nil {
			zap.L().Error("模糊搜索查询模型分组失败", zap.Error(err))
		}
		// 根据查询到的model和model_group构建嵌套结构
		if _, ok := nest[modelGroup.Code]; !ok {
			children := make([]v1.Model, 0)
			children = append(children, *m)
			nest[modelGroup.Code] = &apiv1.ModelWithNested{
				ID:       modelGroup.ID,
				Code:     modelGroup.Code,
				Name:     modelGroup.Name,
				CreateAt: modelGroup.CreateAt,
				Children: children,
			}
		} else {
			// nest中已经存在对应的modelGroup的key，那么就直接添加Children即可
			nest[modelGroup.Code].Children = append(nest[modelGroup.Code].Children, *m)
		}
	}

	// 针对嵌套好的模型分组再包一层，用于排序
	nestedModelList := make(apiv1.NestedModelList, 0)
	for _, v := range nest {
		nestedModelList = append(nestedModelList, *v)
	}
	// 针对模型分组排一下序，当前排序的规则是按照create_at进行排序的
	sort.Sort(nestedModelList)

	return nestedModelList, modelList, nil
}

func (mds *modelDataService) SearchDataInfo(
	ctx context.Context,
	searchFields []string,
	keyword, defaultSelectModelGroup string,
	modelGroupFilter, modelsFilter []interface{},
	pageNumber, pageSizeNumber int64) ([]*v1.ModelData, int64, map[string]map[string]string, error) {
	var (
		err error
		// 构建Bool Query
		// dataQuery := elastic.NewBoolQuery().Must(elastic.NewQueryStringQuery("*" + request.Keyword + "*").Field("*"))\
		dataQuery = elastic.NewBoolQuery()
		dataList  []*v1.ModelData
	)

	escapedKeyword := regexp.QuoteMeta(keyword)

	// 设置高亮
	highlight := elastic.NewHighlight().
		PreTags("<span style='color:#DE463D;'>").
		PostTags("</span>").
		NumOfFragments(10).
		Fragmenter("span").
		FragmentSize(50)

	// 这个查询查询具体数据不查询聚合信息，同时将modelsFilter过滤条件添加进来，如果models大于0，那么modelGroupFilter也必须大于0
	if len(modelsFilter) > 0 {
		dataQuery.Must(elastic.NewTermsQuery("meta.model_group_code.keyword", modelGroupFilter...))
		dataQuery.Must(elastic.NewTermsQuery("meta.model_code.keyword", modelsFilter...))
	} else if len(modelGroupFilter) > 0 {
		dataQuery.Must(elastic.NewTermsQuery("meta.model_group_code.keyword", modelGroupFilter...))
	} else {
		// 如果没有传递过滤条件，那么默认选中第一个model_group
		dataQuery.Must(elastic.NewMatchQuery("meta.model_group_code.keyword", defaultSelectModelGroup))
	}

	// 构造基于keyword的正则匹配，模糊搜索
	regexQuery := elastic.NewBoolQuery()

	// 这块这个操作太low了，字段越多可能以后就越卡。但是目前没有找到更好的办法。regex的性能太差了，且不支持字段的通配。
	for _, field := range searchFields {
		// elastic: Error 400 (Bad Request): [regexp] query does not support [case_insensitive] [type=parsing_exception]
		// ElasticSearch版本问题，之前用的是7.6.2，升级一下：
		// https://stackoverflow.com/questions/70628749/elasticsearch-exception-type-parsing-exception-reason-wildcard-query-does-no
		// 索引keyword类型的字段，text类型的字段可以用，但是受限于分词策略的问题，可能导致分词有问题
		regexQuery.Should(elastic.NewRegexpQuery("data."+field+".keyword", ".*"+escapedKeyword+".*").CaseInsensitive(true))
		// 模糊搜索的时候受限于字段长度，不再使用keyword字段，改为使用text字段。
		// regexQuery.Should(elastic.NewRegexpQuery("data."+field, ".*"+escapedKeyword+".*").CaseInsensitive(true))
		highlight.Field("data." + field + ".keyword")
	}

	// 假设说有四个条件，A和B是明确的过滤条件，比如说model_group，model，C和D是模糊搜索的条件，比如说基于keyword，搜索data.*字段。
	// 这个时候其实要实现一个 A and B and ( C or D )的效果。对应到这里，我们添加的所有regexQuery就是C和D。而模型分组和模型就是A和B
	// 最终实现的一个es的query效果就是下面这样的
	// {
	//  "query": {
	//    "bool": {
	//      "must": [
	//        { "term": { "fieldA": "valueA" } },
	//        { "term": { "fieldB": "valueB" } },
	//        {
	//          "bool": {
	//            "should": [
	//              { "term": { "fieldC": "valueC" } },
	//              { "term": { "fieldD": "valueD" } }
	//            ]
	//          }
	//        }
	//      ]
	//    }
	//  }
	// }
	dataQuery.Must(regexQuery)

	// TODO: 分页参数在SearchSource中设置，不在SearchService中设置，实测在SearchService中设置不生效。待进一步研究是为啥
	queryData := elastic.NewSearchSource().Highlight(highlight).Query(dataQuery).Size(int(pageSizeNumber)).From(int((pageNumber - 1) * pageSizeNumber))
	err = SaveESQueryTOLog(queryData)
	if err != nil {
		return nil, 0, nil, err
	}

	// 查询具体数据
	dataResult, err := mds.store.ModelData().FuzzySearchData(ctx, queryData)
	if err != nil {
		return nil, 0, nil, err
	}

	// 这里构造这个dataWithHightlightFields是为了后面的高亮显示，根据PRD需求，希望高亮在前面显示，但是后面在生成dataInfo的时候
	// 默认是遍历data.Data，这就导致每次都没顺序，因此我这里就单独构造了一个容器用来存储高亮的数据，供构造response使用
	dataWithHighlightFields := make(map[string]map[string]string)
	// hits表示命中的查询结果，每一个hit是一条data的数据
	for _, hit := range dataResult.Hits.Hits {
		var data v1.ModelData
		err = kjson.Unmarshal(hit.Source, &data)
		if err != nil {
			zap.L().Error(err.Error())
			continue
		}
		// hit.Highlight底层是一个map[string][]string类型的数据
		// eg: 示例
		// {
		//    "data.server_name": [
		//        <span style='color:red'>测</span><span style='color:red'>试</span>名称3
		//    ]
		// }
		// 那么这里的key其实就是data.server_name，value就是一个[]string类型的数据，里面是所有高亮后的字符串，我们加到一起就可以
		// 2024-10-29 Update: 目前这段代码已经没用了，但是为了保持原有逻辑，暂时保留，实际的高亮用re来实现。
		for k, v := range hit.Highlight {
			keyField := strings.Split(k, ".")
			// 获取实际的的key，如上注释所示，这个k是一个data.server_name的字符串，我们要获取的是server_name
			key := keyField[len(keyField)-2]
			highlightValue := ""
			// 拼接一下，如上注释所示，这个v是一个[]string类型的数据，所以要遍历一下，我们把遍历的结果都拼接起来
			for _, value := range v {
				highlightValue += value
			}
			if _, ok := data.Data[key]; ok {
				data.Data[key] = highlightValue
				if _, ok := dataWithHighlightFields[key]; !ok {
					dataWithHighlightFields[data.ID] = make(map[string]string)
					fieldInfo := make(map[string]string)
					fieldInfo[key] = highlightValue
					dataWithHighlightFields[data.ID] = fieldInfo
				} else {
					dataWithHighlightFields[data.ID][key] = highlightValue
				}
			}
		}
		dataList = append(dataList, &data)
	}
	return dataList, dataResult.Hits.TotalHits.Value, dataWithHighlightFields, nil
}
