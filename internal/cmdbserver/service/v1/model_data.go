package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/convert"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/mitchellh/mapstructure"
	"github.com/mohae/deepcopy"
	"github.com/xuri/excelize/v2"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ModelDataService interface {
	// CreateModelData 创建模型数据
	CreateModelData(ctx context.Context, operator string, data *v1.ModelData) (interface{}, error)
	// GetModelDataByID 根据模型的ID，获取模型数据列表
	GetModelDataByID(ctx context.Context, ids apiv1.IDsFilter) (*v1.ModelDataResponseWithOutPagination, error)
	// GetModelDataList 获取模型数据列表
	GetModelDataList(ctx context.Context, filter bson.M, page, pageSize int64, sorting string) (*v1.ModelDataResponseWithLabelInfo, error)
	// UpdateModelData 获取模型数据
	UpdateModelData(ctx context.Context, operator string, data *v1.ModelData) (uData *v1.ModelData, err error)
	// DeleteModelData 删除模型数据
	DeleteModelData(ctx context.Context, operator string, dataID *v1.IDFilter) (*v1.ModelData, error)
	// ImportData 导入模型数据
	ImportData(ctx context.Context, operator string, data *v1.ModelData) (*v1.ModelData, error)
	// FuzzySearchModelData 模糊搜索模型数据
	FuzzySearchModelData(ctx context.Context, request *apiv1.FuzzySearchRequest, pageNumber, pageSizeNumber int64) (*apiv1.FuzzySearchResponse, error)
	// CreateModelDataSubOrdinateRelation 创建数据从属关系
	CreateModelDataSubOrdinateRelation(ctx context.Context, operator string, data *v1.DataRelRequest) error
	// CreateModelDataSubOrdinateRelationBatch 批量创建数据从属关系
	CreateModelDataSubOrdinateRelationBatch(ctx context.Context, operator string, data *v1.DataRelRequest) error
	// DeleteModelDataSubOrdinateRelation 删除数据从属关系
	DeleteModelDataSubOrdinateRelation(ctx context.Context, operator string, IDFilter *v1.IDFilter) error
	// CreateModelDataAssociateRelation 创建数据关联关系
	CreateModelDataAssociateRelation(ctx context.Context, operator string, data *v1.DataRelRequest) error
	// DeleteModelDataAssociateRelation 删除数据关联关系
	DeleteModelDataAssociateRelation(ctx context.Context, operator string, data *v1.DataRelRequest) error
	// GetRelInstances 根据模型数据以及关系字段获取可关联的数据列表
	GetRelInstances(ctx context.Context, relAttrID, dataID, keyword, field string, page, pageSize int64) (*v1.ModelDataResponse, error)
	// GetInstancesByRelCode 根据模型数据以及关系字段获取已关联的数据列表
	GetInstancesByRelCode(ctx context.Context, relAttrID, dataID, operator string, page, pageSize int64) (*v1.ModelDataResponseWithRelInfo, error)
	// GetInstancesByRelCodeV2 获取模型的关系数据，全局模式
	GetInstancesByRelCodeV2(ctx context.Context, relAttrID, dataID, operator string, page, pageSize int64) (*v1.RelatedDataResponse, error)
	// CreateModelDataSubOrdinateRelationMove 转移至功能
	CreateModelDataSubOrdinateRelationMove(ctx context.Context, operator string, request *v1.DataNTo1Request) (*v1.DataMoveToResponse, error)
	// GetMoveInstances 获取批量可转移至的目标
	GetMoveInstances(ctx context.Context, ari v1.AvailableRelInstances, page, pageSize int64) (*v1.ModelDataResponse, error)
	// UpdateModelDataRelation 更新数据关系信息，注：仅能更新关系描述
	UpdateModelDataRelation(ctx context.Context, operator string, data *v1.Data1To1Request) error
	// GetModelDataByFilter 根据特定条件获取模型数据
	GetModelDataByFilter(ctx context.Context, searchFilter bson.M) (*v1.ModelDataResponseWithOutPagination, error)
	// GlobalSearchFuzzy 模糊搜索模型数据
	GlobalSearchFuzzy(ctx context.Context, request *apiv1.GlobalSearchFuzzyRequest, pageNumber, pageSizeNumber int64) (*apiv1.GlobalSearchResponse, error)
	// GlobalSearchBatch 批量搜索模型数据
	GlobalSearchBatch(ctx context.Context, request *apiv1.GlobalSearchBatchRequest, pageNumber, pageSizeNumber int64) (*apiv1.GlobalBatchSearchResponse, error)
	// GlobalSearchCombine 组合搜索模型数据
	GlobalSearchCombine(ctx context.Context, request *apiv1.GlobalSearchCombineRequest, pageNumber, pageSizeNumber int64) (*apiv1.GlobalSearchResponse, error)
	// GlobalSearchDownload 全局搜索下载
	GlobalSearchDownload(ctx context.Context, req *apiv1.GlobalSearchCombineRequest) (*excelize.File, error)
	// Offline 下线
	Offline(ctx context.Context, operator string, req *apiv1.OfflineRequest) (*apiv1.OfflineResponse, error)
}

type modelDataService struct {
	store            store.Factory
	checkSystemField bool              // 是否检查系统字段，默认为true，即检查系统字段
	descendantsCache *descendantsCache // 后代数据缓存
}

var _ ModelDataService = (*modelDataService)(nil)

func newModelDataService(srv *service) *modelDataService {
	return &modelDataService{
		store:            srv.store,
		checkSystemField: true,
		descendantsCache: newDescendantsCache(5*time.Minute, 1000), // 5分钟过期，最多缓存1000个条目
	}
}

func (mds *modelDataService) SetCheckSystemField(check bool) {
	mds.checkSystemField = check
}

func (mds *modelDataService) syncZabbix(data *v1.ModelData, action string) {
	zabbixSyncModel := array.StringArray{"switch", "router", "firewall", "loadbalancing", "transmission", "wlc"}
	if zabbixSyncModel.InArray(data.ModelCode) {
		go func() {
			ipField := fmt.Sprintf("%s_in_ip", data.ModelCode)
			ipValue := utils.ToString(data.Data[ipField])
			if err := mds.store.ModelData().SyncToZabbix(data.Name(), ipValue, action); err != nil {
				zap.L().Error("提交zabbix同步任务失败, 数据id: "+data.ID+", 数据名称: "+data.Name(), zap.Error(err))
			}
			zap.L().Debug("提交Zabbix同步任务成功, 数据id: " + data.ID + ", 数据名称: " + data.Name())
		}()
	}
}

// ImportData 导入模型数据
func (mds *modelDataService) ImportData(ctx context.Context, operator string, data *v1.ModelData) (*v1.ModelData, error) {
	zap.L().Debug("ImportData Service called")
	span, ctx := apm.StartSpan(ctx, "ImportData", "service")
	defer span.End()

	// 1. 判断插入的数据是否合法，根据ID是否存在来判断是插入还是更新， 如果说ID不存在，那么就是插入，如果ID存在，那么就是更新
	var (
		action string
	)

	// Q: 这个add_in_edit_template是做什么的？
	// 前端表格中的每一行数据都有一个唯一标识 ( rowKey ) ,当用户勾选该行数据时实际上是选中了该行数据的rowKey
	// 在将编辑模版解析成表格后，使用的是ID字段的值作为rowKey，但是在编辑模版中,新增的数据是没有ID的，所以新增的数据行的rowKey为null
	// 就导致用户无法在前端页面中勾选上该行数据，所以前端需要和后端约定一个特殊的ID值作为rowKey以让用户能够成功勾选上数据
	//
	// 为什么前端不和新建时一样用行索引(1,2...)作为rowKey的原因？
	// 新建时所有的数据都没有rowKey，所以在解析成表格数据结构时会统一处理，所有数据里都会有一个额外的rowKey属性
	// 而编辑模版中如果也按照这种情况来处理的话，需要多遍历两次整个表格数据，因为正常来讲编辑模版中除了用户新增的数据都是有ID值的
	// 所以没必要在遍历所有数据一遍只为去加上一个rowKey，而且如果使用行索引(1,2,3...)作为rowKey，在用户选中数据后
	// 还要根据选中的行索引再去遍历整个表格数据以获取选中行的内容。
	if data.ID == "" || data.ID == "add_in_edit_template" {
		action = "create"
	} else {
		action = "update"
	}

	// 使用批量导入方式导入的数据
	data.InPutType = v1.ImportInput

	// 根据不同的动作去适配不同的操作
	switch action {
	case "create":
		modelAttrs, err := mds.dataValidCheck(ctx, data, "create")
		if err != nil {
			return nil, err
		}
		// 查询出模型名称
		model, err := mds.store.Model().GetModelByCode(ctx, modelAttrs[0].ModelCode)
		if err != nil {
			return nil, err
		}

		// 保存模型数据的部分元数据
		meta := make(mapdata.MapData)
		meta.Set("model_name", model.Name)
		meta.Set("model_code", model.Code)
		meta.Set("model_group_code", model.ModelGroup)

		// 设置data的默认属性
		data.Active = true
		cData, err := mds.store.ModelData().CreateModelData(ctx, operator, data, modelAttrs, meta)
		if err != nil {
			return nil, err
		}

		// 记录审计日志
		dst, err := convert.StructToMap(data.Data)
		if err != nil {
			return nil, err
		}
		// 借助dst 传递模型名称
		mName, _ := meta.Get("model_name")
		dst["model_name"] = mName
		auditLog, err := GenerateAuditLog(ctx, nil, dst, modelAttrs, audit.ActionCreate, audit.ResourceDetailRes, operator, cData.ID, data.ModelCode)
		if err != nil {
			return nil, err
		}

		if err = SaveAuditLog(ctx, mds.store, *auditLog); err != nil {
			return nil, err
		}

		// 同步Zabbix
		mds.syncZabbix(cData, "create")
		return cData, nil
	case "update":
		modelAttrs, err := mds.dataValidCheck(ctx, data, "update")
		if err != nil {
			return nil, err
		}

		// 查询出模型名称
		model, err := mds.store.Model().GetModelByCode(ctx, modelAttrs[0].ModelCode)
		if err != nil {
			return nil, err
		}

		// 查询出旧数据
		oldData, err := mds.store.ModelData().GetModelDataByID(ctx, data.ID)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return nil, errno.ErrDataNotExists.Addf("模型数据id %s 不存在", data.ID)
			}
			return nil, err
		}

		// 更新模型数据
		uData, err := mds.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
			Id:           data.ID,
			ModelData:    data,
			UpdateParent: false,
		})
		if err != nil {
			return nil, err
		}

		// audit log
		src, err := convert.StructToMap(oldData.Data)
		if err != nil {
			return nil, err
		}

		dst, err := convert.StructToMap(data.Data)
		if err != nil {
			return nil, err
		}

		// 借助dst 传递模型名称
		dst["model_name"] = model.Name

		auditLog, err := GenerateAuditLog(ctx, src, dst, modelAttrs, audit.ActionUpdate, audit.ResourceDetailRes, operator, oldData.ID, oldData.ModelCode)
		if err != nil {
			return nil, err
		}
		if err = SaveAuditLog(ctx, mds.store, *auditLog); err != nil {
			return nil, err
		}

		mds.syncZabbix(uData, "update")
		return uData, nil
	default:
		return nil, errno.InternalServerError.Add("数据导入异常，请联系管理员")
	}

}

// CreateModelData 创建模型数据
func (mds *modelDataService) CreateModelData(ctx context.Context, operator string, data *v1.ModelData) (any, error) {
	zap.L().Debug("CreateModelData Service called")
	span, ctx := apm.StartSpan(ctx, "CreateModelData", "service")
	defer span.End()

	// 1. 判断插入的数据是否合法
	modelAttrs, err := mds.dataValidCheck(ctx, data, "create")
	if err != nil {
		return nil, err
	}

	// 查询出模型名称
	model, err := mds.store.Model().GetModelByCode(ctx, modelAttrs[0].ModelCode)
	if err != nil {
		return nil, err
	}

	// 保存模型数据的部分元数据
	meta := make(mapdata.MapData)
	meta.Set("model_name", model.Name)
	meta.Set("model_code", model.Code)
	meta.Set("model_group_code", model.ModelGroup)

	// 设置data的默认属性
	data.Active = true

	// 2. 调用store层的逻辑插入数据，默认不指定的话，插入的类型就是0，即手动插入的数据
	createdData, err := mds.store.ModelData().CreateModelData(ctx, operator, data, modelAttrs, meta)
	if err != nil {
		return nil, err
	}

	// 记录审计日志
	au := &AuditEvent{store: mds.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ResourceDetailRes,
		audit.ActionCreate,
		operator,
		createdData.ID,
		nil,
		createdData,
	); err != nil {
		return nil, err
	}
	if err := au.Save(ctx); err != nil {
		return nil, err
	}

	// 3. 如果是创建的网络分类的数据，需要同步到zabbix, 异步不阻塞
	mds.syncZabbix(createdData, "create")
	return createdData, nil
}

// GetModelDataList 根据模型的code，获取模型数据列表
func (mds *modelDataService) GetModelDataList(ctx context.Context, filter bson.M, page, pageSize int64, sorting string) (*v1.ModelDataResponseWithLabelInfo, error) {
	zap.L().Debug("GetModelDataList Service called")
	span, ctx := apm.StartSpan(ctx, "GetModelDataListService", "service")
	defer span.End()

	pipe := make([]bson.M, 0)
	pipe = append(pipe, bson.M{"$match": filter})
	pipe = append(pipe, bson.M{"$unwind": bson.M{"path": "$label_id_list", "preserveNullAndEmptyArrays": true}})
	pipe = append(pipe, bson.M{"$lookup": bson.M{
		"from":         "label_value",
		"localField":   "label_id_list",
		"foreignField": "_id",
		"as":           "label_value_info",
	}})
	pipe = append(pipe, bson.M{"$unwind": bson.M{"path": "$label_value_info", "preserveNullAndEmptyArrays": true}})
	pipe = append(pipe, bson.M{"$lookup": bson.M{
		"from":         "label_key",
		"localField":   "label_value_info.key_id",
		"foreignField": "_id",
		"as":           "label_key_info",
	}})
	pipe = append(pipe, bson.M{"$unwind": bson.M{"path": "$label_key_info", "preserveNullAndEmptyArrays": true}})
	pipe = append(pipe, bson.M{"$group": bson.M{
		"_id":                 "$_id",
		"model_code":          bson.M{"$first": "$model_code"},
		"data":                bson.M{"$first": "$data"},
		"identify_name":       bson.M{"$first": "$identify_name"},
		"identify_value":      bson.M{"$first": "$identify_value"},
		"input_type":          bson.M{"$first": "$input_type"},
		"create_at":           bson.M{"$first": "$create_at"},
		"update_at":           bson.M{"$first": "$update_at"},
		"parent_id":           bson.M{"$first": "$parent_id"},
		"parent_desc":         bson.M{"$first": "$parent_desc"},
		"associate_instances": bson.M{"$first": "$associate_instances"},
		"labels": bson.M{
			"$addToSet": bson.M{"$cond": bson.A{
				bson.M{"$and": bson.A{
					bson.M{"$ne": bson.A{"$label_key_info", nil}},
					bson.M{"$ne": bson.A{"$label_value_info", nil}},
				}},
				bson.M{
					"key_name":   "$label_key_info.name",
					"key_id":     "$label_key_info._id",
					"value_name": "$label_value_info.value",
					"value_id":   "$label_value_info._id",
				},
				"$$REMOVE",
			}},
		},
	}})
	// 排序阶段, 按创建时间降序排序
	sortStage := make(bson.D, 0)
	if sorting == "desc" {
		sortStage = append(sortStage, bson.E{Key: "create_at", Value: -1})
	} else {
		sortStage = append(sortStage, bson.E{Key: "create_at", Value: 1})
	}
	// 在上面的基础上，额外添加一个排序的条件，如果创建时间相等，则按id降序排序
	sortStage = append(sortStage, bson.E{Key: "_id", Value: -1})
	pipe = append(pipe, bson.M{"$sort": sortStage})
	pipe = append(pipe, bson.M{"$sort": bson.M{"create_at": -1}})
	pipe = append(pipe, bson.M{"$project": bson.M{"_id": 1, "model_code": 1, "data": 1, "identify_name": 1,
		"identify_value": 1, "input_type": 1, "create_at": 1, "update_at": 1, "parent_id": 1, "parent_desc": 1,
		"associate_instances": 1, "labels": bson.D{
			{"$cond", bson.A{
				bson.M{"$or": bson.A{
					// 判断拿到的labels如果是空的话，则返回空数组，目前遇到的情况是会返回一个[{}]，所以数组长度不是0，而是1.
					bson.M{"$eq": bson.A{bson.D{{"$size", "$labels"}}, 0}},
					bson.M{"$eq": bson.A{
						bson.M{"$size": bson.M{"$objectToArray": bson.M{"$arrayElemAt": bson.A{"$labels", 0}}}}, 0},
					},
				}},
				bson.A{},
				"$labels",
			}}}}})
	// 最后一个管道操作是添加分页
	pipe = append(pipe, bson.M{
		"$group": bson.M{"_id": nil, "data": bson.M{"$push": "$$ROOT"}, "total": bson.M{"$sum": 1}}})
	pipe = append(pipe, bson.M{"$project": bson.M{"_id": 0, "total": 1,
		"data": bson.M{"$slice": []interface{}{"$data", (page - 1) * pageSize, pageSize}}}},
	)
	result, err := mds.store.ModelData().GetLabelAggregateData(ctx, pipe)
	if err != nil {
		return nil, err
	}

	if len(result) == 0 {
		return nil, nil
	}

	// 初始化一个response
	resp := v1.ModelDataResponseWithLabelInfo{}
	resp.BuildResponse(result[0].Total, pageSize, page, result[0].DataList)

	return &resp, nil
}

// GetModelDataByID 根据模型的ID，获取模型数据列表
func (mds *modelDataService) GetModelDataByID(ctx context.Context, ids apiv1.IDsFilter) (*v1.ModelDataResponseWithOutPagination, error) {
	span, ctx := apm.StartSpan(ctx, "GetModelDataByID", "service")
	defer span.End()
	modelDataList, err := mds.store.ModelData().GetModelDataListByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}
	return modelDataList, nil
}

// UpdateModelData 更新模型数据
func (mds *modelDataService) UpdateModelData(ctx context.Context, operator string, data *v1.ModelData) (uData *v1.ModelData, err error) {
	zap.L().Debug("UpdateModelData Service called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelData", "service")
	defer span.End()

	// 更新需要判断用户提交过来的字段是不是合法的，这里的逻辑其实和创建模型数据的时候是类似的
	if _, err := mds.dataValidCheck(ctx, data, "update"); err != nil {
		return nil, err
	}

	// 查询出要更新的旧数据
	oldData, err := mds.store.ModelData().GetModelDataByID(ctx, data.ID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("模型数据id %s 不存在", data.ID)
		}
		return nil, err
	}

	// 更新模型数据
	uData, err = mds.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
		Id:           oldData.ID,
		ModelData:    data,
		UpdateParent: false,
	})
	if err != nil {
		zap.L().Error("更新模型数据异常",
			zap.Error(err),
			zap.String("数据名称", data.Name()),
			zap.String("所属模型", data.ModelCode))
		return nil, err
	}

	// audit log
	au := &AuditEvent{store: mds.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ResourceDetailRes,
		audit.ActionUpdate,
		operator,
		oldData.ID,
		oldData,
		data,
	); err != nil {
		return nil, err
	}
	if err := au.Save(ctx); err != nil {
		return nil, err
	}

	// 同步Zabbix
	mds.syncZabbix(uData, "update")
	return
}

// DeleteModelData 删除模型数据
func (mds *modelDataService) DeleteModelData(ctx context.Context, operator string, dataID *v1.IDFilter) (*v1.ModelData, error) {
	zap.L().Debug("DeleteModelData Service called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelDataService", "service")
	defer span.End()

	// 先看看数据有没有把，你得有数据我才能删呀，没有的话，直接从这里就返回就可以了
	d, err := mds.store.ModelData().GetModelDataByID(ctx, dataID.ID)
	if err != nil {
		zap.L().Error(err.Error())
		return v1.NewModelData(), err
	}

	// 1. 删除数据的限制只要是看数据和其他数据之间有没有关联
	// 1.1 如果说该数据是其他数据的父级数据，那么就不能删除，不然以它为父级的数据就找不到父级了
	sons, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"parent_id": d.ID,
	}, nil)
	if err != nil {
		zap.L().Error(err.Error())
		return v1.NewModelData(), err
	}

	if len(sons) > 0 {
		return d, errno.ErrDataCheck.Add("该数据有子数据，不能删除")
	}

	// 1.2 如果说这个数据和其他数据有关联关系，那么这个数据也不能删除
	if len(d.AssociateInstances) > 0 {
		zap.L().Error("该数据有关联数据，不能删除")
		return d, errno.ErrDataCheck.Add("该数据有关联数据，不能删除")
	}

	// 查询出模型
	selectParam := bson.M{
		"code":       1,
		"name":       1,
		"model_code": 1,
	}

	attrFilter := bson.M{"model_code": d.ModelCode}
	modelAttrs, err := mds.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, attrFilter, selectParam)
	if err != nil {
		return nil, err
	}
	// 每个模型，至少应该有两个字段，就是唯一标识和名称，如果这两个字段都没有，有可能是数据库存在异常，需要管理员介入。
	if len(modelAttrs) == 0 {
		return d, errno.ErrDataNotExists.Addf("Err: 模型code %s 下没有任何字段，请咨询管理员", d.ModelCode)
	}

	// 查询出模型名称
	modelMap, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		return d, err
	}
	model := modelMap[d.ModelCode]

	// 3. 没啥关系的话，该删就删除吧，记得要同时删除对应的dataPath数据路径，如果不删除数据路径的话，虽然不会有什么问题（因为对应的id不存在）
	// 但是会使得垃圾数据越来越多，所以要同步的清理干净
	t, _ := mds.store.Common().StartTransaction()

	// 删除数据
	t.Wrapper(func(sessionCtx context.Context) error {
		if err := mds.store.ModelData().DeleteModelData(ctx, operator, dataID.ID, d, modelAttrs, model.Name); err != nil {
			return err
		}

		// 记录审计日志
		au := &AuditEvent{store: mds.store}
		if err := au.Generate(
			ctx,
			audit.AuditLogEvent,
			audit.ResourceDetailRes,
			audit.ActionDelete,
			operator,
			d.ID,
			d,
			nil,
		); err != nil {
			return err
		}
		if err := au.Save(ctx); err != nil {
			return err
		}
		return nil
	})

	// 删除数据路径
	t.Wrapper(func(sessionCtx context.Context) error {
		if err := mds.DeleteDataPath(sessionCtx, operator, dataID.ID); err != nil {
			return err
		}
		return nil
	})

	if err := t.Do(ctx); err != nil {
		return d, err
	}

	return d, nil
}

// GetDataInfo 获取数据详情
func GetDataInfo(mapping map[string]map[string]v1.CommonModelAttribute, data *v1.ModelData,
	highLightFields map[string]string, keyword string) string {
	infoString := ""
	unHighLightFields := make([]string, 0)

	// 遍历一遍data中的Data字段，把高亮的字段筛出来，优先拼接到infoString中，同时非高亮字段放到unHighLightFields中
	// 2024-10-30 由于目前单独在ES中新增了data.info用于做匹配，所以实际下面在mapping中，是获取不到高亮字段的，因此这段代码目前已经无效了
	zap.L().Debug("highlightFields: ", zap.Any("highlightFields", highLightFields))
	for k := range data.Data {
		attrMap := mapping[data.ModelCode]
		if highLightValue, exist := highLightFields[k]; exist {
			if a, exist := attrMap[k]; exist {
				infoString += fmt.Sprintf("%s: %s; ", a.Name, highLightValue)
			}
		} else {
			unHighLightFields = append(unHighLightFields, k)
		}
	}

	// feat: 要求高亮字段在前面展示
	// 遍历一遍unHighLightFields，把非高亮字段拼接到infoString中，这样就实现了高亮字段在前显示，非高亮在后面显示同时还可以展示所有数据
	var matchField, unMatchField []string
	for _, k := range unHighLightFields {
		attrMap := mapping[data.ModelCode]
		// 过滤唯一标识字段，唯一标识字段作为业务属性的时候，意义不大。
		if attrMap[k].Code == fmt.Sprintf("%s_code", data.ModelCode) {
			continue
		}
		// 2024-10-29 最新的高亮的方式，通过regex来实现高亮，因为es的高亮匹配不足够精确，导致往往高亮的内容过长
		fieldValue := utils.ToString(data.Data[k])
		if fieldValue == "" {
			continue
		}
		// 忽略大小写
		escapedKeyword := regexp.QuoteMeta(keyword)
		pattern := `(?i)` + escapedKeyword
		re := regexp.MustCompile(pattern)
		// 后面的数字是表明匹配多少个，0表示不返回任何匹配项，-1表示返回所有匹配项，大于0的整数，写几个返回几个。
		matches := re.FindAllStringIndex(fieldValue, -1)
		if len(matches) > 0 {
			lastIndex := 0
			result := ""
			for _, match := range matches {
				start, end := match[0], match[1]
				result += fieldValue[lastIndex:start]
				result += "<span style='color:#DE463D;'>" + fieldValue[start:end] + "</span>"
				lastIndex = end
			}
			result += fieldValue[lastIndex:]
			if attr, ok := attrMap[k]; ok {
				matchField = append(matchField, fmt.Sprintf("%s: %s;", attr.Name, result))
			}
		} else {
			if attr, ok := attrMap[k]; ok {
				unMatchField = append(unMatchField, fmt.Sprintf("%s: %s;", attr.Name, data.Data[k]))
			}
		}
	}

	infoStringSlice := append(matchField, unMatchField...)
	return strings.Join(infoStringSlice, " ")
}

// CreateModelDataSubOrdinateRelation 创建模型数据从属关系
func (mds *modelDataService) CreateModelDataSubOrdinateRelation(ctx context.Context, operator string, dsr *v1.DataRelRequest) error {
	zap.L().Debug("CreateModelDataSubOrdinateRelation service called")
	span, ctx := apm.StartSpan(ctx, "CreateModelDataSubOrdinateRelationService", "service")
	defer span.End()

	// 1. 首先看看要创建从属关系的数据是否存在，当多人操作的时候，有可能数据已经被删除了，但是前端还没有刷新，这个时候必须校验
	data, err := mds.store.ModelData().GetModelDataByID(ctx, dsr.SourceInstanceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("要创建从属关系的源端数据不存在", zap.Error(err))
			return errno.ErrDataNotExists.Add("要创建从属关系的源端数据不存在")
		}
		zap.L().Error("要创建从属关系的源端数据不存在", zap.Error(err))
		return err
	}

	// 2. 接下来看一下方向，是子找父，还是父找子
	// 因为这里的设计是允许从属关系是双向的，既可以在子端指向父端，也可以在父端指向子端
	rel, err := mds.store.ModelAttributes().GetModelAttributeByID(ctx, dsr.RelID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error(fmt.Sprintf("模型 %s 下的从属关系 %s 不存在", data.ModelCode, dsr.RelID))
			return errno.ErrModelSubordinateNotExists.Addf("模型 %s 下的从属关系 %s 不存在", data.ModelCode, dsr.RelID)
		}
		zap.L().Error(err.Error())
		return err
	}

	// 初始化从属关系属性
	sub := v1.NewSubordinationRelationshipAttribute()
	if err = mapstructure.Decode(rel, sub); err != nil {
		zap.L().Error(err.Error())
		return err
	}

	// 3. 根据父子关系类型，做不同的处理
	if data.ModelCode == sub.ModelCode {
		// 如果说关系字段的ModelCode和data的ModeCode是一样的，说明是子找父亲，关系就是本模型的关系
		// 从属关系的target的长度只能是1
		if len(dsr.TargetInstanceID) != 1 {
			zap.L().Error("从属关系只能与一个父级数据绑定")
			return errno.ErrParameterInvalid.Add("从属关系只能与一个父级数据绑定")
		}

		// 3.1.1 判断是否已经添加了从属关系，如果已经添加过从属关系的话，那么不允许再添加，因为从属关系只能有一个唯一的上级模型数据
		if data.ParentID != "" {
			zap.L().Error(fmt.Sprintf("要新增从属关系的数据 %s 已经存在从属关系，无法绑定其他实例，请先删除后，再尝试重新绑定", dsr.SourceInstanceID))
			return errno.ErrModelDataSubOrdinateRelationExists.Addf("要新增从属关系的数据 %s 已经存在从属关系，无法绑定其他实例，请先删除后，再尝试重新绑定", dsr.SourceInstanceID)
		}

		// 3.1.2. 判断对端数据是否存在, 这里因为在前面有判断过切片的长度，因此这里可以直接取索引
		targetData, err := mds.store.ModelData().GetModelDataByID(ctx, dsr.TargetInstanceID[0])
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				zap.L().Error("要创建从属关系的目标端数据不存在", zap.Error(err))
				return errno.ErrDataNotExists.Add("要创建从属关系的目标端数据不存在")
			}
			zap.L().Error(err.Error())
			return err
		}

		// 3.1.3. 还得判断对端的这个数据是不是目标模型的，有可能随便给一个非目标模型的实例的ID
		targetModelCode := sub.Attrs.RelTo
		if targetData.ModelCode != targetModelCode {
			zap.L().Error(fmt.Sprintf("要创建从属关系的目标端数据 %s 不是目标模型 %s 的实例", dsr.TargetInstanceID, targetModelCode))
			return errno.ErrModelDataMisMatch.Addf("要创建从属关系的目标端数据 %s 不是目标模型 %s 的实例", dsr.TargetInstanceID, targetModelCode)
		}

		// 3.1.4. 如果对端数据存在，那么说明提交上来的数据是合法的，可以进行更新
		data.ParentID = dsr.TargetInstanceID[0]
		data.ParentDesc = dsr.RelDesc

		// 获取要添加的path
		path, err := mds.GetDataTreePath(ctx, data)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}

		// 初始化一个事务
		t, err := mds.store.Common().StartTransaction()
		if err != nil {
			return err
		}

		// 更新data的parent信息
		t.Wrapper(func(sessionCtx context.Context) error {
			if _, err := mds.store.ModelData().UpdateModelData(sessionCtx, &v1.UpdateModelDataStoreRequest{
				Id:           data.ID,
				ModelData:    data,
				UpdateParent: true,
				ParentID:     data.ParentID,
				ParentDesc:   data.ParentDesc,
			}); err != nil {
				zap.L().Error(err.Error())
				return err
			}

			// 创建从属关系审计日志
			au := &AuditEvent{store: mds.store}

			bytes, err := json.Marshal(data)
			if err != nil {
				zap.L().Error("创建从属关系审计日志失败", zap.Error(err))
				return err
			}

			var dataMap mapdata.MapData
			if err := json.Unmarshal(bytes, &dataMap); err != nil {
				zap.L().Error("创建从属关系审计日志失败", zap.Error(err))
				return err
			}

			if err := au.Generate(
				ctx,
				audit.AuditLogEvent,
				audit.ResourceRelationshipSubRes,
				audit.ActionCreate,
				operator,
				data.ID,
				nil,
				dataMap,
			); err != nil {
				zap.L().Error("创建从属关系审计日志失败", zap.Error(err))
				return err
			}

			if err := au.Save(ctx); err != nil {
				zap.L().Error("保存从属关系审计日志失败", zap.Error(err))
				return err
			}

			return nil
		})

		// 更新data的path信息
		t.Wrapper(func(p []*v1.DataTreePath) func(sessionCtx context.Context) error {
			return func(sessionCtx context.Context) error {
				if err := mds.updateDataTreePath(sessionCtx, operator, p); err != nil {
					zap.L().Error(err.Error())
					return err
				}
				return nil
			}
		}(path))

		if err := t.Do(ctx); err != nil {
			return err
		}

	} else if data.ModelCode == sub.Attrs.RelTo {
		// 说明这条关系是父找子，当前的数据对应的关系其实是父子关系中的父亲这一端

		// 3.2.1 查出来所有的子数据
		subDataList, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
			"_id": bson.M{"$in": dsr.TargetInstanceID},
		}, nil)

		if err != nil {
			zap.L().Error(err.Error())
			return errno.ErrDataNotExists.Add("目标端数据不存在")
		}

		var (
			dataList   = make([]interface{}, 0)
			targetList = make([]interface{}, 0)
		)

		// 同时判断一下这些数据是否已经存在从属关系，如果已经存在从属关系，那么就不允许再添加，因为从属关系只能有一个唯一的上级模型数据
		for _, d := range subDataList {
			if d.ParentID != "" {
				zap.L().Error(fmt.Sprintf("要新增从属关系的数据 %s 已经存在从属关系，无法绑定其他实例，请先删除后，再尝试重新绑定", d.Name()))
				return errno.ErrModelDataSubOrdinateRelationExists.Addf("要新增从属关系的数据 %s 已经存在从属关系，无法绑定其他实例，请先删除后，再尝试重新绑定", d.Name())
			}

			if d.ModelCode != sub.ModelCode {
				return errno.ErrModelDataMisMatch.Addf("数据 %s 与关系 %s 不匹配", d.Name(), sub.Name)
			}

			dataList = append(dataList, d.ID)

		}

		for _, d := range dsr.TargetInstanceID {
			targetList = append(targetList, d)
		}

		diff := array.ArrayDiff(targetList, dataList)

		if len(diff) > 0 {
			zap.L().Error(fmt.Sprintf("目标端数据 %s 不存在", diff))
			return errno.ErrDataNotExists.Add(fmt.Sprintf("目标端数据 %s 不存在", diff))
		}

		for _, d := range subDataList {

			d.ParentID = data.ID
			d.ParentDesc = dsr.RelDesc

			// 获取要添加的path
			path, err := mds.GetDataTreePath(ctx, d)
			if err != nil {
				zap.L().Error(err.Error())
				return err
			}

			t, _ := mds.store.Common().StartTransaction()

			t.Wrapper(func(sessionCtx context.Context) error {
				if _, err := mds.store.ModelData().UpdateModelData(sessionCtx, &v1.UpdateModelDataStoreRequest{
					Id:           d.ID,
					ModelData:    nil,
					UpdateParent: true,
					ParentID:     d.ParentID,
					ParentDesc:   d.ParentDesc,
				}); err != nil {
					zap.L().Error(err.Error())
					return err
				}

				// 创建从属关系审计日志
				au := &AuditEvent{store: mds.store}

				bytes, err := json.Marshal(d)
				if err != nil {
					zap.L().Error("创建从属关系审计日志失败", zap.Error(err))
					return err
				}

				var dataMap mapdata.MapData
				if err := json.Unmarshal(bytes, &dataMap); err != nil {
					zap.L().Error("创建从属关系审计日志失败", zap.Error(err))
					return err
				}

				if err := au.Generate(
					ctx,
					audit.AuditLogEvent,
					audit.ResourceRelationshipSubRes,
					audit.ActionCreate,
					operator,
					data.ID,
					nil,
					dataMap,
				); err != nil {
					zap.L().Error("创建从属关系审计日志失败", zap.Error(err))
					return err
				}

				if err := au.Save(ctx); err != nil {
					zap.L().Error("保存从属关系审计日志失败", zap.Error(err))
					return err
				}
				return nil
			})

			t.Wrapper(func(p []*v1.DataTreePath) func(sessionCtx context.Context) error {
				return func(sessionCtx context.Context) error {
					if err := mds.updateDataTreePath(sessionCtx, operator, p); err != nil {
						zap.L().Error(err.Error())
						return err
					}
					return nil
				}
			}(path))

			if err := t.Do(ctx); err != nil {
				return err
			}
		}

	} else {
		// 说明这个关系和数据是没什么关系的，比如说关系是a to b，但是实际上数据是c模型的。
		zap.L().Error(fmt.Sprintf("数据 %s 与关系 %s 不匹配", data.Name(), sub.Name))
		return errno.ErrModelDataMisMatch.Addf("数据 %s 与关系 %s 不匹配", data.Name(), sub.Name)
	}

	return nil
}

// CreateModelDataSubOrdinateRelationBatch 批量创建从属关系
func (mds *modelDataService) CreateModelDataSubOrdinateRelationBatch(ctx context.Context, operator string, dsr *v1.DataRelRequest) error {
	zap.L().Debug("CreateModelDataSubOrdinateRelationBatch service called")
	span, _ := apm.StartSpan(ctx, "CreateModelDataSubOrdinateRelationBatchService", "service")
	defer span.End()

	// 1. 首先看看要创建从属关系的数据是否存在，当多人操作的时候，有可能数据已经被删除了，但是前端还没有刷新，这个时候必须校验
	// , err := mds.store.ModelData().GetModelDataByID(ctx, dsr.SourceInstanceID)
	// if err != nil {
	// 	if errors.Is(err, mongo.ErrNoDocuments) {
	// 		zap.L().Error("要创建从属关系的源端数据不存在", zap.Error(err))
	// 		return errno.ErrDataNotExists.Add("要创建从属关系的源端数据不存在")
	// 	}
	// 	zap.L().Error("要创建从属关系的源端数据不存在", zap.Error(err))
	// 	return err
	// }

	// 2. 查看要批量添加的target的数据是否存在，如果不存在，那么就不允许添加从属关系
	return nil
}

// DeleteModelDataSubOrdinateRelation 删除从属关系
func (mds *modelDataService) DeleteModelDataSubOrdinateRelation(ctx context.Context, operator string, IDFilter *v1.IDFilter) error {
	zap.L().Debug("DeleteModelDataSubOrdinateRelation service called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelDataSubOrdinateRelation", "service")
	defer span.End()

	// 1. 首先查询要删除从属关系的数据是否存在
	data, err := mds.store.ModelData().GetModelDataByID(ctx, IDFilter.ID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Add("要删除从属关系的数据不存在")
		}
		return nil
	}

	// 2. 由于一个数据只可以有一个从属字段，因此这里删除我其实可以不关心你删除的是什么，我只需要知道你要删除从属关系即可。
	// 将主从字段的ID设置为空字符串，即表示删除
	if data.ParentID == "" {
		// 如果说已经置空了，表示当前数据没有和任何其他的数据进行关联，因此就没有必要删除了，直接返回即可。
		return errno.ErrDataParentNotSet.Add("当前数据无从属关系，无需删除")
	}
	data.ParentID = ""
	data.ParentDesc = ""

	// 3. 更新数据
	t, err := mds.store.Common().StartTransaction()
	if err != nil {
		zap.L().Error("开启事务失败", zap.Error(err))
		return err
	}

	t.Wrapper(func(sessionCtx context.Context) error {
		if _, err := mds.store.ModelData().UpdateModelData(sessionCtx, &v1.UpdateModelDataStoreRequest{
			Id:           data.ID,
			ModelData:    data,
			UpdateParent: true,
			ParentID:     data.ParentID,
			ParentDesc:   data.ParentDesc,
		}); err != nil {
			zap.L().Error(err.Error())
			return err
		}

		// 记录审计日志
		au := &AuditEvent{store: mds.store}
		if err := au.Generate(
			ctx,
			audit.AuditLogEvent,
			audit.ResourceRelationshipSubRes,
			audit.ActionDelete,
			operator,
			data.ID,
			data,
			nil,
		); err != nil {
			zap.L().Error("生成从属关系审计日志失败", zap.Error(err))
			return err
		}
		if err := au.Save(ctx); err != nil {
			zap.L().Error("保存从属关系审计日志失败", zap.Error(err))
			return err
		}
		return nil
	})

	t.Wrapper(func(sessionCtx context.Context) error {
		if err := mds.DeleteDataPath(sessionCtx, operator, data.ID); err != nil {
			return err
		}
		return nil
	})

	if err := t.Do(ctx); err != nil {
		return err
	}

	return nil
}

// CreateModelDataAssociateRelation 创建模型数据关联关系
func (mds *modelDataService) CreateModelDataAssociateRelation(ctx context.Context, operator string, data *v1.DataRelRequest) error {
	zap.L().Debug("CreateModelDataAssociateRelation service called")
	span, ctx := apm.StartSpan(ctx, "CreateModelDataAssociateRelationService", "service")
	defer span.End()

	// 1、关系的合法性确认，注意，这里提交上来的是关系字段的ID，而不是model_relation表中关系的ID
	// 1.1、首先查询关系字段是否存在，如果关系字段不存在，那么这个创建数据关系就是不合法的
	attr, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": data.RelID}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系不存在", zap.Error(err))
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
	}
	// 1.2、关系字段存在，再判断关系是否存在，如果关系不存在，那么这个创建数据关系就是不合法的
	rel, err := mds.store.ModelRelation().GetModelRelationByFilter(ctx,
		bson.M{
			// 这里关系因为是双端的，所以要不就是在rel_model1_code和rel_field1_code，要不就是在rel_model2_code和rel_field2_code
			"$or": []bson.M{
				{"rel_model1_code": attr.ModelCode, "rel_field1_code": attr.Code},
				{"rel_model2_code": attr.ModelCode, "rel_field2_code": attr.Code},
			},
		},
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系不存在", zap.Error(err))
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		return err
	}

	// 1.3、判断关系模式是否合法
	relMode := v1.ToRelMode(rel.RelMode)
	if relMode == v1.RelModeUnknown {
		zap.L().Error("关联关系模式不合法", zap.Error(err))
		return errno.ErrModelAssociateNotExists.Add("关联关系模式不合法, 请联系管理员")
	}

	// 2、判断要添加关联关系的源端数据实例是否存在，数据都不存在那就没有添加的必要了。
	src, err := mds.store.ModelData().GetModelDataByID(ctx, data.SourceInstanceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("要创建关联关系的源端数据不存在", zap.Error(err))
			return errno.ErrDataNotExists.Add("要创建关联关系的源端数据不存在")
		}
		return nil
	}

	// 3、计算目标端数据的切片的模型是哪一个，目的是明确出来发起端和目标端的模型的唯一标识得和relation对应才是合法的。
	// 有可能关系是 A 和 B的，但是传递的源或者目的的数据id是C模型的。
	zap.L().Debug("3. 确认源目模型")
	targetModel := ""
	if src.ModelCode == rel.RelModel1Code {
		targetModel = rel.RelModel2Code
	} else if src.ModelCode == rel.RelModel2Code {
		targetModel = rel.RelModel1Code
	} else {
		zap.L().Error("数据与模型关系不匹配", zap.Error(err))
		return errno.ErrModelDataMisMatch.Addf(
			"数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s 和 %s",
			src.ModelCode, rel.RelModel1Code, rel.RelModel2Code)
	}

	// 4、判断目的数据是否存在，目的数据可以是多个
	targetList := make([]*v1.ModelData, 0)
	targetInstanceMap := make(mapdata.MapData)
	for _, tid := range data.TargetInstanceID {
		target, err := mds.store.ModelData().GetModelDataByID(ctx, tid)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				zap.L().Error("要创建关联关系的目标端数据不存在", zap.Error(err))
				return errno.ErrDataNotExists.Add("要创建关联关系的目标端数据不存在")
			}
			return err
		}

		if target.ModelCode != targetModel {
			return errno.ErrModelDataMisMatch.Addf(
				"数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s 和 %s",
				target.ModelCode, rel.RelModel1Code, rel.RelModel2Code)
		}

		targetInstanceMap[tid] = target
		targetList = append(targetList, target)
	}

	// 判断源目的数据以及源目的模型都合法以后还需要判断是否应该创建关系
	switch relMode {
	case v1.RelModeOneToOne:
		// 一对一关系，源端和目标端只能有一个
		if len(targetList) != 1 {
			return errno.ErrModelDataMisMatch.Add("一对一关系，目标端只能有一个")
		}

		// 获取目标端数据
		targetInstance := targetList[0]

		// 检查之前是否已经建立过关系
		for _, instance := range src.AssociateInstances {
			if instance.RelationID == rel.ID {
				return errno.ErrModelDataMisMatch.Add("一对一关系，已经建立过关系，请勿重复建立")
			}
		}

		// 检查目标端是否已经建立过关系
		for _, instance := range targetInstance.AssociateInstances {
			if instance.RelationID == rel.ID {
				return errno.ErrModelDataMisMatch.Add("一对一关系，已经建立过关系，请勿重复建立")
			}
		}

	case v1.RelModeOneToMany:
		// 一对多关系，其中一端数据只能建立一次关系
		// eg: 我们这里以服务器和硬盘的关系举例，硬盘和服务器应该是关联关系，且是一对多的关系
		// one: 服务器
		// many: 硬盘，硬盘可以有多个，但是一块硬盘只能插在一台服务器上，它无法同时为多台物理服务器服务
		if rel.IsOneEnd(src.ModelCode) {
			// 如果源端是单端，以当前的例子来讲，源端就是服务器，相当于在服务器这一端添加对应的硬盘
			// 这里需要遍历当前所有当前这台服务器的关联数据
			for _, instance := range src.AssociateInstances {
				// 找到服务器硬盘这条关系
				if instance.RelationID == rel.ID {
					// 找到关系后，由于服务器是one这一端，因此这个关系之下，不应该有重复的硬盘ID
					if array.InArray(instance.InstanceID, targetInstanceMap.Keys()) {
						return errno.ErrModelDataMisMatch.Add("一对多关系，源端已经建立过关系，请勿重复建立")
					}
				}
			}
		} else {
			// 如果源端是Many端，以当前的例子来讲，源端就是硬盘，相当于在硬盘这一端添加对应的硬盘
			// 这里需要遍历当前所有当前这块硬盘的关联数据，无需校验target，只要关系数据中包含对应rel_id
			// 的数据就说明已经创建过关系了。
			for _, instance := range targetList[0].AssociateInstances {
				if instance.RelationID == rel.ID {
					return errno.ErrModelDataMisMatch.Add("一对多关系，目标端已经建立过关系，请勿重复建立")
				}
			}
		}
	case v1.RelModeManyToMany:
		// 多对多关系，源端和目标端可以有多个，所以只需要校验之前是否已经建立过关系就可以了
		for _, instance := range src.AssociateInstances {
			if instance.RelationID == rel.ID {
				// 同样的关系，只要不和同样的目标重复建立关系即可
				if array.InArray(instance.InstanceID, targetInstanceMap.Keys()) {
					return errno.ErrModelDataMisMatch.Add("多对多关系，源端已经建立过关系，请勿重复建立")
				}
			}
		}

		// 检查目标端是否已经建立过关系
		for _, target := range targetList {
			for _, instance := range target.AssociateInstances {
				if instance.RelationID == rel.ID {
					return errno.ErrModelDataMisMatch.Add("多对多关系，目标端已经建立过关系，请勿重复建立")
				}
			}
		}

	default:
		return errno.ErrModelDataMisMatch.Add("关系模式不合法, 请联系管理员")
	}

	// 5、判断关系和数据如果都是合法的话，那么就可以创建关系了
	targetIDs := make([]string, 0)
	for _, t := range targetList {
		targetIDs = append(targetIDs, t.ID)
	}

	// 更新source端的数据
	if err = mds.store.ModelData().AddDataAssociate(ctx, src, targetIDs, rel.ID, data.RelDesc); err != nil {
		zap.L().Error(err.Error())
		return err
	}

	// 更新target端的数据
	for _, t := range targetList {
		if err = mds.store.ModelData().AddDataAssociate(ctx, t, []string{src.ID}, rel.ID, data.RelDesc); err != nil {
			zap.L().Error(err.Error())
			return err
		}
	}

	// 记录审计日志
	// au := &AuditEvent{store: mds.store}
	// if err := au.Generate(
	// 	ctx,
	// 	audit.AuditLogEvent,
	// 	audit.ResourceRelationshipSubRes,
	// 	audit.ActionCreate,
	// 	operator,
	// 	src.ID,
	// 	src,
	// 	nil,
	// ); err != nil {
	// 	zap.L().Error("生成从属关系审计日志失败", zap.Error(err))
	// 	return err
	// }
	// if err := au.Save(ctx); err != nil {
	// 	zap.L().Error("保存从属关系审计日志失败", zap.Error(err))
	// 	return err
	// }

	return nil
}

func (mds *modelDataService) DeleteModelDataAssociateRelation(ctx context.Context, operator string, data *v1.DataRelRequest) error {
	zap.L().Debug("DeleteModelDataAssociateRelation service called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelDataAssociateRelationService", "service")
	defer span.End()

	// 1、关系的合法性确认
	zap.L().Debug("1. 关系的合法性确认")
	// 1.1、首先查询关系字段是否存在，如果关系字段不存在，那么这个创建数据关系就是不合法的
	attr, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": data.RelID}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系不存在", zap.Error(err))
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
	}
	// 1.2、判断关系是否存在，根据id查询关系，如果关系不存在，那么这个创建数据关系就是不合法的
	if _, err := mds.store.ModelRelation().GetModelRelationByFilter(ctx,
		bson.M{
			// 这里关系因为是双端的，所以要不就是在rel_model1_code和rel_field1_code，要不就是在rel_model2_code和rel_field2_code
			"$or": []bson.M{
				{"rel_model1_code": attr.ModelCode, "rel_field1_code": attr.Code},
				{"rel_model2_code": attr.ModelCode, "rel_field2_code": attr.Code},
			},
		},
	); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系不存在", zap.Error(err))
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		return err
	}

	// 2、判断要删除关联关系的源端数据实例是否存在，数据都不存在那就没有添加的必要了。
	zap.L().Debug("1. 判断源目数据是否存在")
	src, err := mds.store.ModelData().GetModelDataByID(ctx, data.SourceInstanceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("要创建关联关系的源端数据不存在", zap.Error(err))
			return errno.ErrDataNotExists.Add("要创建关联关系的源端数据不存在")
		}
		return nil
	}

	srcInstanceIDs := make([]string, 0)
	for _, source := range src.AssociateInstances {
		srcInstanceIDs = append(srcInstanceIDs, source.InstanceID)
	}

	for _, t := range data.TargetInstanceID {
		if !array.InArray(t, srcInstanceIDs) {
			zap.L().Error(fmt.Sprintf("要删除的关系数据不存在, 数据的ID为%s", t))
			return errno.ErrModelDataMisMatch.Addf("要删除的关系数据不存在, 数据的ID为%s", t)
		}
	}

	// 数据都合法，可以进行删除
	err = mds.store.ModelData().DelDataAssociate(ctx, src, data.TargetInstanceID)
	if err != nil {
		zap.L().Error(err.Error())
		return err
	}

	// 同步删除对端的数据
	for _, t := range data.TargetInstanceID {
		target, err := mds.store.ModelData().GetModelDataByID(ctx, t)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				zap.L().Error("要删除关联关系的目标端数据不存在", zap.Error(err))
				return errno.ErrDataNotExists.Add("要删除关联关系的目标端数据不存在")
			}
			return err
		}

		err = mds.store.ModelData().DelDataAssociate(ctx, target, []string{src.ID})
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
	}

	return nil
}

func (mds *modelDataService) getInstanceByRel(
	ctx context.Context, rel *v1.CommonModelAttribute, data *v1.ModelData,
	page, pageSize int64, operator string) (*v1.PageInfo, []v1.ModelDataWithRelInfo, []v1.CommonModelAttribute, error) {

	var (
		pageInfo = v1.NewPageInfo()
		dataList = make([]v1.ModelDataWithRelInfo, 0)
		attrList = make([]v1.CommonModelAttribute, 0)
	)

	relation, err := rel.ToRelationField()
	if err != nil {
		return nil, nil, nil, err
	}

	// 说明这个关系和数据是没什么关系的，比如说关系是a to b，但是实际上数据是c模型的。
	if data.ModelCode != relation.Local() && data.ModelCode != relation.Remote() {
		zap.L().Error(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s，既不是关系所属模型 %s 也不是关联的父级模型 %s", data.ModelCode, relation.Local(), relation.Remote()))
		return nil, nil, nil, errno.ErrModelDataMisMatch.Add(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s，既不是关系所属模型 %s 也不是关联的父级模型 %s", data.ModelCode, relation.Local(), relation.Remote()))
	}

	switch relation.RelType() {
	case v1.Subordinate:
		if data.ModelCode == relation.Local() {
			// 当前数据的模型code与关系的模型code一致，说明这条关系是子找父，当前数据是子端, 首先，查询要返回数据的所有字段属性
			var err error

			// 如果说数据的父ID为空，说明没有父亲节点，直接返回就可以了
			// 2024-12-03 因为我发现如果查询数据id为空字符串是真的可以查到数据的，可能有脏数据，正常情况下不应该出现这种情况
			if data.ParentID == "" {
				zap.L().Info("数据的父节点未配置")
				pageInfo.Pages = 1
				pageInfo.PageSize = 10
				return pageInfo, dataList, attrList, err
			}

			attrList, err = mds.store.ModelAttributes().GetModelAttributesByCustomFilter(
				ctx, bson.M{
					"model_code": relation.Remote(),
					"type_name":  bson.M{"$ne": "relationship"},
				}, bson.M{})
			if err != nil {
				return nil, dataList, attrList, err
			}

			parentData, err := mds.store.ModelData().GetModelDataByID(ctx, data.ParentID)
			if err != nil {
				if errors.Is(err, mongo.ErrNoDocuments) {
					zap.L().Info(fmt.Sprintf("数据不存在, id为%s", data.ParentID), zap.Error(err))
					pageInfo.CurrentPage = page
					pageInfo.PageSize = pageSize

					return pageInfo, dataList, attrList, nil
				}
				zap.L().Error(err.Error())
				return nil, nil, nil, err
			}

			pageInfo.Total = 1
			pageInfo.Pages = 1
			pageInfo.CurrentPage = page
			pageInfo.PageSize = pageSize

			rackID := ""

			dev := v1.Dev{}

			if relation.Remote() == "rack" {
				rackID = parentData.ID
				dev, err = v1.ToDev(*data)
				if err != nil {
					return nil, nil, nil, err
				}
			}

			dataList = append(dataList, v1.ModelDataWithRelInfo{
				ModelData: *parentData,
				RelDesc:   data.ParentDesc,
				RelName:   relation.RelName(),
				RelID:     relation.GetID(),
				IsRack:    relation.Remote() == "rack",
				StartU:    dev.StartU(),
				RackID:    rackID,
				DelID:     data.ID,
			})

			return pageInfo, dataList, attrList, nil
		} else if data.ModelCode == relation.Remote() {
			// 当前数据的模型和关系模型对端的数据一样，说明这条关系是父找子，当前数据是父端
			var err error
			attrList, err = mds.store.ModelAttributes().GetModelAttributesByCustomFilter(
				ctx, bson.M{"model_code": relation.Local(), "type_name": bson.M{"$ne": "relationship"}}, bson.M{})
			if err != nil {
				return nil, nil, nil, err
			}

			filter := bson.M{"parent_id": data.ID, "model_code": relation.Local()}
			modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, filter, page, pageSize, "")
			if err != nil {
				return nil, nil, attrList, err
			}

			pageInfo.Total = total
			pageInfo.Pages = (total / pageSize) + 1
			pageInfo.CurrentPage = page
			pageInfo.PageSize = pageSize

			rackID := ""

			if relation.Remote() == "rack" {
				rackID = data.ID
			}

			for _, d := range modelDataList {
				var dev v1.Dev
				if relation.Remote() == "rack" {
					dev, err = v1.ToDev(d)
					if err != nil {
						return nil, nil, nil, err
					}
				}

				dataList = append(dataList, v1.ModelDataWithRelInfo{
					ModelData: d,
					RelDesc:   d.ParentDesc,
					RelName:   relation.RelName(),
					RelID:     relation.GetID(),
					IsRack:    relation.Remote() == "rack", // 其实这里也可以用data.ModelCode == "rack"，因为当前数据已经是父级数据了
					StartU:    dev.StartU(),
					RackID:    rackID,
					DelID:     d.ID,
				})
			}

			return pageInfo, dataList, attrList, nil
		} else {
			return nil, nil, nil, errno.InternalServerError.Add("数据与模型关系不匹配，既不是关系所属模型也不是关联的父级模型")
		}
	case v1.Associate:
		// 说明这个关系是关联关系，先拿所有的字段信息
		var err error
		attrList, err = mds.store.ModelAttributes().GetModelAttributesByCustomFilter(
			ctx, bson.M{"model_code": utils.ToString(relation.Remote()),
				"type_name": bson.M{"$ne": "relationship"}}, bson.M{})
		if err != nil {
			return nil, nil, nil, err
		}

		if data.ModelCode != relation.Local() {
			zap.L().Error(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s", data.ModelCode, relation.Local()))
			return nil, nil, nil, errno.ErrModelDataMisMatch.Add(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s", data.ModelCode, relation.Local()))
		}

		// 如果无关联数据，那么直接返回
		if len(data.AssociateInstances) == 0 {
			pageInfo.CurrentPage = page
			pageInfo.PageSize = pageSize
			return pageInfo, dataList, attrList, nil
		}

		// 从model_relation表中查询relation的关系
		filter := bson.M{
			"$or": []bson.M{
				{"rel_model1_code": relation.Local(), "rel_field1_code": relation.LocalFieldCode()},
				{"rel_model2_code": relation.Local(), "rel_field2_code": relation.LocalFieldCode()},
			},
		}
		relationShip, err := mds.store.ModelRelation().GetModelRelationByFilter(ctx, filter)
		if err != nil {
			return nil, nil, nil, err
		}

		// 获取当前关联关系所有实例的ID
		currentAssociateInstanceIDs := make(mapdata.MapData)
		for _, instance := range data.AssociateInstances {
			if instance.RelationID == relationShip.ID {
				currentAssociateInstanceIDs[instance.InstanceID] = instance
			}
		}

		// 定义一个数据的Filter
		dFilter := bson.M{"_id": bson.M{"$in": currentAssociateInstanceIDs.Keys()}}
		modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, dFilter, page, pageSize, "")
		if err != nil {
			return nil, nil, attrList, err
		}

		pageInfo.Total = total
		pageInfo.Pages = (total / pageSize) + 1
		pageInfo.CurrentPage = page
		pageInfo.PageSize = pageSize

		for _, d := range modelDataList {
			dataList = append(dataList, v1.ModelDataWithRelInfo{
				ModelData: d,
				RelDesc:   currentAssociateInstanceIDs[d.ID].(v1.AssociateInstance).RelationDesc,
				IsRack:    relation.Remote() == "rack",
				RackID:    "",
				RelName:   relation.RelName(),
				RelID:     relation.GetID(),
			})
		}

		return pageInfo, dataList, attrList, nil
	default:
		return nil, nil, nil, errno.ErrModelAssociateNotExists.Add("关系类型不存在，既不是从属类型也不是关联关系类型")
	}
}

func (mds *modelDataService) GetInstancesByRelCode(ctx context.Context, relAttrID, dataID, operator string, page, pageSize int64) (*v1.ModelDataResponseWithRelInfo, error) {
	zap.L().Debug("GetInstancesByRelCode service called")
	span, ctx := apm.StartSpan(ctx, "GetInstancesByRelCodeService", "service")
	defer span.End()

	// 1、关系的合法性确认, relAttrID都是关系字段的ID，所以说我们把关系字段查出来做一下校验
	attr, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": relAttrID}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系字段不存在", zap.Error(err))
			return nil, errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		zap.L().Error(err.Error())
		return nil, err
	}

	// 2、查询对应的实例是否存在
	data, err := mds.store.ModelData().GetModelDataByID(ctx, dataID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error(fmt.Sprintf("数据不存在, id为%s", dataID), zap.Error(err))
			return nil, errno.ErrModelAssociateNotExists.Add(fmt.Sprintf("数据不存在, id为%s", dataID))
		}
		zap.L().Error(err.Error())
		return nil, err
	}

	resp := v1.NewModelDataResponseWithRelInfo()
	pageInfo, dataList, attrList, err := mds.getInstanceByRel(ctx, attr, data, page, pageSize, operator)
	if err != nil {
		return nil, err
	}

	resp.Total = pageInfo.Total
	resp.Pages = pageInfo.Pages

	// 如果没有数据，那么当前页就是第一页
	if pageInfo.Total == 0 {
		resp.CurrentPage = 1
	} else {
		resp.CurrentPage = pageInfo.CurrentPage
	}

	resp.PageSize = pageInfo.PageSize

	resp.Data = dataList
	resp.AttrList = attrList

	return resp, nil
}

func (mds *modelDataService) getParentRelation(ctx context.Context, modelCode string) (v1.CommonModelAttribute, error) {
	attrs, err := mds.store.ModelAttributes().GetModelAttrList(ctx)
	if err != nil {
		return v1.CommonModelAttribute{}, err
	}

	for _, attr := range attrs {
		if attr.ModelCode == modelCode && attr.TypeName == "relationship" {
			relType := utils.ToInt(attr.Attrs["rel_type"])
			if relType == 1 {
				return attr, nil
			}
		}
	}

	return v1.CommonModelAttribute{}, errno.ErrModelAttrNotExist.Add("没有找到父级关系字段")
}

func (mds *modelDataService) GetInstancesByRelCodeV2(ctx context.Context, relAttrID, dataID, operator string, page, pageSize int64) (*v1.RelatedDataResponse, error) {
	zap.L().Debug("GetInstancesByRelCodeV2 service called")
	span, ctx := apm.StartSpan(ctx, "GetInstancesByRelCodeV2Service", "service")
	defer span.End()

	// 0. 首先看看对应的数据实例是否存在
	data, err := mds.store.ModelData().GetModelDataByID(ctx, dataID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error(fmt.Sprintf("数据不存在, id为%s", dataID), zap.Error(err))
			return nil, errno.ErrModelAssociateNotExists.Add(fmt.Sprintf("数据不存在, id为%s", dataID))
		}
		zap.L().Error(err.Error())
		return nil, err
	}

	if relAttrID != "" {
		// 如果传递了relAttrID说明是要过滤某个类型的关联关系的数据，首先要看一下关系的合法性
		attr, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": relAttrID}, bson.M{})
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				zap.L().Error("关联关系字段不存在", zap.Error(err))
				return nil, errno.ErrModelAssociateNotExists.Add("关联关系不存在")
			}
			zap.L().Error(err.Error())
			return nil, err
		}

		rel, err := attr.ToRelationField()
		if err != nil {
			return nil, err
		}

		resp := v1.NewRelatedDataResponse()
		pageInfo, dataList, _, err := mds.getInstanceByRel(ctx, attr, data, page, pageSize, operator)
		if err != nil {
			return nil, err
		}

		resp.Total = pageInfo.Total
		resp.Pages = pageInfo.Pages
		resp.CurrentPage = pageInfo.CurrentPage
		resp.PageSize = pageInfo.PageSize

		relatedDataList := v1.NewRelatedDataList()
		for _, d := range dataList {
			i := v1.NewRelatedData()
			i.Relation.RelName = d.RelName
			i.Relation.RelType = int(rel.RelType())
			i.Relation.RelID = rel.GetID()
			i.Relation.RelSourceModelName = rel.LocalModelName()
			i.Relation.RelTargetModelName = rel.RemoteModelName()
			i.Relation.RelationShipDesc = rel.RelDesc()

			if rel.RelType() == v1.Subordinate && data.ModelCode == rel.Remote() {
				i.Relation.RelDesc = d.ParentDesc
				i.Source.SourceInstanceID = d.ID
				i.Source.SourceInstanceName = d.Name()
				i.Source.CurrentData = false
				i.Source.SourceModelCode = rel.Local()
				i.Target.TargetInstanceID = data.ID
				i.Target.TargetInstanceName = data.Name()
				i.Target.TargetModelCode = rel.Remote()
				i.Target.CurrentData = true
				i.DelID = d.DelID

				isRack := rel.Remote() == "rack"
				i.IsRack = isRack
				if isRack {
					dev, err := v1.ToDev(d.ModelData)
					if err != nil {
						return nil, err
					}
					i.StartU = dev.StartU()
					i.RackID = data.ID
				} else {
					i.StartU = 0
					i.RackID = ""
				}

			} else {
				i.Source.SourceInstanceID = data.ID
				i.Source.SourceInstanceName = data.Name()
				i.Source.CurrentData = true
				i.Source.SourceModelCode = rel.Local()
				i.Target.TargetInstanceID = d.ID
				i.Target.TargetInstanceName = d.Name()
				i.Target.TargetModelCode = rel.Remote()
				i.Target.CurrentData = false
				i.DelID = d.DelID

				isRack := rel.Remote() == "rack"
				i.IsRack = isRack
				if isRack {
					dev, err := v1.ToDev(*data)
					if err != nil {
						return nil, err
					}
					i.StartU = dev.StartU()
					i.RackID = d.ID
				} else {
					i.StartU = 0
					i.RackID = ""
				}

				if rel.RelType() == v1.Associate {
					relDesc := ""
					for _, a := range data.AssociateInstances {
						if a.InstanceID == d.ID {
							relDesc = a.RelationDesc
						}
					}
					i.Relation.RelDesc = relDesc
				} else {
					i.Relation.RelDesc = data.ParentDesc
				}
			}

			relatedDataList = append(relatedDataList, *i)
		}
		resp.Data = relatedDataList

		return resp, nil
	} else {
		// data存在, 拿一下data的关联的数据
		assIds := make(mapdata.MapData)
		for _, ass := range data.AssociateInstances {
			// key是当前数据对端的关联数据的id，value是关联的ID
			assIds.Set(ass.InstanceID, ass.RelationID)
		}

		// 拼接获取关联数据的条件
		relatedDataFilter := make([]bson.M, 0)
		// 以当前数据为父的数据
		relatedDataFilter = append(relatedDataFilter, bson.M{"parent_id": data.ID})
		// 以当前数据为子的数据
		if data.ParentID != "" {
			relatedDataFilter = append(relatedDataFilter, bson.M{"_id": data.ParentID})
		}
		// 当前数据关联关系的数据
		if len(assIds.Keys()) > 0 {
			relatedDataFilter = append(relatedDataFilter, bson.M{"_id": bson.M{"$in": assIds.Keys()}})
		}

		// 拿到所有有关系的数据，以数据为依据建立分页，然后填充关系信息
		modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, bson.M{"$or": relatedDataFilter}, page, pageSize, "")
		if err != nil {
			return nil, err
		}

		// 获取attrCodeMap
		attrCodeMap, err := mds.store.ModelAttributes().GetModelAttrMap(ctx, "code")
		if err != nil {
			return nil, err
		}

		// 拼凑response
		resp := v1.NewRelatedDataResponse()
		resp.Total = total
		resp.Pages = (total / pageSize) + 1
		resp.CurrentPage = page
		resp.PageSize = pageSize

		relatedDataList := v1.NewRelatedDataList()

		if len(modelDataList) == 0 {
			resp.Data = relatedDataList
			return resp, nil
		}

		// 如果查询到的数据不为空，则遍历所有的modelData然后进行数据的拼接
		for _, d := range modelDataList {
			i := v1.NewRelatedData()
			if d.ParentID == data.ID {
				// 以当前数据为父的数据
				p, err := mds.getParentRelation(ctx, d.ModelCode)
				if err != nil {
					return nil, err
				}

				attr, err := p.ToRelationField()
				if err != nil {
					return nil, err
				}

				i.Relation.RelName = attr.RelName()
				i.Relation.RelDesc = d.ParentDesc
				i.Relation.RelID = attr.GetID()
				i.Relation.RelType = int(attr.RelType())
				i.Relation.RelSourceModelName = attr.LocalModelName()
				i.Relation.RelTargetModelName = attr.RemoteModelName()
				i.Relation.RelationShipDesc = attr.RelDesc()

				i.Source.SourceInstanceID = d.ID
				i.Source.SourceInstanceName = d.Name()
				i.Source.CurrentData = false
				i.Source.SourceModelCode = attr.Local()
				i.Target.TargetInstanceID = data.ID
				i.Target.TargetInstanceName = data.Name()
				i.Target.CurrentData = true
				i.Target.TargetModelCode = attr.Remote()
				i.DelID = d.ID

				isRack := data.ModelCode == "rack"
				i.IsRack = isRack
				if isRack {
					dev, err := v1.ToDev(d)
					if err != nil {
						return nil, err
					}
					i.StartU = dev.StartU()
					i.RackID = data.ID
				} else {
					i.StartU = 0
					i.RackID = ""
				}

				relatedDataList = append(relatedDataList, *i)

			} else if data.ParentID == d.ID {
				// 以当前数据为子的数据
				p, err := mds.getParentRelation(ctx, data.ModelCode)
				if err != nil {
					return nil, err
				}
				attr, err := p.ToRelationField()
				if err != nil {
					return nil, err
				}

				i.Relation.RelName = attr.RelName()
				i.Relation.RelDesc = data.ParentDesc
				i.Relation.RelID = attr.GetID()
				i.Relation.RelType = int(attr.RelType())
				i.Relation.RelSourceModelName = attr.LocalModelName()
				i.Relation.RelTargetModelName = attr.RemoteModelName()
				i.Relation.RelationShipDesc = attr.RelDesc()

				i.Source.SourceInstanceID = data.ID
				i.Source.SourceInstanceName = data.Name()
				i.Source.CurrentData = true
				i.Source.SourceModelCode = attr.Local()
				i.Target.TargetInstanceID = d.ID
				i.Target.TargetInstanceName = d.Name()
				i.Target.CurrentData = false
				i.Target.TargetModelCode = attr.Remote()
				i.DelID = data.ID

				isRack := attr.Remote() == "rack"
				i.IsRack = isRack
				if isRack {
					dev, err := v1.ToDev(*data)
					if err != nil {
						return nil, err
					}
					i.StartU = dev.StartU()
					i.RackID = d.ID
				} else {
					i.StartU = 0
					i.RackID = ""
				}

				relatedDataList = append(relatedDataList, *i)
			} else {
				// 当前数据关联关系的数据
				relationID := utils.ToString(assIds[d.ID])

				associateRelationShip, err := mds.store.ModelRelation().GetModelRelationByID(ctx, operator, relationID)
				if err != nil {
					return nil, err
				}

				var (
					relAttribute = v1.CommonModelAttribute{}
					exist        bool
				)

				// 如果通过relation来进行判断的话，那么其实还是能看到发起方到底是谁的。1是发起，2是被发起
				// 因此这个时候，我们得判断拿哪一端的关系。
				if associateRelationShip.RelModel1Code == data.ModelCode {
					relAttribute, exist = attrCodeMap[associateRelationShip.RelField1Code]
				} else {
					relAttribute, exist = attrCodeMap[associateRelationShip.RelField2Code]
				}

				if !exist {
					zap.L().Error(fmt.Sprintf("关联关系字段不存在，关联关系字段的ID为 %s", relationID))
					return nil, errno.ErrModelAssociateNotExists.Add(fmt.Sprintf("关联关系字段不存在，关联关系字段的ID为 %s", relationID))
				}

				attr, err := relAttribute.ToRelationField()
				if err != nil {
					return nil, err
				}

				i.Relation.RelName = attr.RelName()
				i.Relation.RelDesc = data.GetAssociateInstance(d.ID).RelationDesc
				i.Relation.RelID = attr.GetID()
				i.Relation.RelType = int(attr.RelType())
				i.Relation.RelSourceModelName = attr.LocalModelName()
				i.Relation.RelTargetModelName = attr.RemoteModelName()
				i.Relation.RelationShipDesc = attr.RelDesc()

				i.Source.SourceInstanceID = data.ID
				i.Source.SourceInstanceName = data.Name()
				i.Source.CurrentData = true
				i.Source.SourceModelCode = attr.Local()

				i.Target.TargetInstanceID = d.ID
				i.Target.TargetInstanceName = d.Name()
				i.Target.CurrentData = false
				i.Target.TargetModelCode = attr.Remote()
				i.IsRack = attr.Remote() == "rack"
				i.StartU = 0
				i.RackID = ""
				i.DelID = ""

				relatedDataList = append(relatedDataList, *i)
			}
		}
		resp.Data = relatedDataList
		return resp, nil
	}
}

// GetRelInstances 获取关联关系的实例
func (mds *modelDataService) GetRelInstances(ctx context.Context, relAttrID, dataID, keyword, field string, page, pageSize int64) (*v1.ModelDataResponse, error) {
	zap.L().Debug("GetRelInstances service called")
	span, ctx := apm.StartSpan(ctx, "GetRelInstancesService", "service")
	defer span.End()

	// 1、关系的合法性确认, relAttrID都是关系字段的ID，所以说我们把关系字段查出来做一下校验
	rel, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": relAttrID}, nil)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系字段不存在", zap.Error(err))
			return nil, errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		zap.L().Error(err.Error())
		return nil, err
	}

	// 2、查询对应的实例是否存在
	data, err := mds.store.ModelData().GetModelDataByID(ctx, dataID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error(fmt.Sprintf("数据不存在, id为%s", dataID), zap.Error(err))
			return nil, errno.ErrModelAssociateNotExists.Add(fmt.Sprintf("数据不存在, id为%s", dataID))
		}
		return nil, err
	}

	// 3、构造一个接口的response响应，并初始化分页参数
	resp := v1.NewModelDataResponse()
	resp.CurrentPage = page
	resp.PageSize = pageSize

	// 4、将关系字段转换为关系对象
	relation, err := rel.ToRelationField()
	if err != nil {
		return nil, err
	}

	relType := relation.RelType()
	if relType == v1.Subordinate {
		var (
			filter = make(bson.M)
			parent bool
			err    error
		)

		// 看看这个数据和这个关系所属的模型是否一致，如果一致的话，那么就证明这条数据的确是和这条关系同属于一个模型。避免出现模型和数据不一致的情况
		// 如果合规的话，当前数据与关系的模型如果不一样的话，那么说明这条数据是子指向父，该条数据是父数据
		if data.ModelCode != rel.ModelCode {
			if data.ModelCode != relation.Remote() {
				zap.L().Error(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s", data.ModelCode, rel.ModelCode))
				return nil, errno.ErrModelDataMisMatch.Addf("数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s", data.ModelCode, rel.ModelCode)
			}
			parent = true
		}

		if !parent {
			// 当前数据是子数据，要关联父数据，那么relation的remote就是父亲的模型
			filter["model_code"] = relation.Remote()
			if keyword != "" {
				dataField := ""
				if field != "" {
					dataField = fmt.Sprintf("data.%s", field)
					filter[dataField] = bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}
				} else {
					ml, err := mds.store.ModelAttributes().GetModelAttrList(ctx)
					if err != nil {
						return nil, err
					}

					subFilters := make([]bson.M, 0)
					for _, a := range ml {
						if a.ModelCode == relation.Remote() && a.TypeName != "relationship" {
							dataField = fmt.Sprintf("data.%s", a.Code)
							subFilters = append(subFilters, bson.M{dataField: bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}})
						}
					}
					filter["$or"] = subFilters
				}
			}
		} else {
			// 当前数据是父亲数据，找孩子。那么relation的local就是孩子的模型
			filter["model_code"] = relation.Local()
			if keyword != "" {
				dataField := ""
				if field != "" {
					dataField = fmt.Sprintf("data.%s", field)
					filter[dataField] = bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}
				} else {
					ml, err := mds.store.ModelAttributes().GetModelAttrList(ctx)
					if err != nil {
						return nil, err
					}

					subFilters := make([]bson.M, 0)
					for _, a := range ml {
						if a.ModelCode == relation.Remote() && a.TypeName != "relationship" {
							dataField = fmt.Sprintf("data.%s", a.Code)
							subFilters = append(subFilters, bson.M{dataField: bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}})
						}
					}
					filter["$or"] = subFilters
				}
			}
		}

		modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, filter, page, pageSize, "")
		if err != nil {
			return nil, err
		}

		resp.Total = total
		resp.Pages = (total / pageSize) + 1

		for _, md := range modelDataList {
			relInst := v1.RelInstance{}
			relInst.ModelData = md

			if data.ParentID != "" && !parent {
				// 子找父亲的时候，如果当前数据的父亲是这个数据，那么就标记为已使用
				if md.ID == data.ParentID {
					relInst.InUse = true
				}
			} else {
				// 父找孩子，也就是当前数据的id是父亲的角色，那么就看哪个孩子已经和该数据建立过关系了
				if md.ParentID == data.ID {
					relInst.InUse = true
				}
			}

			resp.Data = append(resp.Data, &relInst)
		}

	} else if relType == v1.Associate {
		var (
			target v1.AssociationRelationshipAttribute
			err    error
		)

		err = mapstructure.Decode(rel, &target)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		// 判断数据与关联关系是否匹配
		if data.ModelCode != target.ModelCode {
			if data.ModelCode != target.Attrs.RelModelCode {
				zap.L().Error(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s", data.ModelCode))
				return nil, errno.ErrModelDataMisMatch.Addf("数据与模型关系不匹配，数据的模型的code为 %s", data.ModelCode)
			}
		}

		filter := bson.M{"model_code": target.Attrs.RelModelCode}
		currentAssociateIDs := make([]string, 0)
		for _, ass := range data.AssociateInstances {
			currentAssociateIDs = append(currentAssociateIDs, ass.InstanceID)
		}
		// if len(data.AssociateInstances) > 0 {
		// 	filter["_id"] = bson.M{"$nin": currentAssociateIDs}
		// }

		if keyword != "" {
			dataField := ""
			if field != "" {
				dataField = fmt.Sprintf("data.%s", field)
				filter[dataField] = bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}
			} else {
				ml, err := mds.store.ModelAttributes().GetModelAttrList(ctx)
				if err != nil {
					return nil, err
				}

				regexFilters := make([]bson.M, 0)
				for _, a := range ml {
					if a.ModelCode == relation.Remote() && a.TypeName != "relationship" {
						dataField = fmt.Sprintf("data.%s", a.Code)
						regexFilters = append(regexFilters, bson.M{dataField: bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}})
						// filter[dataField] = bson.M{"$regex": ".*" + keyword + ".*", "$options": "i"}
					}
				}

				filter["$or"] = regexFilters
			}
		}

		modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, filter, page, pageSize, "")
		if err != nil {
			return nil, err
		}

		resp.Total = total
		resp.Pages = (total / pageSize) + 1

		for _, md := range modelDataList {
			relInst := v1.RelInstance{}
			relInst.ModelData = md

			// 针对已经建立关系的数据，并不是不展示，而是说标记为已使用
			if array.InArray(md.ID, currentAssociateIDs) {
				relInst.InUse = true
			} else {
				relInst.InUse = false
			}
			resp.Data = append(resp.Data, &relInst)
		}

	} else {
		zap.L().Error(fmt.Sprintf("关系类型不合法，关系类型为 %d", relType))
		return nil, errno.ErrModelDataMisMatch.Addf("关系类型不合法，关系类型为 %d", relType)
	}

	return resp, nil
}

// CreateModelDataSubOrdinateRelationMove 批量转移至，指的是批量将一组节点，添加到某一个节点的子节点下。目前要求这一组节点必须属于一个模型。
func (mds *modelDataService) CreateModelDataSubOrdinateRelationMove(ctx context.Context, operator string, request *v1.DataNTo1Request) (*v1.DataMoveToResponse, error) {
	zap.L().Debug("CreateModelDataSubOrdinateRelationMove Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelDataSubOrdinateRelationMoveService", "service")
	defer span.End()

	// 处理用户提交过来的数据
	reqDataSet := make(map[string]*v1.DataMoveReason)
	resp := v1.NewDataMoveToResponse()

	// 初始化用户要传递的所有数据
	for _, did := range request.SourceInstanceID {
		// 如果说已经放到reqDataSet中，但是又遍历到了该值，说明用户传到了重复的id。
		_, exist := reqDataSet[did]
		if exist {
			// 这个时候InstanceName还没有拿到，所以就用id来替代
			reqDataSet[did].InstanceName = did
			reqDataSet[did].FailedReason = append(reqDataSet[did].FailedReason, "重复的ID")
		} else {
			// 如果没有的话，那么就直接初始化
			reqDataSet[did] = v1.NewDataMoveReason()
		}
	}

	// 初始化一个mapData备用
	reqDataSetMapData := make(mapdata.MapData)
	for k, v := range reqDataSet {
		reqDataSetMapData.Set(k, v)
	}

	// 根据用户提交过来的数据构造一个dataMap用来存储从数据库真实查出来的数据以便后面做数据处理和对比
	dataMap := make(mapdata.MapData)
	dataList, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"_id": bson.M{
			"$in": reqDataSetMapData.Keys(),
		},
	}, nil)

	// 整个接口级别的错误
	if err != nil {
		return nil, err
	}

	// 如果收查不到数据，那就返回报错，说明用户传递的数据有问题，因为在发起请求的时候，要求源的ID数据至少得是1个
	if len(dataList) <= 0 {
		resp.Success = 0
		resp.Failed = len(reqDataSet)

		for _, v := range reqDataSet {
			v.FailedReason = append(v.FailedReason, "数据不存在")
			resp.Result = append(resp.Result, *v)
		}

		return resp, nil
	}

	// 在这里判定一下data是否都是一个模型的，以切片中的第一条数据作为基准。
	dataModel := dataList[0].ModelCode
	for _, data := range dataList {
		if data.ModelCode != dataModel {
			return nil, errno.ErrParameterInvalid.Add("source_instance_id中的数据必须属于同一模型")
		}
		dataMap.Set(data.ID, data)
		reqDataSet[data.ID].InstanceName = utils.ToString(data.Data[fmt.Sprintf("%s_name", data.ModelCode)])
	}

	// 如果用户输入的ID都是有效的话，那么这个时候dataMap的长度应该和用户输入的ID的长度是一致的
	if len(dataList) != len(reqDataSetMapData.Keys()) {
		invalidIDS := reqDataSetMapData.Difference(dataMap)
		for _, idx := range invalidIDS {
			reqDataSet[idx].FailedReason = append(reqDataSet[idx].FailedReason, "数据不存在")
		}
	}

	// 查看一下目的数据是否存在
	target, err := mds.store.ModelData().GetModelDataByID(ctx, request.TargetInstanceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Add("目标数据不存在")
		}
		return nil, err
	}

	// 查看关系对不对
	attr, err := mds.store.ModelAttributes().GetModelAttributeByID(ctx, request.RelID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrModelDataMisMatch.Add("关系不存在")
		}
		return nil, err
	}

	// 只有从属关系才会有转移至的功能，如果你给我传递了非从属关系，那么直接返回异常就完事了。
	if utils.ToInt(attr.Attrs["rel_type"]) != 1 {
		return nil, errno.ErrModelDataMisMatch.Add("关系类型不匹配")
	}

	if utils.ToString(attr.Attrs["rel_to"]) != target.ModelCode || attr.ModelCode != dataModel {
		return nil, errno.ErrModelDataMisMatch.Add("关系类型不匹配")
	}

	// 处理关系
	for _, data := range dataList {
		// 已经存在从属关系的直接跳过，并更新失败原因
		if data.ParentID != "" {
			reqDataSet[data.ID].FailedReason = append(reqDataSet[data.ID].FailedReason, "已经存在从属关系")
			continue
		}

		// 更新子数据的parent字段信息
		data.ParentID = target.ID
		data.ParentDesc = request.RelDesc

		// 获取要添加的path
		path, err := mds.GetDataTreePath(ctx, data)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		t, _ := mds.store.Common().StartTransaction()

		t.Wrapper(func(sessionCtx context.Context) error {
			// 更新源数据的parent字段信息
			if _, err := mds.store.ModelData().UpdateModelData(sessionCtx, &v1.UpdateModelDataStoreRequest{
				Id:           data.ID,
				ModelData:    nil,
				UpdateParent: true,
				ParentID:     data.ParentID,
				ParentDesc:   data.ParentDesc,
			}); err != nil {
				zap.L().Error(err.Error())
				return err
			}
			return nil
		})

		t.Wrapper(func(p []*v1.DataTreePath) func(sessionCtx context.Context) error {
			return func(sessionCtx context.Context) error {
				if err := mds.updateDataTreePath(sessionCtx, operator, p); err != nil {
					zap.L().Error(err.Error())
					return err
				}
				return nil
			}
		}(path))

		if err := t.Do(ctx); err != nil {
			zap.L().Error(err.Error())
			reqDataSet[data.ID].FailedReason = append(reqDataSet[data.ID].FailedReason, err.Error())
			continue
		}
	}

	for _, v := range reqDataSet {
		if len(v.FailedReason) > 0 {
			resp.Failed++
		} else {
			resp.Success++
			v.FailedReason = append(v.FailedReason, "成功")
		}
		resp.Result = append(resp.Result, *v)
	}

	return resp, nil
}

// GetMoveInstances 获取可转移的实例，初期的设想是只有从属关系才能转移。
func (mds *modelDataService) GetMoveInstances(ctx context.Context, ari v1.AvailableRelInstances, page, pageSize int64) (*v1.ModelDataResponse, error) {
	zap.L().Debug("GetMoveInstances service called")
	span, ctx := apm.StartSpan(ctx, "GetMoveInstancesService", "service")
	defer span.End()

	// 1、关系的合法性确认, relAttrID都是关系字段的ID，所以说我们把关系字段查出来做一下校验
	rel, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": ari.RelAttrID}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系字段不存在", zap.Error(err))
			return nil, errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		zap.L().Error(err.Error())
		return nil, err
	}

	// 2、查询对应的实例是否存在
	var dataMap = make(map[string]*v1.ModelData)
	for _, dataID := range ari.DataIDs {
		// 2、查询对应的实例是否存在
		data, err := mds.store.ModelData().GetModelDataByID(ctx, dataID)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				zap.L().Error(fmt.Sprintf("数据不存在, id为%s", dataID), zap.Error(err))
				return nil, errno.ErrModelAssociateNotExists.Add(fmt.Sprintf("数据不存在, id为%s", dataID))
			}
			return nil, err
		}
		dataMap[dataID] = data
	}

	dataParents := make([]string, 0)
	for _, data := range dataMap {
		if data.ParentID != "" {
			dataParents = append(dataParents, data.ParentID)
		}
	}

	// 3、看看这个数据和这个关系所属的模型是否一致，如果一致的话，那么就证明这条数据的确是和这条关系同属于一个模型。避免出现模型和数据不一致的情况
	// 侧面也说明，这个是子找父的关系，从属关系，且数据的modelCode和关系的modelCode一致的时候，就是子找父亲。
	for _, data := range dataMap {
		if data.ModelCode != rel.ModelCode {
			zap.L().Error(fmt.Sprintf("数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s", data.ModelCode, rel.ModelCode))
			return nil, errno.ErrModelDataMisMatch.Addf("数据与模型关系不匹配，数据的模型的code为 %s，但是关系的模型为 %s", data.ModelCode, rel.ModelCode)
		}
	}

	resp := v1.NewModelDataResponse()
	relType := utils.ToInt(rel.Attrs["rel_type"])
	if relType != 1 {
		zap.L().Error(fmt.Sprintf("关系类型不合法，只能是从属关系，关系类型为 %d", relType))
		return nil, errno.ErrModelRelationType.Addf("关系类型不合法，只能是从属关系，关系类型为 %d", relType)
	}

	relField, err := rel.ToRelationField()
	if err != nil {
		return nil, err
	}

	filter := bson.M{"model_code": relField.Remote()}

	// 如果说传递了关键字，那么就要按照关键字进行模糊查询
	if ari.Keyword != "" {
		dataField := ""
		if ari.Field != "" {
			// 同时如果指定了field字段，那么就要按照指定的字段进行模糊查询
			dataField = fmt.Sprintf("data.%s", ari.Field)
			filter[dataField] = bson.M{"$regex": ".*" + ari.Keyword + ".*", "$options": "i"}
		} else {
			ml, err := mds.store.ModelAttributes().GetModelAttrList(ctx)
			if err != nil {
				return nil, err
			}

			subFilters := make([]bson.M, 0)
			for _, a := range ml {
				if a.ModelCode == relField.Remote() && a.TypeName != "relationship" {
					dataField = fmt.Sprintf("data.%s", a.Code)
					// 过滤调唯一标识字段
					if dataField == fmt.Sprintf("%s_code", a.ModelCode) {
						continue
					}
					subFilters = append(subFilters, bson.M{dataField: bson.M{"$regex": ".*" + ari.Keyword + ".*", "$options": "i"}})
				}
			}
			filter["$or"] = subFilters
		}
	}

	// 这里查询出来的数据，都是可建立关系的父亲数据
	modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, filter, page, pageSize, "")
	if err != nil {
		return nil, err
	}

	resp.Total = total
	resp.Pages = (total / pageSize) + 1

	for _, md := range modelDataList {
		relInst := v1.RelInstance{}
		relInst.ModelData = md

		// 如果说当前数据的id在dataParents中，那么就标记为已使用
		if array.InArray(md.ID, dataParents) {
			relInst.InUse = true
		}

		resp.Data = append(resp.Data, &relInst)
	}

	return resp, nil
}

// UpdateModelDataRelation 更新模型数据关系
func (mds *modelDataService) UpdateModelDataRelation(ctx context.Context, operator string, data *v1.Data1To1Request) error {
	zap.L().Debug("UpdateModelDataRelation service called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelDataRelationService", "service")
	defer span.End()

	// 1、关系的合法性确认, relAttrID都是关系字段的ID，所以说我们把关系字段查出来做一下校验
	rel, err := mds.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": data.RelID}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("关联关系字段不存在", zap.Error(err))
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		zap.L().Error(err.Error())
		return err
	}

	// 2、验证一下源目端数据是否存在
	s, err := mds.store.ModelData().GetModelDataByID(ctx, data.SourceInstanceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("源端数据不存在", zap.Error(err))
			return errno.ErrDataNotExists.Add("源端数据不存在")
		}
		zap.L().Error(err.Error())
		return err
	}

	t, err := mds.store.ModelData().GetModelDataByID(ctx, data.TargetInstanceID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("目标端数据不存在", zap.Error(err))
			return errno.ErrDataNotExists.Add("目标端数据不存在")
		}
		zap.L().Error(err.Error())
		return err
	}

	relType := utils.ToInt(rel.Attrs["rel_type"])
	switch v1.RelationType(relType) {
	case v1.Subordinate:
		sub := v1.NewSubordinationRelationshipAttribute()
		if err := mapstructure.Decode(rel, &sub); err != nil {
			return err
		}
		if s.ParentID == data.TargetInstanceID {
			// 保存一个当前数据的快照
			snapDInterface := deepcopy.Copy(*s)
			snapD, ok := snapDInterface.(v1.ModelData)
			if !ok {
				zap.L().Error("快照数据类型不匹配")
				return errno.ErrModelDataMisMatch.Add("快照数据类型不匹配")
			}

			// 说明这是一个子 to 父的关系，当前的数据的数据是子数据
			if s.ParentDesc == data.RelDesc {
				zap.L().Debug("描述未发生变化，不进行更新")
				return nil
			}
			s.ParentDesc = data.RelDesc
			if _, err := mds.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
				Id:           s.ID,
				ModelData:    nil,
				UpdateParent: true,
				ParentID:     s.ParentID,
				ParentDesc:   s.ParentDesc,
			}); err != nil {
				zap.L().Error(err.Error())
				return err
			}

			au := &AuditEvent{store: mds.store}
			if err := au.Generate(
				ctx,
				audit.AuditLogEvent,
				audit.ResourceRelationshipSubRes,
				audit.ActionUpdate,
				operator,
				s.ID,
				snapD,
				s,
			); err != nil {
				return err
			}

			if err := au.Save(ctx); err != nil {
				zap.L().Error(err.Error())
				return err
			}
			return nil
		} else if t.ParentID == data.SourceInstanceID {
			// 保存一个当前数据的快照
			snapD, err := t.Copy()
			if err != nil {
				zap.L().Error("创建数据快照失败")
				return errno.InternalServerError.Add("创建数据快照失败")
			}

			// 说明这是一个父 to 子的关系
			if t.ParentDesc == data.RelDesc {
				zap.L().Debug("描述未发生变化，不进行更新")
				return nil
			}

			t.ParentDesc = data.RelDesc
			if _, err := mds.store.ModelData().UpdateModelData(ctx, &v1.UpdateModelDataStoreRequest{
				Id:           t.ID,
				ModelData:    nil,
				UpdateParent: true,
				ParentID:     t.ParentID,
				ParentDesc:   t.ParentDesc,
			}); err != nil {
				zap.L().Error(err.Error())
				return err
			}

			au := &AuditEvent{store: mds.store}
			if err := au.Generate(
				ctx,
				audit.AuditLogEvent,
				audit.ResourceRelationshipSubRes,
				audit.ActionUpdate,
				operator,
				t.ID,
				snapD,
				t,
			); err != nil {
				return err
			}

			if err := au.Save(ctx); err != nil {
				zap.L().Error(err.Error())
				return err
			}
			return nil

		} else {
			zap.L().Error("数据不匹配")
			return errno.ErrModelDataMisMatch.Add("数据不匹配")
		}

	case v1.Associate:
		ass := v1.NewAssociationRelationshipAttribute()
		err := mapstructure.Decode(rel, &ass)
		if err != nil {
			return err
		}

		assList := make([]v1.AssociateInstance, 0)
		for _, a := range s.AssociateInstances {
			if a.InstanceID == data.TargetInstanceID {
				a.RelationDesc = data.RelDesc
			}
			assList = append(assList, a)
		}
		s.AssociateInstances = assList

		err = mds.store.ModelData().UpdateModelDataAssociation(ctx, operator, s)
		if err != nil {
			zap.L().Error(err.Error())
			return err
		}
		return nil
	default:
		zap.L().Error("不合法的关系类型")
		return errno.ErrModelRelationType.Add("不合法的关系类型")
	}
}

// GetModelDataByFilter 根据特定条件获取模型数据
func (mds *modelDataService) GetModelDataByFilter(ctx context.Context, searchFilter bson.M) (*v1.ModelDataResponseWithOutPagination, error) {
	zap.L().Debug("GetModelDataByFilter service called")
	span, ctx := apm.StartSpan(ctx, "GetModelDataByFilterService", "service")
	defer span.End()

	modelDataList, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, searchFilter, nil)
	if err != nil {
		return nil, err
	}

	resp := new(v1.ModelDataResponseWithOutPagination)
	resp.Data = make([]v1.ModelData, 0)
	for _, md := range modelDataList {
		resp.Data = append(resp.Data, *md)
	}
	resp.Total = int64(len(modelDataList))

	return resp, nil
}

// Offline 下线资源，包括设备，机柜，机房，职场
func (mds *modelDataService) Offline(ctx context.Context, operator string, req *apiv1.OfflineRequest) (*apiv1.OfflineResponse, error) {
	zap.L().Debug("Offline service called")
	span, ctx := apm.StartSpan(ctx, "OfflineService", "service")
	defer span.End()

	// 有效的下线模型代码列表，当前先仅支持这些
	validOfflineModelCodeList := []string{"switch", "router", "firewall", "loadbalancing", "audit", "secure",
		"transmission", "wlc", "netflow", "server", "vm", "storage", "safety", "office", "idc", "rack"}

	resp := apiv1.NewOfflineResponse()
	for _, instanceID := range req.InstanceIDs {
		md, err := mds.store.ModelData().GetModelDataByID(ctx, instanceID)
		if err != nil {
			resp.FailedCount++
			resp.FailedItems = append(resp.FailedItems, apiv1.OfflineFailedItem{
				InstanceID: instanceID,
				Reason:     err.Error(),
			})
			continue
		}

		// 目前我们只会针对定义好的一些模型兼容下线的操作，其他模型不支持下线
		if !array.InArray(md.ModelCode, validOfflineModelCodeList) {
			resp.FailedCount++
			resp.FailedItems = append(resp.FailedItems, apiv1.OfflineFailedItem{
				InstanceID: instanceID,
				Reason:     "不支持的下线的模型代码",
			})
			continue
		}

		// 下线操作主要完成的工作是什么？
		// 1、既然要下线，首先就要将资源的关系先清理掉，不管是从属还是关联关系
		// 2、如果当前资源是虚拟机类的资产，直接删除资源并记录审计日志
		// 3、如果是实体资产，需要清空资源的部分信息，比如主机名，IP地址，机架位，负责人等，对应的状态要改为下架或者下线然后记录审计日志。
		// 4、考虑子数据的情况，父级数据失效后，对应的子数据也就失效了，同时还要更新treepath闭包表，否则会有很多垃圾数据

	}

	return resp, nil
}
