package v1

import (
	"context"
	"errors"
	"sort"
	"strconv"
	"strings"

	"ks-knoc-server/internal/cmdbserver/store"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type LabelService interface {
	// CreateLabel 创建Label标签
	CreateLabel(ctx context.Context, req *v1.CreateLabelRequest) error
	GetLabels(ctx context.Context, keyList []string, page, pageSize int64) (*v1.GetLabelsResponse, error)
	DeleteLabel(ctx context.Context, operator, labelID string) error
	UpdateLabelKey(ctx context.Context, keyID string, req *v1.UpdateLabelKeyRequest) error
	UpdateLabelValue(ctx context.Context, valueID string, req *v1.UpdateLabelValueRequest) error
	BindingLabels(ctx context.Context, operator string, req *v1.LabelBindingRequest) error
	UpdateBindingLabels(ctx context.Context, operator string, req *v1.LabelUnBindingRequest) error
	UnBindingLabels(ctx context.Context, operator string, req *v1.LabelUnBindingRequest) error
	SearchLabeledData(ctx context.Context, req *v1.SearchLabeledDataRequest, page, pageSize int64) (*v1.SearchLabeledDataResponse, error)
}

type labelService struct {
	store store.Factory
}

var _ LabelService = (*labelService)(nil)

// 最大允许绑定50个Label，避免无限制的绑定标签
const LabelBindingLimit int = 50

func newLabelService(srv *service) *labelService {
	return &labelService{store: srv.store}
}

func (l *labelService) DeleteLabel(ctx context.Context, operator, labelID string) error {
	zap.L().Debug("DeleteLabel Service Called")
	span, ctx := apm.StartSpan(ctx, "DeleteLabel", "service")
	defer span.End()

	zap.L().Debug("要删除的Label标签ID为:" + labelID)

	// 先看看这个id对应的label value存不存在，如果存在的话，这里就不会返回报错，err应返回nil
	v, err := l.store.Labels().GetLabelValueByID(ctx, []string{labelID})
	if err != nil {
		return err
	}

	if len(v) == 0 {
		return errno.ErrDataNotExists.Add("标签不存在")
	}

	// 把删除label_value和对应的label_key放到一个事物里面去做
	t, err := l.store.Common().StartTransaction()
	if err != nil {
		return err
	}

	// 先检查要删除的标签是否还绑定了数据，如果说绑定了数据，那么就先解绑更新数据
	t.Wrapper(func(sessionCtx context.Context) error {
		// 如果说有数据绑定了该标签，那么对应的数据也应该被更新
		dataList, err := l.store.ModelData().GetModelDataByCustomFilter(sessionCtx, bson.M{
			"label_id_list": bson.M{"$in": []string{labelID}},
		}, nil)
		if err != nil {
			zap.L().Error("GetModelData Failed, Error is " + err.Error())
			return err
		}

		if len(dataList) == 0 {
			zap.L().Debug("没有数据绑定了该标签，直接返回")
			return nil
		}

		updatedDataList := make([]*v1.ModelData, 0)
		for _, data := range dataList {
			oldLabels := array.StringArray(data.LabelIDList)
			deletedID := array.StringArray{labelID}
			newLabels := deletedID.ArrayDiff(oldLabels)

			zap.L().Debug("删除数据标签",
				zap.String("数据ID", data.ID),
				zap.Any("旧标签", oldLabels),
				zap.Any("要删除的", deletedID),
				zap.Any("新标签", newLabels))

			data.LabelIDList = newLabels
			updatedDataList = append(updatedDataList, data)
		}

		// 批量更新
		if err := l.store.ModelData().UpdateDataLabels(sessionCtx, operator, updatedDataList); err != nil {
			zap.L().Error("Bulk Update ModelData Failed, Error is " + err.Error())
			return err
		}

		return nil
	})

	t.Wrapper(func(sessionCtx context.Context) error {
		// 删除对应的value
		if err := l.store.Labels().DeleteLabel(sessionCtx, labelID); err != nil {
			return err
		}
		return nil
	})

	t.Wrapper(func(sessionCtx context.Context) error {
		// 我们还要检查一个事情，就是对应的key是否还有对应的value，如果没有的话，就把key也删除
		lvs, err := l.store.Labels().GetLabelValuesByKeyIDs(sessionCtx, []string{v[0].KeyID})
		if err != nil {
			return err
		}

		// 如果说找不到了，那么就直接删除key就可以了
		if len(lvs) == 0 {
			if err := l.store.Labels().DeleteLabelKeyByID(sessionCtx, v[0].KeyID); err != nil {
				return err
			}
		}
		return nil
	})

	if err := t.Do(ctx); err != nil {
		return err
	}

	return nil
}

func (l *labelService) GetLabels(ctx context.Context, keyList []string, page, pageSize int64) (*v1.GetLabelsResponse, error) {
	zap.L().Debug("GetLabels Service Called")
	span, ctx := apm.StartSpan(ctx, "GetLabels", "service")
	defer span.End()

	var labelList = make([]v1.LabelKeyValuePair, 0)

	// 先拿到所有的key（分页后的）, 排好序的。
	keys, err := l.store.Labels().GetLabelKeys(ctx, keyList, page, pageSize)
	if err != nil {
		return nil, err
	}

	if len(keys) == 0 {
		return nil, errno.ErrDataNotExists.Add("标签不存在")
	}

	kids := make([]string, 0)
	for _, k := range keys {
		kids = append(kids, k.ID)
	}

	// 根据key的id列表拿到所有的标签values
	values, err := l.store.Labels().GetLabelValuesByKeyIDs(ctx, kids)
	if err != nil {
		return nil, err
	}

	if len(values) == 0 {
		return nil, errno.InternalServerError.Add("内部异常，标签键存在，但对应的标签值不存在，请联系管理员")
	}

	// 构造一个map，key是标签key的id，value是对应的键值对的信息
	lkMap := make(mapdata.MapData)
	for _, k := range keys {
		lkMap.Set(k.ID, v1.LabelKeyValuePair{
			LabelKeyID: k.ID,
			LabelKey:   k.Name,
			LabelValues: make([]struct {
				KeyID      string `json:"key_id"`
				LabelValue string `json:"label_value"`
				ValueID    string `json:"value_id"`
			}, 0),
			Describe: k.Describe,
		})
	}

	// 遍历查找到的label value
	for _, v := range values {
		// 先拿到对应的keyValuePair
		kvPairInterface, _ := lkMap.Get(v.KeyID)
		kvPair := kvPairInterface.(v1.LabelKeyValuePair)

		// 更新kvPair中的LabelValues信息
		kvPair.LabelValues = append(kvPair.LabelValues, struct {
			KeyID      string `json:"key_id"`
			LabelValue string `json:"label_value"`
			ValueID    string `json:"value_id"`
		}{KeyID: v.KeyID, LabelValue: v.Value, ValueID: v.ID})

		// 回写回这个map
		lkMap.Set(v.KeyID, kvPair)
	}

	// 构造response
	for _, v := range lkMap {
		labelList = append(labelList, v.(v1.LabelKeyValuePair))
	}

	total, err := l.store.Labels().GetLabelKeysCount(ctx, keyList)
	if err != nil {
		return nil, err
	}

	// 排一下序，不然前端返回的数据顺序老是来回变
	sort.Slice(labelList, func(i, j int) bool {
		return labelList[i].LabelKeyID < labelList[j].LabelKeyID
	})

	resp := v1.GetLabelsResponse{
		Total:       total,
		Pages:       (total / pageSize) + 1,
		PageSize:    pageSize,
		CurrentPage: page,
		Data:        labelList,
	}

	return &resp, nil
}

func (l *labelService) CreateLabel(ctx context.Context, req *v1.CreateLabelRequest) error {
	zap.L().Debug("CreateLabel Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateLabel", "service")
	defer span.End()

	// 创建之前无非就是要查询一下之前是否有同样的key+value的标签被创建过
	exist, ls, err := l.store.Labels().LabelExists(ctx, req)
	if err != nil {
		return err
	}

	// 判断标签是否已经存在，这里是存在即返回错误，即使说有些标签不存在，因为有不符合条件，也会一并返回，不予创建。
	if exist {
		errMsg := strings.Builder{}
		errMsg.WriteString("标签已经存在：")
		for idx, v := range ls {
			if idx != 0 {
				errMsg.WriteString(", ")
			}
			errMsg.WriteString(v)
		}
		return errno.ErrLabelExists.Add(errMsg.String())
	}

	// 创建标签
	var keyID string

	// 构造一个事物，标签的key和value要同时插入
	t, err := l.store.Common().StartTransaction()
	if err != nil {
		return err
	}

	// 首先检测一下对应的key是不是存在, 如果key不存在，则插入key
	k, err := l.store.Labels().GetSpecifiedLabelKeyByName(ctx, req.LabelKey)
	// 如果说err不为nil，有可能是真的有异常，还有一种可能是是查不到对应的文档
	if err != nil {
		// 说明没有对应的key，要创建一下
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Debug("对应Label的key不存在，先创建对应的key", zap.String("key", req.LabelKey))
			t.Wrapper(func(sessionCtx context.Context) error {
				newKeyID, err := l.store.Labels().CreateLabelKey(ctx, req.LabelKey, req.Describe)
				if err != nil {
					return err
				}

				if newKeyID == "" {
					return errno.InternalServerError.Add("内部异常，创建标签key失败")
				}

				keyID = newKeyID
				return nil
			})
		} else {
			return err
		}
	} else {
		keyID = k.ID
	}

	t.Wrapper(func(sessionCtx context.Context) error {
		for _, v := range req.LabelValues {
			// 查一下对应的label标签是不是已经有了，如果已经有了，那就无需添加了，直接跳过即可。
			_, err := l.store.Labels().GetLabelValue(sessionCtx, keyID, v)
			if err == nil {
				zap.L().Debug("对应的label标签已经存在，无需添加", zap.String("key", req.LabelKey), zap.String("value", v))
				continue
			}

			// 如果不存在则创建, 此时err一定不为nil
			if errors.Is(err, mongo.ErrNoDocuments) {
				if err := l.store.Labels().CreateLabelValue(ctx, keyID, v); err != nil {
					return err
				}
				zap.L().Debug("对应的label标签不存在，添加", zap.String("key", req.LabelKey), zap.String("value", v))
				continue
			}

			return err
		}
		return nil
	})

	if err := t.Do(ctx); err != nil {
		zap.L().Error(err.Error())
		return err
	}

	return nil
}

func (l *labelService) UpdateLabelKey(ctx context.Context, keyID string, req *v1.UpdateLabelKeyRequest) error {
	zap.L().Debug("UpdateLabelKey Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateLabelKey", "service")
	defer span.End()

	if err := l.store.Labels().UpdateLabelKey(ctx, keyID, req); err != nil {
		return err
	}
	return nil
}

func (l *labelService) UpdateLabelValue(ctx context.Context, valueID string, req *v1.UpdateLabelValueRequest) error {
	zap.L().Debug("UpdateLabelValue Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateLabelValue", "service")
	defer span.End()

	// 验证一下value的值是否合法
	if !v1.CheckLabelValue(req.Name) {
		return errno.ErrParameterInvalid.Add("标签值不合法")
	}

	// 标签值没啥问题的话，那么就可以直接更新了
	if err := l.store.Labels().UpdateLabelValue(ctx, valueID, req); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Add("标签不存在")
		}
		return err
	}
	return nil
}

func (l *labelService) BindingLabels(ctx context.Context, operator string, req *v1.LabelBindingRequest) error {
	zap.L().Debug("BindingLabels Service Called")
	span, ctx := apm.StartSpan(ctx, "BindingLabels", "service")
	defer span.End()

	// 需要确认一下每一个标签都是有效的
	lvs, err := l.store.Labels().GetLabelValueByID(ctx, req.LabelList)
	if err != nil {
		return err
	}

	// 如果说都查不到标签，那就直接返回错误
	if len(lvs) == 0 {
		return errno.ErrParameterInvalid.Add("标签不存在")
	}

	// 只要长度不相等，说明有的标签不合法，直接返回报错
	if len(lvs) != len(req.LabelList) {
		return errno.ErrParameterInvalid.Add("")
	}

	// 先完成标签内部的自检，比如避免提交的多个标签同属于一个key
	keyMap := make(map[string]struct{})
	for _, v := range lvs {
		if _, ok := keyMap[v.KeyID]; ok {
			return errno.ErrParameterInvalid.Add("标签不合法, 同一个key下不能有重复的标签值")
		}
		keyMap[v.KeyID] = struct{}{}
	}

	updatedDataList := make([]*v1.ModelData, 0)
	for _, dataID := range req.DataList {
		data, err := l.store.ModelData().GetModelDataByID(ctx, dataID)
		if err != nil {
			return err
		}

		// 要添加的label标签
		toAddedLabels := array.StringArray(req.LabelList)
		// 当前数据的label标签
		dataLabels := make(array.StringArray, 0)
		// 如果说字段为空或者长度为0，那么就直接把要添加的label id赋值给data的label id列表即可
		if len(data.LabelIDList) == 0 {
			data.LabelIDList = toAddedLabels
		} else {
			ls, err := l.store.Labels().GetLabelValueByID(ctx, data.LabelIDList)
			if err != nil {
				return err
			}

			for _, n := range lvs {
				for _, o := range ls {
					if n.KeyID == o.KeyID {
						return errno.ErrLabelExists.Add("标签不合法, 同一个key下不能有重复的标签值")
					}
				}
			}

			dataLabels = data.LabelIDList
			add := dataLabels.ArrayDiff(toAddedLabels)
			// 如果说add的长度为0，说明没有要添加的标签，直接跳过即可
			if len(add) == 0 {
				continue
			}
			// 更新data
			for _, v := range add {
				data.LabelIDList = append(data.LabelIDList, v)
			}
		}
		// 上限是50个
		if len(data.LabelIDList) > LabelBindingLimit {
			return errno.ErrLabelBindingExceedLimit.Add("标签数量超过" + strconv.Itoa(LabelBindingLimit) + "个")
		}
		updatedDataList = append(updatedDataList, data)
	}

	// 如果说要更新的数据列表为空的话，那就没必要往下走了，直接返回吧
	if len(updatedDataList) == 0 {
		zap.L().Info("当前没有数据的Label标签需要更新")
		return nil
	}

	// 最终进行数据的批量更新
	if err := l.store.ModelData().UpdateDataLabels(ctx, operator, updatedDataList); err != nil {
		return err
	}

	return nil
}

// UpdateBindingLabels 更新绑定标签
func (l *labelService) UpdateBindingLabels(ctx context.Context, operator string, req *v1.LabelUnBindingRequest) error {
	zap.L().Debug("UpdateBindingLabels Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateBindingLabels", "service")
	defer span.End()

	lvs, err := l.store.Labels().GetLabelValueByID(ctx, req.LabelIDs)
	if err != nil {
		return err
	}

	if len(lvs) != len(req.LabelIDs) {
		return errno.ErrDataNotExists.Add("标签不存在")
	}

	data, err := l.store.ModelData().GetModelDataByID(ctx, req.DataID)
	if err != nil {
		return err
	}

	data.LabelIDList = req.LabelIDs

	if err := l.store.ModelData().UpdateDataLabels(ctx, operator, []*v1.ModelData{data}); err != nil {
		return err
	}

	return nil
}

func (l *labelService) UnBindingLabels(ctx context.Context, operator string, req *v1.LabelUnBindingRequest) error {
	zap.L().Debug("UnBindingLabels Service Called")
	span, ctx := apm.StartSpan(ctx, "UnBindingLabels", "service")
	defer span.End()

	lvs, err := l.store.Labels().GetLabelValueByID(ctx, req.LabelIDs)
	if err != nil {
		return err
	}

	if len(lvs) != len(req.LabelIDs) {
		return errno.ErrDataNotExists.Add("标签不存在")
	}

	data, err := l.store.ModelData().GetModelDataByID(ctx, req.DataID)
	if err != nil {
		return err
	}

	toRemoveLabels := array.StringArray(req.LabelIDs)
	dataLabels := array.StringArray(data.LabelIDList)

	// 有可能要删除的id根本不在数据的label列表里，所以这里需要做一下差集的操作
	newLabels := toRemoveLabels.ArrayDiff(dataLabels)
	data.LabelIDList = newLabels

	if err := l.store.ModelData().UpdateDataLabels(ctx, operator, []*v1.ModelData{data}); err != nil {
		return err
	}

	return nil
}

// SearchLabeledData 根据label标签获取对应的数据
// 入参接收model code和对应的label列表，返回包含label标签的数据，注意，这里数据要做一次加工，因为data返回labels都是id，聚合查询示例如下
// 整个过程只需要一次数据库操作，减少了网络开销, MongoDB可以优化聚合操作，通常比多次单独查询更高效。
// db.model_data.aggregate([
//
//	{ $match: { labels: {$in: ["66a9d97e6098ae3db50f7fed"]} } },
//	{ $unwind: "$labels" },
//	{ $lookup: {from: "label_value", localField: "labels", foreignField: "_id", as: "label_value_info"} },
//	{ $unwind: "$label_value_info" },
//	{ $lookup: {from: "label_key", localField: "label_value_info.key_id", foreignField: "_id", as: "label_key_info"}},
//	{ $unwind: "$label_key_info"},
//	{ $project: {
//	    "_id":1, "model_code":1, "data":1,"identify_name":1,
//	    "identify_value":1,"input_type":1, "create_at":1,
//	    "update_at":1,"parent_desc":1,"associate_instances":1,
//	    "label":{key:"$label_key_info.name",value:"$label_value_info.value"}}},
//	{ $group: {
//	    "_id": "$_id", "model_code": { $first:"$model_code" }, data: { $first: "$data" },
//	    identify_name: { $first: "$identify_name" }, identify_value: { $first: "$identify_value" },
//	    input_type: { $first: "$input_type" }, create_at: { $first: "$create_at" },
//	    update_at: { $first: "$update_at" }, parent_id: { $first: "$parent_id" },
//	    parent_desc: { $first: "$parent_desc" }, associate_instances: { $first: "$associate_instances" },
//	    labels: { $push: "$label" }
//	  }
//	}
//
// ])
//
// 1 第一个$match是先把包含对应label id的数据给筛选出来
// 2 通过unwind将labels字段打散成多个document，因为一个数据对应多个标签，打散后，对应的数据绑定的标签有几个，对应的数据就有几条
// 3 打散后，对应的labels就从array变成了普通的string，然后我们连表查询label_value，然后连表后查询的新字段命名成label_value_info
//
//	注意，因为是连表查询，查询label_value表id为labels对应id的数据，所以拿到的label_value_info肯定是一个array。
//
// 4 此时我们再把label_value_info这个array给打开。打开后，label_value_info就变成了一个object
// 5 再做连表，查label_key这个collection，字段是key_id对应的是label_key的_id，然后重命名成label_key_info
// 6 同上，因为拿到的也是一个列表也要做unwind
// 7 使用project进行投影，把我们想要的字段都拿到
// 8 最后一步是做聚合，把同样的一条数据做聚合即可。
// ])
// 优势：减少与数据库的交互次，MongoDB内部对聚合管道进行了优化，采用流式处理方式。这意味着数据在管道的各个阶段之间流动，而不是一次性加载所有数据到内存中
func (l *labelService) SearchLabeledData(ctx context.Context, req *v1.SearchLabeledDataRequest, page, pageSize int64) (*v1.SearchLabeledDataResponse, error) {
	zap.L().Debug("SearchLabeledData Service Called")
	span, ctx := apm.StartSpan(ctx, "SearchLabeledData", "service")
	defer span.End()

	// 定义一个pipeline，该pipeline目的是为了查看符合标签的数据覆盖了几个模型
	// 当然如果说用户明确指定了查询的模型，则过滤条件按照用户指定的模型来算，但是有一种可能虽然用户指定了模型，但是对应模型下没有数据。
	// 先把对应的model的汇总信息查出来
	// db.model_data.aggregate([
	//  {
	//    $match: { labels: { $in: ["66a9d97e6098ae3db50f7fed"] } }
	//  },
	//	{
	//	  $group: {"_id": "$model_code", count: { $sum: 1 }},
	//  },
	//	{
	//	  $sort: {count: -1}
	//	}
	// ])
	pipe1 := make([]bson.M, 0)
	m1 := make(bson.M)
	if len(req.ModelCode) > 0 {
		m1["model_code"] = bson.M{"$in": req.ModelCode}
	}
	m1["labels"] = bson.M{"$in": req.LabelID}
	pipe1 = append(pipe1, bson.M{"$match": m1})
	// group中的_id其实和model_data中的_id没太大关系，这里指的是聚合操作中用于指定分组依据的字段，它可以是任何字段或表达式，不一定要是文档的 _id
	pipe1 = append(pipe1, bson.M{"$group": bson.M{"_id": "$model_code", "count": bson.M{"$sum": 1}}})
	// 按照count进行排序
	pipe1 = append(pipe1, bson.M{"$sort": bson.M{"count": -1}})
	info, err := l.store.ModelData().GetAggregatedData(ctx, pipe1)
	if err != nil {
		return nil, err
	}

	// 如果没有找到符合数据的model的话，那就说明没有对应的数据符合要求，直接返回就可以了
	if len(info) == 0 {
		return nil, err
	}

	// 获取model mapping
	mapping, err := l.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	matchedModelInfoList := make([]v1.SearchLabeledModelInfo, 0)
	for _, m := range info {
		code := utils.ToString(m["_id"])
		mInfo := v1.SearchLabeledModelInfo{
			ModelCode: code,
			ModelName: mapping[code].Name,
			DataCount: utils.ToInt(m["count"]),
		}
		matchedModelInfoList = append(matchedModelInfoList, mInfo)
	}

	// 获取到匹配数据有效的model_code后挑选第一个进行展示，当然前提是如果用户没有选择的话。如果用户指定了，以用户指定的的为准
	chosenModel := matchedModelInfoList[0].ModelCode

	// 定义第二个pipeline用于查询数据的具体信息并返回数据信息
	pipe2 := make([]bson.M, 0)
	m2 := make(bson.M)
	m2["model_code"] = chosenModel
	m2["labels"] = bson.M{"$in": req.LabelID}

	pipe2 = append(pipe2, bson.M{"$match": m2})
	pipe2 = append(pipe2, bson.M{"$unwind": "$labels"})
	pipe2 = append(pipe2, bson.M{"$lookup": bson.M{
		"from":         v1.LabelValueColName,
		"localField":   "labels",
		"foreignField": "_id",
		"as":           "label_value_info",
	}})
	pipe2 = append(pipe2, bson.M{"$unwind": "$label_value_info"})
	pipe2 = append(pipe2, bson.M{"$lookup": bson.M{
		"from":         v1.LabelKeyColName,
		"localField":   "label_value_info.key_id",
		"foreignField": "_id",
		"as":           "label_key_info",
	}})
	pipe2 = append(pipe2, bson.M{"$unwind": "$label_key_info"})
	pipe2 = append(pipe2, bson.M{"$project": bson.M{
		"_id": 1, "model_code": 1, "data": 1, "identify_name": 1,
		"identify_value": 1, "input_type": 1, "create_at": 1,
		"update_at": 1, "parent_desc": 1, "associate_instances": 1,
		"label": bson.M{
			"key_name":   "$label_key_info.name",
			"key_id":     "$label_key_info._id",
			"value_name": "$label_value_info.value",
			"value_id":   "$label_value_info._id",
		},
	}})
	pipe2 = append(pipe2, bson.M{"$group": bson.M{
		"_id":                 "$_id",
		"model_code":          bson.M{"$first": "$model_code"},
		"data":                bson.M{"$first": "$data"},
		"identify_name":       bson.M{"$first": "$identify_name"},
		"identify_value":      bson.M{"$first": "$identify_value"},
		"input_type":          bson.M{"$first": "$input_type"},
		"create_at":           bson.M{"$first": "$create_at"},
		"update_at":           bson.M{"$first": "$update_at"},
		"parent_id":           bson.M{"$first": "$parent_id"},
		"parent_desc":         bson.M{"$first": "$parent_desc"},
		"associate_instances": bson.M{"$first": "$associate_instances"},
		"labels":              bson.M{"$push": "$label"},
	}})
	pipe2 = append(pipe2, bson.M{"$group": bson.M{"_id": nil, "data_list": bson.M{"$push": "$$ROOT"}, "total": bson.M{"$sum": 1}}})
	pipe2 = append(pipe2, bson.M{"$project": bson.M{"_id": 0, "total": 1,
		"data": bson.M{"$slice": []interface{}{"$data_list", (page - 1) * pageSize, pageSize}}}},
	)

	// 查询符合要求的数据
	agg, err := l.store.ModelData().GetLabelAggregateData(ctx, pipe2)
	if err != nil {
		zap.L().Error("获取聚合数据失败, 错误信息为: " + err.Error())
		return nil, err
	}

	if len(agg) == 0 {
		zap.L().Info("没有符合条件的数据")
		return nil, nil
	}

	aggData := agg[0]
	// 构造Response信息
	resp := v1.SearchLabeledDataResponse{
		Data:             aggData.DataList,
		Total:            aggData.Total,
		CurrentPage:      page,
		PageSize:         pageSize,
		Pages:            (aggData.Total / pageSize) + 1,
		SearchModelCode:  chosenModel,
		MatchedModelCode: matchedModelInfoList,
	}

	return &resp, nil
}
