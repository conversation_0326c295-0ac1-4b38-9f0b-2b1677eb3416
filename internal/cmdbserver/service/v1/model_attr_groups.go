package v1

import (
	"context"
	"errors"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"
)

type ModelAttrGroupService interface {
	// CreateModelAttrGroup 创建模型属性分组
	CreateModelAttrGroup(ctx context.Context, operator string, mag *v1.ModelAttrGroup) (*v1.ModelAttrGroup, error)
	// GetModelAttrGroupList 获取模型属性分组列表
	GetModelAttrGroupList(ctx context.Context, modelCode string, filter bson.M) ([]v1.ModelAttrGroup, error)
	// GetModelAttrGroupByCode 根据code获取模型属性分组
	GetModelAttrGroupByCode(ctx context.Context, code string) (*v1.ModelAttrGroup, error)
	// UpdateModelAttrGroup 更新模型属性分组
	UpdateModelAttrGroup(ctx context.Context, operator string, mag map[string]interface{}) error
	// DeleteModelAttrGroup 删除模型属性分组
	DeleteModelAttrGroup(ctx context.Context, operator string, mag *v1.InstanceFilter) error
}

type modelAttrGroupService struct {
	store store.Factory
}

var _ ModelAttrGroupService = (*modelAttrGroupService)(nil)

func newModelAttrGroupService(srv *service) *modelAttrGroupService {
	return &modelAttrGroupService{store: srv.store}
}

// CreateModelAttrGroup 创建模型属性组
func (mags *modelAttrGroupService) CreateModelAttrGroup(ctx context.Context, operator string, mag *v1.ModelAttrGroup) (*v1.ModelAttrGroup, error) {
	zap.L().Debug("CreateModelAttrGroup ModelTemplateService Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelGroupService", "service")
	defer span.End()
	// 1. 首先要判断这个属性分组对应的model是否存在，如果模型都不存在，那么就不应该创建模型属性的分组
	_, err := mags.store.Model().GetModelByCode(ctx, mag.ModelCode)
	if err != nil {
		return nil, errno.ErrDataNotExists.Add("模型" + mag.ModelCode + "不存在")
	}

	// 2. 查看一下名称有没有冲突的
	magList, err := mags.store.ModelAttrGroup().GetModelAttrGroupByFilter(ctx, bson.M{"name": mag.Name, "model_code": mag.ModelCode})
	if err == nil && len(magList) != 0 {
		return nil, errno.ErrModelAttrGroupNameExist.Addf("模型[%s]下已存在名称为[%s]的属性分组, 同模型下，分组名称不允许重复", mag.ModelCode, mag.Name)
	}

	// 3. 确认模型存在后即可进行属性分组的添加工作
	instance, err := mags.store.ModelAttrGroup().CreateModelAttrGroup(ctx, operator, mag)
	if err != nil {
		return nil, err
	}

	// 4. 记录审计日志
	au := &AuditEvent{store: mags.store}
	if err := au.Generate(ctx, audit.AuditLogEvent, audit.ModelAttributesGroupRes, audit.ActionCreate, operator, instance.ID, nil, mag); err != nil {
		zap.L().Error("生成模型属性分组审计日志失败", zap.Error(err))
		return nil, err
	}
	if err := au.Save(ctx); err != nil {
		zap.L().Error("保存模型属性分组审计日志失败", zap.Error(err))
		return nil, err
	}
	return instance, nil
}

// GetModelAttrGroupList 获取模型属性组列表
func (mags *modelAttrGroupService) GetModelAttrGroupList(ctx context.Context, modelCode string, filter bson.M) ([]v1.ModelAttrGroup, error) {
	span, ctx := apm.StartSpan(ctx, "GetModelAttrGroupList", "service")
	defer span.End()

	// 首先需要判断用户传入进来的模型的code是否存在
	_, err := mags.store.Model().GetModelByCode(ctx, modelCode)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Add("模型" + modelCode + "不存在")
		}
		return nil, err
	}
	modelAttrGroupList, err := mags.store.ModelAttrGroup().GetModelAttrGroupList(ctx, modelCode, filter)
	if err != nil {
		return nil, err
	}
	return modelAttrGroupList, nil
}

// GetModelAttrGroupByCode 获取模型属性组
func (mags *modelAttrGroupService) GetModelAttrGroupByCode(ctx context.Context, code string) (*v1.ModelAttrGroup, error) {
	zap.L().Debug("GetModelAttrGroupByCode ModelTemplateService Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttrGroupByCode", "service")
	defer span.End()

	modelAttrGroup, err := mags.store.ModelAttrGroup().GetModelAttrGroupByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	return modelAttrGroup, nil
}

// UpdateModelAttrGroup 更新模型属性组
func (mags *modelAttrGroupService) UpdateModelAttrGroup(ctx context.Context, operator string, mag map[string]any) error {
	zap.L().Debug("UpdateModelAttrGroup ModelAttrGroupService Called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelAttrGroupService", "service")
	defer span.End()

	// 拼接模型属性分组
	magCode := utils.ToString(mag["code"])
	if magCode == "" {
		return errno.ErrModelAttrGroupNotExists.Add("要更新的模型属性分组不存在")
	}
	magInstance, err := mags.store.ModelAttrGroup().GetModelAttrGroupByCode(ctx, magCode)
	if err != nil {
		return errno.ErrModelAttrGroupNotExists.Add("要更新的模型属性分组" + magCode + "不存在")
	}
	// 内置的分组不允许修改
	if magInstance.IsSystemCreated() {
		return errno.ErrBuiltinObject.Add("系统内置的分组不允许修改")
	}
	// 非内置分组，只允许修改Name，且名称不能为空
	groupName := utils.ToString(mag["name"])
	if groupName == "" {
		return errno.InternalServerError.Add("更新的模型属性分组名称不能为空")
	}
	// 当Name相同的时候直接返回即可，说明不需要更新
	if groupName == magInstance.Name {
		return nil
	}
	// 排除一下是否有名称重复的，一个模型下，不允许有重复的名称的分组
	magList, err := mags.store.ModelAttrGroup().GetModelAttrGroupByFilter(ctx, bson.M{"name": groupName, "model_code": magInstance.ModelCode})
	if err != nil {
		zap.L().Error("获取模型属性分组列表失败", zap.Error(err))
		return err
	}
	if len(magList) != 0 {
		return errno.ErrModelAttrGroupNameExist.Addf("模型 [%s] 下已存在名称为 [%s] 的属性分组, 同模型下，分组名称不允许重复", magInstance.ModelCode, groupName)
	}
	// 备份原数据用于记录审计日志
	magNameCopy := map[string]any{"name": magInstance.Name}

	// 如果没有重复的，那么就可以进行更新了，但是也仅限于更新名称	
	magInstance.Name = groupName
	magInstance.SetUpdateAt()

	// 更新模型属性分组
	if err = mags.store.ModelAttrGroup().UpdateModelAttrGroup(ctx, operator, magInstance); err != nil {
		return err
	}

	// 记录审计日志
	au := &AuditEvent{store: mags.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelAttributesGroupRes,
		audit.ActionUpdate,
		operator,
		magInstance.ID,
		magNameCopy,
		magInstance,
	); err != nil {
		zap.L().Error("生成模型属性分组审计日志失败", zap.Error(err))
		return err
	}
	if err := au.Save(ctx); err != nil {
		zap.L().Error("保存模型属性分组审计日志失败", zap.Error(err))
		return err
	}

	return nil
}

// DeleteModelAttrGroup 删除模型属性组
func (mags *modelAttrGroupService) DeleteModelAttrGroup(ctx context.Context, operator string, mag *v1.InstanceFilter) error {
	zap.L().Debug("DeleteModelAttrGroup ModelTemplateService Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelAttrGroupService", "service")
	defer span.End()

	// 1、首先需要判断一下这个属性分组是不是内置属性分组，如果是内置的则不允许删除
	m, err := mags.store.ModelAttrGroup().GetModelAttrGroupByCode(ctx, mag.Code)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Add("属性分组 " + mag.Code + " 不存在")
		}
		return err
	}
	// 判断这个是否是由系统创建的内置属性分组
	if m.IsSystemCreated() || m.IsBuiltIn() {
		return errno.InternalServerError.Add("系统创建的内置属性分组不允许删除")
	}
	// 2、判断属性分组下面只要是有属性，就不允许删除
	attrList, err := mags.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{"attr_group": mag.Code}, bson.M{"_id": 1})
	if err != nil {
		return err
	}
	if len(attrList) > 0 {
		return errno.InternalServerError.Add("属性分组下面有属性，不允许删除")
	}
	if err = mags.store.ModelAttrGroup().DeleteModelAttrGroup(ctx, operator, mag); err != nil {
		return err
	}

	// 记录审计日志
	au := &AuditEvent{store: mags.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelAttributesGroupRes,
		audit.ActionDelete,
		operator,
		m.ID,
		m,
		nil,
	); err != nil {
		zap.L().Error("生成模型属性分组审计日志失败", zap.Error(err))
		return err
	}
	if err := au.Save(ctx); err != nil {
		zap.L().Error("保存模型属性分组审计日志失败", zap.Error(err))
		return err
	}

	return nil
}
