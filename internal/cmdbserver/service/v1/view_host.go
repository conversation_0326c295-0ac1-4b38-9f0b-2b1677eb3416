package v1

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// getUnAttributedDevice 获取未归属的设备
func (v *viewService) getUnAttributedDevice(ctx context.Context, filter bson.M, page, pageSize int64) (*v1.HostViewListResponse, error) {
	// 构造一个response
	resp := v1.NewHostViewListResponse()

	// 配置可以筛选的模型，目前这个是写死的
	resp.Models = append(resp.Models, v.getHostViewModel(ctx, "default")...)

	// 未归属的设备，首先没有父亲，其次model_code是服务器或者虚拟机【主机视图限定】
	filter["parent_id"] = ""

	// 查询一下这样的数据
	modelDataList, total, err := v.store.ModelData().GetModelDataList(ctx, filter, page, pageSize, "")
	if err != nil {
		zap.L().Error("GetModelDataList failed", zap.Error(err))
		return nil, err
	}

	// 如果没有符合要求的未归属数据，直接返回resp即可，使用resp中被初始化的默认值
	if len(modelDataList) == 0 {
		return resp, nil
	}

	// 更新一下response中的一些值
	resp.Total = total
	resp.Pages = (total / pageSize) + 1
	resp.PageSize = pageSize
	resp.CurrentPage = page

	// 把查到的数据填充到response中
	for _, d := range modelDataList {
		host := v1.NewOneHost()
		host.ModelData = d
		host.HasChildren = false
		host.Children = make([]*v1.ModelData, 0)

		resp.Data = append(resp.Data, host)
	}

	return resp, nil
}

// getCloudServer 获取云主机的数据
func (v *viewService) getCloudServer(ctx context.Context, filter bson.M, page, pageSize int64) (*v1.HostViewListResponse, error) {
	// 构造一个response
	resp := v1.NewHostViewListResponse()

	// 代表的是云主机
	resp.Models = append(resp.Models, v.getHostViewModel(ctx, "cloud_server")...)

	// 查询所有的云主机
	filter["model_code"] = "cloud"
	modelDataList, total, err := v.store.ModelData().GetModelDataList(ctx, filter, page, pageSize, "")
	if err != nil {
		zap.L().Error("GetModelDataList failed", zap.Error(err))
		return nil, err
	}

	if len(modelDataList) == 0 {
		return resp, nil
	}

	resp.Total = total
	resp.Pages = (total / pageSize) + 1
	resp.PageSize = pageSize
	resp.CurrentPage = page

	for _, d := range modelDataList {
		host := v1.NewOneHost()
		host.ModelData = d
		host.HasChildren = false
		host.Children = make([]*v1.ModelData, 0)

		resp.Data = append(resp.Data, host)
	}

	return resp, nil
}
