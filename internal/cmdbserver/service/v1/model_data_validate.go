package v1

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/clock"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/mitchellh/mapstructure"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

const UniversalPrefix = "universal"

func GetUniversalFieldName(fieldCode, modelCode string) string {
	// 以模型code为分隔符，分隔成前缀和后缀，最多分一次，把前缀的模型code分出来就可以。
	fieldSuffix := strings.SplitN(fieldCode, modelCode, 2)[1]
	universalFieldName := fmt.Sprintf("%s%s", UniversalPrefix, fieldSuffix)
	return universalFieldName
}

// 判断用户提交过来的数据是否合法
func (mds *modelDataService) dataValidCheck(ctx context.Context, data *v1.ModelData, action string) ([]v1.CommonModelAttribute, error) {
	// 加锁与释放锁的操作
	mds.store.ModelData().GetDataImportLock(ctx, data.ModelCode)
	defer mds.store.ModelData().ReleaseDataImportLock(ctx, data.ModelCode)

	// 如果说对应的identify_value过长，那么就给他截断，目前的格式为{model_code}_{将名称转换为拼音}_{时间戳}
	identifyValue := strings.Split(data.IdentifyValue, "_")
	// 将中间的转换为拼音的名称部分取出来，提前判断一下，如果用户传递的没有以上述格式拼接，说明不合法
	if len(identifyValue) < 2 {
		return nil, errno.ErrDataInvalid.Add("不合法的identify_value")
	}
	newName := strings.Join(identifyValue[1:len(identifyValue)-1], "_")
	// 做数据的截断
	if len(newName) > 60 {
		newIdentifyValue := fmt.Sprintf("%s_%s_%s%d", data.ModelCode, newName[:25], identifyValue[2], rand.Int())
		data.Data[data.ModelCode+"_code"] = newIdentifyValue
		data.IdentifyValue = newIdentifyValue
	}

	// 判断插入的数据是否合法
	// 1. 判断用户有没有传data数据，没传递的话，那下面什么都不用看了，因为至少得有名称和唯一标识两个字段，不可能一个字段也没有
	// data.Data是一个map，如果map的长度为0，那么就说明用户没有传递任何数据
	if len(data.Data) == 0 {
		return nil, errno.ErrParameterRequired.Add("请传递data数据")
	}

	// 更新操作的时候id字段必须传递
	if action == "update" && data.ID == "" {
		return nil, errno.ErrParameterRequired.Add("更新操作必须传递id字段")
	}
	dataCode, ok := data.Data[data.ModelCode+"_code"]
	if !ok {
		return nil, errno.InternalServerError.Addf("data中找不到code为%s的字段，请检查数据是否合规", data.ModelCode+"_code")
	}
	if data.IdentifyValue != dataCode {
		return nil, errno.ErrParameterInvalid.Addf("identify_value必须与data中的%s字段保持一致", dataCode)
	}

	// 2. 首先判断用户要插入/更新的数据的这个模型本身是否存在
	modelCode := data.ModelCode
	modelMapping, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		return nil, err
	}
	if _, exist := modelMapping[modelCode]; !exist {
		return nil, errno.ErrModelCodeNotExists.Addf("模型code %s 不存在, 请检查请求数据是否合法", modelCode)
	}

	// 3. 检查用户插入/更新的字段是否是合法的，有可能说用户插入/更新的字段在模型中并不存在，或者必填字段没填写
	// 3.1 获取模型的所有字段
	// 设置select过滤参数，保留code，is_null, type_name, is_unique, is_editable，丢掉_id字段
	selectParam := bson.M{
		"code":        1,
		"_id":         0,
		"is_null":     1,
		"type_name":   1,
		"is_unique":   1,
		"is_editable": 1,
		"universal":   1,
		"attr_group":  1,
		"attrs":       1,
		"name":        1,
		"model_code":  1,
	}
	// 定义一个过滤条件，过滤掉系统字段
	// attrFilter := bson.M{
	//     "model_code": modelCode,
	//     "attr_group": bson.M{
	//         "$ne": fmt.Sprintf("%s_system", modelCode),
	//     },
	// }
	attrFilter := bson.M{"model_code": modelCode}
	modelAttrs, err := mds.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, attrFilter, selectParam)
	if err != nil {
		return nil, err
	}
	// 每个模型，至少应该有两个字段，就是唯一标识和名称，如果这两个字段都没有，有可能是数据库存在异常，需要管理员介入。
	if len(modelAttrs) == 0 {
		return nil, errno.ErrDataNotExists.Addf("模型code %s 下没有任何字段，请咨询管理员", modelCode)
	}
	// 3.2 判断用户提交的字段是否是合法的
	// TODO: 这里多次循环操作，是比较臃肿的，待优化
	var (
		attrCodeSlice       []string           // 初始化模型属性code的切片
		userAttrCodeSlice   = data.Data.Keys() // 初始化用户提交的属性code的切片
		requiredFields      []string           // 初始化必填字段的切片
		uniqueFields        []string           // 初始化唯一字段的切片
		unEditableFields    []string           // 初始化可编辑字段的切片
		typeStringFields    []string           // 类型为string的字段
		systemAttrFields    []string           // 系统字段
		nonSystemAttrFields []string           // 非系统字段
		SubEnumFields       []string           // 是否是子枚举字段
		GeneralEnumFields   []string           // 是否是普通枚举字段
		UniversalFields     []string           // 是否是通用字段
		attrCodeMap         = make(map[string]v1.CommonModelAttribute)
		systemAttrMap       = make(map[string]v1.CommonModelAttribute)
		nonSystemAttrMap    = make(map[string]v1.CommonModelAttribute)
	)

	// 遍历模型的所有字段，并存放到初始化的切片里
	for _, modelAttr := range modelAttrs {
		attrCodeSlice = append(attrCodeSlice, modelAttr.Code)
		attrCodeMap[modelAttr.Code] = modelAttr
		// 判断类型为string的字段，如果字段是string类型的，那么就存放到初始化的切片里
		if modelAttr.TypeName == "string" {
			typeStringFields = append(typeStringFields, modelAttr.Code)
		}
		// 判断必填字段，如果字段是必填的，那么就存放到初始化的切片里，判断的内容不涵盖关系字段
		if !modelAttr.IsNull && modelAttr.TypeName != "relationship" {
			requiredFields = append(requiredFields, modelAttr.Code)
		}
		// 判断唯一字段，如果字段是唯一的，那么就存放到初始化的切片里，判断的内容不涵盖关系字段
		if modelAttr.IsUnique && modelAttr.TypeName != "relationship" {
			uniqueFields = append(uniqueFields, modelAttr.Code)
		}
		// 判断是否是通用字段，如果字段是通用的，那么就存放到初始化的切片里，判断的内容不
		if modelAttr.Universal && modelAttr.TypeName != "relationship" {
			UniversalFields = append(UniversalFields, modelAttr.Code)
		}
		// 将继承的子枚举字段放在一起
		if modelAttr.TypeName == "select" {
			// 首先看看是否能在枚举字段中取出来inherit这个字段
			if inherit, ok := modelAttr.Attrs["inherit"]; ok {
				// 如果可以取出来的话，那么就判断是否是继承的子枚举字段
				if isInherit, ok := inherit.(bool); ok && isInherit {
					// 如果是继承的子枚举字段，那么就存放到初始化的切片里
					SubEnumFields = append(SubEnumFields, modelAttr.Code)
				} else {
					GeneralEnumFields = append(GeneralEnumFields, modelAttr.Code)
				}
			}
		}
		// 判断可编辑字段，如果字段是不可编辑的，那么就存放到初始化的切片里，判断的内容不涵盖关系字段
		if !modelAttr.IsEditAble && modelAttr.TypeName != "relationship" {
			unEditableFields = append(unEditableFields, modelAttr.Code)
		}
		// 系统分组比较特殊，将系统分组剥离出来，单独存放到一个map里，主要用于程序或者脚本更新
		// 把系统字段和非系统字段给拆开来，分别存放到两个map里
		if modelAttr.AttrGroup == fmt.Sprintf("%s_system", modelCode) {
			systemAttrMap[modelAttr.Code] = modelAttr
			systemAttrFields = append(systemAttrFields, modelAttr.Code)
		} else {
			nonSystemAttrMap[modelAttr.Code] = modelAttr
			nonSystemAttrFields = append(nonSystemAttrFields, modelAttr.Code)
		}
	}

	// 遍历用户提交过来的字段并做逐一校验
	for _, userPostAttrCode := range userAttrCodeSlice {
		// 判断用户提交的字段是否在模型中存在，有可能模型并不存在这个字段，用户瞎填了一个
		// 这里在前端是不允许用户提交系统字段的，前端置灰，但是数据更新的时候，需要带着字段过来，所以得允许接口上传系统字段
		// 但是不作为一些校验的依据，比如必填字段，唯一字段，不可编辑字段等等
		if ok := utils.FindStrInStringSlice(attrCodeSlice, userPostAttrCode); !ok {
			return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性code %s 不存在", userPostAttrCode)
		}
		modelAttrObj := attrCodeMap[userPostAttrCode]
		userPostAttrValue := strings.TrimSpace(utils.ToString(data.Data[userPostAttrCode]))
		// 按照约定，type为string的字段，最大长度为512
		if ok := utils.FindStrInStringSlice(typeStringFields, userPostAttrCode); ok {
			// 如果是string类型的字段，那么就判断用户提交的字段长度是否超过了最大长度
			postDataValue, exist := data.Data[userPostAttrCode]
			if exist && postDataValue == nil {
				v := utils.ToString(postDataValue)
				if len(v) > 512 {
					return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性code %s 的长度超过了最大长度512", userPostAttrCode)
				}
			}
		}

		// 看一下值的类型是否合法，主要针对int和float类型的字段做一下判断
		if userPostAttrValue != "" {
			// 格式化示例数据
			sample := strings.TrimSpace(utils.ToString(modelAttrObj.Attrs["sample"]))
			if sample != "" {
				sample = fmt.Sprintf("如：%s", sample)
			}
			switch modelAttrObj.TypeName {
			case "int":
				// 如果字段是int类型的，就用ToInt方法转换一下，转换失败的话，默认会返回零值，即0，因此这个时候，我们还需要
				// 判断一下，用户提交的字段是否是0，如果是0，那么就说明用户提交的字段是合法的，如果不是0，那么就说明用户提交的字段是不合法的
				if utils.ToInt(data.Data[userPostAttrCode]) == 0 && userPostAttrValue != "0" {
					return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望整数类型 %s", modelAttrObj.Name, sample)
				}
			case "float":
				if utils.ToFloat64(data.Data[userPostAttrCode]) == 0 && userPostAttrValue != "0" {
					return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望浮点数类型 %s", modelAttrObj.Name, sample)
				}
			case "bool":
				boolValue := strings.ToLower(userPostAttrValue)
				if boolValue != "true" && boolValue != "false" {
					return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望布尔类型 %s", modelAttrObj.Name, sample)
				}
			case "date":
				// 这里有一个很蛋疼的地方，就是你在excel里面输入了2006-01-02，它会自动给你转换成2006/01/02，这个时候就会报错，所以得兼容
				// 但是react的前端组件比较神奇的地方在于既可以支持2006-01-02这种格式，也可以支持2006/01/02这种格式，只是展示上会不友好。
				d1, err1 := time.Parse("2006-01-02", userPostAttrValue)
				d2, err2 := time.Parse("2006/01/02", userPostAttrValue)
				if err1 != nil && err2 != nil {
					return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望日期类型 %s", modelAttrObj.Name, sample)
				}

				// 能走到这里，说明，err1或者err2，必然有一个是err是nil
				// 纠正一下日期格式，统一使用中横线分隔，因为传递进来的是data的指针，所以直接改就完事了
				if err1 != nil {
					data.Data[userPostAttrCode] = d2.Format("2006-01-02")
				} else {
					data.Data[userPostAttrCode] = d1.Format("2006-01-02")
				}
			case "datetime":
				_, err := time.Parse("15:04:05", userPostAttrValue)
				if err != nil {
					return nil, errno.ErrDataInvalid.Addf("用户提交的模型属性 [%s] 的值类型不合法，期望日期时间类型 %s", modelAttrObj.Name, sample)
				}
			}
		}

		// 如果字段有设置正则表达式，那么就判断用户提交的字段是否符合正则表达式，这里不判断系统字段的
		// 因为只有手动录入，更新，批量导入的时候才会用到这个方法，但是系统字段是不允许用户提交的，包括模板上传的时候也是不允许的
		if modelAttrObj.AttrGroup != fmt.Sprintf("%s_system", modelCode) || mds.checkSystemField {
			rule, ok := modelAttrObj.Attrs["rule"].(map[string]interface{})
			if ok {
				ruleRe, ok := rule["rule_re"].(string)
				if ok {
					var userPostData string
					userPostData, ok = data.Data[userPostAttrCode].(string)
					// 有可能用户传递的并不是字符串类型，而是数字类型，这里需要做一下转换，所有的data数据都给转换为字符串类型
					if !ok {
						userPostData = utils.ToString(data.Data[userPostAttrCode])
					}

					// 首先拿一下字段的type类型
					typeName := attrCodeMap[userPostAttrCode].TypeName
					// 当rule规则以及用户传递的数据都不为空的时候，且字段类型不是bool的时候
					if ruleRe != "" && userPostData != "" && typeName != "bool" {
						var re *regexp.Regexp

						if typeName == "string" || typeName == "textarea" {
							var pattern string
							// 解决：regexp: Compile(`^[\u4e00-\u9fa5]*$`): error parsing regexp: invalid escape sequence: `\u`
							pattern, err = strconv.Unquote(`"` + ruleRe + `"`)
							// 解析失败，说明没有转义字符，会报错invalid syntax，需要兼容一下这个错误， err类型为*errors.errorString
							if err != nil {
								pattern = ruleRe
							}
							re = regexp.MustCompile(pattern)
						} else {
							re = regexp.MustCompile(ruleRe)
						}
						if ok := re.MatchString(userPostData); !ok {
							name := attrCodeMap[userPostAttrCode].Name
							errMsg := ""
							if r := v1.GetRegexName(ruleRe); r != "" {
								errMsg = fmt.Sprintf("用户提交的模型属性字段[%s] 不符合正则规则: [%s]; 用户提交的值为 '%s'", name, r, userPostData)
							} else {
								sampleData, ok := modelAttrObj.Attrs["sample"].(string)
								if ok {
									errMsg = fmt.Sprintf("用户提交的模型属性字段[%s] 不符合正则规则，示例数据为：%s; 用户提交的值为 '%s'", name, sampleData, userPostData)
									return nil, errno.ErrDataInvalid.Add(errMsg)
								}
								errMsg = fmt.Sprintf("用户提交的模型属性字段[%s] 不符合正则规则，用户提交的值为 '%s', 正则规则为 '%s'", name, userPostData, ruleRe)
							}
							return nil, errno.ErrDataInvalid.Add(errMsg)
						}
					}
				}
			} else {
				zap.L().Info("模型属性字段没有设置正则表达式", zap.String("userPostAttrCode", userPostAttrCode))
			}
		}
	}

	// 遍历必填字段，如果用户提交的数据中没有这个字段，那么就返回报错信息
	for _, requiredField := range requiredFields {
		// 判断必填字段是否在用户提交的字段里面了，如果不在，那么就报错，注意要排除系统字段，系统字段的必填逻辑不在这里判断
		// 因为系统字段是不允许用户提交的，所以不需要判断是否必填，需要在单独的脚本或者agent逻辑中去判断
		currentAttrGroup := attrCodeMap[requiredField].AttrGroup
		// 要过滤掉系统字段
		if currentAttrGroup != fmt.Sprintf("%s_system", modelCode) || mds.checkSystemField {
			if ok := utils.FindStrInStringSlice(userAttrCodeSlice, requiredField); !ok {
				return nil, errno.ErrParameterRequired.Addf("必填字段 %s 没有传递", attrCodeMap[requiredField].Name)
			}
			// 判断用户提交的字段是否为空
			if data.Data[requiredField] == "" || data.Data[requiredField] == nil {
				// 如果必填字段(必须得给个值)同时又是不可编辑的字段（不让你给值），那么有可能这个字段是后加的，后加的就不需要必填了，因为之前的数据都没有这个字段
				// 但是需要限定条件，仅限更新操作的时候，创建的时候，必填字段还是必填的
				isUnEditable := utils.FindStrInStringSlice(unEditableFields, requiredField)
				if !isUnEditable || (isUnEditable && action == "create") {
					return nil, errno.ErrParameterRequired.Addf("必填字段 %s 不能为空", attrCodeMap[requiredField].Name)
				}
			}
		}
	}

	// 判断字段的唯一性，如果是唯一字段的话，那么就需要判断是否重复（不管是更新还是创建都需要校验唯一性）
	for _, uniqueField := range uniqueFields {
		modelAttrObj := attrCodeMap[uniqueField]
		if modelAttrObj.AttrGroup != fmt.Sprintf("%s_system", modelCode) || mds.checkSystemField {
			// 目前第一个版本中，我们data数据中的所有value其实都是字符串，包括int float这些，但是不排除后续会有其他类型的数据
			// v1: data.Data虽然是一个map[string]interface{}但是一期，基本等价于 map[string]string
			// TODO: 后续版本可能会随着业务复杂 data.Data也可能是 map[string]structA这种类型的数据。如果有了，这里的逻辑就都需要更新
			// 有可能用户设置了唯一，但是却没传递，这个时候不能直接去map取值，否则有可能存在空指针异常，需要先进行判断
			dataValue := ""
			filter := bson.M{}
			filter["model_code"] = modelCode

			// 注意，这里有一个点，就是非必填字段用户可以不填，那么不填的时候，其实字段是不存在的，所以这个时候，如果取值的话，其实值是nil
			// 而不是空字符串，因此目前的逻辑，针对唯一字段，不传递的唯一字段，可以存在多份的。
			// 这里判断nil是排除有字段值，但是传递过来的json的value是null的情况
			v, exist := data.Data[uniqueField]
			if exist && v != nil {
				dataValue = utils.ToString(v)
				// 目前默认所有的data的值都是用string来存储的
				filter["data."+uniqueField] = dataValue
			} else {
				filter["data."+uniqueField] = dataValue
			}

			// 我们不对空值做唯一性的校验，要不然所有为空值的都算是重复的了，唯一约束的校验都过不去
			if dataValue == "" {
				continue
			}

			dList, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, filter, nil)
			if err != nil {
				return nil, errno.ErrDataCheck.Add(err.Error())
			}
			// 如果dList的长度大于1，那么说明数据库里已经有多条数据符合过滤条件的数据，那么说明数据库中已经有重复的数据了，脏数据，直接返回错误
			zap.L().Debug("判断字段的唯一性",
				zap.String("uniqueField", uniqueField),
				zap.String("dataValue", dataValue),
				zap.Int("existDataLength", len(dList)),
				zap.String("action", action),
				zap.String("modelCode", modelCode),
				zap.String("timestamp", clock.GetFormattedTime(time.Now().UnixNano())),
				zap.String("dataCode", data.IdentifyValue),
			)
			if len(dList) > 1 {
				return nil, errno.ErrDataCheck.Addf("数据库中字段 %s 的值 %s 已经存在多条，数据有误，请联系管理员", attrCodeMap[uniqueField].Name, dataValue)
			}
			// 如果不大于1，那么可能的值就是0或者1，如果是0，那么说明数据库里没有符合过滤条件的数据，那么就是正常的，如果是1，说明已有数据
			switch action {
			case "create":
				if len(dList) == 1 {
					return nil, errno.ErrDataCheck.Addf("模型字段 %s 的值 %s 已经存在，不能重复", attrCodeMap[uniqueField].Name, dataValue)
				}
			case "update":
				// 更新的时候也得查有没有数据冲突，但是得把自己给排除掉，所以说这条数据的id不能和数据库中查到的这条数据的ID一致
				if len(dList) == 1 && dList[0].ID != data.ID {
					// 如果是更新操作的时候，不可编辑字段也会回传，但是这个时候只要回传的值和数据库的一样，其实就可以忽略。
					// if modelAttrObj.Code == fmt.Sprintf("%s_name", modelCode) &&
					// 	dList[0].Data[uniqueField] == dataValue {
					// 	continue
					// }
					return nil, errno.ErrDataCheck.Addf("模型字段 %s 的值 %s 已经存在，不能重复", attrCodeMap[uniqueField].Name, dataValue)
				}
			default:
				return nil, errno.ErrDataCheck.Addf("action %s 不合法，只能是create或者update", action)
			}
		}
	}

	// 查看非继承枚举字段的合法性
	for _, enumField := range GeneralEnumFields {
		field := attrCodeMap[enumField]

		// 有一种情况，不可编辑字段，在首次编辑以后值就固定了，后续也不会变更了，永远是这个值了，但是这个时候，枚举字段的枚举值发生了变化
		// 这个时候我们就不做校验了，直接让字段值保持原样就要可以了。
		if !field.IsEditAble && action == "update" {
			continue
		}

		fieldData, ok := data.Data[enumField].(string)
		// 如果是空那就说明非必填字段，用户没填而已，那就不校验了。
		if fieldData == "" {
			continue
		}

		// 如果说能取到数据的话，就需要校验数据是否和枚举值匹配，如果取不到数据，那么就不需要校验了，因为这个字段是非必填的，必填的逻辑在上面已经校验过了
		exist := false
		if ok {
			opts := field.Attrs["opts"].(primitive.A)
			for _, opt := range opts {
				optMap := opt.(map[string]interface{})
				if strings.TrimSpace(optMap["name"].(string)) == fieldData {
					exist = true
					break
				}
			}
		}

		if !exist {
			return nil, errno.ErrDataCheck.Addf("字段 %s 的值 %s 不合法, 在枚举值中不存在，请检查提交的数据", field.Name, fieldData)
		}
	}

	// 遍历继承枚举字段，查看子枚举与父枚举之间的继承关系是否合规
	for _, enumField := range SubEnumFields {
		var (
			selectField v1.SelectAttribute
		)

		if err := mapstructure.Decode(attrCodeMap[enumField], &selectField); err != nil {
			return nil, errno.ErrDataCheck.Add("检查继承枚举字段时，解析字段属性出错, 请联系管理员, 错误详情为: " + err.Error())
		}

		// 同上，如果是可编辑字段同时操作是更新操作的时候，那么就不需要校验了，因为这个字段的值已经固定了，不会变更了
		if !selectField.IsEditAble && action == "update" {
			continue
		}

		sonData := strings.TrimSpace(utils.ToString(data.Data[enumField]))
		if sonData == "" {
			continue
		}

		// 先检查子枚举值是否合法
		exist := false
		for _, opt := range selectField.Attrs.Options {
			if opt.Name == sonData {
				exist = true
				break
			}
			zap.L().Debug("继承枚举字段", zap.String("postValue", opt.Name), zap.String("sonData", sonData))
		}

		if !exist {
			return nil, errno.ErrDataCheck.Addf("字段 %s 的值 %s 不合法, 在枚举值中不存在，请检查提交的数据", selectField.Name, sonData)
		}

		// 查询一下父级枚举值字段的值
		parentDataString, ok := data.Data[selectField.Attrs.ChainData.ParentCode].(string)
		// 这个时候取不到parentData，可能用户根本没有传递，但是这个时候传递了子枚举值，那么这个时候这个提报的数据就是非法的。
		if !ok && sonData != "" {
			return nil, errno.ErrDataCheck.Addf("数据校验失败，子枚举字段[%s]传递了的数据[%s]，但是父级枚举字段为空，请检查提交的数据", selectField.Name, sonData)
		}
		// 再检查子枚举值字段的继承关系是否合法
		for _, chain := range selectField.Attrs.ChainData.DataTree {
			for _, child := range chain.Children {
				if child.Name == sonData {
					if chain.Name != parentDataString {
						return nil, errno.ErrDataCheck.Addf("字段 %s 的值 %s 不合法, 与父级枚举值 %s 不匹配，请检查提交的数据", selectField.Name, sonData, parentDataString)
					}
				}
			}
		}
	}

	// 遍历通用字段，在data中补充通用字段数据, 比如server_sn是一个通用字段，那么就在data中再补充一个universal_sn
	for _, universal := range UniversalFields {
		universalFieldName := GetUniversalFieldName(universal, modelCode)
		if data.MetaData == nil {
			data.MetaData = make(mapdata.MapData)
		}
		// 这里不管是create还是update，只要强制覆盖就对了，这里需要的是始终保持最新。
		data.MetaData[universalFieldName] = data.Data[universal]
	}

	// 只有更新的时候，才需要判断是否可编辑，初始化创建的时候无所谓
	if action == "update" {
		oldData, err := mds.store.ModelData().GetModelDataByID(ctx, data.ID)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return nil, errno.ErrDataNotExists.Addf("模型数据id %s 不存在", data.ID)
			}
			return nil, err
		}
		for _, unEditableField := range unEditableFields {
			oldValue, ok := oldData.Data[unEditableField]
			// 如果说在老的数据中拿不到这个字段，那么可能是有一种情况，比如某个字段是后添加的，那么老数据天添加的时候，data中根本就不会存在
			// 这个字段，所以我们拿不到，这种情况下，我们就不需要判断是否可编辑了，因为老数据中本来就没有这个字段
			if !ok {
				oldValue = ""
			}
			newValue, ok := data.Data[unEditableField]
			if !ok {
				// 用户可能没有传递对应的字段，那么我们就不需要判断是否可编辑了，因为用户没有传递这个字段的值，那么我们也不需要更新这个字段
				continue
			}

			if oldValue != newValue {
				return nil, errno.ErrDataFieldUnEditable.Addf("不可编辑字段 %s 不能更新", attrCodeMap[unEditableField].Name)
			}
		}
	}

	return modelAttrs, nil
}
