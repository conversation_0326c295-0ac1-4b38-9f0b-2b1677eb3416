package v1

import (
	"ks-knoc-server/internal/cmdbserver/store"
)

// Service 定义一个通用的Interface接口
type Service interface {
	ModelGroup() ModelGroupService
	Model() ModelService
	ModelAttr() ModelAttrService
	ModelAttrGroup() ModelAttrGroupService
	ModelData() ModelDataService
	ModelTemplate() ModelTemplateService
	ModelRelation() ModelRelationService
	Audit() AuditService
	Users() UsersService
	View() ViewService
	Host() HostService
	Labels() LabelService
	Biz() BizService
	Agent() AgentService
}

var _ Service = (*service)(nil)

type service struct {
	store store.Factory
}

// NewService ...
func NewService(store store.Factory) Service {
	return &service{
		store: store,
	}
}

func (s *service) Agent() AgentService {
	return newAgentService(s)
}

func (s *service) ModelGroup() ModelGroupService {
	return newModelGroupService(s)
}

func (s *service) Model() ModelService {
	return newModelService(s)
}

func (s *service) ModelAttrGroup() ModelAttrGroupService {
	return newModelAttrGroupService(s)
}

func (s *service) ModelAttr() ModelAttrService {
	return newModelAttrService(s)
}

func (s *service) ModelData() ModelDataService {
	return newModelDataService(s)
}

func (s *service) ModelTemplate() ModelTemplateService {
	return newModelTemplate(s)
}

func (s *service) Audit() AuditService {
	return newAuditService(s)
}

func (s *service) Users() UsersService {
	return newUsersService(s)
}

func (s *service) ModelRelation() ModelRelationService {
	return newModelRelationService(s)
}

func (s *service) View() ViewService {
	return newViewService(s)
}

func (s *service) Host() HostService {
	return newHostService(s)
}

func (s *service) Labels() LabelService {
	return newLabelService(s)
}

func (s *service) Biz() BizService {
	return newBizService(s)
}
