package v1

import (
	"context"
	"encoding/json"
	"sort"

	"ks-knoc-server/internal/cmdbserver/store"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

const RackPlaceHolder = "rack_placeholder"

// checkRackSpare 检查机柜空闲U位
func checkRackSpare(ctx context.Context, rackInstance *v1.ModelData, startU, height int, store store.Factory, dev v1.Device) (bool, error) {
	zap.L().Debug("checkRackSpare function called")

	// 首先初始化一个列表用于保存机柜上的所有设备（包含标准设备和占位设备）
	deviceList := make([]v1.Device, 0)

	// 1. 首先先把机柜上的所有设备都拉出来
	dataList, err := store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"parent_id": rackInstance.ID}, nil)
	if err != nil {
		return false, err
	}

	// 2. 机柜上除了有设备，还有占位的东西，比如说配线架，ODF等，这种时候，虽然这里没设备，但是被占用，依然不可以放其他设备，需要标记为占用
	phList := make([]v1.PlaceHolder, 0)
	phs, ok := rackInstance.Data.Get(RackPlaceHolder)
	if ok && phs != "" {
		if err := json.Unmarshal([]byte(utils.ToString(phs)), &phList); err != nil {
			return false, err
		}
	}

	// 2. 如果说一台设备都没有，那么就直接往上面放就得了
	if len(dataList) == 0 && len(phList) == 0 {
		return true, nil
	}

	// 3. 往机柜上填设备
	for _, data := range dataList {
		// 当匹配到我们要操作的设备的时候，我们得看一下这台设备是不是已经在机柜上了，如果已经在机柜上了，说明是已上架设备挪位置，那么就要把自己排除掉
		// 判断的依据就是看一下这台设备的StartU是不是0，如果是0，那么就说明这台设备还没有上架到机柜上，如果不是0，那么就说明这台设备已经上架到机柜上了
		if data.ID == dev.DataID() && dev.StartU() != 0 {
			// 把自己这一段区间过滤掉
			continue
		}
		deviceList = append(deviceList, v1.Dev(*data))
	}

	// 4. 直接把占位设备加进来，因为占位设备没有ID，目前的设计不允许占位设备调整位置，你直接删掉然后重新加就可以。
	for _, ph := range phList {
		deviceList = append(deviceList, ph)
	}

	// 5. 初始化一个数组用于保存机柜上所有设备的range信息
	rackRange := make([][2]int, 0)
	for _, device := range deviceList {
		r, valid := device.Range()

		zap.L().Debug("rack range information", zap.Any("r", r), zap.Any("deviceID", device.DataID()))
		if valid {
			rackRange = append(rackRange, r)
		}
	}

	// 把当前要添加的设备加进来
	rackRange = append(rackRange, [2]int{startU, startU + height - 1})

	// 先针对占用的区间排序一下
	sort.Slice(rackRange, func(i, j int) bool {
		return rackRange[i][0] < rackRange[j][0]
	})

	// Debug 打点，输出机柜其实位置的range数组
	zap.L().Debug("rack range information", zap.Any("rackRange", rackRange))

	// 检查下一个区间是否在前一个区间结束之前开始（不光能检查新的，还能检查老的有没有脏数据），如果区间有重叠，那么就说明机柜上有设备占用了这个U位
	for i := 1; i < len(rackRange); i++ {
		if rackRange[i][0] <= rackRange[i-1][1] {
			return false, nil
		}
	}

	return true, nil
}
