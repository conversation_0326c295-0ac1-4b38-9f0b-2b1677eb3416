package v1

import (
	"context"
	"errors"
	"fmt"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/mitchellh/mapstructure"
	"github.com/mohae/deepcopy"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ModelAttrService interface {
	// CreateModelAttribute 创建模型属性
	CreateModelAttribute(ctx context.Context, operator string, attr map[string]interface{}) (interface{}, error)
	// UpdateModelAttribute 更新模型属性
	UpdateModelAttribute(ctx context.Context, operator string, attr map[string]interface{}) error
	// GetModelAttributeList 根据属性Code获取模型属性列表
	GetModelAttributeList(ctx context.Context, modelCode string, filter bson.M) ([]v1.CommonModelAttribute, error)
	// GetModelAttributeByFilter 根据过滤条件获取模型属性
	GetModelAttributeByFilter(ctx context.Context, filter bson.M) (*v1.CommonModelAttribute, error)
	// GetNestedAttributeList 根据属性Code获取嵌套模型属性列表
	GetNestedAttributeList(ctx context.Context, modelCode string) ([]apiv1.NestedModelAttr, error)
	// DeleteModelAttribute 删除模型属性
	DeleteModelAttribute(ctx context.Context, operator string, attrCode *v1.InstanceFilter) error
	// GetModelRules 获取模型规则
	GetModelRules(ctx context.Context) (map[string][]interface{}, error)
}

type modelAttrService struct {
	store store.Factory
}

var _ ModelAttrService = (*modelAttrService)(nil)

func newModelAttrService(srv *service) *modelAttrService {
	return &modelAttrService{store: srv.store}
}

// GetModelRules 获取模型属性的规则
func (mas *modelAttrService) GetModelRules(ctx context.Context) (map[string][]interface{}, error) {
	zap.L().Debug("GetModelRules Service Called")
	span, ctx := apm.StartSpan(ctx, "GetModelRules", "service")
	defer span.End()

	modelRules, err := mas.store.ModelAttributes().GetModelRules(ctx)
	if err != nil {
		return nil, err
	}
	var modelRulesResponse = make(map[string][]interface{})
	for _, modelRule := range modelRules {
		modelRulesResponse[modelRule.RuleType] = append(modelRulesResponse[modelRule.RuleType], modelRule)
	}
	return modelRulesResponse, nil
}

// GetModelAttributeList 根据模型的code获取模型属性列表
func (mas *modelAttrService) GetModelAttributeList(ctx context.Context, modelCode string, filter bson.M) ([]v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttributeList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeList", "service")
	defer span.End()

	// 首先要查询一下这个模型的code是否存在
	_, err := mas.store.Model().GetModelByCode(ctx, modelCode)
	if err != nil {
		return nil, errno.ErrParameterInvalid.Addf("传递的模型唯一标识 [%s] 不存在", modelCode)
	}

	// 过滤掉关系的字段
	filter["type_name"] = bson.M{"$ne": "relationship"}

	// 只展示is_display为true的字段
	filter["is_display"] = true

	// 这里可以设置返回的内容中不包含_id字段，比如设置selectFilter := bson.M{"_id": 0}，然后作为filter参数传递给GetModelAttributeList
	modelAttrList, err := mas.store.ModelAttributes().GetModelAttributeList2(ctx, modelCode, nil, filter)
	if err != nil {
		return nil, err
	}
	return modelAttrList, nil
}

// GetModelAttributeByFilter 根据模型属性的filter过滤条件获取模型属性，只返回一条数据
func (mas *modelAttrService) GetModelAttributeByFilter(ctx context.Context, filter bson.M) (*v1.CommonModelAttribute, error) {
	zap.L().Debug("GetModelAttributeByFilter Service Called")
	span, ctx := apm.StartSpan(ctx, "GetModelAttributeByFilter", "service")
	defer span.End()

	modelAttr, err := mas.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, filter, bson.M{})
	if err != nil {
		return nil, err
	}
	return modelAttr, nil
}

func (mas *modelAttrService) GetNestedAttributeList(ctx context.Context, modelCode string) ([]apiv1.NestedModelAttr, error) {
	zap.L().Debug("GetNestedAttributeList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetNestedAttributeList", "service")
	defer span.End()

	// 1. 拿到模型属性列表
	modelAttrList, err := mas.store.ModelAttributes().GetModelAttributeList2(ctx, modelCode, nil, bson.M{
		"type_name":  bson.M{"$ne": "relationship"},
		"is_display": true, // 2024-07-24 因为目前json字段暂未上线，因此强制隐藏
	})
	if err != nil {
		return nil, err
	}
	// 2. 拿到模型属性组列表
	modelAttrGroupList, err := mas.store.ModelAttrGroup().GetModelAttrGroupList(ctx, modelCode, bson.M{
		"relation": bson.M{"$ne": true},
	})
	if err != nil {
		return nil, err
	}
	// 3. 组合模型属性以及模型分组
	var (
		modelAttrToAttrGroup = make(map[string][]v1.CommonModelAttribute)
		nestedModelAttrList  []apiv1.NestedModelAttr
	)

	for _, modelAttr := range modelAttrList {
		attrGroup := modelAttr.AttrGroup
		modelAttrToAttrGroup[attrGroup] = append(modelAttrToAttrGroup[attrGroup], modelAttr)
	}

	for _, attrGroup := range modelAttrGroupList {
		if modelAttrToAttrGroup[attrGroup.Code] == nil {
			modelAttrToAttrGroup[attrGroup.Code] = make([]v1.CommonModelAttribute, 0)
		}
		m := apiv1.NestedModelAttr{
			ID:            attrGroup.ID,
			Name:          attrGroup.Name,
			Code:          attrGroup.Code,
			Total:         len(modelAttrToAttrGroup[attrGroup.Code]),
			BuiltIn:       attrGroup.Builtin,
			SystemCreated: attrGroup.SystemCreated,
			Children:      modelAttrToAttrGroup[attrGroup.Code],
		}
		nestedModelAttrList = append(nestedModelAttrList, m)
	}
	return nestedModelAttrList, nil
}

// CreateModelAttribute 创建模型属性
func (mas *modelAttrService) CreateModelAttribute(ctx context.Context, operator string, attr map[string]any) (interface{}, error) {
	zap.L().Debug("CreateModelAttribute Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelAttribute", "service")
	defer span.End()

	// 1. 判断model_code是否存在
	modelCode := utils.ToString(attr["model_code"])
	if modelCode == "" {
		return nil, errno.ErrParameterInvalid.Add("模型唯一标识不能为空")
	}
	model, err := mas.store.Model().GetModelByCode(ctx, modelCode)
	if err != nil {
		return nil, errno.ErrDataNotExists.Add("模型唯一标识" + modelCode + "不存在")
	}

	// 2. 判断模型属性分组是否存在
	attrGroupCode := utils.ToString(attr["attr_group"])
	if attrGroupCode == "" {
		return nil, errno.ErrParameterInvalid.Add("属性分组不能为空")
	}
	mag, err := mas.store.ModelAttrGroup().GetModelAttrGroupByCode(ctx, attrGroupCode)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("属性分组%s不存在", attrGroupCode)
		}
		return nil, err
	}

	// 3. 判断attr_group和model_code是否对应，有可能传入的attr_code完全不是model_code对应模型的属性分组
	if mag.ModelCode != modelCode {
		return nil, errno.ErrParameterInvalid.Add("模型属性分组与模型不匹配")
	}

	// 4. 同一个模型下的字段名称以及唯一标识都不允许重复，因此在添加之前需要判断一下, 如果说能查到，那么就说明已存在，有冲突
	// 如果能查到数据，err应该为nil，如果查不到数据，err应该为mongo.ErrNoDocuments
	if _, err := mas.GetModelAttributeByFilter(ctx, bson.M{"model_code": modelCode, "code": attr["code"]}); err == nil {
		return nil, errno.ErrModelAttrCreate.Addf("模型[%s]下已存在唯一标识为 [%s] 的属性", model.Name, attr["code"])
	}
	if _, err = mas.GetModelAttributeByFilter(ctx, bson.M{"model_code": modelCode, "name": attr["name"]}); err == nil {
		return nil, errno.ErrModelAttrCreate.Addf("模型[%s]下已存在名称为 [%s] 的属性", model.Name, attr["name"])
	}

	// 5. 针对具体的类型，构造不同的结构体实例
	var ma any
	typeName := utils.ToString(attr["type_name"])
	// 根据不同的数据类型创建不同的结构体
	if typeName == "string" {
		stringAttr := &v1.StringAttribute{}
		stringAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &stringAttr); err != nil {
			return nil, err
		}
		err = stringAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = stringAttr
	} else if typeName == "int" {
		intAttr := &v1.IntAttribute{}
		intAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &intAttr); err != nil {
			return nil, err
		}
		err = intAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = intAttr
	} else if typeName == "float" {
		floatAttr := &v1.FloatAttribute{}
		floatAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &floatAttr); err != nil {
			return nil, err
		}
		err = floatAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = floatAttr
	} else if typeName == "date" {
		dateAttr := &v1.DateAttribute{}
		dateAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &dateAttr); err != nil {
			return nil, err
		}
		err = dateAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = dateAttr
	} else if typeName == "bool" {
		boolAttr := &v1.BoolAttribute{}
		boolAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &boolAttr); err != nil {
			return nil, err
		}
		err = boolAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = boolAttr
	} else if typeName == "datetime" {
		dateTimeAttr := &v1.DateTimeAttribute{}
		dateTimeAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &dateTimeAttr); err != nil {
			return nil, err
		}
		err = dateTimeAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = dateTimeAttr
	} else if typeName == "select" {
		selectAttr := &v1.SelectAttribute{}
		selectAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &selectAttr); err != nil {
			return nil, err
		}
		err := CheckSelectValidate(ctx, selectAttr, mas, "create")
		if err != nil {
			return nil, err
		}
		err = selectAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = selectAttr
	} else if typeName == "textarea" {
		textareaAttr := &v1.TextareaAttribute{}
		textareaAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, &textareaAttr); err != nil {
			return nil, err
		}
		err := textareaAttr.Validate()
		if err != nil {
			return nil, err
		}
		ma = textareaAttr
	} else if typeName == "json" {
		jsonAttr := v1.NewJsonAttribute()
		jsonAttr.SetDefault(attr)
		if err := mapstructure.Decode(attr, jsonAttr); err != nil {
			return nil, err
		}
		ma = jsonAttr
	} else {
		return nil, errno.ErrParameterInvalid.Add("不支持的属性类型")
	}
	// 6. 确认无误后，即可添加字段
	modelAttr, err := mas.store.ModelAttributes().CreateModelAttributes(ctx, operator, ma)
	if err != nil {
		return nil, err
	}

	// 审计日志
	au := &AuditEvent{store: mas.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelAttributeRes,
		audit.ActionCreate,
		operator,
		modelAttr.ID,
		nil,
		ma,
	); err != nil {
		return nil, err
	}
	if err := au.Save(ctx); err != nil {
		return nil, err
	}
	return modelAttr, nil
}

func CheckSelectValidate(ctx context.Context, s *v1.SelectAttribute, mas *modelAttrService, action string) error {
	// 判断一下用户有没有传递枚举字段
	// TODO 只判断opts传没传还不行，还需要判断id和name都传递了才可以
	if len(s.Attrs.Options) == 0 {
		return errno.ErrParameterInvalid.Add("枚举字段不能为空")
	}

	// 判断枚举值是否有重复的
	if conflict, opts := s.OptionValueConflict(); conflict {
		return errno.ErrParameterInvalid.Add(fmt.Sprintf("枚举字段的选项值重复, 重复的选项值为: %s", opts))
	}

	if s.Attrs.Inherit {
		// ***** 该枚举字段继承了其它的枚举字段 *****

		// 首先看一下这个枚举字段是不是继承的字段，如果是继承的字段的话，应该符合如下的一些校验规则
		// 校验父级的数据
		parentCode := s.Attrs.ChainData.ParentCode
		// 父级字段不能为空
		if parentCode == "" {
			return errno.ErrParameterInvalid.Add("继承字段的父级字段不能为空, chain_data.parent_code必须传递")
		}
		// 父级的继承字段不可以是自己（一般按照正常的前端逻辑，不会出现这种情况，但是保险起见，还是看一下）
		if parentCode == s.Code {
			return errno.ErrParameterInvalid.Add("继承字段的父级字段不可以是自己")
		}
		// 父级字段必须是存在的字段
		parentInstance, err := mas.GetModelAttributeByFilter(ctx, bson.M{"code": parentCode})
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				return errno.ErrParameterInvalid.Add("父级字段不存在")
			}
			return err
		}
		// 父级字段存在的情况下还得看一下父级字段是不是select类型的字段
		if parentInstance.TypeName != "select" {
			return errno.ErrParameterInvalid.Add("父级字段必须是select类型的字段")
		}
		// 如果是继承的字段的话，那么depth的值只能是2，除此之外其他的值都不对
		if s.Attrs.Depth <= 1 || s.Attrs.Depth > 2 {
			return errno.ErrParameterInvalid.Add("参数不合规，depth深度的值只能是2")
		}
		// chain_data中的data_tree也不随便填写，先判断一下chain_data和data_tree是否都存在, 避免用户data_tree这个字段只传一个null
		dataTree := s.Attrs.ChainData.DataTree
		if len(dataTree) == 0 {
			return errno.ErrParameterInvalid.Add("当字段为继承枚举字段时，chain_data.data_tree必须传递")
		}
		// 我需要判断一下data_tree中的父级id是存在的，且children中的子id在自己的opts中也存在
		// 定义一个父级字段的opts的id的列表
		parentOpts := make(map[string]interface{}, 0)
		currentOpts := make(map[string]interface{}, 0)
		// 这个opts是primitive.A{}类型的，设计一个有序的bson
		opts, ok := parentInstance.Attrs["opts"]
		if !ok {
			return errno.ErrParameterRequired.Add("父级字段的opts不存在")
		}
		// 填充父级字段的opts的id列表
		for _, v := range opts.(primitive.A) {
			vm := v.(map[string]interface{})
			optID, ok := vm["id"]
			if !ok {
				return errno.ErrParameterInvalid.Add("父级字段的opts中的id必须传递，请检查提交的数据是否正确")
			}
			optName, ok := vm["name"]
			if !ok {
				return errno.ErrParameterInvalid.Add("父级字段的opts中的name必须传递，请检查提交的数据是否正确")
			}
			parentOpts[optID.(string)] = map[string]string{"id": optID.(string), "name": optName.(string)}
		}
		// 填充当前枚举字段的opts的id列表
		for _, v := range s.Attrs.Options {
			currentOpts[v.ID] = map[string]string{"id": v.ID, "name": v.Name}
		}
		// 分别判断用户提交的data_tree中的父级id和子id是否存在，有可能前端传错或者用户瞎写。
		chainChildDatas := make(map[string]map[string]interface{}, 0)
		for _, d := range dataTree {
			// 参数首先看一下父级id是否存在
			if !utils.FindStrInStringSlice(utils.MapKeys(parentOpts), d.ID) {
				return errno.ErrParameterInvalid.Add("父级字段的opts中不存在id为 '" + d.ID + "' 的选项")
			}
			// 如果存在再判定一下名称是否一致
			if parentOpts[d.ID].(map[string]string)["name"] != d.Name {
				return errno.ErrParameterInvalid.Add("data_tree中父级字段的opts中id为 '" + d.ID + "' 的选项的名称与实际的父级字段id为 '" + d.ID + "' 的opts的名称不一致, 父级字段的opts中的名称为 '" + parentOpts[d.ID].(map[string]string)["name"] + "'" + "提交的名称为 '" + d.Name + "'")
			}
			for _, c := range d.Children {
				chainChildDatas[c.ID] = map[string]interface{}{"id": c.ID, "name": c.Name}
				if !utils.FindStrInStringSlice(utils.MapKeys(currentOpts), c.ID) {
					return errno.ErrParameterInvalid.Add("当前枚举字段中不存在id为 '" + c.ID + "' 的选项")
				}
				if currentOpts[c.ID].(map[string]string)["name"] != c.Name {
					return errno.ErrParameterInvalid.Add("data_tree.children中opts id为 '" + c.ID + "' 的选项的名称与当前attr.opts id为 '" + c.ID + "' 的名称不一致, attr.opts中的名称为 '" + currentOpts[c.ID].(map[string]string)["name"] + "'" + "提交的名称为 '" + c.Name + "'")
				}
			}
		}
		// chain_data中的data_tree下的children下opts的个数得和attrs.opts相等
		if len(chainChildDatas) != len(s.Attrs.Options) {
			return errno.ErrParameterInvalid.Add("chain_data.data_tree.children中的opts的个数与attrs.opts的个数不一致")
		}
	} else {
		// ****** 非继承的枚举字段 ******

		// 为0的时候，有可能是用户故意传了0，或者说用户没传，因为depth是非必填字段，所以为0的时候正好是零值，不报错，单独处理一下即可
		if s.Attrs.Depth == 0 {
			s.Attrs.Depth = 1
		}

		// 如果不是继承字段的话，那么depth应该是1~3之间的整数
		if s.Attrs.Depth != 1 {
			return errno.ErrParameterInvalid.Add("非继承字段的depth的值只能为1")
		}

		// 如果当前的操作是更新操作的话，需要单独做处理
		if action == "update" {
			// 默认当前的s是新的数据，要和旧的数据作对比，因此从数据库查到这个枚举字段的旧数据
			old, err := mas.GetModelAttributeByFilter(ctx, bson.M{"code": s.Code})
			if err != nil {
				return errno.InternalServerError.Add(err.Error())
			}

			// 将old转换为一个SelectAttribute类型的变量
			oldOpt, err := old.ToSelectField()
			if err != nil {
				return errno.InternalServerError.Add(err.Error())
			}

			del, edit, add := oldOpt.Compare(*s)

			// 查询一下有没有该模型的其他字段继承了该字段
			sonSelectAttrs, err := mas.GetModelAttributeList(ctx, s.ModelCode, bson.M{
				"attrs.inherit":                true,
				"attrs.chain_data.parent_code": s.Code,
				"type_name":                    "select",
			})
			if err != nil {
				zap.L().Error("获取子枚举字段失败", zap.Error(err))
				return errno.InternalServerError.Add(err.Error())
			}

			sonSelectFields := make([]v1.SelectAttribute, 0)
			for _, son := range sonSelectAttrs {
				sonSelectField, err := son.ToSelectField()
				if err != nil {
					return errno.InternalServerError.Add(err.Error())
				}
				sonSelectFields = append(sonSelectFields, *sonSelectField)
			}

			// 遍历所有的子字段，并更新子字段的chain_data.data_tree
			for _, son := range sonSelectFields {
				// 首先拿到子字段的ChainData中的DataTree
				dt := son.Attrs.ChainData.DataTree
				// 构造一个map，方便后续的操作
				dtMap := make(mapdata.MapData)
				// 构造一个新的options，用来存储需要保留的options
				newOptions := v1.NewSelectOptions()
				// dtMap Key为Option的ID，value为SelectDataTree类型的数据，注意这里存的是值不是指针，统一存值
				for _, t := range dt {
					dtMap[t.ID] = t
				}

				// 遍历一下需要删除的字段，如果说dtMap中存在，那么就删除
				for _, d := range del {
					// 如果说要删除的option的ID在dtMap中存在，那么就删除
					if t, exist := dtMap.Get(d.ID); exist {
						// 删除主要分两个部分，一个是chain_data下DataTree中的数据
						dtMap.Delete(d.ID)
						// 第二个部分是删除子枚举字段本身的options，因为子枚举字段的option
						// 其实是和父级枚举字段的option强关联的，如果父级字段的option删除了
						// 那么子枚举字段中对应的option也需要删除
						// t本身对应的是一个SelectDataTree类型的数据，我们需要把它转换成SelectDataTree类型
						newt := t.(v1.SelectDataTree)
						// 遍历子枚举字段的options，和要删除的options做对比，如果说不相等，那么就是需要保留的
						for _, c := range son.Attrs.Options {
							for _, child := range newt.Children {
								if c.ID != child.ID {
									newOptions = append(newOptions, c)
								}
							}
						}
					}
				}

				// 遍历一下add，这个是需要做添加的字段
				for _, a := range add {
					_, exist := dtMap.Get(a.ID)
					if !exist {
						nv := v1.NewSelectDataTree()
						nv.ID = a.ID
						nv.Name = a.Name
						dtMap.Set(a.ID, *nv)
					}
				}

				// 遍历一下需要编辑的字段，更新dtMap中的数据
				for _, e := range edit {
					v, exist := dtMap.Get(e.ID)
					if exist {
						nv := v.(v1.SelectDataTree)
						nv.Name = e.Name
						dtMap.Set(e.ID, nv)
					}
				}

				// 2024-02-19 fix: 如果说没有删除的option，那么这个时候至少需要保留原来的option
				if len(newOptions) == 0 {
					newOptions = son.Attrs.Options
				}

				// 更新子枚举字段的DataTree
				son.Attrs.ChainData.DataTree = func() []v1.SelectDataTree {
					var dataTree = make([]v1.SelectDataTree, 0)
					for _, v := range dtMap {
						dataTree = append(dataTree, v.(v1.SelectDataTree))
					}
					return dataTree
				}()

				son.Attrs.Options = newOptions

				// 回写到数据库里
				u := make(map[string]interface{})
				if err = mapstructure.Decode(son, &u); err != nil {
					return errno.InternalServerError.Add(err.Error())
				}

				if err := mas.store.ModelAttributes().UpdateModelAttributes(ctx, u); err != nil {
					return errno.InternalServerError.Add(err.Error())
				}
			}
		}

	}
	return nil
}

// CanDeleteUniqueProp 是否可以删除字段的唯一属性
func (mas *modelAttrService) CanDeleteUniqueProp(ctx context.Context, attr any) error {
	zap.L().Debug("CanDeleteUniqueProp Function Called")
	span, ctx := apm.StartSpan(ctx, "CheckUniqueField", "service")
	defer span.End()

	var (
		v   = &v1.CommonModelAttribute{}
		err error
	)

	if err = mapstructure.Decode(attr, &v); err != nil {
		return err
	}

	// 查看模型下除传递的字段外是否还有其他唯一字段，传递的attr表示要删除的字段
	filter := bson.M{
		"$and": []bson.M{
			{"model_code": v.ModelCode, "is_unique": true},
			{"code": bson.M{"$ne": v.Code}},
			{"code": bson.M{"$ne": fmt.Sprintf("%s_code", v.ModelCode)}},
		},
	}
	attrs, err := mas.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, filter, bson.M{})

	if err != nil {
		return err
	}

	// 表示除了当前字段，没有其他的唯一字段了
	if len(attrs) == 0 {
		return errno.ErrModelAttrUniqueAtLeastOne.Add("模型下至少需要有一个唯一字段，当前字段不可取消唯一属性")
	}

	return nil
}

// UpdateModelAttribute 更新模型属性
// 在更新模型属性中有几个注意的点
// 1. 首先属性一旦创建，意味着属性的类型就确定了，属性本身的attrs那个字段就固定了，类型不能再修改，否则数据会出现异常
// 2. 唯一标识也不可以进行修改，否则会导致数据异常（主要是关系的数据会出现异常）
// 3. attr_group可以携带，即允许用户修改属性的分组
func (mas *modelAttrService) UpdateModelAttribute(ctx context.Context, operator string, attr map[string]interface{}) error {
	zap.L().Debug("UpdateModelAttribute Service Called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelAttribute", "service")
	defer span.End()

	// 1. 首先要判断用户更新的这个属性到底存不存在，参数合法性的判断，同时判断用户提交的code, model_group, attr_group是否存在
	modelAttr, err := mas.store.ModelAttributes().GetOneModelAttrMapByCustomFilter(ctx, bson.M{
		"code":       attr["code"].(string),
		"model_code": attr["model_code"].(string),
		"attr_group": attr["attr_group"].(string),
	}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Addf("要修改的属性不存在，请检查过滤条件中的code:%s, model_code:%s "+
				"attr_group:%s 三个参数是否正确", attr["code"].(string), attr["model_code"].(string), attr["attr_group"].(string))
		}
		return err
	}
	// 2. 任何情况下都不允许修改唯一标识，名称，字段类型，唯一标识在提交的时候会对Code是否存在做校验，所以这里不需要再做校验了
	if modelAttr["type_name"].(string) != attr["type_name"].(string) {
		return errno.ErrParameterInvalid.Add("字段类型不可以修改，原字段类型为: " + modelAttr["type_name"].(string))
	}
	if modelAttr["name"].(string) != attr["name"].(string) {
		return errno.ErrParameterInvalid.Add("字段名称不允许修改，原字段名称为: " + modelAttr["name"].(string))
	}
	// 3. 接下来看要修改的这个属性所在的模型下面是否有数据，为了保证数据的一致性，如果已经有数据了，那么修改的内容随类型不同而不同
	dataList, err := mas.store.ModelData().GetModelDataByModelCode(ctx, attr["model_code"].(string))
	if err != nil {
		return err
	}
	// 4. 对比一下用户提交过来的数据和数据库的数据，看看有哪些字段是不一样的，然后根据字段的不同，做不同的处理
	differentFields := utils.CompareMaps(attr, modelAttr)
	// 删除一些调用侧不敏感的字段
	for _, field := range []string{"_id", "create_at", "update_at", "id", "system_created"} {
		delete(differentFields, field)
	}

	// 检查是否可以删除唯一属性
	unique, exist := modelAttr["is_unique"]
	if !exist {
		return errno.ErrParameterInvalid.Add("is_unique字段不存在")
	}

	// TODO: 我理解只有从唯一变为不唯一的时候才需要校验，但是有两种情况
	// 1. 从唯一变为不唯一，这个时候需要校验
	// 2. 有可能数据压根没填写
	if unique.(bool) && !attr["is_unique"].(bool) {
		err = mas.CanDeleteUniqueProp(ctx, attr)
		if err != nil {
			return err
		}
	}

	typeName := attr["type_name"].(string)
	switch typeName {
	case "string":
		// 初始化一个字符串的实例，然后将用户提交的数据进行解析，同创建一样，需要符合基本的数据结构
		stringAttr := &v1.StringAttribute{}
		if err := mapstructure.Decode(attr, &stringAttr); err != nil {
			return err
		}
		// 校验用户提示字段以及sample字段是否合规
		err = stringAttr.Validate()
		if err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "string")
		if err != nil {
			return err
		}
	case "textarea":
		textAreaAttr := &v1.TextareaAttribute{}
		if err := mapstructure.Decode(attr, &textAreaAttr); err != nil {
			return err
		}
		// 校验用户提示字段以及sample字段是否合规
		err = textAreaAttr.Validate()
		if err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "textarea")
		if err != nil {
			return err
		}
	case "int":
		intAttr := &v1.IntAttribute{}
		if err := mapstructure.Decode(attr, &intAttr); err != nil {
			return err
		}
		// 校验用户提示字段以及sample字段是否合规
		err = intAttr.Validate()
		if err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "int")
		if err != nil {
			return err
		}
	case "float":
		floatAttr := &v1.FloatAttribute{}
		if err := mapstructure.Decode(attr, &floatAttr); err != nil {
			return err
		}
		// 校验用户提示字段以及sample字段是否合规
		err = floatAttr.Validate()
		if err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "float")
		if err != nil {
			return err
		}
	case "select":
		selectAttr := &v1.SelectAttribute{}
		if err := mapstructure.Decode(attr, &selectAttr); err != nil {
			return err
		}
		// 校验用户提示字段以及sample字段是否合规
		err := CheckSelectValidate(ctx, selectAttr, mas, "update")
		if err != nil {
			return err
		}
		err = selectAttr.Validate()
		if err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "select")
		if err != nil {
			return err
		}
	case "date":
		dateAttr := &v1.DateAttribute{}
		if err := mapstructure.Decode(attr, &dateAttr); err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "date")
		if err != nil {
			return err
		}
	case "datetime":
		dateTimeAttr := &v1.DateTimeAttribute{}
		if err := mapstructure.Decode(attr, &dateTimeAttr); err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "datetime")
		if err != nil {
			return err
		}
	case "bool":
		boolAttr := &v1.BoolAttribute{}
		if err := mapstructure.Decode(attr, &boolAttr); err != nil {
			return err
		}
		modelAttr, err = checkUpdateValidate(len(dataList), differentFields, modelAttr, "bool")
		if err != nil {
			return err
		}
	default:
		return errno.InternalServerError.Addf("未知的属性类型，内部错误信息为：%s", err.Error())
	}

	// 4. 更新我们的属性
	err = mas.store.ModelAttributes().UpdateModelAttributes(ctx, modelAttr)
	if err != nil {
		return err
	}

	// 5. 刷新缓存
	if _, err := mas.store.ModelAttributes().SetModelAttributesCache(ctx); err != nil {
		return nil
	}

	return nil
}

func checkUpdateValidate(count int, differentFields, modelAttr map[string]interface{}, typeName string) (map[string]interface{}, error) {
	copyDifferentFields := deepcopy.Copy(differentFields).(map[string]interface{})
	if count > 0 {
		// 字符串，文本域，整型，浮点型，可以改用户提示和示例
		if utils.FindStrInStringSlice([]string{"string", "textarea", "int", "float"}, typeName) {
			// 当已经有数据的时候，字符串类型的字段，只有用户提示和示例可以编辑，其他的都不可以编辑
			if attrs, ok := differentFields["attrs"]; ok {
				attrMap, ok := attrs.(map[string]interface{})
				if !ok {
					return nil, errno.ErrParameterInvalid.Add("attrs字段不合法")
				}
				if uh, ok := attrMap["user_hint"]; ok {
					modelAttr["attrs"].(map[string]interface{})["user_hint"] = uh
					delete(differentFields["attrs"].(map[string]interface{}), "user_hint")
				}
				if s, ok := attrMap["sample"]; ok {
					modelAttr["attrs"].(map[string]interface{})["sample"] = s
					delete(differentFields["attrs"].(map[string]interface{}), "sample")
				}
				if len(differentFields["attrs"].(map[string]interface{})) == 0 {
					delete(differentFields, "attrs")
				}
			}
			if len(differentFields) > 0 {
				return nil, errno.ErrParameterInvalid.Add("当前模型已存在数据，只有用户提示和示例可以编辑，其他的都不可以编辑")
			}
		}
		// 枚举类型，可以改用户提示和示例和枚举值
		if typeName == "select" {
			// 从差异化字段中获取attrs字段
			if attrs, ok := differentFields["attrs"]; ok {
				attrMap, ok := attrs.(map[string]interface{})
				if !ok {
					return nil, errno.ErrParameterInvalid.Add("attrs字段不合法")
				}
				if uh, ok := attrMap["user_hint"]; ok {
					modelAttr["attrs"].(map[string]interface{})["user_hint"] = uh
					delete(differentFields["attrs"].(map[string]interface{}), "user_hint")
				}
				// 更新枚举值
				if opts, ok := attrMap["opts"]; ok {
					modelAttr["attrs"].(map[string]interface{})["opts"] = opts
					delete(differentFields["attrs"].(map[string]interface{}), "opts")
				}
				// depth是隐式字段，只用来表示枚举字段是不是继承的，因此这个是可以编辑，需要扣掉
				if _, ok := attrMap["depth"]; ok {
					delete(differentFields["attrs"].(map[string]interface{}), "depth")
				}

				// chain_data是枚举值的链式数据，保存上下级枚举字段的继承关系，因此这个是可以编辑，需要扣掉
				if _, ok := attrMap["chain_data"]; ok {
					delete(differentFields["attrs"].(map[string]interface{}), "chain_data")
				}

				if len(differentFields["attrs"].(map[string]interface{})) == 0 {
					delete(differentFields, "attrs")
				}
			}

			if len(differentFields) > 0 {
				return nil, errno.ErrParameterInvalid.Add("当前模型已存在数据，只有用户提示和枚举值可以编辑，其他的都不可以编辑")
			}
		}
		// 日期类型，时间类型以及布尔类型，只能改用户提示
		if utils.FindStrInStringSlice([]string{"date", "datetime", "bool"}, typeName) {
			if attrs, ok := differentFields["attrs"]; ok {
				attrMap, ok := attrs.(map[string]interface{})
				if !ok {
					return nil, errno.ErrParameterInvalid.Add("attrs字段不合法")
				}
				if uh, ok := attrMap["user_hint"]; ok {
					modelAttr["attrs"].(map[string]interface{})["user_hint"] = uh
					delete(differentFields["attrs"].(map[string]interface{}), "user_hint")
				}
				if len(differentFields["attrs"].(map[string]interface{})) == 0 {
					delete(differentFields, "attrs")
				}
			}
			if len(differentFields) > 0 {
				return nil, errno.ErrParameterInvalid.Add("当前模型已存在数据，只有用户提示可以编辑，其他的都不可以编辑")
			}
		}

		err := utils.UpdateMap(copyDifferentFields, modelAttr)
		if err != nil {
			return nil, err
		}
	} else {
		// 没有数据的话，除字段的唯一标识，名称，字段类型不可以改，其他均可修改
		// 在UpdateMap中，会将differentFields中的字段更新到modelAttr中，但是如果说differentFields中的字段在modelAttr中不存在的话
		// 那么就会报错
		err := utils.UpdateMap(differentFields, modelAttr)
		if err != nil {
			return nil, err
		}
	}
	return modelAttr, nil
}

// DeleteModelAttribute 删除模型属性
// 删除遵循几个点，只要是有实例还在使用，那么就不允许删除这个关联属性
// 1. 这个删除的判断，不能判断模型下是否有实例，而应该判断是否有实例在使用
// 2. 删除的时候，要对等的把对端的关联属性也删除掉，避免遗留脏数据造成后期无法维护
// 3. 如果是是内置的关联属性，那么就不允许删除，应该优先把内置的选项设置为false才可以删除，由系统创建的属性直接不允许你删除
func (mas *modelAttrService) DeleteModelAttribute(ctx context.Context, operator string, attrCode *v1.InstanceFilter) error {
	span, ctx := apm.StartSpan(ctx, "DeleteModelAttribute", "service")
	defer span.End()

	// 1. 首先判断这个属性到底存不存在
	attr, err := mas.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"code": attrCode.Code}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Addf("模型属性 %s 不存在", attrCode.Code)
		}
		return err
	}
	// 2. 判断一下是否是内置属性，如果是内置属性则不允许删除
	if attr.IsBuiltIn() {
		return errno.InternalServerError.Addf("内置属性 %s 不允许删除", attrCode.Code)
	}

	// 3. 判断一下是不是系统创建的属性，如果是系统创建的属性的话，那么不允许删除。
	if attr.CheckIsSystemCreated() {
		return errno.InternalServerError.Addf("系统创建的属性 %s 不允许删除", attrCode.Code)
	}

	// 4. 判断一下模型下是否已经有资源，如果说有资源了，且这个字段都有值，那么就不允许删除，但是如果值都是空的话，那么就可以删除
	dataList, err := mas.store.ModelData().GetModelDataByModelCode(ctx, attr.ModelCode)
	// 当查不到数据的时候，会返回Doc not exist的报错，因此只需要关注返回的err即可。
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	// 如果说有数据，还得看数据对应的字段是否有值，如果有值的话，那么就不允许删除
	if len(dataList) > 0 {
		// 遍历每一条数据，看看是否有值
		for _, data := range dataList {
			// 有可能数据是在创建字段之前创建的，那么其实这个时候是拿不到这个字段的。所以得判断字段是否存在
			dataValue, exist := data.Data[attr.Code]
			// 如果存在我们再判断一下值是否为空值，因为data中的所有字段的值都是字符串，如果说存在且不为空字符串，那么就不允许删除
			// 这个时候说明对应有的数据在这个字段上有值了，删除了这个字段，那么就会导致数据丢失，因此不允许删除
			if exist && dataValue != "" {
				return errno.ErrModelAttrDelete.Add("该模型下有实例在使用该属性，不允许删除")
			}
		}
	}

	// 5. 检查是否可以删除唯一属性
	if err = mas.CanDeleteUniqueProp(ctx, attr); err != nil {
		return err
	}

	// 7. 需要单独校验一下是不是枚举，且继承了其他的枚举属性
	if attr.TypeName == "select" {
		// 我们要找的是当前模型下，类型是枚举，且继承了当前字段的枚举字段
		subSelectAttrs, err := mas.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
			"model_code":                   attr.ModelCode,
			"code":                         bson.M{"$ne": attr.Code},
			"type_name":                    "select",
			"attrs.inherit":                true,
			"attrs.chain_data.parent_code": attr.Code,
		}, bson.M{})

		if err != nil {
			return err
		}

		if len(subSelectAttrs) > 0 {
			return errno.ErrModelAttrDelete.Add("该模型下其他枚举属性继承了当前枚举属性，不允许删除，请先清理依赖关系")
		}
	}

	// 删除属性
	if err := mas.store.ModelAttributes().DeleteModelAttributes(ctx, operator, attrCode); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrModelAttrDelete.Addf("要删除的模型属性 %s 不存在", attrCode.Code)
		}
		return err
	}

	// 记录审计日志
	au := &AuditEvent{store: mas.store}
	if err := au.Generate(ctx, audit.AuditLogEvent, audit.ModelAttributeRes, audit.ActionDelete, operator, attr.ID, attr, nil); err != nil {
		return err
	}
	if err := au.Save(ctx); err != nil {
		return err
	}

	// 这里先保留，在关系管理模块的时候会用到。
	// 判断一下是否有实例在以该模型为父级模型，如果有，说明其他人在引用你，那么你就不允许删除，否则数据就不一致了。
	// subAttr, err := mas.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
	//    "attrs.rel_type":  1,             // 从属关系
	//    "attrs.rel_model": attrCode.Code, // 指向该模型
	// }, bson.M{})
	// if err != nil {
	//    return err
	// }
	// if len(subAttr) > 0 {
	//    return errno.InternalServerError.Add("该模型为其他模型的父级模型，不允许删除")
	// }
	// 4. 判断一下字段的类型，删除常规类型的字段，只需要删除该字段即可，删除关系类型的字段，还需要做数据的清理
	// typeName := attr["type_name"].(string)
	// 5. 如果是非关系字段，或者关系字段已通过校验判断，那么可以直接删除
	// err = mas.store.ModelAttributes().DeleteModelAttributes(ctx, attrCode)
	// if err != nil {
	//    return err
	// }

	// m, err := mas.store.Model().GetModelByCode(ctx, attr.ModelCode)
	// if err != nil {
	//    return err
	// }

	return nil
}

// GetModelAttrMapping 获取模型属性的映射关系
// eg: 举例说明，结构如下
//
//	{
//	    "model1" {
//	        "model1_code": [v1.CommonModelAttribute对象],
//	        "model1_name": [v1.CommonModelAttribute对象],
//	    }
//	}
func GetModelAttrMapping(ctx context.Context, models []string, sf store.Factory) (map[string]map[string]v1.CommonModelAttribute, error) {
	mapping := make(map[string]map[string]v1.CommonModelAttribute)
	for _, model := range models {
		attrs, err := sf.ModelAttributes().
			GetModelAttributesByCustomFilter(
				ctx,
				bson.M{"model_code": model},
				bson.M{"code": 1, "name": 1, "type_name": 1})
		if err != nil {
			return nil, err
		}
		inner := make(map[string]v1.CommonModelAttribute)
		for _, attr := range attrs {
			if _, ok := inner[attr.Code]; !ok {
				inner[attr.Code] = attr
			}
		}
		mapping[model] = inner
	}
	return mapping, nil
}
