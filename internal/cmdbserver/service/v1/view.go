package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"

	"ks-knoc-server/internal/cmdbserver/store"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ViewService interface {
	RackView(ctx context.Context, dataID string) (*v1.RackViewResponse, error)
	RackViewInstance(ctx context.Context, dataID string, page, pageSize int64) (*v1.RackViewInstanceResponse, error)
	HostTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error)
	HostDataView(ctx context.Context, dataID, keyword, filterKey string, filter bson.M, page, pageSize int64, code []string) (*v1.HostViewListResponse, error)
	PhysicalTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error)
	GetNetworkTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error)
	GetStoreroomTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error)
	GetBizTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error)
	GetBizDataView(ctx context.Context) (*v1.BizViewListResponse, error)
	GetRoomInfo(ctx context.Context, operator, roomID string) (*v1.RoomResponse, error)
}

type viewService struct {
	store store.Factory
}

var _ ViewService = (*viewService)(nil)

func newViewService(srv *service) *viewService {
	return &viewService{store: srv.store}
}

func (v *viewService) HostTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error) {
	zap.L().Debug("HostTreeDataView Service Called")
	span, ctx := apm.StartSpan(ctx, "HostTreeDataViewService", "service")
	defer span.End()

	var (
		cityMap       = make(map[string]*v1.TreeNode) // 区域节点Map
		officeMap     = make(map[string]*v1.TreeNode) // 职场节点Map
		idcMap        = make(map[string]*v1.TreeNode) // 机房节点Map
		enginRoomMap  = make(map[string]*v1.TreeNode)
		enginRoomList = make([]*v1.TreeNode, 0)
	)

	enginRoomMap["idc"] = &v1.TreeNode{Children: make([]*v1.TreeNode, 0), NodeType: v1.NodeTypeRoot, Title: "IDC机房", Key: "idc"}
	enginRoomMap["self-built"] = &v1.TreeNode{Children: make([]*v1.TreeNode, 0), NodeType: v1.NodeTypeRoot, Title: "自建机房", Key: "self-built_engine_room"}
	enginRoomMap["cloud"] = &v1.TreeNode{Children: make([]*v1.TreeNode, 0), NodeType: v1.NodeTypeRoot, Title: "云主机", Key: "cloud_host"}

	enginRoomRoot := v1.NewTreeNode()
	enginRoomRoot.Key = "engine_room"
	enginRoomRoot.Title = "机房视图"
	enginRoomRoot.NodeType = v1.NodeTypeRoot

	// 拿区域
	cityList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "city",
	}, nil)
	if err != nil {
		zap.L().Error("获取区域失败", zap.Error(err))
		return nil, err
	}

	for _, city := range cityList {
		cityNode := v1.TreeNode{
			Key:      city.ID,
			Title:    city.Name(),
			NodeType: v1.NodeTypeCity,
			Children: make([]*v1.TreeNode, 0),
		}
		enginRoomRoot.Children = append(enginRoomRoot.Children, &cityNode)
		cityMap[city.ID] = &cityNode
	}

	// 拿职场
	officeList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "office",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	// 被IDC托管的职场(如小米机房，我们也算作职场，小米机房下有xm202，201等)
	hostedByIDCOffice := make([]*v1.TreeNode, 0)
	for _, office := range officeList {
		officeNode := &v1.TreeNode{
			Key:      office.ID,
			Title:    office.Name(),
			NodeType: v1.NodeTypeOffice,
			Children: make([]*v1.TreeNode, 0),
		}
		officeMap[office.ID] = officeNode
		if city, ok := cityMap[office.ParentID]; ok {
			// TODO： 自建机房，后面还要区分是否是云主机
			city.Children = append(city.Children, officeNode)
		} else {
			if by, ok := office.Data["office_host_by_idc"].(string); ok && by == "true" {
				hostedByIDCOffice = append(hostedByIDCOffice, officeNode)
			}
		}
	}

	// 拿机房
	idcList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "idc",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, idc := range idcList {
		idcNode := &v1.TreeNode{
			Key:      idc.ID,
			Title:    idc.Name(),
			NodeType: v1.NodeTypeIDC,
			Children: make([]*v1.TreeNode, 0),
		}

		if i, ok := officeMap[idc.ParentID]; ok {
			i.Children = append(i.Children, idcNode)
			idcMap[idc.ID] = idcNode
		}
	}

	// 拿机柜
	rackList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"model_code": "rack"}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, rack := range rackList {
		rackNode := &v1.TreeNode{
			Key:      rack.ID,
			Title:    rack.Name(),
			NodeType: v1.NodeTypeRack,
			Children: make([]*v1.TreeNode, 0),
		}

		if idc, ok := idcMap[rack.ParentID]; ok {
			idc.Children = append(idc.Children, rackNode)
		}

	}

	// 归并整理机房分类
	enginRoomMap["idc"].Children = append(enginRoomMap["idc"].Children, hostedByIDCOffice...)
	enginRoomMap["self-built"].Children = append(enginRoomMap["self-built"].Children, enginRoomRoot.Children...)

	// 初始化response
	resp := v1.NewHostTreeResponse()
	for _, enginRoom := range enginRoomMap {
		enginRoomList = append(enginRoomList, enginRoom)
	}

	// 增加无归属节点
	enginRoomList = append(enginRoomList, &v1.TreeNode{
		Title:    "无归属",
		Key:      "unattributed",
		NodeType: v1.NodeUnattributed,
		Children: make([]*v1.TreeNode, 0),
	})

	resp.Data = append(resp.Data, enginRoomList...)

	return resp, nil
}

func (v *viewService) PhysicalTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error) {
	zap.L().Debug("PhysicalTreeDataView Service Called")
	span, ctx := apm.StartSpan(ctx, "PhysicalTreeDataView", "service")
	defer span.End()

	physicalRoot := v1.NewTreeNode()
	physicalRoot.Key = "physical"
	physicalRoot.Title = "物理视图"
	physicalRoot.NodeType = v1.NodeTypeRoot

	var (
		cityMap   = make(map[string]*v1.TreeNode)
		officeMap = make(map[string]*v1.TreeNode)
		idcMap    = make(map[string]*v1.TreeNode)
		// rackMap   = make(map[string]*v1.TreeNode)
	)

	// 拿区域
	cityList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "city",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, city := range cityList {
		cityNode := v1.TreeNode{
			Key:      city.ID,
			Title:    city.Data[fmt.Sprintf("%s_name", city.ModelCode)].(string),
			NodeType: v1.NodeTypeCity,
			Children: make([]*v1.TreeNode, 0),
		}

		physicalRoot.Children = append(physicalRoot.Children, &cityNode)
		cityMap[city.ID] = &cityNode
	}

	// 拿职场
	officeList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "office",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, office := range officeList {
		officeNode := &v1.TreeNode{
			Key:      office.ID,
			Title:    office.Data[fmt.Sprintf("%s_name", office.ModelCode)].(string),
			NodeType: v1.NodeTypeOffice,
			Children: make([]*v1.TreeNode, 0),
		}

		if city, ok := cityMap[office.ParentID]; ok {
			city.Children = append(city.Children, officeNode)
			officeMap[office.ID] = officeNode
		}
	}

	// 拿机房
	idcList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "idc",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, idc := range idcList {
		idcNode := &v1.TreeNode{
			Key:      idc.ID,
			Title:    idc.Data[fmt.Sprintf("%s_name", idc.ModelCode)].(string),
			NodeType: v1.NodeTypeIDC,
			Children: make([]*v1.TreeNode, 0),
		}

		if office, ok := officeMap[idc.ParentID]; ok {
			office.Children = append(office.Children, idcNode)
			idcMap[idc.ID] = idcNode
		}
	}

	// 拿机柜
	rackList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"model_code": "rack"}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, rack := range rackList {
		rackNode := v1.TreeNode{
			Key:      rack.ID,
			Title:    rack.Data[fmt.Sprintf("%s_name", rack.ModelCode)].(string),
			NodeType: v1.NodeTypeRack,
			Children: make([]*v1.TreeNode, 0),
		}

		if idc, ok := idcMap[rack.ParentID]; ok {
			idc.Children = append(idc.Children, &rackNode)
		}
	}

	// 给机柜排序
	for _, idc := range idcMap {
		sort.Sort(idc.Children)
	}

	resp := v1.NewHostTreeResponse()
	for _, physical := range physicalRoot.Children {
		resp.Data = append(resp.Data, physical)
	}
	resp.Data = append(resp.Data, &v1.TreeNode{
		Title:    "无归属",
		Key:      "unattributed",
		NodeType: v1.NodeUnattributed,
	})

	return resp, nil
}

func (v *viewService) GetNetworkTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error) {
	zap.L().Debug("GetNetworkTreeDataView Service Called")
	span, ctx := apm.StartSpan(ctx, "GetNetworkTreeDataView", "service")
	defer span.End()

	networkRoot := v1.NewTreeNode()
	networkRoot.Key = utils.UUID()
	networkRoot.Title = "网络视图"
	networkRoot.NodeType = v1.NodeTypeRoot

	var (
		cityMap   = make(map[string]*v1.TreeNode)
		officeMap = make(map[string]*v1.TreeNode)
		idcMap    = make(map[string]*v1.TreeNode)
		// rackMap   = make(map[string]*v1.TreeNode)
	)

	// 拿区域
	cityList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "city",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, city := range cityList {
		cityNode := v1.TreeNode{
			Key:      city.ID,
			Title:    city.Data[fmt.Sprintf("%s_name", city.ModelCode)].(string),
			NodeType: v1.NodeTypeCity,
			Children: make([]*v1.TreeNode, 0),
		}

		networkRoot.Children = append(networkRoot.Children, &cityNode)
		cityMap[city.ID] = &cityNode
	}

	// 拿职场
	officeList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "office",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, office := range officeList {
		officeNode := &v1.TreeNode{
			Key:      office.ID,
			Title:    office.Data[fmt.Sprintf("%s_name", office.ModelCode)].(string),
			NodeType: v1.NodeTypeOffice,
			Children: make([]*v1.TreeNode, 0),
		}

		if city, ok := cityMap[office.ParentID]; ok {
			city.Children = append(city.Children, officeNode)
			officeMap[office.ID] = officeNode
		}
	}

	// 拿机房
	idcList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "idc",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, idc := range idcList {
		idcNode := &v1.TreeNode{
			Key:      idc.ID,
			Title:    idc.Data[fmt.Sprintf("%s_name", idc.ModelCode)].(string),
			NodeType: v1.NodeTypeIDC,
			Children: make([]*v1.TreeNode, 0),
		}

		if office, ok := officeMap[idc.ParentID]; ok {
			office.Children = append(office.Children, idcNode)
			idcMap[idc.ID] = idcNode
		}
	}

	// 拿机柜
	rackList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"model_code": "rack"}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, rack := range rackList {
		rackNode := v1.TreeNode{
			Key:      rack.ID,
			Title:    rack.Data[fmt.Sprintf("%s_name", rack.ModelCode)].(string),
			NodeType: v1.NodeTypeRack,
			Children: make([]*v1.TreeNode, 0),
		}

		if idc, ok := idcMap[rack.ParentID]; ok {
			idc.Children = append(idc.Children, &rackNode)
		}

	}

	resp := v1.NewHostTreeResponse()
	for _, physical := range networkRoot.Children {
		resp.Data = append(resp.Data, physical)
	}

	// 增加无归属节点
	resp.Data = append(resp.Data, &v1.TreeNode{
		Title:    "无归属",
		Key:      "unattributed",
		NodeType: v1.NodeUnattributed,
	})
	return resp, nil
}

func (v *viewService) GetStoreroomTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error) {
	zap.L().Debug("GetStoreroomTreeDataView Service Called")
	span, ctx := apm.StartSpan(ctx, "GetStoreroomTreeDataView", "service")
	defer span.End()

	storeroomRoot := v1.NewTreeNode()
	storeroomRoot.Key = "storeroom"
	storeroomRoot.Title = "库房视图"
	storeroomRoot.NodeType = v1.NodeTypeRoot

	var (
		cityMap   = make(map[string]*v1.TreeNode)
		officeMap = make(map[string]*v1.TreeNode)
	)

	// 拿区域
	cityList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "city",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, city := range cityList {
		cityNode := v1.TreeNode{
			Key:      city.ID,
			Title:    city.Data[fmt.Sprintf("%s_name", city.ModelCode)].(string),
			NodeType: v1.NodeTypeCity,
			Children: make([]*v1.TreeNode, 0),
		}

		storeroomRoot.Children = append(storeroomRoot.Children, &cityNode)
		cityMap[city.ID] = &cityNode
	}

	// 拿职场
	officeList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "office",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, office := range officeList {
		officeNode := &v1.TreeNode{
			Key:      office.ID,
			Title:    office.Data[fmt.Sprintf("%s_name", office.ModelCode)].(string),
			NodeType: v1.NodeTypeOffice,
			Children: make([]*v1.TreeNode, 0),
		}

		if city, ok := cityMap[office.ParentID]; ok {
			city.Children = append(city.Children, officeNode)
			officeMap[office.ID] = officeNode
		}
	}

	// 库房
	storeroomList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"model_code": "rack"}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, storeroom := range storeroomList {
		storeroomNode := v1.TreeNode{
			Key:      storeroom.ID,
			Title:    storeroom.Data[fmt.Sprintf("%s_name", storeroom.ModelCode)].(string),
			NodeType: v1.NodeTypeStoreroom,
			Children: make([]*v1.TreeNode, 0),
		}

		if office, ok := officeMap[storeroom.ParentID]; ok {
			office.Children = append(office.Children, &storeroomNode)
		}

	}

	resp := v1.NewHostTreeResponse()
	for _, storeroom := range storeroomRoot.Children {
		resp.Data = append(resp.Data, storeroom)
	}
	// resp.Data = append(resp.Data, physicalRoot)

	// 增加无归属节点
	resp.Data = append(resp.Data, &v1.TreeNode{
		Title:    "无归属",
		Key:      "unattributed",
		NodeType: v1.NodeUnattributed,
	})

	return resp, nil
}

func (v *viewService) GetBizTreeDataView(ctx context.Context) (*v1.HostTreeResponse, error) {
	zap.L().Debug("GetBizTreeDataView Service Called")
	span, ctx := apm.StartSpan(ctx, "GetBizTreeDataView", "service")
	defer span.End()

	bizRoot := v1.NewTreeNode()
	bizRoot.Key = "service"

	bizRoot.Title = "业务视图"
	bizRoot.NodeType = v1.NodeTypeRoot

	var (
		bizMap = make(map[string]*v1.TreeNode)
		appMap = make(map[string]*v1.TreeNode)
	)

	// 拿业务
	bizList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "biz",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, biz := range bizList {
		bizNode := v1.TreeNode{
			Key:      biz.ID,
			Title:    biz.Data[fmt.Sprintf("%s_name", biz.ModelCode)].(string),
			NodeType: v1.NodeTypeBiz,
			Children: make([]*v1.TreeNode, 0),
		}

		bizRoot.Children = append(bizRoot.Children, &bizNode)
		bizMap[biz.ID] = &bizNode
	}

	// 拿应用
	appList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "app",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, app := range appList {
		appNode := &v1.TreeNode{
			Key:      app.ID,
			Title:    app.Data[fmt.Sprintf("%s_name", app.ModelCode)].(string),
			NodeType: v1.NodeTypeApp,
			Children: make([]*v1.TreeNode, 0),
		}

		if biz, ok := bizMap[app.ParentID]; ok {
			biz.Children = append(biz.Children, appNode)
			appMap[app.ID] = appNode
		}
	}

	// 拿服务
	serviceList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": "service",
	}, nil)
	if err != nil {
		zap.L().Error("GetModelDataByCustomFilter failed", zap.Error(err))
		return nil, err
	}

	for _, service := range serviceList {
		serviceNode := &v1.TreeNode{
			Key:      service.ID,
			Title:    service.Data[fmt.Sprintf("%s_name", service.ModelCode)].(string),
			NodeType: v1.NodeTypeService,
			Children: make([]*v1.TreeNode, 0),
		}

		if service, ok := appMap[service.ParentID]; ok {
			service.Children = append(service.Children, serviceNode)
		}
	}

	resp := v1.NewHostTreeResponse()
	for _, biz := range bizRoot.Children {
		resp.Data = append(resp.Data, biz)
	}

	// 增加无归属节点
	resp.Data = append(resp.Data, &v1.TreeNode{
		Title:    "无归属",
		Key:      "unattributed",
		NodeType: v1.NodeUnattributed,
	})

	return resp, nil
}

func (v *viewService) RackView(ctx context.Context, dataID string) (*v1.RackViewResponse, error) {
	zap.L().Debug("RackView Service Called")
	span, ctx := apm.StartSpan(ctx, "RackViewService", "service")
	defer span.End()

	// 首先初始化一个Response
	resp := v1.NewRackViewResponse()

	// 首先判断要查询的机柜在不在
	d, err := v.store.ModelData().GetModelDataByID(ctx, dataID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("机柜ID %s不存在", dataID)
		}
		return nil, err
	}

	// 看看到底是不是机柜
	r, err := v1.ToRackData(*d)
	if err != nil {
		return nil, err
	}

	if r.ModelCode != v1.DCModelBuiltInRack {
		return nil, errno.ErrDataInvalid.Add("传入的数据不是机柜数据")
	}

	// 填充机柜的高度
	resp.Height = r.Height()

	// 首先查询一下机柜的子数据有哪些
	// 1. 机柜下不一定是服务器，有可能是交换机，路由器，可能是任何的设备
	// 2. 查询有两种方式，可以查询data的ParentID为机柜ID的数据，也可以路径表中parent为该ID的数据，只不过路径表只能查出来ID，还需要查询一下数据
	//    我这里直接选用查询数据本身的方式，避免多查一次数据库
	devices, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"parent_id": dataID}, nil)
	if err != nil {
		return nil, err
	}
	zap.L().Debug("GetRackViewDevicesList", zap.Any("devices", devices))

	// 还有占位设备
	phList := make([]v1.PlaceHolder, 0)
	phListRecord, exist := r.Data.Get(RackPlaceHolder)
	if exist && phListRecord != "" {
		zap.L().Debug("PlaceHolderList", zap.Any("holderListInString", phListRecord))
		if err := json.Unmarshal([]byte(utils.ToString(phListRecord)), &phList); err != nil {
			return nil, err
		}
	}

	// 当没有设备，也没有占位的时候，也需要返回一个空的设备块
	if len(devices) == 0 && len(phList) == 0 {
		rackUnit := v1.NewRackUnit()
		rackUnit.StartU = 1
		rackUnit.EndU = resp.Height
		rackUnit.Height = 42
		rackUnit.Usage = v1.RackDeviceEmpty
		rackUnit.DeviceType = v1.DeviceTypeEmpty

		resp.RackUnits = append(resp.RackUnits, *rackUnit)
		return resp, nil
	}

	// 向机柜中填充设备
	for _, device := range devices {
		rackUnit := v1.NewRackUnit()
		unitDev := v1.Dev(*device)
		startU := unitDev.StartU()

		// 说明压根没设置起始U位，或者设置的起始U位不合法，不会在response中返回
		if startU <= 0 {
			continue
		}

		// 说明设备没有设置高度，不会在response中返回
		if unitDev.Height() == 0 {
			zap.L().Warn("设备没有设置高度，不会在response中返回", zap.String("device_id", unitDev.ID))
			continue
		}

		rackUnit.StartU = unitDev.StartU()
		rackUnit.Height = unitDev.Height()
		rackUnit.EndU = unitDev.EndU()
		if unitDev.Standard() {
			rackUnit.Usage = v1.RackDeviceTypeOccupy
			rackUnit.DeviceType = v1.DeviceTypeStandard
		} else {
			rackUnit.Usage = v1.RackDeviceTypeOccupy
			rackUnit.DeviceType = v1.DeviceTypeNonStandard
		}
		modelMap, err := v.store.Model().GetModelMapping(ctx)
		if err != nil {
			return nil, err
		}

		model, exist := modelMap[unitDev.ModelCode]
		if !exist {
			return nil, errno.ErrModelNotFound.Addf("model_code: %s 不存在", unitDev.ModelCode)
		}

		rackUnit.ModelInfo = v1.ModelInfo{
			Name: model.Name,
			Code: model.Code,
		}

		rackUnit.Device = v1.DeviceInfo{
			DeviceIP:   unitDev.IP(),
			DeviceSn:   unitDev.SN(),
			DeviceIcon: unitDev.Icon(),
			Name:       unitDev.Name(),
		}

		rackUnit.DeviceOrigin = device.Data
		rackUnit.Properties = unitDev.GetProperties(unitDev.ModelCode)
		resp.RackUnits = append(resp.RackUnits, *rackUnit)
	}

	for _, ph := range phList {
		rackUnit := v1.NewRackUnit()
		if ph.StartU() <= 0 {
			continue
		}

		if ph.Height() == 0 {
			zap.L().Warn("占位符没有设置高度，不会在response中返回")
			continue
		}

		rackUnit.StartU = ph.StartU()
		rackUnit.Height = ph.Height()
		rackUnit.EndU = ph.EndU()
		rackUnit.Usage = v1.RackDeviceTypeOccupy
		rackUnit.DeviceType = v1.DeviceTypeDCInfra
		rackUnit.DeviceOrigin = ph
		resp.RackUnits = append(resp.RackUnits, *rackUnit)
	}

	// 填充空余的区域
	unusedRackRange := v.getUnusedRackRange(resp.RackUnits, resp.Height)
	for _, unused := range unusedRackRange {
		rackUnit := v1.NewRackUnit()
		rackUnit.StartU = unused[0]
		rackUnit.EndU = unused[1]
		rackUnit.Height = rackUnit.EndU - rackUnit.StartU + 1
		rackUnit.Usage = v1.RackDeviceEmpty
		rackUnit.DeviceType = v1.DeviceTypeEmpty
		resp.RackUnits = append(resp.RackUnits, *rackUnit)
	}

	// 按照U位从小到大排序
	sort.Sort(resp.RackUnits)

	// debug log
	units := make([]any, 0)
	for _, u := range resp.RackUnits {
		if u.Usage != v1.RackDeviceEmpty {
			units = append(units, [2]int{u.StartU, u.EndU})
		}
	}
	zap.L().Debug("RackUnits", zap.Any("Units", units))

	return resp, nil
}

// getUnusedRackRange 计算机柜上空余的区域
func (v *viewService) getUnusedRackRange(units v1.RackUints, end int) [][]int {
	// 先给所有已经放到机柜上的设备排个序，按照起始U位从小到大排序
	sort.Sort(units)
	zap.L().Debug("getUnusedRackRange", zap.Any("units", units))

	latestEnd := 1

	// 填充空余的区域，创建一个二维数组，每一个二维数组代表着一个设备占用的使用区间。目前需要计算出来除占用之外的空闲区间
	var unusedRackRange = make([][]int, 0)
	for _, unit := range units {
		// 找到当前区间前面的空隙
		if latestEnd < unit.StartU {
			unusedRackRange = append(unusedRackRange, []int{latestEnd, unit.StartU - 1})
		}

		// 更新上一个区间的结束位置, 这里一定要判等，否则会出现，空余和实际的占用重叠的情况
		if latestEnd <= unit.EndU {
			// 这里因为机柜的Start和Stop的U位是一个闭区间，所以下一个start要在上一个end的基础上+1
			latestEnd = unit.EndU + 1
		}
	}

	// 检查最后一个区间之后是否还有空隙
	if latestEnd <= end {
		unusedRackRange = append(unusedRackRange, []int{latestEnd, end})
	}

	return unusedRackRange
}

func (v *viewService) getHostViewModel(ctx context.Context, dataType string) []v1.SimpleModel {
	modelMap, _ := v.store.Model().GetModelMapping(ctx)
	models := make([]v1.SimpleModel, 0)

	switch dataType {
	case "default":
		models = append(models, v1.SimpleModel{Code: v1.HostModelBuiltInServer, Name: modelMap[v1.HostModelBuiltInServer].Name})
		models = append(models, v1.SimpleModel{Code: v1.HostModelBuiltInVirtualMachine, Name: modelMap[v1.HostModelBuiltInVirtualMachine].Name})
	case "cloud_server":
		models = append(models, v1.SimpleModel{Code: v1.HostModelBuiltInCloudServer, Name: modelMap[v1.HostModelBuiltInVirtualMachine].Name})
	}

	return models
}

func (v *viewService) HostDataView(ctx context.Context, dataID, keyword, filterKey string, dataFilter bson.M, page, pageSize int64, code []string) (*v1.HostViewListResponse, error) {
	zap.L().Debug("HostDataView Service Called")
	span, ctx := apm.StartSpan(ctx, "HostDataViewService", "service")
	defer span.End()

	var (
		// 初始化过滤器中的model_code
		modelCodeFilter = make([]string, 0)
		// 主机视图固定查看的模型
		hostViewBuiltInModel = []string{v1.HostModelBuiltInServer, v1.HostModelBuiltInVirtualMachine}
		// 构造一个Response响应
		resp = v1.NewHostViewListResponse()
		// 初始化数据列表
		dataList        = make([]v1.ModelData, 0)
		hosts           = make([]*v1.OneHost, 0)
		dataCount int64 = 0
		err       error
	)

	// 默认可以筛选的模型就只有两个，要不是物理机，要不就是虚拟机，目前不会有其他的。
	resp.Models = append(resp.Models, v.getHostViewModel(ctx, "default")...)

	// 提前判断一些比较特殊的情况，能少查一次库就少查一次。
	if dataID == "unattributed" {
		// 代表的是无归属的机器
		if resp, err = v.getUnAttributedDevice(ctx, dataFilter, page, pageSize); err != nil {
			return nil, err
		}
		return resp, nil
	} else if dataID == "cloud_server" {
		// 代表的是云主机
		if resp, err = v.getCloudServer(ctx, dataFilter, page, pageSize); err != nil {
			return nil, err
		}
		return resp, nil
	}

	// 首先看看dataID对应的数据是否存在
	if _, err = v.store.ModelData().GetModelDataByID(ctx, dataID); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("data_id: %s异常，不存在对应的数据", dataID)
		}
		return nil, err
	}

	// 首先检测一下传递的这个模型的code是不是有问题的，目前主机视图中只能接受server或者vm
	if !utils.IsArraySubSet(hostViewBuiltInModel, code) {
		errMsg := fmt.Sprintf("code 不可以为 %s 和 %s 之外的值", v1.HostModelBuiltInServer,
			v1.HostModelBuiltInVirtualMachine)
		zap.L().Error(errMsg)
		return nil, errno.ErrParameterInvalid.Add(errMsg)
	}

	// 构造一个filter过滤器，从路径表中查找数据。
	pathFilter := bson.M{"parent_id": dataID}

	// 补充pathFilter关于model_code的筛选条件
	if len(code) > 0 {
		if len(code) == 1 {
			modelCodeFilter = append(modelCodeFilter, code[0])
		} else {
			modelCodeFilter = code
		}
		pathFilter["child_model_code"] = bson.M{"$in": modelCodeFilter}
	} else {
		// 如果说用户默认没有传递的话，默认我们就搜索物理机，即模型标识为server的数据
		pathFilter["child_model_code"] = bson.M{"$in": v1.HostModelBuiltInServer}
	}

	// 查询parentID为传递过来的DataID以及孩子的模型为modelCodeFilter的路径数据，查到的路径数据都是建立了从属关系的数据
	pathList, err := v.store.View().GetHostDataView(ctx, pathFilter)
	if err != nil {
		return nil, err
	}

	// 获取到pathList中的子这一端的部分数据，这些数据就是我们需要的数据
	subIDs := pathList.Descendants()
	if len(subIDs) == 0 {
		return resp, nil
	}

	// dataFilter中需要根据传入的模型以及keyword补充过滤条件
	if len(code) == 1 {
		// 通过path表可以拿到对应一个路径树的'叶子节点'对应的资源，将这个条件追加到数据的过滤器中
		dataFilter["_id"] = bson.M{"$in": subIDs}

		// 当code只有一个的时候，属性字段固定，我们就可以按照字段进行模糊搜索
		if filterKey != "" && keyword != "" {
			dataFilter["data."+filterKey] = bson.M{
				"$regex":   ".*" + keyword + ".*",
				"$options": "i", // 匹配包含关键字的字符串，不区分大小写
			}
		}

		if dataList, dataCount, err = v.store.ModelData().GetModelDataList(ctx, dataFilter, page, pageSize, ""); err != nil {
			return nil, err
		}
	} else {
		// 当code大于一个的时候，字段属性就不固定了，这个时候，就需要基于全局进行模糊搜索，而不是字段
		pipeline := mongo.Pipeline{
			// 第一个stage，先把对应的树下的数据都查出来
			bson.D{
				{
					"$match", bson.D{
						{
							"_id", bson.D{
								{
									"$in", subIDs,
								},
							},
						},
					},
				},
			},
			// 第二个stage，将data字段展开，展开成一个数组，对应的k为dataKeys，v为data的值
			bson.D{{"$addFields", bson.D{{"dataKeys", bson.D{{"$objectToArray", "$data"}}}}}},
			// 第三个stage，将dataKeys.v的值进行模糊搜索，其实也就是对data字段值进行模糊搜索的结果
			bson.D{{
				"$match", bson.D{
					{
						"dataKeys.v", bson.D{
							{"$regex", ".*" + keyword + ".*"},
							{"$options", "i"},
						},
					},
				},
			}},
			// 第四个stage，要保存一下对应文档的数量
			bson.D{{"$addFields", bson.D{{"total", "$count"}}}},
			// 第五个stage，这个时候我们要再去做分页
			bson.D{{"$skip", (page - 1) * pageSize}},
			bson.D{{"$limit", pageSize}},
		}
		if dataList, err = v.store.ModelData().GetDataListByPipeLine(ctx, pipeline); err != nil {
			return nil, err
		}
	}

	// 加入了filter过滤以后，有可能有路径，但是regex不匹配，也会导致查询不到数据
	// 当然还有一种可能就是数据库中存在脏数据，也会导致dataList=0
	if len(dataList) == 0 {
		resp.Total = 0
		resp.Pages = 1
		resp.PageSize = pageSize
		resp.CurrentPage = page
		return resp, nil
	}

	// 设置页码和分页信息
	resp.PageSize = pageSize
	resp.CurrentPage = page

	// 根据modelCodeFilter来填充数据
	// 1. 如果查询的是物理机的数据，那么就需要把每一台物理机上的虚拟机也查出来
	// 2. 如果查询的是虚拟机的数据，那么就需要把每一台虚拟机所在的物理机也查出来
	if len(modelCodeFilter) != 1 {
		total := utils.ToInt64(len(dataList))
		resp.Total = total
		resp.Pages = (total / pageSize) + 1

		// 这个时候拿到的数据应该既有物理机的，也有虚拟机的
		myhost := dataList
		fmt.Println(myhost)
	} else {
		modelCode := modelCodeFilter[0]
		if modelCode == v1.HostModelBuiltInServer {
			// 更新resp中的数据
			resp.Total = dataCount
			resp.Pages = (dataCount / pageSize) + 1

			// 当model是物理机的时候，这个dlist拿到的就是server的数据的对象，我们需要做的是为每台物理机，填补每一台物理机上的虚拟机
			for _, d := range dataList {
				host := v1.NewOneHost()
				host.ModelData = d
				vmList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
					"parent_id":  d.ID,
					"model_code": v1.HostModelBuiltInVirtualMachine,
				}, nil)
				if err != nil {
					return nil, err
				}

				if len(vmList) > 0 {
					host.HasChildren = true
					host.Children = append(host.Children, vmList...)
				}

				hosts = append(hosts, host)
			}
		} else if modelCode == v1.HostModelBuiltInVirtualMachine {
			// 更新resp中的数据
			// 2024-04-18 按照新沟通的，仅仅是虚拟机的搜索，不再返回嵌套关系的数据
			resp.Total = dataCount
			resp.Pages = (dataCount / pageSize) + 1

			// 当model是虚拟机的时候，这个dataList拿到的就是虚拟机的数据的对象
			hostParentMap := make(map[string]*v1.OneHost)
			for _, d := range dataList {
				host := v1.NewOneHost()

				// 看看hostParentMap中是否已经存了对应的parent信息，如果没有存，就存一下，如果存了，就直接用
				// 这里解决的问题是，属于同一台物理机的虚拟机要合并展示，而不要展示多条宿主机的信息。
				if _, ok := hostParentMap[d.ID]; !ok {
					hostParentMap[d.ID] = host
					host.ModelData = d
					host.HasChildren = false
					hosts = append(hosts, host)
				}
			}
		}
	}

	// if len(modelCodeFilter) == 1 && modelCodeFilter[0] == v1.HostModelBuiltInServer {
	// 	// 获取子代数据的数量
	// 	// TODO: 这里其实使用子代数据统计数量是不准确的。
	// 	// total, err := v.store.ModelData().GetDataPathCount(ctx,
	// 	// 	bson.M{"parent_id": dataID, "child_model_code": v1.HostModelBuiltInServer})
	// 	// if err != nil {
	// 	// 	return nil, err
	// 	// }
	//
	// } else if len(modelCodeFilter) == 1 && modelCodeFilter[0] == v1.HostModelBuiltInVirtualMachine {
	// 	// 获取子代数据的数量，这个时候实际的数量应该以vm的数量为准，因为搜的是vm
	// 	// total, err := v.store.ModelData().GetDataPathCount(ctx, bson.M{"parent_id": dataID, "child_model_code": v1.HostModelBuiltInVirtualMachine})
	// 	// if err != nil {
	// 	// 	return nil, err
	// 	// }
	//
	// } else {
	// 	// 都没选和两个都选，其实效果都是一样的
	//
	// }

	// if modelCodeFilter == v1.HostModelBuiltInServer {
	// 	// 获取子代数据的数量
	// 	// TODO: 这里其实使用子代数据统计数量是不准确的。
	// 	// total, err := v.store.ModelData().GetDataPathCount(ctx,
	// 	// 	bson.M{"parent_id": dataID, "child_model_code": v1.HostModelBuiltInServer})
	// 	// if err != nil {
	// 	// 	return nil, err
	// 	// }
	//
	// 	// 更新resp中的数据
	// 	total := int64(len(dataList))
	// 	resp.Total = total
	// 	resp.Pages = (total / pageSize) + 1
	//
	// 	// 当model是物理机的时候，这个dlist拿到的就是server的数据的对象，我们需要做的是为每台物理机，填补每一台物理机上的虚拟机
	// 	for _, d := range dataList {
	// 		host := v1.NewOneHost()
	// 		host.ModelData = *d
	// 		vmList, err := v.store.ModelData().GetModelDataByCustomFilter(
	// 			ctx, bson.M{
	// 				"parent_id":  d.ID,
	// 				"model_code": v1.HostModelBuiltInVirtualMachine},
	// 		)
	// 		if err != nil {
	// 			return nil, err
	// 		}
	//
	// 		if len(vmList) > 0 {
	// 			host.HasChildren = true
	// 			host.Children = append(host.Children, vmList...)
	// 		}
	//
	// 		hosts = append(hosts, host)
	// 	}
	// } else if modelCodeFilter == v1.HostModelBuiltInVirtualMachine {
	//
	//
	// }

	resp.Data = hosts

	return resp, nil
}

func (v *viewService) RackViewInstance(ctx context.Context, dataID string, page, pageSize int64) (*v1.RackViewInstanceResponse, error) {
	zap.L().Debug("RackViewInstance Service Called")
	span, ctx := apm.StartSpan(ctx, "RackViewInstanceService", "service")
	defer span.End()

	// 首先看看机柜在不在，有可能传递的ID所对应的机柜已经被删除了
	d, err := v.store.ModelData().GetModelDataByID(ctx, dataID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("机柜ID %s不存在", dataID)
		}
		return nil, err
	}

	// 看看这个data是不是机柜
	_, err = v1.ToRackData(*d)
	if err != nil {
		return nil, err
	}

	// 查看所有和机柜关联的device设备
	modelDataList, total, err := v.store.ModelData().GetModelDataList(ctx, bson.M{"parent_id": dataID}, page, pageSize, "")
	if err != nil {
		return nil, err
	}

	resp := v1.NewRackViewInstanceResponse()
	resp.Total = total
	resp.Pages = (total / pageSize) + 1
	resp.PageSize = pageSize
	resp.CurrentPage = page

	// 填充数据, 首先查询一下这个设备的模型信息
	modelMap, err := v.store.Model().GetModelMapping(ctx)
	if err != nil {
		return nil, err
	}
	for _, data := range modelDataList {
		model, exist := modelMap[data.ModelCode]
		if !exist {
			return nil, errno.ErrModelNotFound.Addf("model_code: %s 不存在", d.ModelCode)
		}

		dev, _ := v1.ToDev(data)

		// 填充数据
		resp.Items = append(resp.Items, v1.RackInstance{
			DeviceID:  data.ID,
			ModelName: model.Name,
			Name:      dev.Name(),
			SN:        dev.SN(),
			StartU:    dev.StartU(),
			UID:       dev.UID(),
			IP:        dev.IP(),
			Height:    dev.Height(),
			CreateAt:  dev.CreateAt,
			UpdateAt:  dev.UpdateAt,
		})
	}

	// 排一下序，按照创建时间从旧到新的顺序
	sort.Sort(resp.Items)

	return resp, nil
}

func (v *viewService) GetRoomInfo(ctx context.Context, operator, roomID string) (*v1.RoomResponse, error) {
	zap.L().Debug("GetRoomInfo Service Called")
	span, ctx := apm.StartSpan(ctx, "GetRoomInfoService", "service")
	defer span.End()

	// 首先根据用户提交的roomID查询一下数据，到底是不是机房的数据
	room, err := v.store.ModelData().GetModelDataByID(ctx, roomID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errno.ErrDataNotExists.Addf("机房ID %s不存在", roomID)
		}
		return nil, err
	}

	if room.ModelCode != "idc" {
		return nil, errno.ErrDataInvalid.Addf("机房ID %s不是机房", roomID)
	}

	// 如果是机房的话，拿一下机房的数据布局
	layout := ""
	layoutString, exist := room.Data["idc_layout"]
	if !exist {
		zap.L().Warn("机房没有数据布局", zap.String("room_id", roomID), zap.String("room_name", room.Name()))
		layout = "1,1"
	} else {
		layout = utils.ToString(layoutString)
		if layout == "" {
			zap.L().Warn("机房没有数据布局", zap.String("room_id", roomID), zap.String("room_name", room.Name()))
			layout = "1,1"
		}
	}

	l := strings.Split(layout, ",")
	if len(l) != 2 {
		zap.L().Error("机房布局配置不合法", zap.String("layout", layout), zap.Any("layout", l))
		return nil, errno.ErrDataInvalid.Addf("机房ID %s 布局配置不合法", roomID)
	}

	x := utils.ToInt(l[0])
	y := utils.ToInt(l[1])

	// 获取设备的数量
	filter := bson.M{"parent_id": roomID, "child_model_code": bson.M{"$ne": "rack"}}
	cnt, err := v.store.ModelData().GetDataPathCount(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 先构造一个假的数据吧，下面的电力，设备总数，空置率都是假的，先用假数据塞上。
	newRoomInfo := v1.NewRoom(roomID, x, y)
	newRoomInfo.RoomProperty.DeviceTotal = int(cnt)
	newRoomInfo.RoomProperty.VacancyRate = 20
	newRoomInfo.RoomProperty.Power = 3000

	// 拿一下所有的机柜数据
	rackList, err := v.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{"model_code": "rack", "parent_id": roomID}, nil)
	if err != nil {
		return nil, err
	}

	// 设置一下机房的机柜总数
	newRoomInfo.RoomProperty.RackTotal = len(rackList)

	// 遍历每一个机柜填充位置
	for _, rack := range rackList {
		r := v1.NewRackInfo()
		r.EleType = v1.EleTypeRack

		rackEle, err := v1.ToRackData(*rack)
		if err != nil {
			return nil, err
		}

		r.RackID = rackEle.ID
		r.RackName = rackEle.RackNumber()
		r.Height = rackEle.Height()
		r.VacancyRate = rackEle.VacancyRate()
		r.Power = rackEle.Power()

		rx, ry, err := rackEle.Position()
		if err != nil {
			return nil, err
		}

		// 设置一下机柜的位置
		err = newRoomInfo.Layout.SetBox(rx-1, ry-1, *r)
		if err != nil {
			zap.L().Error("SetBox failed", zap.Error(err))
			return nil, err
		}
	}

	roomInfoResp := v1.NewRoomResponse()

	roomInfoResp.Room = *room
	roomInfoResp.RoomProperty = newRoomInfo.RoomProperty
	roomInfoResp.Layout = newRoomInfo.Layout

	return roomInfoResp, nil
}

func (v *viewService) GetBizDataView(ctx context.Context) (*v1.BizViewListResponse, error) {
	return nil, nil
}
