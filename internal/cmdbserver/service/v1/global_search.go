package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"

	"github.com/olivere/elastic/v7"
	"github.com/xuri/excelize/v2"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"
)

const (
	MetaDataPrefix         = "meta_data."
	MaxColWidth    float64 = 25
)

// GlobalSearchFuzzy 模糊搜索
func (mds *modelDataService) GlobalSearchFuzzy(ctx context.Context, req *apiv1.GlobalSearchFuzzyRequest, pageNumber, pageSizeNumber int64) (*apiv1.GlobalSearchResponse, error) {
	zap.L().Debug("GlobalSearchFuzzy Service called")
	span, ctx := apm.StartSpan(ctx, "GlobalSearchFuzzyService", "service")
	defer span.End()

	var (
		err error
		// 初始化一个FuzzySearchResponse
		fuzzySearchResponse = &apiv1.GlobalSearchResponse{}
	)

	// 去除关键字两侧的空格
	kw := strings.TrimSpace(req.Keyword)

	// case1: 切换分组，默认选中所有模型, 此时仅传递模型分组code即可
	// case2: 切换模型，可以选择多个模型
	modelGroupFilter := make([]any, 0)
	modelFilter := make([]any, 0)

	if req.ModelGroupCode != "" {
		modelGroupFilter = append(modelGroupFilter, req.ModelGroupCode)
	}

	// 构造一个模型的map，用于后面的模型过滤
	modelMapping, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		return fuzzySearchResponse, err
	}

	// 如果说传递了模型的话
	if len(req.ModelCode) > 0 {
		if len(modelGroupFilter) <= 0 {
			return fuzzySearchResponse, errno.ErrFuzzySearchModelGroupCodeEmpty.Add("当模型不为空时，模糊搜索模型组code不能为空")
		}
		for _, model := range req.ModelCode {
			if _, ok := modelMapping[model]; !ok {
				return fuzzySearchResponse, errno.ErrModelCodeNotExists.Addf("模型code %s 不存在", model)
			}
			if modelMapping[model].ModelGroup != req.ModelGroupCode {
				return fuzzySearchResponse, errno.ErrModelNotBelongToModelGroup.Addf("模型 %s 不属于模型分组 %s, 或分组 %s 不存在", model, req.ModelGroupCode, req.ModelGroupCode)
			}
			modelFilter = append(modelFilter, model)
		}
	}

	// 聚合搜索，返回聚合结果以及匹配到的模型code列表
	aggregationResult, modelList, err := mds.SearchAggregationInfo(ctx, []string{"info"}, kw)
	if err != nil {
		return fuzzySearchResponse, err
	}

	// 当没有任何筛选条件的时候，默认选中一个第一个模型分组
	var defaultSelectModelGroup string
	if len(aggregationResult) == 0 {
		zap.L().Error("聚合搜索结果为空，无法获取默认模型分组")
		return fuzzySearchResponse, nil
	}

	// 如果可以获取到聚合结果，首先更新聚合结果
	fuzzySearchResponse.ModelGroups = aggregationResult

	defaultSelectModelGroup = aggregationResult[0].Code

	// 获取dataList
	dataResult, total, _, err := mds.SearchDataInfo(ctx, []string{"info"},
		kw, defaultSelectModelGroup, modelGroupFilter, modelFilter, pageNumber, pageSizeNumber)
	if err != nil {
		zap.L().Error(err.Error())
		return fuzzySearchResponse, err
	}

	// 构造返回的数据
	pages := (total / pageSizeNumber) + 1
	dataResponse := make([]apiv1.DataResponse, 0)

	// 获取模型属性的映射
	modelAttrMapping, err := GetModelAttrMapping(ctx, modelList, mds.store)
	if err != nil {
		return fuzzySearchResponse, err
	}

	// 拼凑返回的数据
	for _, v := range dataResult {
		dataInfo := func() string {
			infoString := ""
			for k := range v.Data {
				attrMap := modelAttrMapping[v.ModelCode]
				if _, exist := attrMap[k]; !exist {
					continue
				}
				if attrMap[k].Code == fmt.Sprintf("%s_code", v.ModelCode) {
					continue
				}
				infoString += fmt.Sprintf("%s: %s; ", attrMap[k].Name, utils.ToString(v.Data[k]))
			}
			return infoString
		}()

		title := v.Name()
		dataResponse = append(dataResponse, apiv1.DataResponse{
			ModelData: *v,
			Info:      dataInfo,
			Title:     title,
		})
	}

	fuzzySearchResponse.Data = apiv1.SearchResultDataBlock{
		PageSize:    pageSizeNumber,
		Pages:       pages,
		CurrentPage: pageNumber,
		Total:       total,
		Data:        dataResponse,
	}

	return fuzzySearchResponse, nil
}

// GlobalSearchBatch 批量搜索
func (mds *modelDataService) GlobalSearchBatch(ctx context.Context, req *apiv1.GlobalSearchBatchRequest, pageNumber, pageSizeNumber int64) (*apiv1.GlobalBatchSearchResponse, error) {
	zap.L().Debug("GlobalSearchBatch Service called")
	span, ctx := apm.StartSpan(ctx, "GlobalSearchBatchService", "service")
	defer span.End()

	// 集中对 keywords 去除特殊字符
	keyMap := make(mapdata.MapData)
	for _, v := range req.Keywords {
		// 过滤掉空的字符串
		kw := strings.TrimSpace(v)
		if kw == "" {
			continue
		}
		// keywords = append(keywords, regexp.QuoteMeta(kw))
		// 暂时先把QuoteMeta方法去掉，因为当输入ip地址的时候，会把.变成\. 导致匹配失败
		// 第一步先去重
		if _, ok := keyMap[kw]; !ok {
			keyMap[kw] = 1
		} else {
			keyMap[kw] = keyMap[kw].(int) + 1
		}
	}

	// termQuery查询接收的都是interface类型的数据，所以我们要做一道转换
	keywords := make([]any, 0)
	for k := range keyMap {
		keywords = append(keywords, k)
	}

	// 搜索主要分为两个部分，一个是要获取聚合的信息，另外一部分则是要获取真实的数据信息
	aggBoolQuery := elastic.NewBoolQuery()
	searchField := MetaDataPrefix + req.SearchField + ".keyword"
	zap.L().Debug("searchField: " + searchField)
	aggTermQuery := elastic.NewTermsQuery(searchField, keywords...)
	aggBoolQuery.Filter(aggTermQuery)

	// part1: 获取模型和模型分组的聚合信息
	aggQ := elastic.NewTermsAggregation().Field("meta.model_code.keyword").Size(1000)
	queryAggregationInfo := elastic.NewSearchSource().Aggregation("model_code_agg", aggQ).Query(aggBoolQuery).Size(0)
	if err := SaveESQueryTOLog(queryAggregationInfo); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	aggInfoResult, err := mds.store.ModelData().QuerySource(ctx, queryAggregationInfo)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	// 获取集合信息，其中Aggregations是一个map[string]json.RawMessage，所以拿到默认得就是bytes
	aggInfo := aggInfoResult.Aggregations["model_code_agg"]
	var agg AggregationInfo
	if err := json.Unmarshal(aggInfo, &agg); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	nest := make(map[string]*apiv1.ModelWithNested)

	modelList := make([]string, 0)
	for _, bucket := range agg.Buckets {
		modelList = append(modelList, bucket.Key)
		var m *v1.Model
		m, err = mds.store.Model().GetModelByCode(ctx, bucket.Key)
		if err != nil {
			zap.L().Error("模糊搜索查询模型失败", zap.Error(err))
			continue
		}
		// 更新搜索到的数据的数量
		m.Total = bucket.DocCount

		// 查询这个模型所属的模型分组
		var modelGroup *v1.ModelGroup
		modelGroup, err = mds.store.ModelGroup().GetModelGroup(ctx, m.ModelGroup)
		if err != nil {
			zap.L().Error("模糊搜索查询模型分组失败", zap.Error(err))
		}

		// 根据查询到的model和model_group构建嵌套结构
		if _, ok := nest[modelGroup.Code]; !ok {
			children := make([]v1.Model, 0)
			children = append(children, *m)
			nest[modelGroup.Code] = &apiv1.ModelWithNested{
				ID:       modelGroup.ID,
				Code:     modelGroup.Code,
				Name:     modelGroup.Name,
				CreateAt: modelGroup.CreateAt,
				Children: children,
			}
		} else {
			nest[modelGroup.Code].Children = append(nest[modelGroup.Code].Children, *m)
		}
	}

	// 针对嵌套好的模型分组再包一层，用于排序
	nestedModelList := make(apiv1.NestedModelList, 0)
	for _, v := range nest {
		nestedModelList = append(nestedModelList, *v)
	}
	// 针对模型分组排一下序，当前排序的规则是按照create_at进行排序的
	sort.Sort(nestedModelList)

	// part2: 取出第一个modelGroup，作为默认的模型分组
	defaultModelGroup := ""
	if len(nestedModelList) > 0 {
		defaultModelGroup = nestedModelList[0].Code
	}

	// part3: 获取数据
	dataQuery := elastic.NewBoolQuery()
	dataTermQuery := elastic.NewTermsQuery(searchField, keywords...)
	dataQuery.Filter(dataTermQuery)

	if len(req.ModelCode) > 0 {
		modelFilter := make([]interface{}, 0)
		for _, v := range req.ModelCode {
			modelFilter = append(modelFilter, v)
		}
		dataQuery.Must(elastic.NewTermsQuery("meta.model_code.keyword", modelFilter...))
		dataQuery.Must(elastic.NewTermsQuery("meta.model_group_code.keyword", req.ModelGroupCode))
	} else if req.ModelGroupCode != "" {
		dataQuery.Must(elastic.NewTermsQuery("meta.model_group_code.keyword", req.ModelGroupCode))
	} else {
		dataQuery.Must(elastic.NewMatchQuery("meta.model_group_code.keyword", defaultModelGroup))
	}

	queryData := elastic.NewSearchSource().
		Query(dataQuery).
		Size(int(pageSizeNumber)).
		From(int((pageNumber - 1) * pageSizeNumber))
	if err := SaveESQueryTOLog(queryData); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	dataResult, err := mds.store.ModelData().QuerySource(ctx, queryData)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	dataList := make([]*v1.ModelData, 0)
	for _, hit := range dataResult.Hits.Hits {
		var data v1.ModelData
		err = json.Unmarshal(hit.Source, &data)
		if err != nil {
			zap.L().Error(err.Error())
			continue
		}
		dataList = append(dataList, &data)
		// v, ok := data.MetaData[req.SearchField]
		// if !ok {
		// 	return nil, errors.New("使用关键字查询到了不包含关键字的数据，请管理员检查数据是否正常")
		// }
		// if utils.ToString(v) != "" {
		// 	delete(notMatchedMap, utils.ToString(v))
		// }
	}

	total := dataResult.Hits.TotalHits.Value
	pages := (total / pageSizeNumber) + 1

	// 获取模型属性的映射
	modelAttrMapping, err := GetModelAttrMapping(ctx, modelList, mds.store)
	if err != nil {
		return nil, err
	}

	dataResponse := make([]apiv1.DataResponse, 0)
	for _, v := range dataList {
		dataInfo := func() string {
			infoString := ""
			for k := range v.Data {
				attrMap := modelAttrMapping[v.ModelCode]
				if _, exist := attrMap[k]; !exist {
					continue
				}
				if attrMap[k].Code == fmt.Sprintf("%s_code", v.ModelCode) {
					continue
				}
				infoString += fmt.Sprintf("%s: %s; ", attrMap[k].Name, utils.ToString(v.Data[k]))
			}
			return infoString
		}()
		title := v.Name()
		dataResponse = append(dataResponse, apiv1.DataResponse{
			ModelData: *v,
			Info:      dataInfo,
			Title:     title,
		})
	}

	resp := apiv1.NewGlobalBatchSearchResponse()
	resp.ModelGroups = nestedModelList
	resp.Data = apiv1.SearchResultDataBlock{
		PageSize:    pageSizeNumber,
		Pages:       pages,
		CurrentPage: pageNumber,
		Total:       total,
		Data:        dataResponse,
	}

	// Part4: 判断哪些关键字没有匹配到, 测试的请求示例
	// {
	//  "aggregations": {
	//    "keywords": {
	//      "filters": {
	//        "filters": {
	//          "Cisco交换机": {
	//            "term": {
	//              "meta_data.universal_name.keyword": "Cisco交换机"
	//            }
	//          },
	//          "H3C交换机": {
	//            "term": {
	//              "meta_data.universal_name.keyword": "H3C交换机"
	//            }
	//          },
	//          "交换机-其他": {
	//            "term": {
	//              "meta_data.universal_name.keyword": "交换机-其他"
	//            }
	//          }
	//        }
	//      }
	//    }
	//  },
	//  "size": 0
	// }
	filtersAgg := elastic.NewFiltersAggregation()
	for _, v := range keywords {
		filtersAgg.FilterWithName(utils.ToString(v), elastic.NewTermQuery(MetaDataPrefix+req.SearchField+".keyword", v))
	}
	keyWordFilterQuery := elastic.NewSearchSource().Size(0).Aggregation("keywords", filtersAgg)
	keyWordFilterResult, err := mds.store.ModelData().QuerySource(ctx, keyWordFilterQuery)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	// ExampleData:
	// {
	//     "buckets":{
	//         "Cisco交换机":{"doc_count":11},
	//         "H3C交换机":{"doc_count":17},
	//         "交换机-其他":{"doc_count":3}
	//     }
	// }
	raw := keyWordFilterResult.Aggregations["keywords"]
	kwAggMap := make(mapdata.MapData)
	if err := json.Unmarshal(raw, &kwAggMap); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	filterHits, ok := kwAggMap["buckets"].(map[string]any)
	if !ok {
		zap.L().Error("解析聚合后的关键字命中结果失败")
		return nil, errno.InternalServerError.Add("解析聚合后的关键字命中结果失败")
	}

	notMatchedKw := make([]string, 0)
	for k, cntInfo := range filterHits {
		cnt, ok := cntInfo.(map[string]any)
		if !ok {
			zap.L().Error("解析聚合后的关键字命中结果失败")
			return nil, errors.New("解析聚合后的关键字命中结果失败")
		}
		// 当匹配数为0的时候证明没有匹配上
		if utils.ToInt(cnt["doc_count"]) == 0 {
			notMatchedKw = append(notMatchedKw, k)
		}
	}

	// 无法匹配上的数据, 初始化一个容器进行保存
	notMatched := make([]string, 0)
	// 遍历notMatchedMap，如果某个key的value为1，那么添加一次，如果value大于1，那么就有几次添加几次
	// 按照产品要求，即使一个值被传递多次，那么每一次都是独立的值，那么这样的话，
	for _, k := range notMatchedKw {
		times := keyMap[k].(int)
		if times == 1 {
			notMatched = append(notMatched, k)
		} else {
			for i := 0; i < times; i++ {
				notMatched = append(notMatched, k)
			}
		}
	}

	resp.NotMatched = notMatched

	return resp, nil
}

// buildCombineSearchFilter 构建组合搜索的过滤条件
// @param req 组合搜索请求
// @return filter 过滤条件
// @return modelCodeFilter 模型code过滤条件
// @return error 错误
func (mds *modelDataService) buildCombineSearchFilter(ctx context.Context, req *apiv1.GlobalSearchCombineRequest) (bson.M, []string, error) {
	// 构建过滤条件
	filter := make(bson.M)

	// 获取模型映射
	modelMapping, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, nil, err
	}

	// 把组合搜索的条件放到一个map里，用于查询的时候快速定位查询条件
	conditions := make(map[string][]string)
	for _, v := range req.CombineFields {
		if _, ok := conditions[v.FieldName]; !ok {
			conditions[v.FieldName] = v.FieldValue
		}
	}

	// 获取到device_type, device_type其实对应的就是模型，
	// device_type往往就限定了你要选择的模型已经被固定了，我就不关心用户传递过来的model_code和model_group_code了。
	// 这里要做一下判断，用户是有可能不选择设备类型的。但是用户如果传递了，那么这个优先级就是最高的。
	// 就不用管什么模型分组和模型了
	modelCodeFilter := make([]string, 0)
	deviceType, deviceTypePassed := conditions["device_type"]
	// 如果用户传递了的device_type
	if deviceTypePassed {
		// 如果用户传递了设备类型，那么这个设备类型就是确定的，以用户传递的的设备类型为最高优先级
		if len(deviceType) > 0 {
			filter["model_code"] = bson.M{"$in": deviceType}
			modelCodeFilter = append(modelCodeFilter, deviceType...)
			// 删除掉device_type，因为已经用不到了
			delete(conditions, "device_type")
		} else {
			// 如果说没有传递deviceType的话，这个时候就要用户传递的model_code来进行判断了
			if len(req.ModelCode) > 0 {
				// 其实传递了model_code就已经限定了查询的范围，仍然要求传递model_group_code的原因是为了判断传递的模型和模型分组是不是相匹配的
				// 如果指定了模型的code，那么必然也要执行模型分组，目前搜索做不到跨分组模型搜索
				if _, err := mds.store.ModelGroup().GetModelGroup(ctx, req.ModelGroupCode); err != nil {
					zap.L().Error(err.Error())
					return nil, nil, err
				}
				// 判断一下传递的model_code是否在模型分组中
				for _, v := range req.ModelCode {
					if _, ok := modelMapping[v]; !ok {
						zap.L().Error("模型code不存在", zap.String("model_code", v))
						return nil, nil, errors.New("模型code不存在")
					}
					modelObj := modelMapping[v]
					if modelObj.ModelGroup != req.ModelGroupCode {
						zap.L().Error("模型code和模型分组不匹配, 模型" + modelObj.Name + "所属的分组为" + modelObj.ModelGroup + ", 传递的模型分组为" + req.ModelGroupCode)
						return nil, nil, errors.New("模型code和模型分组不匹配")
					}
				}
				// 判断完成后，我们以model_code作为过滤条件
				filter["model_code"] = bson.M{"$in": req.ModelCode}
				modelCodeFilter = append(modelCodeFilter, req.ModelCode...)
			} else if req.ModelGroupCode != "" {
				// 当没有传递确定的model_code的时候，那么就以模型分组为过滤条件
				filter["meta.model_group_code"] = req.ModelGroupCode
			}
		}
	}

	// 针对office和idc单独进行处理，因为这两个属性是比较特殊的，都是从属关系继承的属性。
	offices, officesPassed := conditions["global_office"]
	pointToOffice := make(mapdata.MapData)
	if officesPassed {
		pathFilter := bson.M{}
		// 这里的offices是office的id列表
		if len(offices) > 0 {
			pathFilter["parent_id"] = bson.M{"$in": offices}
		}
		if len(modelCodeFilter) > 0 {
			pathFilter["child_model_code"] = bson.M{"$in": modelCodeFilter}
		}
		// 把对应office下对应model_code所有数据都查出来
		paths, err := mds.store.ModelData().GetDataPathByFilter(ctx, pathFilter)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, nil, err
		}
		// 符合条件的数据id列表
		for _, path := range paths {
			pointToOffice.Set(path.ChildID, struct{}{})
		}
		// 删除掉office
		delete(conditions, "global_office")
	}

	// 为什么目前这两个要单独处理，因为idc和office的code都可以进步缩小查询的范围，不过按照prd来看的话，目前office和idc暂时不进行关系约束。
	// 我这里提出来单独进行处理，如果后续需要进行关系约束的话，我也可以很方便的进行操作。
	idcList, idcPassed := conditions["global_idc"]
	pointToIdc := make(mapdata.MapData)
	if idcPassed {
		pathFilter := bson.M{}
		if len(idcList) > 0 {
			pathFilter["parent_id"] = bson.M{"$in": idcList}
		}
		if len(modelCodeFilter) > 0 {
			pathFilter["child_model_code"] = bson.M{"$in": modelCodeFilter}
		}
		paths, err := mds.store.ModelData().GetDataPathByFilter(ctx, pathFilter)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, nil, err
		}
		// 符合条件的数据id列表
		for _, path := range paths {
			pointToIdc.Set(path.ChildID, struct{}{})
		}
		// 删除掉idc
		delete(conditions, "global_idc")
	}

	// 职场和机房，有几种情况，
	// 1. 用户没有传递office和idc，那么就查询所有数据
	// 2. 用户传递了office和idc，那么就查询office和idc交集的数据
	// 3. 用户传递了office，没有传递idc，那么就查询office的数据
	// 4. 用户传递了idc，没有传递office，那么就查询idc的数据
	if len(pointToOffice) != 0 && len(pointToIdc) != 0 {
		dataIDs := pointToOffice.Intersection(pointToIdc)
		// 这种又传递了office也传递了idc的，但是交集为空的，那么就证明这查出来的数据可能都对不上，比如你选元中心，但是选了成都的机房，这样肯定搜不出来
		if len(dataIDs) == 0 {
			return nil, nil, nil
		}
		filter["_id"] = bson.M{"$in": dataIDs}
	} else if len(pointToOffice) != 0 {
		// 如果说传递了idc，但是实际上没有idc的数据，那么就返回空，说明选择了两个没啥关联的职场和机房，不应该有交集存在
		if idcPassed {
			return nil, nil, nil
		}
		filter["_id"] = bson.M{"$in": pointToOffice.Keys()}
	} else if len(pointToIdc) != 0 {
		if officesPassed {
			return nil, nil, nil
		}
		filter["_id"] = bson.M{"$in": pointToIdc.Keys()}
	} else {
		// 如果用户没有传递office和idc，那么就查询所有数据，直接执行就可以了
		// 但是如果说传递了idc或者office，但是都不满足上面的条件，说明idc和office的数据都查不出来，那么就返回空
		if officesPassed || idcPassed {
			return nil, nil, nil
		}
	}

	// 遍历所有要过滤的条件
	for fieldName, fieldValue := range conditions {
		// 如果用户的fieldValue传递过来是一个空的切片，那么就忽略
		if len(fieldValue) == 0 {
			continue
		}
		switch fieldName {
		case "server_plan":
			filter["data.server_plan"] = bson.M{"$in": fieldValue}
		case "universal_status":
			filter["meta_data.universal_status"] = bson.M{"$in": fieldValue}
		case "universal_belong_to":
			for _, v := range fieldValue {
				switch v {
				case "it", "idc", "personal":
				default:
					zap.L().Error("不支持的belong_to值", zap.String("belong_to", v))
					return nil, nil, errors.New("不支持的belong_to值")
				}
			}

			defaultAll := array.StringArray{"it", "personal", "idc"}
			// 如果说传递的和默认得一样，那么其实选了和没选一样，就不用筛选
			if defaultAll.ArrayValueEqual(fieldValue) {
				continue
			}

			if len(fieldValue) == 1 {
				if fieldValue[0] == "personal" {
					filter["$and"] = []bson.M{
						{"meta_data.universal_belong_to": bson.M{"$exists": true}},
						{"meta_data.universal_belong_to": bson.M{"$ne": ""}},
						{"meta_data.universal_belong_to": bson.M{"$ne": "it"}},
						{"meta_data.universal_belong_to": bson.M{"$ne": "idc"}},
						{"meta_data.universal_belong_to": bson.M{"$ne": nil}},
					}
				} else {
					filter["meta_data.universal_belong_to"] = fieldValue[0]
				}
			} else if len(fieldValue) == 2 {
				if array.StringArray(fieldValue).InArray("personal") {
					// 获取另一个要筛选的
					var (
						another string
						kw      string
					)
					if fieldValue[0] == "personal" {
						kw = fieldValue[1]
					} else {
						kw = fieldValue[0]
					}
					if kw == "it" {
						another = "idc"
					} else {
						another = "it"
					}
					filter["$and"] = []bson.M{
						{"meta_data.universal_belong_to": bson.M{"$exists": true}},
						{"meta_data.universal_belong_to": bson.M{"$ne": ""}},
						{"meta_data.universal_belong_to": bson.M{"$ne": another}},
						{"meta_data.universal_belong_to": bson.M{"$ne": nil}},
					}
				} else {
					filter["meta_data.universal_belong_to"] = bson.M{"$in": fieldValue}
				}
			}
		case "universal_overdue_status":
			filter["meta_data.universal_overdue_status"] = bson.M{"$in": fieldValue}
		case "universal_brand":
			filter["meta_data.universal_brand"] = bson.M{"$in": fieldValue}
		case "universal_model":
			filter["meta_data.universal_model"] = bson.M{"$in": fieldValue}
		case "universal_owner":
			filter["meta_data.universal_owner"] = bson.M{"$in": fieldValue}
		default:
			zap.L().Error("不支持的过滤条件", zap.String("fieldName", fieldName))
			return nil, nil, errors.New("不支持的过滤条件")
		}
	}

	return filter, modelCodeFilter, nil
}

// GlobalSearchCombine 组合搜索模型数据
func (mds *modelDataService) GlobalSearchCombine(ctx context.Context, req *apiv1.GlobalSearchCombineRequest, pageNumber, pageSizeNumber int64) (*apiv1.GlobalSearchResponse, error) {
	zap.L().Debug("GlobalSearchCombine Service called")
	span, ctx := apm.StartSpan(ctx, "GlobalSearchCombineService", "service")
	defer span.End()

	// 初始化返回值
	resp := &apiv1.GlobalSearchResponse{}

	// 构建过滤条件
	filter, deviceTypeFilter, err := mds.buildCombineSearchFilter(ctx, req)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 如果过滤条件为空，那么就返回空
	if len(filter) == 0 {
		return resp, nil
	}

	// 查询聚合内容
	pipeline := make([]bson.M, 0)
	pipeline = append(pipeline, bson.M{"$match": filter})
	pipeline = append(pipeline, bson.M{"$group": bson.M{"_id": "$model_code", "count": bson.M{"$sum": 1}, "create_at": bson.M{"$first": "$create_at"}}})
	pipeline = append(pipeline, bson.M{"$sort": bson.M{"create_at": -1}})
	// pipeline = append(pipeline, bson.M{"$group": bson.M{"_id": "$model_code", "count": bson.M{"$sum": 1}}})
	// pipeline = append(pipeline, bson.M{"$sort": bson.M{"count": -1}})
	pipeline = append(pipeline, bson.M{"$limit": 30})
	aggInfo, err := mds.store.ModelData().GetAggregatedData(ctx, pipeline)
	if err != nil {
		zap.L().Error(err.Error())
		return resp, err
	}

	// 使用一个map来保存每一个模型的数据数量
	modelDataCount := make(map[string]int)
	for _, m := range aggInfo {
		k := utils.ToString(m["_id"])
		v := utils.ToInt(m["count"])
		modelDataCount[k] = v
	}

	// 没有查到数据的话直接返回即可
	if len(modelDataCount) == 0 {
		return resp, nil
	}

	// 拼接模型和模型分组
	nest := make(map[string]*apiv1.ModelWithNested)
	for k, v := range modelDataCount {
		var m *v1.Model
		m, err = mds.store.Model().GetModelByCode(ctx, k)
		if err != nil {
			zap.L().Error("搜索查询模型失败", zap.Error(err))
			continue
		}
		// 更新搜索到的数据的数量
		m.Total = int64(v)

		// 查询这个模型所属的模型分组
		var modelGroup *v1.ModelGroup
		modelGroup, err = mds.store.ModelGroup().GetModelGroup(ctx, m.ModelGroup)
		if err != nil {
			zap.L().Error("模糊搜索查询模型分组失败", zap.Error(err))
		}

		// 根据查询到的model和model_group构建嵌套结构
		if _, ok := nest[modelGroup.Code]; !ok {
			children := make(v1.ModelList, 0)
			children = append(children, *m)
			nest[modelGroup.Code] = &apiv1.ModelWithNested{
				ID:       modelGroup.ID,
				Code:     modelGroup.Code,
				Name:     modelGroup.Name,
				CreateAt: modelGroup.CreateAt,
				Children: children,
			}
		} else {
			nest[modelGroup.Code].Children = append(nest[modelGroup.Code].Children, *m)
		}
	}

	for _, nested := range nest {
		sort.Sort(nested.Children)
	}

	// 针对嵌套好的模型分组再包一层，用于排序
	nestedModelList := make(apiv1.NestedModelList, 0)
	for _, v := range nest {
		nestedModelList = append(nestedModelList, *v)
	}
	// 针对模型分组排一下序，当前排序的规则是按照create_at进行排序的
	sort.Sort(nestedModelList)
	resp.ModelGroups = nestedModelList

	// 查到所有的model备用
	modelMap, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		return resp, err
	}

	if len(req.ModelCode) > 0 {
		// 用户传递了，说明用户手动勾选了对应模型的复选框, 这个时候需要确认勾选的是不是都是一个模型分组下的
		mg := modelMap[req.ModelCode[0]].ModelGroup
		for i := 1; i < len(req.ModelCode); i++ {
			if modelMap[req.ModelCode[i]].ModelGroup != mg {
				zap.L().Error("不允许跨模型勾选模型", zap.String("model_code", req.ModelCode[i]))
				return resp, errors.New("不允许跨模型勾选模型")
			}
		}
		filter["model_code"] = bson.M{"$in": req.ModelCode}
	} else if len(deviceTypeFilter) > 0 {
		// 如果说用户没有勾选的话，那么第二看搜索条件中的device_type，其实也是对应的模型
		// 这个时候model_code是作为条件查询的条件存在的，因此不需要关心是不是一个模型下的
		// 但是这个时候我们默认还是应该展示某一个分组下的数据
		var mg string
		if req.ModelGroupCode == "" {
			// 用户没有传递模型分组，默认挑选第一个进行展示
			mg = nestedModelList[0].Code
		} else {
			mg = req.ModelGroupCode
		}

		modelFilter := make([]string, 0)
		for _, v := range deviceTypeFilter {
			if modelMap[v].ModelGroup == mg {
				modelFilter = append(modelFilter, v)
			}
		}
		filter["model_code"] = bson.M{"$in": modelFilter}
	} else {
		// 如果用户既没有传递model_code，也没有传递device_type
		// 此时需要判断一下firstModelGroup是谁
		var firstModelGroup string
		if req.ModelGroupCode != "" {
			firstModelGroup = req.ModelGroupCode
		} else {
			firstModelGroup = nestedModelList[0].Code
		}
		models := make([]string, 0)
		for _, m := range nest[firstModelGroup].Children {
			models = append(models, m.Code)
		}
		filter["model_code"] = bson.M{"$in": models}
	}
	modelDataList, total, err := mds.store.ModelData().GetModelDataList(ctx, filter, pageNumber, pageSizeNumber, "")
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	pages := (total / pageSizeNumber) + 1

	dataResponse := make([]apiv1.DataResponse, 0)

	// 这里直接返回即可，条件搜索不需要做高亮的配置
	modelAttrMapping, err := mds.store.ModelAttributes().GetModelAttrMap(ctx, "code")
	if err != nil {
		zap.L().Error(err.Error())
		return resp, err
	}

	// 构建一个map，key是model_code，value是attr_code的数组
	modelAttrsArrayMapping := make(map[string]v1.CommonModelAttributeList)
	// k是attr_code，v是attr
	for _, v := range modelAttrMapping {
		// 如果说对应的model_code在map中不存在
		mc := v.ModelCode
		if _, ok := modelAttrsArrayMapping[mc]; !ok {
			modelAttrsArrayMapping[mc] = make(v1.CommonModelAttributeList, 0)
		}
		// 排除掉关系字段
		if v.TypeName == "relationship" {
			continue
		}
		if v.Code == fmt.Sprintf("%s_code", mc) {
			continue
		}
		modelAttrsArrayMapping[mc] = append(modelAttrsArrayMapping[mc], v)
	}

	// 排个序
	for _, v := range modelAttrsArrayMapping {
		sort.Sort(v)
	}

	for _, d := range modelDataList {
		infoString := strings.Builder{}

		// 拿到模型对应的attrs, 这个attrs已经排好序了
		attrs := modelAttrsArrayMapping[d.ModelCode]

		// 遍历attr
		for _, attr := range attrs {
			vString := utils.ToString(d.Data[attr.Code])
			if vString != "" {
				infoString.WriteString(attr.Name)
				infoString.WriteString(": ")
				infoString.WriteString(vString)
				infoString.WriteString("; ")
			}
		}

		// 遍历数据的Data这个map，但是默认是没有顺序的
		// for k, v := range d.Data {
		// 	vString := utils.ToString(v)
		// 	if vString != "" {
		// 		if attr, ok := modelAttrMapping[k]; ok {
		// 			infoString.WriteString(attr.Name)
		// 			infoString.WriteString(": ")
		// 			infoString.WriteString(utils.ToString(v))
		// 			infoString.WriteString("; ")
		// 		} else {
		// 			zap.L().Warn("模型属性不存在", zap.String("model_code", d.ModelCode), zap.String("attr_code", k))
		// 		}
		// 	}
		// }

		dataResponse = append(dataResponse, apiv1.DataResponse{
			ModelData: d,
			Info:      infoString.String(),
			Title:     d.Name(),
		})
	}

	data := &apiv1.SearchResultDataBlock{
		PageSize:    pageSizeNumber,
		Pages:       pages,
		CurrentPage: pageNumber,
		Total:       total,
		Data:        dataResponse,
	}

	resp.Data = *data

	return resp, nil
}

// GlobalSearchDownload 全局搜索下载
func (mds *modelDataService) GlobalSearchDownload(ctx context.Context, req *apiv1.GlobalSearchCombineRequest) (*excelize.File, error) {
	zap.L().Debug("GlobalSearchDownload Function Called")
	span, ctx := apm.StartSpan(ctx, "GlobalSearchDownload", "service")
	defer span.End()

	// 构建过滤条件
	filter, _, err := mds.buildCombineSearchFilter(ctx, req)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 查询数据
	modelDataList, err := mds.store.ModelData().GetModelDataByCustomFilter(ctx, filter, nil)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 组织数据，数据最终是按照模型进行划分，一个模型是一个sheet
	modelDataMapping := make(map[string][]*v1.ModelData)
	// 构建一个mapping，key是model code，value是数据指针的切片
	for _, d := range modelDataList {
		if _, ok := modelDataMapping[d.ModelCode]; !ok {
			modelDataMapping[d.ModelCode] = make([]*v1.ModelData, 0)
		}
		modelDataMapping[d.ModelCode] = append(modelDataMapping[d.ModelCode], d)
	}

	// 首先获取model mapping
	modelMapping, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 构建Excel
	file := excelize.NewFile()
	for modelCode, dataList := range modelDataMapping {
		// 首先看一下数据是不是至少大于0条，如果说没有数据话，那么也没必要创建sheet
		if len(modelDataList) == 0 {
			continue
		}

		model, ok := modelMapping[modelCode]
		if !ok {
			zap.L().Error("模型不存在", zap.String("model_code", modelCode))
			continue
		}

		// 把当前的模型对应的字段都查出来
		modelAttrs, err := mds.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
			"model_code": modelCode,
			"type_name":  bson.M{"$ne": "relationship"},
			"code":       bson.M{"$ne": fmt.Sprintf("%s_code", modelCode)},
		}, nil)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		if _, err := WriteTemplateExcel(model, modelAttrs, dataList, true, file); err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		/**
		// 创建模型的sheet
		sheetName := model.Name
		idx, err := file.NewSheet(sheetName)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		// 将刚创建的sheet设置为活跃的index
		file.SetActiveSheet(idx)

		// 初始化表头
		if err := file.SetCellValue(sheetName, "A1", "字段名(请勿编辑)"); err != nil {
			return nil, err
		}
		if err := file.SetCellValue(sheetName, "A2", "字段类型(请勿编辑)"); err != nil {
			return nil, err
		}
		if err := file.SetCellValue(sheetName, "A3", "字段标识(请勿编辑)"); err != nil {
			return nil, err
		}
		if err := file.SetCellValue(sheetName, "A4", "示例(请勿编辑)"); err != nil {
			return nil, err
		}

		// 设置初始化的列宽，startCol和endCol分别表示起始列和结束列，A列是说明列
		if err := file.SetColWidth(sheetName, "A", "A", 20); err != nil {
			return nil, err
		}

		// 设置A列的样式
		rowAStyleID, err := excel.GetColATableStyle(file)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}
		if err := file.SetCellStyle(sheetName, "A1", "A4", rowAStyleID); err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		// 字段类型和字段标识使用的样式
		tableStyleId, err := excel.GetFieldTableColStyle(file)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		// 字段类型使用的样式
		fieldNameStyleId, err := excel.GetFieldNameStyle(file)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		// 列数取决于字段数量
		colNumber := 'B' + len(modelAttrs)

		// 把列数转换为列名, 65是A的ASCII码
		cell := utils.ConvertToColumnTitle(colNumber - 64)

		// 填充B2到cell+"4", 假设说cell是I，那么就是B2到I4，其实是一个矩形区域
		if err := file.SetCellStyle(sheetName, "B2", cell+"4", tableStyleId); err != nil {
			return nil, err
		}
		// 这里填充的是一个横向区域从B1到cell+"1"
		if err := file.SetCellStyle(sheetName, "B1", cell+"1", fieldNameStyleId); err != nil {
			return nil, err
		}

		_ = file.SetCellValue(sheetName, "A5", "实例数据")

		// 针对ID列单独进行处理
		_ = file.SetCellValue(sheetName, "B1", "ID")
		_ = file.SetCellValue(sheetName, "B2", "字符串")
		_ = file.SetCellValue(sheetName, "B3", "id")
		_ = file.SetCellValue(sheetName, "B4", modelDataList[0].ID)

		// 计算数据要填充到多少行，我们要把A4之后的所有行都填充上对应的样式
		endRowOfColA := fmt.Sprintf("A%d", 4+len(modelDataList))
		if err := file.SetCellStyle(sheetName, "A5", endRowOfColA, rowAStyleID); err != nil {
			zap.L().Error(err.Error())
		}

		// ID列设置的稍微宽一些
		if err := file.SetColWidth(sheetName, "B", "B", 1.2*float64(len(modelDataList[0].ID))); err != nil {
			zap.L().Error(err.Error())
		}

		// 获取示例数据表格样式ID
		exampleColStyleID, err := excel.GetSampleColStyle(file)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		var (
			col      int32 = 0   // 初始化列数
			prefix   int32 = 0   // 初始化一个前缀，用于列大于Z列的时候，保存对应的列前缀
			startCol       = 'C' // 上面A列是说明列，B列是ID列，我们正式从C列开始写数据
		)

		// 填充示例数据
		identity := fmt.Sprintf("%s_code", modelCode)
		for _, attr := range modelAttrs {
			// 我不关心唯一标识字段，因为一堆随机生成的字符串，直接pass即可
			if attr.Code == identity {
				continue
			}

			// 获取字段名称, 字段名称由两个部分组成，列明和行坐标（x, y）
			// 列名称：比如A，B，AA，AAA等
			// 行坐标：比如1，2，3，4等
			// 所以字段名称就是列名称+行坐标，比如：A1，B1，AA1，AAA1等
			var cellName string
			// 初始化列数，随着字段的遍历逐步自增
			index := col
			// 初始化列，从C列开始，默认首次循环，index为0，即列为C列，随每一次遍历，index增加1，列后移
			columnRune := startCol + index

			// ASCII中，Z是90，如果超过90，那么就重置列数，比如91代表的就应该是AA
			if columnRune > 90 {
				// @是64, A是65
				startCol = 64
				// 65代表 A
				prefix = startCol + 1
				col = 1
				// 1
				index = col
				// 64 + 1 = 65, 也是A
				columnRune = startCol + index
			}

			if prefix > 0 {
				cellName = fmt.Sprintf("%c%c", prefix, columnRune)
			} else {
				cellName = fmt.Sprintf("%c", columnRune)
			}

			// 首先写入第一行，第一行写字段名称
			if !attr.IsNull {
				_ = file.SetCellValue(sheetName, cellName+"1", attr.Name)
				_ = file.SetCellRichText(sheetName, cellName+"1", excel.GetRichTextRedStyle(attr.Name+"(必填)"))
			} else if !attr.IsEditAble {
				_ = file.SetCellValue(sheetName, cellName+"1", attr.Name)
				_ = file.SetCellRichText(sheetName, cellName+"1", excel.GetRichTextRedStyle(attr.Name+"(不可编辑)"))
			} else if attr.IsUnique {
				_ = file.SetCellValue(sheetName, cellName+"1", attr.Name)
				_ = file.SetCellRichText(sheetName, cellName+"1", excel.GetRichTextRedStyle(attr.Name+"(唯一)"))
			} else {
				_ = file.SetCellValue(sheetName, cellName+"1", attr.Name)
			}

			// 第二行写字段类型
			_ = file.SetCellValue(sheetName, cellName+"2", enum.GetI18NTypeName(attr.TypeName))

			// 第三行要写入字段的标识
			_ = file.SetCellValue(sheetName, cellName+"3", attr.Code)
			// 看名称和唯一标识谁更长一点，以谁的长度为准，避免表格出现展示不全的情况，提升用户的使用体验
			codeLength := utils.ToFloat64(len(attr.Code))
			nameLength := utils.ToFloat64(len(attr.Name))
			colWidth := math.Max(codeLength, nameLength)
			if err := file.SetColWidth(sheetName, cellName, cellName, 1.2*colWidth); err != nil {
				zap.L().Error(err.Error())
				return nil, err
			}

			// 第四行写入示例值
			example := ""
			switch attr.TypeName {
			case enum.Bool:
				// 布尔值的示例值
				example = "TRUE"
			case enum.Date:
				// 日期示例值
				example = "2025/1/31"
			case enum.Time:
				// 时间示例值
				example = "10:01:01"
			case enum.Select:
				// 枚举类型的示例值
				opts := attr.Attrs["opts"].(primitive.A)
				depth := utils.ToInt(attr.Attrs["depth"])
				if depth == 1 {
					// 非继承的枚举字段
					for _, opt := range opts {
						tempOpt := opt.(map[string]interface{})
						if example == "" {
							example = fmt.Sprintf("填写实际值，如：%s\n", tempOpt["name"])
							_ = file.SetColWidth(sheetName, cellName, cellName, float64(len(example))*0.8)
						}
						example += fmt.Sprintf("- %s\n", tempOpt["name"])
					}
				} else {
					// 继承的枚举字段
					chainData := attr.Attrs["chain_data"].(map[string]interface{})
					dataTree := chainData["data_tree"].(primitive.A)
					parentCode := chainData["parent_code"].(string)
					for _, findAttr := range modelAttrs {
						if findAttr.Code == parentCode {
							_ = file.SetCellValue(sheetName, cellName+"2", enum.GetI18NTypeName(attr.TypeName)+"（上级："+findAttr.Name+")")
						}
					}
					for _, primitiveNode := range dataTree {
						node := primitiveNode.(map[string]interface{})
						parentName := node["name"]
						if node["children"] == nil {
							continue
						}
						kids := node["children"].(primitive.A)
						for _, kid := range kids {
							children := kid.(map[string]interface{})
							if example == "" {
								example = fmt.Sprintf("填写实际值，如：%s\n", children["name"].(string))
							}
							example += fmt.Sprintf("%s-%s\n", parentName, children["name"].(string))
						}
					}
				}
			default:
				sample := utils.ToString(attr.Attrs["sample"])
				if sample == "" {
					break
				}
				example = sample
			}
			// 写入示例值
			_ = file.SetCellValue(sheetName, cellName+"4", example)
			// 设置示例值的样式
			_ = file.SetCellStyle(sheetName, cellName+"4", cellName+"4", exampleColStyleID)

			// 列数增加
			col++
		}

		// 填充数据
		for idx1, modelData := range modelDataList {
			idCell := fmt.Sprintf("%c%d", 'B', 5+idx1)
			// 写id
			_ = file.SetCellValue(sheetName, idCell, modelData.ID)
			for idx2, attr := range modelAttrs {
				// 跳过的字段，不要累加idx2，否则会造成有的列为空的。
				if attr.Code == identity {
					idx2 -= 1
					continue
				}
				dataValue := utils.ToString(modelData.Data[attr.Code])
				if dataValue == "" {
					continue
				}
				// 从C列开始，A代表1，则C代表的是3
				colNameStr := utils.ConvertToColumnTitle(3 + idx2)
				cellName := fmt.Sprintf("%s%d", colNameStr, 5+idx1)

				// TODO: 写数据的时候，以最大的列宽为准，调优展示样式
				// width, err := file.GetColWidth(sheetName, colNameStr)
				// if err != nil {
				// 	zap.L().Error(err.Error())
				// 	return nil, err
				// }
				// dataValueRune := []rune(dataValue)
				// if len(dataValueRune) > utils.ToInt(width) {
				// 	if len(dataValueRune) < utils.ToInt(MaxColWidth) {
				// 		// 如果长度大于当前列宽，但是小于最大列宽，那么就设置为当前数据的长度
				// 		_ = file.SetColWidth(sheetName, colNameStr, colNameStr, utils.ToFloat64(len(dataValue)))
				// 	} else {
				// 		// 如果长度大于当前列宽，且大于最大列宽，那么就设置为最大列宽
				// 		_ = file.SetColWidth(sheetName, colNameStr, colNameStr, MaxColWidth)
				// 		v := ""
				// 		runeIdx := 0
				// 		for runeIdx < len(dataValueRune) {
				// 			if runeIdx > 0 {
				// 				v += "\r\n" // 换行
				// 			}
				// 			end := runeIdx + utils.ToInt(MaxColWidth)
				// 			if end > len(dataValueRune) {
				// 				end = len(dataValueRune)
				// 			}
				// 			r := []rune(dataValue)
				// 			v += string(r[runeIdx:end])
				// 			if runeIdx+int(MaxColWidth) > len(dataValueRune) {
				// 				runeIdx++
				// 				break
				// 			}
				// 			runeIdx += int(MaxColWidth)
				// 		}
				// 		v += string(dataValueRune[runeIdx:])
				// 		_ = file.SetCellValue(sheetName, cellName, v)
				// 		continue
				// 	}
				// }

				_ = file.SetCellValue(sheetName, cellName, dataValue)
			}
		}
		**/
	}

	// 把默认生成的sheet1删除
	if err := file.DeleteSheet("sheet1"); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 把第一个sheet设置为活跃度sheet
	file.SetActiveSheet(0)

	return file, nil
}
