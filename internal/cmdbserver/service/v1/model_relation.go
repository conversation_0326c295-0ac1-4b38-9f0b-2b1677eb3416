package v1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/pkg/array"
	"ks-knoc-server/pkg/convert"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"github.com/mitchellh/mapstructure"
	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ModelRelationService interface {
	// CreateModelSubOrdinateRelation 创建模型从属关系
	CreateModelSubOrdinateRelation(ctx context.Context, operator string, mrr *v1.ModelSubordinateRelationRequest) (interface{}, error)
	// DeleteModelSubOrdinateRelation 删除模型从属关系
	DeleteModelSubOrdinateRelation(ctx context.Context, operator string, instanceFilter *v1.InstanceFilter) error
	// CreateModelAssociateRelation 创建模型关联关系
	CreateModelAssociateRelation(ctx context.Context, operator string, mar *v1.ModelAssociateRelationRequest) (interface{}, error)
	// DeleteModelAssociateRelation 删除模型关联关系
	DeleteModelAssociateRelation(ctx context.Context, operator string, idFilter *v1.IDFilter) error
	// GetModelRelations 获取模型的所有关系
	GetModelRelations(ctx context.Context, filter string) (*v1.ModelRelationResponse, error)
	// UpdateModelRelation 更新模型关系（仅描述）
	UpdateModelRelation(ctx context.Context, operator string, req *v1.UpdateModelRelationRequest) error
	// GetModelRelationTopo 获取模型关系拓扑图
	GetModelRelationTopo(ctx context.Context) (*v1.ModelRelationTopo, error)
}

type modelRelationService struct {
	store store.Factory
}

var _ ModelRelationService = (*modelRelationService)(nil)

func newModelRelationService(srv *service) ModelRelationService {
	return &modelRelationService{store: srv.store}
}

func (m *modelRelationService) DeleteModelSubOrdinateRelation(ctx context.Context, operator string, instanceFilter *v1.InstanceFilter) error {
	zap.L().Debug("DeleteModelSubOrdinateRelation Service Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelSubOrdinateRelationService", "DELETE")
	defer span.End()

	var err error

	// 1. 首先需要判断要删除的这个从属关系是否存在，code是唯一的，因此可以直接通过code来查询
	attr, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(
		ctx, bson.M{"code": instanceFilter.Code}, bson.M{})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("删除从属关系失败，从属关系不存在", zap.Error(err))
			return errno.ErrModelSubordinateNotExists.Add("从属关系不存在")
		}
		return err
	}

	// 初始化从属关系对象
	subordinate := v1.NewSubordinationRelationshipAttribute()
	if err = mapstructure.Decode(attr, subordinate); err != nil {
		zap.L().Error(err.Error())
		return err
	}

	if subordinate.Attrs.RelType != int(v1.Subordinate) {
		zap.L().Error("删除从属关系失败，要删除的关系并不是从属关系", zap.Error(err))
		return errno.ErrModelSubordinateNotExists.Add("删除从属关系失败，要删除的关系并不是从属关系")
	}

	// 2. 内置模型关系不可以删除
	if attr.IsBuiltIn() {
		return errno.ErrBuiltinObject.Add("内置模型关系不可以删除")
	}

	// 3. 这里删除的是自己这个模型的从属关系，删除前需要看看是否有数据使用了这个关联，如服务器指向了机柜，如果此时已经有好几百台服务器指向了这个机柜
	// 直接删了这个从属关系，说明这几百台服务器就没有机柜了，这是不允许的。因此需要先判断是否有数据使用了这个关联，如果有数据使用了这个关联，那么就不允许删除
	// 对标PRD：已有资源关系的模型关系不可删除

	// 3.1 首先每个模型只能有一个父模型，那么我们只要查询modelCode为关系的modelCode，且parentID不为空的数据即可
	dataList, err := m.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"model_code": attr.ModelCode,
		"parent_id":  bson.M{"$ne": ""},
	}, nil)
	if err != nil {
		zap.L().Error("删除从属关系失败，查询模型数据失败", zap.Error(err))
		return err
	}
	// 如果说返回的数据长度大于0，证明已经有数据在使用这个从属关系了，不允许删除
	if len(dataList) > 0 {
		return errno.ErrModelSubordinateInUse.Add("已有资源关系的模型关系不可删除")
	}

	// TODO: 3.2 这里要拿一下模型的对象传递给下面的删除方法，在删除方法中的审计部分需要用到，这块其实逻辑很冗余，没必要，看看是否可以把
	// 对应的一些模型信息集成到model attribute中去处理。这样就不需要再单独查一次了。
	modelMap, err := m.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return err
	}
	model, ok := modelMap[attr.ModelCode]
	if !ok {
		zap.L().Error("查询模型失败", zap.Error(err))
		return errno.InternalServerError.Add("查询模型失败")
	}

	// 如果上述检查都过了，那么就正常删除即可
	t, err := m.store.Common().StartTransaction()
	if err != nil {
		return err
	}

	// 删除从属关系属性字段
	t.Wrapper(func(operator string, instanceFilter *v1.InstanceFilter) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			err := m.store.ModelAttributes().DeleteModelAttributes(sessionCtx, operator, instanceFilter)
			if err != nil {
				zap.L().Error("删除从属关系失败", zap.Error(err))
				return err
			}

			// audit log
			src := make(map[string]interface{}, 2)
			src["name"] = instanceFilter.Name
			src["model"] = model.Name

			auditLog, err := GenerateAuditLog(ctx, src, nil, nil, audit.ActionDelete, audit.ModelAttributeRes, operator, "", model.Code)
			if err != nil {
				return err
			}

			if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
				return err
			}

			return nil
		}
	}(operator, instanceFilter))

	// 还得更新一下当前模型的parent字段，将其置为空
	t.Wrapper(func(sessionCtx context.Context) error {
		if err := m.store.Model().ResetModelParentCode(sessionCtx, model.Code); err != nil {
			zap.L().Error("更新模型的父级Code失败", zap.Error(err))
			return err
		}
		return nil
	})

	// 删除从属关系是从子端发起的，你还得删除父级模型children数组中的子模型code
	t.Wrapper(func(sessionCtx context.Context) error {
		parentCode := subordinate.Attrs.RelTo
		md, exist := modelMap[parentCode]
		if !exist {
			zap.L().Error("查询模型失败", zap.Error(err))
			return errno.InternalServerError.Add("查询模型失败")
		}

		deleteElem := func(s []string, ele string) []string {
			j := 0
			for _, v := range s {
				if v != ele {
					s[j] = v
					j++
				}
			}
			return s[:j]
		}

		newChildrenCode := deleteElem(md.ChildrenCode, subordinate.ModelCode)
		md.ChildrenCode = newChildrenCode

		err := m.store.Model().UpdateModel(sessionCtx, &md)
		if err != nil {
			zap.L().Error("更新父模型失败", zap.Error(err))
			return err
		}
		return nil
	})

	err = t.Do(ctx)
	if err != nil {
		return err
	}

	// 同时还需要判断一下这个父关系是不是机柜，如果是机柜，那么还需要删除机柜的起始U位和高度两个字段
	// TODO: 目前先不做删除U位和高度相关的操作，如果说创建了，那就创建了。也不做删除，如果说后续有必要删除的话，再删除。
	// if attr.Attrs["rel_to"].(string) == "rack" {
	//
	// }

	return nil
}

func (m *modelRelationService) checkRelationNameConflict(ctx context.Context, name string) (bool, error) {
	attrs, err := m.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"type_name": "relationship",
		"name":      strings.TrimSpace(name),
	}, bson.M{})

	if err != nil {
		zap.L().Error("检查关系名称冲突失败", zap.Error(err))
		return false, err
	}

	if len(attrs) > 0 {
		return true, nil
	}

	return false, nil
}

// CreateModelSubOrdinateRelation 创建模型从属关系
func (m *modelRelationService) CreateModelSubOrdinateRelation(ctx context.Context, operator string, mrr *v1.ModelSubordinateRelationRequest) (any, error) {
	zap.L().Debug("CreateModelRelation Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelRelation", "POST")
	defer span.End()

	// 0. 所有关系名称不允许重复，放在第一位进行校验
	conflict, err := m.checkRelationNameConflict(ctx, mrr.Name)
	if err != nil {
		zap.L().Error("检查关系名称冲突失败", zap.Error(err))
		return nil, err
	}

	if conflict {
		return nil, errno.ErrModelSubordinateExists.Add("关系名称已存在")
	}

	// 1. 首先判断发起端模型是否存在，即源端模型(子模型)是否存在
	src, err := m.store.Model().GetModelByCode(ctx, mrr.ModelCode)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("无法查询到模型", zap.Error(err))
			return nil, errno.ErrModelNotFound.Add("模型不存在")
		}
		zap.L().Error("查询模型失败", zap.Error(err))
		return nil, err
	}

	// 2. 处理具体的逻辑
	// 从属关系其实也是一个模型字段，但是对应字段的type_name为relationship，是一种特殊的字段，只不过载体是model_attr
	subordinate := v1.NewSubordinationRelationshipAttribute()

	// 2.1 首先需要判断该模型是否已经创建了从属关系，一个从属模型只允许创建一个从属关系。如果已存在，需要先删除再创建
	// 这里直接查询模型属性中有没有从属类型的字段就可以了，一个模型就可以有0个或者1个从属字段。
	attrs, err := m.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"model_code":     mrr.ModelCode,
		"type_name":      "relationship",
		"attrs.rel_type": v1.Subordinate,
	}, bson.M{})
	if err != nil {
		zap.L().Error("查询从属关系失败", zap.Error(err))
		return nil, err
	}
	if len(attrs) > 0 {
		return nil, errno.ErrModelSubordinateExists.Add("从属关系已存在")
	}

	// 2.2 获取父模型是否存在
	relTo := mrr.RelTo
	parentModel, err := m.store.Model().GetModelByCode(ctx, relTo)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("创建主从关系失败，目标端模型不存在", zap.Error(err))
			return nil, errno.ErrModelNotFound.Add("模型不存在")
		}
		zap.L().Error("创建主从关系失败", zap.Error(err))
		return nil, err
	}

	// 2.3 避免环路，避免主从关系转一圈回到自己(环路检测的判断中，指向自己也是不可以的，也会引发报错)
	loop, err := m.CheckLoop(ctx, mrr.ModelCode, mrr.RelTo)
	if err != nil {
		zap.L().Error("检测环路失败", zap.Error(err))
		return nil, err
	}
	if loop {
		return nil, errno.ErrSubordinateLoop.Add("从属关系出现环路")
	}

	// 判断一下目标模型建立过关联关系。如果建立过关联关系，那么就不允许建立从属关系
	filter := bson.M{
		"model_code":                 src.Code,
		"attrs.rel_local_model_code": src.Code,
		"attrs.rel_model_code":       relTo,
	}
	r, err := m.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, filter, nil)
	if err != nil {
		zap.L().Error("查询关联关系失败", zap.Error(err))
		return nil, err
	}
	if len(r) > 0 {
		return nil, errno.ErrModelRelationConflict.Add("已和目标模型已经建立过关联关系，不允许再建立从属关系")
	}

	// 2.4 初始化相关字段
	subordinate.Code = FormatRelationCode(mrr.ModelCode, mrr.RelTo, 1, mrr.Name)
	subordinate.ModelCode = mrr.ModelCode
	subordinate.Name = mrr.Name
	subordinate.Builtin = mrr.BuiltIn
	subordinate.TypeName = "relationship"
	subordinate.AttrGroup = fmt.Sprintf("%s_relation", mrr.ModelCode)
	subordinate.Init()
	subordinate.Attrs.RelFrom = mrr.ModelCode
	subordinate.Attrs.RelFromName = src.Name
	subordinate.Attrs.RelTo = relTo
	subordinate.Attrs.RelType = int(v1.Subordinate)
	subordinate.Attrs.RelToDesc = mrr.RelDesc
	subordinate.Attrs.RelToName = parentModel.Name

	// 4. 创建从属关系，其实创建从属关系有三个步骤，这三个步骤应该在一个事务中，否则会有脏数据的产生
	// 第一步是创建从属关系这个字段
	// 第二步是设置当前模型的parent字段
	// 第三步是设置父模型的children字段
	t, err := m.store.Common().StartTransaction()
	if err != nil {
		return nil, err
	}
	// 4.1 创建从属关系字段
	t.Wrapper(func(operator string, rel interface{}) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			ma, err := m.store.ModelAttributes().CreateModelAttributes(sessionCtx, operator, rel)
			if err != nil {
				zap.L().Error("创建从属关系失败", zap.Error(err))
				return err
			}

			// 审计日志
			attrFields := make(mapdata.MapData)
			bytes, err := json.Marshal(ma)
			if err != nil {
				return err
			}
			if err := json.Unmarshal(bytes, &attrFields); err != nil {
				return err
			}

			attrId := utils.ToString(attrFields["id"])
			if attrId == "" {
				zap.L().Error("创建从属关系失败，获取属性ID失败")
				return errors.New("创建从属关系失败，获取属性ID失败")
			}

			au := &AuditEvent{store: m.store}
			if err := au.Generate(
				ctx,
				audit.AuditLogEvent,
				audit.ModelRelationshipSubRes,
				audit.ActionCreate,
				operator,
				attrId,
				nil,
				attrFields,
			); err != nil {
				return err
			}
			if err := au.Save(ctx); err != nil {
				zap.L().Error("保存审计日志失败", zap.Error(err))
				return err
			}
			return nil
		}
	}(operator, subordinate))

	// 4.2 更新当前模型的父级模型以及父模型的子模型字段。
	t.Wrapper(func() func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			var err error
			son, err := m.store.Model().GetModelByCode(sessionCtx, mrr.ModelCode)
			if err != nil {
				return nil
			}
			// 更新当前模型的父级模型
			son.ParentCode = relTo

			if err := m.store.Model().UpdateModel(sessionCtx, son); err != nil {
				zap.L().Error("更新子模型失败", zap.Error(err))
				return err
			}

			// 更新父级模型的Children字段，父模型的ChildrenCode是一个array，直接append进去然后更新。
			parent, err := m.store.Model().GetModelByCode(sessionCtx, relTo)
			if err != nil {
				return nil
			}

			// 避免重复添加children
			if !array.InArray(parent.ChildrenCode, mrr.ModelCode) {
				parent.ChildrenCode = append(parent.ChildrenCode, mrr.ModelCode)

				err = m.store.Model().UpdateModel(sessionCtx, parent)
				if err != nil {
					zap.L().Error("更新父模型失败", zap.Error(err))
					return err
				}
			}
			return nil
		}
	}())

	// 附加逻辑: 如果指向的父级是rack机柜类型，那么动态的新增两个字段，一个是设备的起始U位，一个是设备的高度
	// 字段名称固定为如下的结构，其中{{ model_code }}为当前模型的code，机柜模型的名称固定为rack，该模型为内置定义好的。
	// 起始U位: {{ model_code }}_start_u
	// 设备高度: {{ model_code }}_height
	if relTo == "rack" {
		t.Wrapper(func() func(sessionCtx context.Context) error {
			return func(sessionCtx context.Context) error {
				StartU := &v1.IntAttribute{
					BaseAttribute: v1.BaseAttribute{
						GeneralModelObj: v1.GeneralModelObj{
							Code:    fmt.Sprintf("%s_start_u", mrr.ModelCode),
							Name:    "起始U位",
							Builtin: true,
						},
						IsNull:        true,
						IsUnique:      false,
						IsEditAble:    true,
						SystemCreated: true,
						ModelCode:     mrr.ModelCode,
						AttrGroup:     fmt.Sprintf("%s_system", mrr.ModelCode),
						Describe:      "起始U位",
						TypeName:      "int",
					},
					Attrs: struct {
						v1.ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
						UserHint         string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
						Unit             string `json:"unit" bson:"unit" mapstructure:"unit"`
						Sample           string `json:"sample" bson:"sample" mapstructure:"sample"`
					}{
						ModelAttrRule: v1.ModelAttrRule{},
						UserHint:      "请输入设备起始U位",
						Unit:          "U",
						Sample:        "1",
					},
				}

				StartU.SetDefault(nil)

				// 首先查询一下是否已经存在了这个字段，如果存在了，那么就不需要再创建了
				_, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(sessionCtx, bson.M{
					"model_code": mrr.ModelCode,
					"code":       fmt.Sprintf("%s_start_u", mrr.ModelCode),
				}, bson.M{})

				// 如果说没有异常，那么就说明其实查出来了，已经存在这个字段了，此时直接返回nil，不再创建，
				if err == nil {
					zap.L().Info("设备起始U位字段已经存在，不需要再创建")
					return nil
				}

				if err != nil {
					if errors.Is(err, mongo.ErrNoDocuments) {
						zap.L().Debug("设备起始U位字段不存在，创建该字段")
						ma, err := m.store.ModelAttributes().CreateModelAttributes(sessionCtx, operator, StartU)
						if err != nil {
							return err
						}

						// 审计日志
						maAttr, err := convert.StructToMap(ma)
						if err != nil {
							zap.L().Error("Convert Struct to map failed", zap.Error(err))
							return err
						}

						maID := utils.ToString(maAttr["id"])

						auditLog, err := GenerateAuditLog(ctx,
							nil, maAttr, nil, audit.ActionCreate, audit.ModelAttributeRes, operator, maID, "")
						if err != nil {
							return err
						}

						if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
							return err
						}
						return nil
					}
					return err
				}
				return nil
			}
		}())

		t.Wrapper(func() func(sessionCtx context.Context) error {
			return func(sessionCtx context.Context) error {
				Height := &v1.IntAttribute{
					BaseAttribute: v1.BaseAttribute{
						GeneralModelObj: v1.GeneralModelObj{
							Code:    fmt.Sprintf("%s_height", mrr.ModelCode),
							Name:    "设备高度",
							Builtin: true,
						},
						IsNull:        true,
						IsUnique:      false,
						IsEditAble:    true,
						SystemCreated: true,
						ModelCode:     mrr.ModelCode,
						AttrGroup:     fmt.Sprintf("%s_basic", mrr.ModelCode),
						Describe:      "设备高度",
						TypeName:      "int",
					},
					Attrs: struct {
						v1.ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
						UserHint         string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
						Unit             string `json:"unit" bson:"unit" mapstructure:"unit"`
						Sample           string `json:"sample" bson:"sample" mapstructure:"sample"`
					}{
						ModelAttrRule: v1.ModelAttrRule{},
						UserHint:      "请输入设备高度",
						Unit:          "U",
						Sample:        "1",
					},
				}

				Height.SetDefault(nil)

				// 首先查询一下是否已经存在了这个字段，如果存在了，那么就不需要再创建了
				_, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(sessionCtx, bson.M{
					"model_code": mrr.ModelCode,
					"code":       fmt.Sprintf("%s_height", mrr.ModelCode),
				}, bson.M{})

				// 如果说没有异常，那么就说明其实查出来了，已经存在这个字段了，此时直接返回nil，不再创建，
				if err == nil {
					zap.L().Info("设备高度字段已经存在，不需要再创建")
					return nil
				} else {
					if errors.Is(err, mongo.ErrNoDocuments) {
						zap.L().Debug("设备高度字段不存在，创建该字段")
						ma, err := m.store.ModelAttributes().CreateModelAttributes(sessionCtx, operator, Height)
						if err != nil {
							return err
						}
						// 审计日志
						maAttr, err := convert.StructToMap(ma)
						if err != nil {
							zap.L().Error("Convert Struct to map failed", zap.Error(err))
							return err
						}

						maID := utils.ToString(maAttr["id"])

						auditLog, err := GenerateAuditLog(ctx,
							nil, maAttr, nil, audit.ActionCreate, audit.ModelAttributeRes, operator, maID, "")
						if err != nil {
							return err
						}

						if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
							return err
						}
						return nil
					}
					return err
				}
			}
		}())

		t.Wrapper(func() func(sessionCtx context.Context) error {
			return func(sessionCtx context.Context) error {
				Standard := &v1.IntAttribute{
					BaseAttribute: v1.BaseAttribute{
						GeneralModelObj: v1.GeneralModelObj{
							Code:    fmt.Sprintf("%s_standard", mrr.ModelCode),
							Name:    "是否为标准设备",
							Builtin: true,
						},
						IsNull:        false,
						IsUnique:      false,
						IsEditAble:    true,
						SystemCreated: true,
						ModelCode:     mrr.ModelCode,
						AttrGroup:     fmt.Sprintf("%s_basic", mrr.ModelCode),
						Describe:      "是否为标准设备",
						TypeName:      "bool",
					},
					Attrs: struct {
						v1.ModelAttrRule `json:"rule" bson:"rule" mapstructure:"rule"`
						UserHint         string `json:"user_hint" bson:"user_hint" mapstructure:"user_hint"`
						Unit             string `json:"unit" bson:"unit" mapstructure:"unit"`
						Sample           string `json:"sample" bson:"sample" mapstructure:"sample"`
					}{
						ModelAttrRule: v1.ModelAttrRule{},
						UserHint:      "请选择设备是否为标准设备",
						Unit:          "",
						Sample:        "true",
					},
				}

				Standard.SetDefault(nil)

				// 首先查询一下是否已经存在了这个字段，如果存在了，那么就不需要再创建了
				_, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(sessionCtx, bson.M{
					"model_code": mrr.ModelCode,
					"code":       fmt.Sprintf("%s_standard", mrr.ModelCode),
				}, bson.M{})

				// 如果说没有异常，那么就说明其实查出来了，已经存在这个字段了，此时直接返回nil，不再创建，
				if err == nil {
					zap.L().Info("是否为标准设备字段已经存在，不需要再创建")
					return nil
				}

				if errors.Is(err, mongo.ErrNoDocuments) {
					zap.L().Debug("是否为标准设备字段不存在，创建该字段")
					ma, err := m.store.ModelAttributes().CreateModelAttributes(sessionCtx, operator, Standard)
					if err != nil {
						return err
					}
					// 审计日志
					maAttr, err := convert.StructToMap(ma)
					if err != nil {
						zap.L().Error("Convert Struct to map failed", zap.Error(err))
						return err
					}

					maID := utils.ToString(maAttr["id"])

					auditLog, err := GenerateAuditLog(ctx,
						nil, maAttr, nil, audit.ActionCreate, audit.ModelAttributeRes, operator, maID, "")
					if err != nil {
						return err
					}

					if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
						return err
					}
					return nil
				}

				return err
			}
		}())
	}

	if err = t.Do(ctx); err != nil {
		zap.L().Error("创建从属关系失败", zap.Error(err))
		return nil, err
	}

	// 刷新缓存，只记录日志，不管刷新是否成功，不影响正常的业务流程
	if err = m.store.Model().RefreshModelCache(ctx); err != nil {
		zap.L().Error("刷新缓存失败", zap.Error(err))
	}

	return subordinate, nil
}

// FormatRelationCode 格式化关系字段得唯一标识，src和dst分别为源目两端的模型的code
func FormatRelationCode(src, dst string, relType int, name string) string {
	relationType := v1.RelationType(relType)

	tmpName := utils.ToPinyin(name)
	timestamp := time.Now().UnixNano()
	if len(tmpName) > 5 {
		tmpName = tmpName[:5]
	}

	switch relationType {
	// MongoDB对于索引字段支持的最大长度是1024字节，当使用转拼音操作的时候，有可能对应的中文字符转换成拼音后，会导致超过1024字节的限制
	case v1.Subordinate:
		tmpl := fmt.Sprintf("%s_belong_%s_", src, dst)
		return fmt.Sprintf("%s%s%d", tmpl, tmpName, timestamp)
	case v1.Associate:
		tmpl := fmt.Sprintf("%s_connect_%s_", src, dst)
		return fmt.Sprintf("%s%s%d", tmpl, tmpName, timestamp)
	default:
		return ""
	}
}

// CheckLoop 检查是否存在环路
func (m *modelRelationService) CheckLoop(ctx context.Context, son, parent string) (bool, error) {
	var err error
	mlist, err := m.store.Model().GetAllModelList(ctx)
	if err != nil {
		zap.L().Error("查询模型列表失败", zap.Error(err))
		return false, err
	}

	// 1. 首先构建一个模型code的映射
	modelMap := make(map[string]v1.Model)
	for _, model := range mlist {
		modelMap[model.Code] = model
	}

	// 2. 遍历所有的模型，判断是否存在环路，首先所有模型之间并不是所有模型都会存在主从关系，只部分模型存在主从关系
	// 部分模型的主从关系形成的是多条链路，因此需要遍历模型并将多条链路都构建出来。
	var root []v1.Model
	for _, model := range mlist {
		var modelObj v1.Model

		if model.ParentCode != "" {
			modelObj = modelMap[model.ParentCode]
			modelObj.ChildrenCode = append(modelObj.ChildrenCode, model.Code)
		} else {
			modelObj = model
		}

		root = append(root, modelObj)
	}

	// 3. 我们的目的是要给模型添加父级模型，确保没有环路的关键是确保要添加的父模型不在该模型的子模型链条中。其实这个链条是一棵树
	// 比如  root
	//       /  \
	//      a    b
	//     / \  / \
	//    c   d e  f
	// ① 首先如果说我们要添加的这个模型的父模型是空的，那么就意味着当前模型一定是树根。如果说父模型不是空的，我们在之前的逻辑就已经判定完毕了
	// 有父模型的不允许再创建父级模型关系，需要先把父级模型关系删除再创建。因此根本不会走到CheckLoop的逻辑中来。
	// ② 既然是树根，那么我们只需要遍历这个树就可以了 (注意，这个树不一定是一个二叉树，可能是多叉树)，就比如物理机，交换机，防火墙都可以放到机柜上
	// ③ 那我们我们需要做的就是遍历这棵树，然后判断父级模型是否在遍历后的树的子节点里就可以了。

	// 首先判断子模型和父模型是否为空（虽然理论上这种情况不会存在，但是还是需要判断）
	if son == "" || parent == "" {
		return false, errno.ErrParameterRequired.Add("检测环路时，子模型和父模型都不能为空")
	}

	for _, v := range root {
		// 首先把这棵树取出来
		if v.Code == son {
			// 这棵树就是个树根，没有子节点
			if len(v.ChildrenCode) == 0 {
				// 父级节点指向了自己，这是不允许的，形成了自环路，返回true，即存在环路
				if v.Code == parent {
					return true, nil
				}
				// 这棵树只有一个节点，且父级节点不是自己，不存在环路，返回false，表示不存在环路
				return false, nil
			} else {
				// 这棵树有子节点，那么我们需要遍历这棵树，判断父级节点是否在子节点中
				var treeNodes []string
				var dfs func(root *v1.Model)
				// 先序遍历
				dfs = func(root *v1.Model) {
					if root == nil {
						return
					}
					treeNodes = append(treeNodes, root.Code)
					for _, t := range root.ChildrenCode {
						if t != "" {
							mod := modelMap[t]
							dfs(&mod)
						}
					}
				}
				// 遍历树并将树的节点放到treeNodes中
				dfs(&v)
				// 如果父级节点在子节点中，那么就存在环路，返回true
				if array.InArray(parent, treeNodes) {
					return true, nil
				}
				// 如果父级节点不在子节点中，那么就不存在环路，返回false
				return false, nil
			}
		}
	}

	return false, nil
}

// CreateModelAssociateRelation 创建模型关联关系
func (m *modelRelationService) CreateModelAssociateRelation(ctx context.Context, operator string, mar *v1.ModelAssociateRelationRequest) (any, error) {
	zap.L().Debug("CreateModelAssociateRelation Service Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelAssociateRelationService", "service")
	defer span.End()

	var err error

	// 0. 不可以指向自己
	if mar.RelModel1Code == mar.RelModel2Code {
		return nil, errno.ErrParameterInvalid.Add("关联关系不可以指向自己")
	}

	// 所有关系名称不允许重复，放在第一位进行校验
	conflict, err := m.checkRelationNameConflict(ctx, mar.Name)
	if err != nil {
		zap.L().Error("检查关系名称冲突失败", zap.Error(err))
		return nil, err
	}

	if conflict {
		zap.L().Error("关系名称已存在", zap.String("name", mar.Name))
		return nil, errno.ErrModelAssociateNameExist.Add("关系名称已存在")
	}

	// 判断源模型是否存在
	sourceModel, err := m.store.Model().GetModelByCode(ctx, mar.RelModel1Code)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("无法查询到模型", zap.Error(err))
			return nil, errno.ErrModelNotFound.Add("模型不存在")
		}
		zap.L().Error("查询模型失败", zap.Error(err))
		return nil, err
	}

	// 判断目标模型是否存在
	destModel, err := m.store.Model().GetModelByCode(ctx, mar.RelModel2Code)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("无法查询到模型", zap.Error(err))
			return nil, errno.ErrModelNotFound.Add("模型不存在")
		}
		zap.L().Error("查询模型失败", zap.Error(err))
		return nil, err
	}

	// 从属关系以及关联关系是互斥的，如果m1和m2已经建立过从属关系，那么就不允许再建立关联关系
	attrs, err := m.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"$or": []bson.M{
			{"model_code": mar.RelModel1Code, "attrs.rel_type": 1, "attrs.rel_to": mar.RelModel2Code},
			{"model_code": mar.RelModel2Code, "attrs.rel_type": 1, "attrs.rel_to": mar.RelModel1Code},
		},
	}, nil)
	if err != nil {
		zap.L().Error("查询从属关系失败", zap.Error(err))
		return nil, err
	}

	if len(attrs) > 0 {
		zap.L().Error("已和目标模型已经建立过从属关系，不允许再建立关联关系")
		return nil, errno.ErrModelRelationConflict.Add("已和目标模型已经建立过从属关系，不允许再建立关联关系")
	}

	// 2. 同一个模型下的名称要唯一
	if _, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{
		"model_code": mar.RelModel1Code,
		"name":       mar.Name,
	}, bson.M{}); err == nil {
		zap.L().Error("关系名称重复", zap.String("name", mar.Name), zap.String("model_code", mar.RelModel1Code))
		return nil, errno.ErrModelAssociateNameExist.Add("关系名称重复")
	}

	if _, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{
		"model_code": mar.RelModel2Code,
		"name":       mar.Name,
	}, bson.M{}); err == nil {
		zap.L().Error("关系名称重复", zap.String("name", mar.Name), zap.String("model_code", mar.RelModel2Code))
		return nil, errno.ErrModelAssociateNameExist.Add("关系名称重复")
	}

	// 3. 创建一条关系和两端的关系，因为关联关系是双端的，因此要分别在两端创建字段。
	// 初始化源目的模型的关系字段
	sourceRelField := v1.NewAssociationRelationshipAttribute()
	sourceRelFieldCode := FormatRelationCode(mar.RelModel1Code, mar.RelModel2Code, 2, mar.Name)

	destRelField := v1.NewAssociationRelationshipAttribute()
	destRelFieldCode := FormatRelationCode(mar.RelModel2Code, mar.RelModel1Code, 2, mar.Name)

	sourceRelField.Code = sourceRelFieldCode
	sourceRelField.ModelCode = mar.RelModel1Code
	sourceRelField.Name = mar.Name
	sourceRelField.TypeName = "relationship"
	sourceRelField.AttrGroup = fmt.Sprintf("%s_relation", mar.RelModel1Code)
	sourceRelField.Init()
	sourceRelField.Attrs.RelModelCode = mar.RelModel2Code
	sourceRelField.Attrs.RelFieldCode = destRelFieldCode
	sourceRelField.Attrs.RelType = int(v1.Associate)
	sourceRelField.Attrs.RelDesc = mar.RelDesc
	sourceRelField.Attrs.RelModelName = destModel.Name
	sourceRelField.Attrs.RelLocalModelCode = sourceModel.Code
	sourceRelField.Attrs.RelLocalModelName = sourceModel.Name
	sourceRelField.Builtin = mar.BuiltIn

	destRelField.Code = destRelFieldCode
	destRelField.ModelCode = mar.RelModel2Code
	destRelField.Name = mar.Name
	destRelField.TypeName = "relationship"
	destRelField.AttrGroup = fmt.Sprintf("%s_relation", mar.RelModel2Code)
	destRelField.Init()
	destRelField.Attrs.RelModelCode = mar.RelModel1Code
	destRelField.Attrs.RelFieldCode = sourceRelFieldCode
	destRelField.Attrs.RelType = int(v1.Associate)
	destRelField.Attrs.RelDesc = mar.RelDesc
	destRelField.Attrs.RelModelName = sourceModel.Name
	destRelField.Attrs.RelLocalModelCode = destModel.Code
	destRelField.Attrs.RelLocalModelName = destModel.Name
	destRelField.Builtin = mar.BuiltIn

	// 初始化关系
	relAssociate := v1.NewAssociateRelations()
	relAssociate.RelMode = int(mar.RelMode)
	relAssociate.RelModel1Code = mar.RelModel1Code
	relAssociate.RelModel2Code = mar.RelModel2Code
	relAssociate.RelField1Code = sourceRelFieldCode
	relAssociate.RelField2Code = destRelFieldCode
	relAssociate.RelModel1Name = sourceModel.Name
	relAssociate.RelModel2Name = destModel.Name
	relAssociate.Builtin = mar.BuiltIn

	// 插入之前，首先要判断一下对应的relation是否已经存在
	if _, err := m.store.ModelRelation().GetModelAssociateRelationByCustomFilter(ctx, bson.M{
		"rel_type":        2,
		"rel_field1_code": sourceRelFieldCode,
		"rel_field2_code": destRelFieldCode,
		"rel_model1_code": mar.RelModel1Code,
		"rel_model2_code": mar.RelModel2Code,
	}); err == nil {
		// 能查到说明没报错，也没有报ErrDocNotExist，那么就是已经存在了
		return nil, err
	}

	// 4. 构建一个事务同时把这三个事情都干了
	t, err := m.store.Common().StartTransaction()
	if err != nil {
		return nil, err
	}
	// 事务封装task
	t.Wrapper(func(operator string, rel any) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			ma, err := m.store.ModelAttributes().CreateModelAttributes(sessionCtx, operator, rel)
			if err != nil {
				zap.L().Error("创建关联关系失败", zap.Error(err))
				return err
			}
			// 审计日志
			maAttr, err := convert.StructToMap(ma)
			if err != nil {
				zap.L().Error("Convert Struct to map failed", zap.Error(err))
				return err
			}

			maID := utils.ToString(maAttr["id"])
			auditLog, err := GenerateAuditLog(ctx,
				nil, maAttr, nil, audit.ActionCreate, audit.ModelAttributeRes, operator, maID, "")
			if err != nil {
				return err
			}

			if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
				return err
			}
			return nil
		}
	}(operator, sourceRelField))

	t.Wrapper(func(operator string, rel any) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			ma, err := m.store.ModelAttributes().CreateModelAttributes(sessionCtx, operator, rel)
			if err != nil {
				zap.L().Error("创建关联关系失败", zap.Error(err))
				return err
			}
			// 审计日志
			maAttr, err := convert.StructToMap(ma)
			if err != nil {
				zap.L().Error("Convert Struct to map failed", zap.Error(err))
				return err
			}

			maID := utils.ToString(maAttr["id"])

			auditLog, err := GenerateAuditLog(ctx,
				nil, maAttr, nil, audit.ActionCreate, audit.ModelAttributeRes, operator, maID, "")
			if err != nil {
				return err
			}

			if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
				return err
			}
			return nil
		}
	}(operator, destRelField))

	t.Wrapper(func(operator string, rel *v1.AssociateRelations) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			err := m.store.ModelRelation().CreateModelAssociateRelation(sessionCtx, operator, rel)
			if err != nil {
				zap.L().Error("创建关联关系失败", zap.Error(err))
				return err
			}
			return nil
		}
	}(operator, relAssociate))

	// 执行事务
	if err = t.Do(ctx); err != nil {
		zap.L().Error("创建关联关系失败", zap.Error(err))
		return nil, err
	}

	return sourceRelField, nil
}

func (m *modelRelationService) DeleteModelAssociateRelation(ctx context.Context, operator string, idFilter *v1.IDFilter) error {
	zap.L().Debug("DeleteModelAssociateRelation Service Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelAssociateRelationService", "DELETE")
	defer span.End()

	// 我们在删除关联关系的时候，其实只需要删除一个关联关系的ID就可以了，因为这里删除的仅仅是模型的关联关系
	zap.L().Debug("1. 判断关联关系是否存在")
	attr, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{"_id": idFilter.ID}, bson.M{})
	if err != nil {
		zap.L().Error("无法查询到关联关系", zap.Error(err))
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		return err
	}
	rel, err := m.store.ModelRelation().GetModelRelationByFilter(ctx, bson.M{
		"$or": []bson.M{
			{"rel_field1_code": attr.Code, "rel_model1_code": attr.ModelCode},
			{"rel_field2_code": attr.Code, "rel_model2_code": attr.ModelCode},
		},
	})
	if err != nil {
		zap.L().Error("无法查询到对应的关联关系", zap.Error(err))
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrModelAssociateNotExists.Add("关联关系不存在")
		}
		return err
	}

	// 如果是内置或者由系统创建的关联关系，那么就不允许删除
	zap.L().Debug("2. 判断关联关系是否是内置关联关系")
	// 其实这里的判断是有一点多余的，在创建的时候，是一个事务，因此三个对象（两个关联关系的字段+关系对象）BuiltIn的属性是保持一致的。
	if rel.Builtin || attr.Builtin {
		zap.L().Error("内置模型关系不可以删除")
		return errno.ErrBuiltinObject.Add("内置模型关系不可以删除")
	}

	// 查看是否已经有数据使用了这个关联关系，如果有数据使用了这个关联关系，那么就不允许删除
	zap.L().Debug("3. 判断关联关系是否已经被使用")
	dataList, err := m.store.ModelData().GetModelDataByCustomFilter(ctx, bson.M{
		"associate_instances.rel_id": rel.ID,
	}, nil)

	if err != nil {
		zap.L().Error("查询模型数据失败", zap.Error(err))
		return err
	}

	if len(dataList) > 0 {
		zap.L().Error("已有资源关系的模型关系不可删除")
		return errno.ErrModelAssociateInUse.Add("已有资源关系的模型关系不可删除")
	}

	// 如果上述检查都过了，那么就正常删除即可，删除和添加其实是一样的，一共分为三个步骤，删除两端的关系字段
	// 删除model_relation这个集合中的关系，三个操作必须同时成功，否则就回滚，因此也需要放到一个事务中
	zap.L().Debug("4. 删除关联关系")
	s, err := m.store.Common().StartTransaction()
	if err != nil {
		zap.L().Error(err.Error())
		return nil
	}

	modelMap, err := m.store.Model().GetModelMapping(ctx)
	if err != nil {
		zap.L().Error(err.Error())
		return err
	}

	s.Wrapper(func(operator string, rel *v1.AssociateRelations) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			model, ok := modelMap[rel.RelModel1Code]
			if !ok {
				zap.L().Error("模型不存在", zap.String("model_code", rel.RelModel2Code))
				return errno.ErrModelNotFound.Add("模型不存在")
			}
			err := m.store.ModelAttributes().DeleteModelAttributes(sessionCtx, operator, &v1.InstanceFilter{Code: rel.RelField1Code})
			if err != nil {
				zap.L().Error("删除关联关系失败", zap.Error(err))
				return err
			}

			// audit log
			src := make(map[string]interface{}, 2)
			src["name"] = ""
			src["model"] = model.Name

			auditLog, err := GenerateAuditLog(ctx, src, nil, nil, audit.ActionDelete, audit.ModelAttributeRes, operator, "", model.Code)
			if err != nil {
				return err
			}

			if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
				return err
			}
			return nil
		}
	}(operator, rel))

	s.Wrapper(func(operator string, rel *v1.AssociateRelations) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			model, ok := modelMap[rel.RelModel2Code]
			if !ok {
				zap.L().Error("模型不存在", zap.String("model_code", rel.RelModel2Code))
				return errno.ErrModelNotFound.Add("模型不存在")
			}

			if err := m.store.ModelAttributes().DeleteModelAttributes(sessionCtx, operator, &v1.InstanceFilter{Code: rel.RelField2Code}); err != nil {
				zap.L().Error("删除关联关系失败", zap.Error(err))
				return err
			}

			// audit log
			src := make(map[string]interface{}, 2)
			src["name"] = ""
			src["model"] = model.Name

			auditLog, err := GenerateAuditLog(ctx, src, nil, nil, audit.ActionDelete, audit.ModelAttributeRes, operator, "", model.Code)
			if err != nil {
				return err
			}

			if err = SaveAuditLog(ctx, m.store, *auditLog); err != nil {
				return err
			}
			return nil
		}
	}(operator, rel))

	s.Wrapper(func(operator string, relID string) func(sessionCtx context.Context) error {
		return func(sessionCtx context.Context) error {
			if err := m.store.ModelRelation().DeleteModelAssociateRelation(sessionCtx, operator, relID); err != nil {
				zap.L().Error("删除关联关系失败", zap.Error(err))
				return err
			}
			return nil
		}
	}(operator, rel.ID))

	// 执行事务
	err = s.Do(ctx)
	if err != nil {
		zap.L().Error("删除关联关系失败", zap.Error(err))
		return err
	}

	return nil
}

// GetModelRelations 获取模型关系
func (m *modelRelationService) GetModelRelations(ctx context.Context, filter string) (*v1.ModelRelationResponse, error) {
	zap.L().Debug("GetModelRelations Service Called")
	span, ctx := apm.StartSpan(ctx, "GetModelRelationsService", "GET")
	defer span.End()

	// 1. 首先判断模型是否存在
	_, err := m.store.Model().GetModelByCode(ctx, filter)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			zap.L().Error("无法查询到模型", zap.Error(err))
			return nil, errno.ErrModelNotFound.Add("模型不存在")
		}
		zap.L().Error("查询模型失败", zap.Error(err))
		return nil, err
	}

	// 2. 查询当前模型的关系，即该模型的关联关系，以及由该模型为子模型的从属关系
	modelRelations, err := m.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"$or": []bson.M{
			{"model_code": filter, "type_name": "relationship"},   // 查询当前模型关联的，以及当前模型指向父模型的关系
			{"attrs.rel_to": filter, "type_name": "relationship"}, // 查看以当前模型为父模型的关系
		},
	}, bson.M{})

	if err != nil {
		zap.L().Error("查询关联关系失败", zap.Error(err))
		return nil, err
	}

	// 3. 创建response响应
	resp := v1.NewModelRelationResponse()

	for _, rel := range modelRelations {
		relType := utils.ToInt(rel.Attrs["rel_type"])
		switch v1.RelationType(relType) {
		case v1.Subordinate:
			var sub v1.SubordinationRelationshipAttribute
			if err := mapstructure.Decode(rel, &sub); err != nil {
				return nil, err
			}

			// isRack是定制化的代码，用于唯一判断机柜这个特殊类型。当isRack为True的时候，需要展示设置U位的配置页面供前端判断
			isRack := false
			if sub.Attrs.RelTo == "rack" {
				isRack = true
			}

			resp.Subordinate = append(resp.Subordinate, struct {
				v1.SubordinationRelationshipAttribute `json:",inline"`
				IsParent                              bool `json:"is_parent"`
				IsRack                                bool `json:"is_rack"`
			}{
				SubordinationRelationshipAttribute: sub,
				IsParent:                           sub.ModelCode != filter,
				IsRack:                             isRack,
			})

		case v1.Associate:
			var ass v1.AssociationRelationshipAttribute
			if err := mapstructure.Decode(rel, &ass); err != nil {
				return nil, err
			}
			relation, err := m.store.ModelRelation().GetModelRelationByFilter(ctx, bson.M{
				"$or": []bson.M{
					{"rel_field1_code": rel.Code},
					{"rel_field2_code": rel.Code},
				},
			})
			if err != nil {
				zap.L().Error("查询关联关系失败", zap.Error(err))
				return nil, err
			}
			relMode := 0
			if relation.RelMode == relMode {
				relMode = 2
			} else {
				relMode = relation.RelMode
			}
			if err != nil {
				zap.L().Error("查询关联关系失败", zap.Error(err))
			}
			resp.Associate = append(resp.Associate, struct {
				v1.AssociationRelationshipAttribute `json:",inline"`
				RelMode                             int `json:"rel_mode"`
			}{
				AssociationRelationshipAttribute: ass,
				RelMode:                          relMode,
			})
			
		default:
			return nil, errno.ErrModelRelationType.Add("关系类型错误")
		}
	}

	return resp, nil
}

func (m *modelRelationService) UpdateModelRelation(ctx context.Context, operator string, req *v1.UpdateModelRelationRequest) error {
	zap.L().Debug("UpdateModelRelation function called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelRelationService", "service")
	defer span.End()

	var err error

	// 首先查询这个关系是否存在
	rel, err := m.store.ModelAttributes().GetModelAttributeByID(ctx, req.RelID)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrModelRelationNotExist.Add("模型关系不存在，请检查rel_id是否合法")
		}
		return err
	}

	// 如果关系存在的话，需要看一下关系到底是什么类型的。
	var relField v1.RelationField
	if relField, err = rel.ToRelationField(); err != nil {
		return err
	}

	switch relField.RelType() {
	case v1.Subordinate:
		err = m.store.ModelRelation().UpdateModelRelation(ctx, v1.Subordinate, relField.GetID(), req.RelDesc)
		if err != nil {
			return err
		}
	case v1.Associate:
		ass1 := v1.NewAssociationRelationshipAttribute()
		err := mapstructure.Decode(rel, &ass1)
		if err != nil {
			return err
		}

		ass2, err := m.store.ModelAttributes().GetSingleModelAttributeByCustomFilter(ctx, bson.M{
			"model_code": ass1.Attrs.RelModelCode,
			"code":       ass1.Attrs.RelFieldCode,
		}, bson.M{})
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				zap.L().Error(fmt.Sprintf("模型关系[%s]不存在", ass1.Attrs.RelFieldCode))
				return errno.ErrModelRelationNotExist.Add(fmt.Sprintf("模型关系[%s]不存在", ass1.Attrs.RelFieldCode))
			}
			zap.L().Error(err.Error())
			return err
		}

		if err := m.store.ModelRelation().UpdateModelRelation(ctx, v1.Associate, ass1.ID, req.RelDesc); err != nil {
			return err
		}

		if err = m.store.ModelRelation().UpdateModelRelation(ctx, v1.Associate, ass2.ID, req.RelDesc); err != nil {
			return err
		}

	default:
		return errno.ErrModelRelationType.Add("未知的关系类型")
	}

	// 如无异常，则刷新缓存
	_, err = m.store.ModelAttributes().SetModelAttributesCache(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (m *modelRelationService) GetModelRelationTopo(ctx context.Context) (*v1.ModelRelationTopo, error) {
	zap.L().Debug("GetModelRelationTopo function called")
	span, ctx := apm.StartSpan(ctx, "GetModelRelationTopoService", "service")
	defer span.End()

	// 1. 首先查询所有的模型
	mlist, err := m.store.Model().GetAllModelList(ctx)
	if err != nil {
		zap.L().Error("查询模型列表失败", zap.Error(err))
		return nil, err
	}

	// 2. 查询所有关系字段
	attrs, err := m.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"type_name": "relationship",
	}, bson.M{})
	if err != nil {
		zap.L().Error("查询关系字段失败", zap.Error(err))
		return nil, err
	}

	// 3. 构建response
	resp := v1.NewModelRelationTopo()
	resp.Nodes = append(resp.Nodes, mlist...)

	relSet := make(map[string]struct{}, 0)
	for _, attr := range attrs {

		var (
			relCode1 string
			relCode2 string
		)

		// 如果不存在的话就添加进去
		if _, ok := relSet[attr.Name]; !ok {

			relSet[attr.Name] = struct{}{}

			relType := utils.ToInt(attr.Attrs["rel_type"])
			if relType == 1 {
				relCode1 = utils.ToString(attr.Attrs["rel_from"])
				relCode2 = utils.ToString(attr.Attrs["rel_to"])
			} else if relType == 2 {
				relCode1 = utils.ToString(attr.Attrs["rel_model_code"])
				relCode2 = utils.ToString(attr.Attrs["rel_local_model_code"])
			}

			relDesc := utils.ToString(attr.Attrs["rel_desc"])

			resp.Edges = append(resp.Edges, v1.Edge{
				ID:            attr.ID,
				Code:          attr.Code,
				Name:          attr.Name,
				Builtin:       attr.Builtin,
				ModelCode:     attr.ModelCode,
				SystemCreated: attr.SystemCreated,
				RelType:       relType,
				RelCode1:      relCode1,
				RelCode2:      relCode2,
				RelDesc:       relDesc,
			})
		} else {
			zap.L().Debug(fmt.Sprintf("去重关系名称 [%s]", attr.Name))
		}
	}

	return resp, nil
}
