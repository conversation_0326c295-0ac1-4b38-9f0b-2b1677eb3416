package v1

import (
	"context"
	"fmt"
	"math"
	"strings"

	"ks-knoc-server/internal/cmdbserver/store"
	api "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	model "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/pkg/mapdata"
	"ks-knoc-server/pkg/utils"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type AgentService interface {
	Report(ctx context.Context, operator string, payload *api.Payload) error
}

type agentService struct {
	store          store.Factory
	serviceFactory *service
}

var _ AgentService = (*agentService)(nil)

func newAgentService(svc *service) *agentService {
	return &agentService{
		store:          svc.store,
		serviceFactory: svc,
	}
}

func (s *agentService) checkDisks(ctx context.Context, operator string, disks []api.Disk) error {
	// 物理机, 唯一标识符是SN
	diskSNMap := make(map[string]api.Disk)
	for _, disk := range disks {
		if _, ok := diskSNMap[disk.DetailInfo.SN]; !ok {
			diskSNMap[disk.DetailInfo.SN] = disk
		}
	}

	diskSNList := make([]string, 0)
	for sn := range diskSNMap {
		diskSNList = append(diskSNList, sn)
	}

	// 查询出所有磁盘
	existingDisks, err := s.store.ModelData().GetModelDataByCustomFilter(ctx,
		bson.M{"meta_data.universal_sn": bson.M{"$in": diskSNList}}, nil)
	if err != nil {
		zap.L().Error("查询磁盘失败", zap.Error(err))
		return err
	}

	existingDisksMap := make(map[string]bool)
	for _, disk := range existingDisks {
		if sn, ok := disk.MetaData["universal_sn"]; ok {
			existingDisksMap[utils.ToString(sn)] = true
		}
	}

	var nonExistDisks []string
	for sn := range diskSNMap {
		if !existingDisksMap[sn] {
			nonExistDisks = append(nonExistDisks, sn)
		}
	}

	// TODO: 创建磁盘, 当前暂时不维护设备和磁盘之间的关系，仅做配件信息的录入
	if len(nonExistDisks) > 0 {
		for _, sn := range nonExistDisks {
			zap.L().Debug("未查到对应的磁盘，准备创建", zap.String("sn", sn))
			d := diskSNMap[sn]

			// 获取磁盘大小
			sizeSections := strings.Split(d.BasicInfo.Size, " ")
			if len(sizeSections) != 2 {
				zap.L().Error("磁盘大小格式不正确", zap.String("size", d.BasicInfo.Size))
				continue
			}
			size := utils.ToFloat64(sizeSections[0])
			unit := strings.ToUpper(strings.TrimSpace(sizeSections[1]))
			if unit == "GB" {
				size = math.Ceil(size)
			} else {
				if unit != "TB" {
					zap.L().Error("磁盘大小单位不正确", zap.String("size", d.BasicInfo.Size))
					continue
				}
				size = size * 1024
				size = math.Ceil(size)
			}

			// HDD-SAS-894GB
			// SSD-SAS-894GB
			name := fmt.Sprintf("%s-%s-%dGB", d.BasicInfo.Med, d.BasicInfo.Intf, utils.ToInt(size))

			// 生成磁盘的唯一标识符
			identifyValue := utils.GenerateIdentifyValue("disk", name)


			if _, err := s.serviceFactory.ModelData().CreateModelData(ctx, operator, &model.ModelData{
				ModelCode: "disk",
				Data: mapdata.MapData{
					"disk_code": identifyValue,
					"disk_name": name,
					"disk_model": d.BasicInfo.Model,
					"disk_sn": d.DetailInfo.SN,
					"disk_media": d.BasicInfo.Med,
					"disk_interface": d.BasicInfo.Intf,
					"disk_size": utils.ToInt(size),
				},
				IdentifyName:  "disk_code",
				IdentifyValue: identifyValue,
			}); err != nil {
				zap.L().Error("创建磁盘失败", zap.Error(err))
				return err
			}
		}
	} else {
		zap.L().Debug("没有需要创建的磁盘")
	}

	return nil
}

// Report 上报数据
func (s *agentService) Report(ctx context.Context, operator string, payload *api.Payload) error {
	zap.L().Debug("Report Function Called")
	span, ctx := apm.StartSpan(ctx, "Report", "service")
	defer span.End()

	// 获取请求体
	if payload.IsVM {
		zap.L().Debug("虚拟机上报数据", zap.String("ip", payload.DeviceID))
		// 虚拟机, 唯一标识符是IP地址
		modelDatas, err := s.store.ModelData().GetModelDataByCustomFilter(ctx,
			bson.M{"meta_data.universal_internal_ip": payload.DeviceID}, nil)
		if err != nil {
			return err
		}

		if len(modelDatas) == 0 {
			zap.L().Error("未查询到对应的虚拟机", zap.String("identifier", payload.DeviceID))
			return fmt.Errorf("未查询到对应的虚拟机: %s", payload.DeviceID)
		}

		// 更新模型数据

	} else {
		// 物理机, 唯一标识符是SN
		zap.L().Debug("物理机上报数据", zap.String("sn", payload.SystemInfo.SN))
		if err := s.checkDisks(ctx, operator, payload.DiskInfo.Disks); err != nil {
			return err
		}
	}

	return nil
}
