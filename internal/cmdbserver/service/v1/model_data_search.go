package v1

import (
	"context"

	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	kjson "ks-knoc-server/pkg/json"

	"github.com/olivere/elastic/v7"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

// getESQuery 获取ES查询语句
func getESQuery(qs *elastic.SearchSource) (string, error) {
	sourceQuery, err := qs.Source()
	if err != nil {
		return "", err
	}
	var jsonRawQuery []byte
	jsonRawQuery, err = kjson.Marshal(sourceQuery)
	if err != nil {
		return "", err
	}
	return string(jsonRawQuery), nil
}

// SaveESQueryTOLog 保存ES查询语句到日志中
func SaveESQueryTOLog(qs *elastic.SearchSource) (err error) {
	queryStr, err := getESQuery(qs)
	if err != nil {
		return
	}
	zap.L().Info("FuzzySearchModelData Store Called", zap.String("query", queryStr))
	return
}

// FuzzySearchModelData 模糊搜索模型数据
func (mds *modelDataService) FuzzySearchModelData(ctx context.Context, request *apiv1.FuzzySearchRequest, pageNumber, pageSizeNumber int64) (*apiv1.FuzzySearchResponse, error) {
	// 配置APM
	zap.L().Debug("FuzzySearchModelData Service called")
	span, ctx := apm.StartSpan(ctx, "FuzzySearchModelDataService", "service")
	defer span.End()

	var err error

	// 情况1：搜索的标签页切到了一个新的tab的时候，应该会把之前选中的模型清理掉，然后只传递一个模型分组过来。
	// 情况2：如果同时传递了模型和模型分组，那么可能是在某个分组下，选中了模型。作为过滤条件。但是此时需要判定模型是否属于这个分组。
	modelGroupFilter := make([]interface{}, 0)
	modelsFilter := make([]interface{}, 0)

	if request.ModelGroupCode != "" {
		modelGroupFilter = append(modelGroupFilter, request.ModelGroupCode)
	}

	// 构造一个模型的map，用于后面的模型过滤
	modelMapping, err := mds.store.Model().GetModelMapping(ctx)
	if err != nil {
		return nil, err
	}

	// 如果说传递了模型的话
	if len(request.ModelCode) > 0 {
		// 如果说传递了模型的code，那么就必须传递了模型分组的code
		if len(modelGroupFilter) <= 0 {
			return nil, errno.ErrFuzzySearchModelGroupCodeEmpty.Add("当模型不为空时，模糊搜索模型组code不能为空")
		}
		// 如果传递了模型，那么就是在模型下搜索的基础上，添加模型的过滤条件。
		for _, model := range request.ModelCode {
			// 有可能传递的模型都是错误的，所以需要判断一下
			if _, ok := modelMapping[model]; !ok {
				return nil, errno.ErrModelCodeNotExists.Addf("模型code %s 不存在", model)
			}
			// 判断模型是否属于这个分组
			if modelMapping[model].ModelGroup != request.ModelGroupCode {
				return nil, errno.ErrModelNotBelongToModelGroup.Addf("模型 %s 不属于模型分组 %s, 或分组 %s 不存在", model, request.ModelGroupCode, request.ModelGroupCode)
			}
			modelsFilter = append(modelsFilter, model)
		}
	}

	aggregationResult, modelList, err := mds.SearchAggregationInfo(ctx, []string{"info"}, request.Keyword)
	if err != nil {
		return nil, err
	}

	// 取出排序后的第一个model_group作为过滤条件，默认是没有model进行选中的。
	var defaultSelectModelGroup string
	if len(aggregationResult) > 0 {
		defaultSelectModelGroup = aggregationResult[0].Code
	}

	// 获取dataList
	dataResult, total, dataWithHighlightFields, err := mds.SearchDataInfo(ctx, []string{"info"},
		request.Keyword, defaultSelectModelGroup, modelGroupFilter, modelsFilter, pageNumber, pageSizeNumber)

	if err != nil {
		return nil, err
	}

	// 初始化一个FuzzySearchResponse
	fuzzySearchResponse := &apiv1.FuzzySearchResponse{}
	fuzzySearchResponse.ModelGroups = aggregationResult

	// 构造返回的数据
	pages := (total / pageSizeNumber) + 1
	dataResponse := make([]apiv1.DataResponse, 0)

	// 获取模型属性的映射
	modelAttrMapping, err := GetModelAttrMapping(ctx, modelList, mds.store)
	if err != nil {
		return nil, err
	}

	// 拼凑返回的数据
	for _, v := range dataResult {
		dataInfo := GetDataInfo(modelAttrMapping, v, dataWithHighlightFields[v.ID], request.Keyword)
		title := v.Name()
		dataResponse = append(dataResponse, apiv1.DataResponse{
			ModelData: *v,
			Info:      dataInfo,
			Title:     title,
		})
	}
	fuzzySearchResponse.Data = apiv1.SearchResultDataBlock{
		PageSize:    pageSizeNumber,
		Pages:       pages,
		CurrentPage: pageNumber,
		Total:       total,
		Data:        dataResponse,
	}
	return fuzzySearchResponse, nil
}
