package v1

import (
	"context"
	"sort"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/audit"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
)

type ModelService interface {
	// CreateModel 创建模型
	CreateModel(ctx context.Context, operator string, model *v1.Model) (*v1.Model, error)
	// GetModelList 获取模型列表
	GetModelList(ctx context.Context) ([]v1.Model, error)
	// GetModelMapping 获取模型的映射
	GetModelMapping(ctx context.Context) (map[string]v1.Model, error)
	// GetModelListWithPagination 获取模型列表
	GetModelListWithPagination(ctx context.Context, page, pageSize int64) (*apiv1.PaginatedModelResponse, error)
	// GetNestedModelList 获取嵌套的模型列表
	GetNestedModelList(ctx context.Context) ([]apiv1.ModelWithNested, error)
	// GetNestedModelListWithPagination 获取嵌套的模型列表，带分页的
	GetNestedModelListWithPagination(ctx context.Context, page, pageSize int64) (*apiv1.PaginatedModelResponseWithNested, error)
	// DeleteModel 删除模型
	DeleteModel(ctx context.Context, operator string, model *v1.InstanceFilter) error
	// GetModelByCode 根据模型的Code获取模型的详情
	GetModelByCode(ctx context.Context, code string) (*v1.Model, error)
	// GetIcons 获取模型的图标
	GetIcons(ctx context.Context) ([]v1.ModelIcon, error)
}

type modelService struct {
	store store.Factory
}

var _ ModelService = (*modelService)(nil)

func newModelService(srv *service) *modelService {
	return &modelService{store: srv.store}
}

// CreateModel 创建模型
func (ms *modelService) CreateModel(ctx context.Context, operator string, model *v1.Model) (*v1.Model, error) {
	zap.L().Debug("CreateModel Function Called")
	span, ctx := apm.StartSpan(ctx, "CreateModel", "service")
	defer span.End()

	// 设置默认的模型icon
	if model.Icon == "" {
		// 目前先写死了吧，就是这个了
		model.Icon = "https://s1-11624.kwimgs.com/kos/nlav11624/ks-knoc-server/cmdb/server.png"
	}

	// 先查一下字段是否有冲突，名称和唯一标识都不能重复，这种查询方式好lowB，看看有没有其他的方式可以判定
	if _, err := ms.store.Model().GetModelByCode(ctx, model.Code); err == nil {
		return nil, errno.ErrDuplicateKey.Add("唯一标识重复")
	}
	models, _ := ms.store.Model().GetModelListByFilter(ctx, bson.M{"name": model.Name})
	if len(models) > 0 {
		return nil, errno.ErrDuplicateKey.Add("模型名称重复")
	}

	modelInstance, err := ms.store.Model().CreateModel(ctx, operator, model)
	if err != nil {
		return nil, err
	}

	// 记录审计日志
	au := &AuditEvent{store: ms.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelRes,
		audit.ActionCreate,
		operator,
		modelInstance.ID,
		nil,
		modelInstance,
	); err != nil {
		return nil, err
	}
	if err := au.Save(ctx); err != nil {
		return nil, err
	}

	return modelInstance, nil
}

// GetModelList 获取模型列表
func (ms *modelService) GetModelList(ctx context.Context) ([]v1.Model, error) {
	zap.L().Debug("GetModelList ModelService Called")
	span, ctx := apm.StartSpan(ctx, "GetModelListService", "service")
	defer span.End()

	modelList, err := ms.store.Model().GetAllModelList(ctx)
	if err != nil {
		return nil, err
	}
	return modelList, nil
}

// GetModelMapping 获取模型映射，在GetModelList的基础上做了一层嵌套，返回的结果是map，其中key为model_code，value为v1.Model
func (ms *modelService) GetModelMapping(ctx context.Context) (map[string]v1.Model, error) {
	zap.L().Debug("GetModelMapping ModelService Called")
	span, ctx := apm.StartSpan(ctx, "GetModelMappingService", "service")
	defer span.End()

	modelMapping, err := ms.store.Model().GetModelMapping(ctx)
	if err != nil {
		return nil, err
	}
	return modelMapping, nil
}

// GetModelListWithPagination 获取模型列表
func (ms *modelService) GetModelListWithPagination(ctx context.Context, page, pageSize int64) (*apiv1.PaginatedModelResponse, error) {
	zap.L().Debug("GetModelList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetModelListService", "service")
	defer span.End()

	modelList, err := ms.store.Model().GetModelListWithPagination(ctx, bson.M{}, page, pageSize)
	if err != nil {
		return nil, err
	}
	return modelList, nil
}

// GetNestedModelList 获取嵌套的模型列表
func (ms *modelService) GetNestedModelList(ctx context.Context) ([]apiv1.ModelWithNested, error) {
	zap.L().Debug("GetNestedModelList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetNestedModelListService", "service")
	defer span.End()

	modelList, err := GenerateNestedModel(ctx, ms.store)
	if err != nil {
		return nil, err
	}

	return modelList, nil
}

// GetNestedModelListWithPagination 获取嵌套的模型列表，带分页的
func (ms *modelService) GetNestedModelListWithPagination(ctx context.Context, page, pageSize int64) (*apiv1.PaginatedModelResponseWithNested, error) {
	zap.L().Debug("GetNestedModelList Service Called")
	span, ctx := apm.StartSpan(ctx, "GetNestedModelListService", "service")
	defer span.End()

	// 1. 获取指定分页的模型分组列表
	groupList, err := ms.store.ModelGroup().GetModelGroupListWithPagination(ctx, page, pageSize)
	if err != nil {
		return nil, err
	}
	total, err := ms.store.ModelGroup().Count(ctx)
	if err != nil {
		return nil, err
	}
	// 2. 获取所有的模型列表
	groupCodeList := make([]string, 0)
	for _, group := range groupList {
		groupCodeList = append(groupCodeList, group.Code)
	}
	modelList, err := ms.store.Model().GetModelListByFilter(ctx, bson.M{"model_group": bson.M{"$in": groupCodeList}})
	if err != nil {
		return nil, err
	}
	// 3. 将模型分组和模型列表进行组合
	var (
		modelToGroup    = make(map[string][]v1.Model)
		modelCodeList   = make([]string, 0)
		modelMap        = make(map[string]v1.Model)
		nestedModelList []apiv1.ModelWithNested
	)
	for _, model := range modelList {
		modelCodeList = append(modelCodeList, model.Code)
		modelMap[model.Code] = model
	}
	// 查询到所有模型的数据总数，这里只查询一次，然后根据模型的code来获取对应的数据总数
	countInfo, err := ms.store.ModelData().GetModelDataCount(ctx, modelCodeList)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 更新模型的total字段
	for _, counter := range countInfo {
		model := modelMap[counter.Code]
		model.Total = counter.Count
		modelToGroup[model.ModelGroup] = append(modelToGroup[model.ModelGroup], model)
	}

	for _, group := range groupList {
		if modelToGroup[group.Code] == nil {
			modelToGroup[group.Code] = make([]v1.Model, 0)
		}
		m := apiv1.ModelWithNested{
			ID:       group.ID,
			Name:     group.Name,
			Code:     group.Code,
			Total:    len(modelToGroup[group.Code]),
			BuiltIn:  group.Builtin,
			Children: modelToGroup[group.Code],
		}
		nestedModelList = append(nestedModelList, m)
	}
	pages := (total / pageSize) + 1
	pmr := &apiv1.PaginatedModelResponseWithNested{
		Total:       total,
		Pages:       pages,
		PageSize:    pageSize,
		CurrentPage: page,
		Data:        nestedModelList,
	}
	return pmr, nil
}

// DeleteModel 删除模型
func (ms *modelService) DeleteModel(ctx context.Context, operator string, model *v1.InstanceFilter) error {
	zap.L().Debug("DeleteModel ModelTemplateService Called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelService", "service")
	defer span.End()
	// 1. 首先要判断这个模型下面还有没有数据
	dataList, err := ms.store.ModelData().GetModelDataByModelCode(ctx, model.Code)
	if err != nil {
		return nil
	}
	// 如果说长度大于0的话，说明模型下面还有数据，不可以被删除
	if len(dataList) > 0 {
		return errno.ErrDataDelete.Add("模型下有数据，无法直接删除模型，请清理数据后再删除模型")
	}

	// 2. 内置模型不允许删除
	m, err := ms.store.Model().GetModelByCode(ctx, model.Code)
	if err != nil {
		zap.L().Error("模型不存在", zap.Error(err))
		return err
	}
	if m.IsBuiltIn() {
		return errno.ErrBuiltinObject.Add("内置模型不允许删除")
	}

	// 3. 判断模型是否存在关系依赖，首先判断是否是其他模型的父模型
	sub, err := ms.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"type_name":      "relationship",
		"attrs.rel_type": 1,
		"attrs.rel_to":   model.Code,
	}, bson.M{})
	if err != nil {
		zap.L().Error("获取模型关系依赖失败", zap.Error(err))
		return err
	}
	if len(sub) > 0 {
		return errno.ErrModelSubordinateInUse.Add("该模型为其他父级模型，无法直接删除模型，请先清理主从关系后再删除模型")
	}

	// 4. 判断是否和其他模型有关联
	ass, err := ms.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"type_name":  "relationship",
		"model_code": model.Code,
	}, bson.M{})
	if err != nil {
		return err
	}

	if len(ass) > 0 {
		return errno.ErrModelAssociateInUse.Add("该模型与其他模型存在关联，无法直接删除模型，请先清理关联关系后，再删除模型")
	}

	model.Name = m.Name
	// 5. 没有数据且没有关系约束的时候，就可以删除模型了，注意删除模型的时候，要同时删除模型的属性分组以及属性
	err = ms.store.Model().DeleteModel(ctx, operator, model)
	if err != nil {
		zap.L().Error("删除模型失败", zap.Error(err))
		return err
	}

	// 记录审计日志
	au := &AuditEvent{store: ms.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelRes,
		audit.ActionDelete,
		operator,
		m.ID,
		m,
		nil,
	); err != nil {
		return err
	}
	if err := au.Save(ctx); err != nil {
		return err
	}
	return nil
}

// GetModelByCode 根据Code获取模型信息
func (ms *modelService) GetModelByCode(ctx context.Context, code string) (*v1.Model, error) {
	zap.L().Debug("GetModelByCode Service Called")
	span, ctx := apm.StartSpan(ctx, "GetModelByCode", "service")
	defer span.End()

	model, err := ms.store.Model().GetModelByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	return model, nil
}

// GetIcons 获取图标列表
func (ms *modelService) GetIcons(ctx context.Context) ([]v1.ModelIcon, error) {
	zap.L().Debug("GetIcons ModelTemplateService Called")
	span, ctx := apm.StartSpan(ctx, "GetIcons", "service")
	defer span.End()

	icons, err := ms.store.Model().GetIcons(ctx)
	if err != nil {
		return nil, err
	}
	return icons, nil
}

// GenerateNestedModel 获取嵌套的模型列表，在模型的外层嵌套模型分组
// 2023-09-27
// 优化：添加模型分组列表以及模型分组的缓存，同时将获取模型Count的操作放到一个查询中，避免多次请求数据库，请求时延从1000ms降低到50ms左右
func GenerateNestedModel(ctx context.Context, sf store.Factory) ([]apiv1.ModelWithNested, error) {
	// 1. 获取所有的模型分组列表
	groupList, err := sf.ModelGroup().GetModelGroupList(ctx)
	if err != nil {
		return nil, err
	}
	// 2. 获取所有的模型列表
	modelList, err := sf.Model().GetAllModelList(ctx)
	if err != nil {
		return nil, err
	}
	// 3. 将模型分组和模型列表进行组合
	var (
		modelToGroup    = make(map[string]v1.ModelList)
		modelCodeList   = make([]string, 0)
		modelMap        = make(map[string]v1.Model)
		nestedModelList []apiv1.ModelWithNested
	)
	// 遍历所有模型列表
	for _, model := range modelList {
		modelCodeList = append(modelCodeList, model.Code)
		modelMap[model.Code] = model
	}
	// 查询到所有模型的数据总数，这里只查询一次，然后根据模型的code来获取对应的数据总数
	countInfo, err := sf.ModelData().GetModelDataCount(ctx, modelCodeList)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	for _, counter := range countInfo {
		model := modelMap[counter.Code]
		model.Total = counter.Count
		modelToGroup[model.ModelGroup] = append(modelToGroup[model.ModelGroup], model)
	}

	// 遍历所有模型分组
	for _, group := range groupList {
		// 如果modelToGroup这个map对应的key下没有数据，那么就初始化一个空的v1.Model的数组
		if modelToGroup[group.Code] == nil {
			modelToGroup[group.Code] = make([]v1.Model, 0)
		} else {
			sort.Sort(modelToGroup[group.Code])
		}

		nestedModelList = append(nestedModelList, apiv1.ModelWithNested{
			ID:       group.ID,
			Name:     group.Name,
			Code:     group.Code,
			Total:    len(modelToGroup[group.Code]),
			Children: modelToGroup[group.Code],
		})
	}
	return nestedModelList, nil
}
