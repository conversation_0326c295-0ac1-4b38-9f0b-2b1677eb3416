package v1

import (
	"context"

	"ks-knoc-server/internal/cmdbserver/store"
	apiv1 "ks-knoc-server/internal/common/base/api/cmdbserver/v1"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"

	"go.elastic.co/apm"
)

type AuditService interface {
	GetAuditRecordList(ctx context.Context, request apiv1.AuditRecordRequest, page, pageSize int64) (*v1.AuditRecordResponse, error)
	GetAuditRecordDetail(ctx context.Context, request apiv1.AuditRecordRequestByID, page, pageSize int64) (*v1.AuditRecordResponse, error)
}

type auditService struct {
	store store.Factory
}

var _ AuditService = (*auditService)(nil)

func newAuditService(srv *service) *auditService {
	return &auditService{store: srv.store}
}

func (as *auditService) GetAuditRecordList(ctx context.Context, request apiv1.AuditRecordRequest, page, pageSize int64) (*v1.AuditRecordResponse, error) {
	span, ctx := apm.StartSpan(ctx, "GetAuditRecordList", "service")
	defer span.End()

	audits, err := as.store.Audit().GetAuditRecordList(ctx, request, page, pageSize)
	if err != nil {
		return nil, err
	}

	return audits, nil
}

func (as *auditService) GetAuditRecordDetail(ctx context.Context, request apiv1.AuditRecordRequestByID, page, pageSize int64) (*v1.AuditRecordResponse, error) {
	span, ctx := apm.StartSpan(ctx, "GetAuditRecordDetail", "service")
	defer span.End()

	audits, err := as.store.Audit().GetAuditRecordDetail(ctx, request, page, pageSize)
	if err != nil {
		return nil, err
	}
	return audits, nil
}
