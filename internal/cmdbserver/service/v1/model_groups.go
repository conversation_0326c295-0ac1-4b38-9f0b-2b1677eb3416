package v1

import (
	"context"
	"errors"

	"ks-knoc-server/internal/common/audit"

	"go.mongodb.org/mongo-driver/mongo"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	"ks-knoc-server/internal/cmdbserver/store"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
)

type ModelGroupService interface {
	// GetModelGroupList 获取模型分组列表
	GetModelGroupList(ctx context.Context) ([]v1.ModelGroup, error)
	// CreateModelGroup 创建模型分组
	CreateModelGroup(ctx context.Context, operator string, mg *v1.ModelGroup) (*v1.ModelGroup, error)
	// UpdateModelGroup 更新模型分组
	UpdateModelGroup(ctx context.Context, operator string, mg map[string]interface{}) error
	// DeleteModelGroup 删除模型分组
	DeleteModelGroup(ctx context.Context, operator string, mg *v1.InstanceFilter) error
}

type modelGroupService struct {
	store store.Factory
}

var _ ModelGroupService = (*modelGroupService)(nil)

func newModelGroupService(srv *service) *modelGroupService {
	return &modelGroupService{store: srv.store}
}

// GetModelGroupList 获取模型分组列表
func (mgs *modelGroupService) GetModelGroupList(ctx context.Context) ([]v1.ModelGroup, error) {
	zap.L().Debug("GetModelGroupList ModelTemplateService Called")
	span, ctx := apm.StartSpan(ctx, "GetModelGroupList", "service")
	defer span.End()

	modelGroupList, err := mgs.store.ModelGroup().GetModelGroupList(ctx)
	if err != nil {
		return nil, err
	}
	return modelGroupList, nil
}

// CreateModelGroup 创建模型分组
func (mgs *modelGroupService) CreateModelGroup(ctx context.Context, operator string, mg *v1.ModelGroup) (*v1.ModelGroup, error) {
	zap.L().Debug("CreateModelGroup ModelGroupService Called")
	span, ctx := apm.StartSpan(ctx, "CreateModelGroupService", "service")
	defer span.End()

	// 创建模型分组
	modelGroupInstance, err := mgs.store.ModelGroup().CreateModelGroup(ctx, operator, mg)
	if err != nil {
		return nil, err
	}

	// 记录审计日志
	au := &AuditEvent{store: mgs.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelGroupRes,
		audit.ActionCreate,
		operator,
		modelGroupInstance.ID,
		nil,
		mg,
	); err != nil {
		return nil, err
	}
	if err := au.Save(ctx); err != nil {
		return nil, err
	}

	return modelGroupInstance, nil
}

// UpdateModelGroup 更新模型分组
func (mgs *modelGroupService) UpdateModelGroup(ctx context.Context, operator string, mg map[string]any) error {
	zap.L().Debug("UpdateModelGroup ModelGroupService called")
	span, ctx := apm.StartSpan(ctx, "UpdateModelGroupService", "service")
	defer span.End()

	// 更新之前先把数据库里的模型分组给查出来
	modelGroup, err := mgs.store.ModelGroup().GetModelGroup(ctx, mg["code"].(string))
	if err != nil {
		return err
	}
	// 判断是否是内置的分组，内置分组不允许更新
	if modelGroup.IsBuiltIn() {
		return errno.ErrBuiltinObject.Addf("内置对象不允许更新")
	}

	newModelGroupName, ok := mg["name"].(string)
	if !ok {
		return errno.ErrDataNotExists.Addf("模型分组不存在")
	}

	// 如果新的模型分组名称和原来的模型分组名称相同，那么直接返回提交成功即可，无需记录审计日志
	if newModelGroupName == modelGroup.Name {
		return nil
	}

	// 由于name是必填项，因此不允许更新为空字符串
	if newModelGroupName == "" {
		return errno.ErrUpdateModelGroup.Add("模型分组名称不允许为空")
	}

	if _, err = mgs.store.ModelGroup().GetModelGroupByFilter(ctx, bson.M{"name": newModelGroupName}); err == nil {
		return errno.ErrUpdateModelGroup.Addf("模型分组名称不可重复，分组名称 [%s] 已经存在", newModelGroupName)
	}

	// 更新操作
	if err := mgs.store.ModelGroup().UpdateModelGroup(ctx, operator, mg, modelGroup); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return errno.ErrDataNotExists.Addf("您要更新的模型分组 %s 并不存在", mg["code"])
		}
		return err
	}

	// 审计日志
	au := &AuditEvent{store: mgs.store}
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelGroupRes,
		audit.ActionUpdate,
		operator,
		modelGroup.ID,
		modelGroup,
		mg,
	); err != nil {
		return err
	}
	if err := au.Save(ctx); err != nil {
		return err
	}

	return nil
}

// DeleteModelGroup 删除模型分组
func (mgs *modelGroupService) DeleteModelGroup(ctx context.Context, operator string, mg *v1.InstanceFilter) error {
	zap.L().Debug("DeleteModelGroup ModelTemplateService called")
	span, ctx := apm.StartSpan(ctx, "DeleteModelGroupService", "service")
	defer span.End()

	// 1. 首先看是不是内置的模型分组，如果是的话，那么不允许删除
	modelGroup, err := mgs.store.ModelGroup().GetModelGroup(ctx, mg.Code)
	if err != nil {
		return err
	}
	if modelGroup.IsBuiltIn() {
		return errno.ErrBuiltinObject.Addf("内置对象不允许删除")
	}
	mg.Name = modelGroup.Name
	// 2. 如果是非内置模型分组的话，还需要看分组下有没有模型，没有模型才允许删除。
	// 在这里只需要判断模型分组下有没有模型就可以了，不用再判断模型下有没有数据，因为这几个对象之间都是级联的关系
	// 模型分组下有模型不允许删除模型分组，模型下面有数据，不允许删除模型，属性分组下有属性，也就不允许删除属性分组；
	// 每一个对象只关注自己的那一部分即可。
	modelList, err := mgs.store.Model().GetModelListByFilter(ctx, bson.M{"model_group": mg.Code})
	if err != nil {
		return err
	}
	// 如果分组下有模型，那么返回来的切片大小一定是大于0的
	if len(modelList) > 0 {
		return errno.ErrDataDelete.Add("模型分组下存在模型，无法删除")
	}
	// 3. 删除模型分组
	if err = mgs.store.ModelGroup().DeleteModelGroup(ctx, operator, mg); err != nil {
		return errno.ErrDataNotExists.Addf("您要删除的模型分组 %s 并不存在", mg.Code)
	}
	// 4. audit log
	au := &AuditEvent{store: mgs.store}	
	if err := au.Generate(
		ctx,
		audit.AuditLogEvent,
		audit.ModelGroupRes,
		audit.ActionDelete,
		operator,
		modelGroup.ID,
		mg,
		nil,
	); err != nil {
		return err
	}
	if err := au.Save(ctx); err != nil {
		return err
	}
	return nil
}
