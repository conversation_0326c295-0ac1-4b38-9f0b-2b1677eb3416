package v1

import (
	"fmt"
	"math"

	"ks-knoc-server/internal/cmdbserver/store"
	"ks-knoc-server/internal/common/base/enum"
	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/internal/common/excel"
	"ks-knoc-server/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type ModelTemplateService interface {
	GetModelDataExcel(ctx *gin.Context, modelId, excludeId []string, modelCode, mode string) (*excelize.File, error)
	GetModelTemplateExcel(ctx *gin.Context, modelCode string) (*excelize.File, error)
}

type StoreMethod interface {
	GetStore() store.Factory
}

type modelTemplate struct {
	store store.Factory
}

var _ ModelTemplateService = (*modelTemplate)(nil)

func newModelTemplate(srv *service) *modelTemplate {
	return &modelTemplate{
		store: srv.store,
	}
}

func (t modelTemplate) GetModelDataExcel(ctx *gin.Context, modelIds, excludeId []string, modelCode, mode string) (*excelize.File, error) {
	var err error

	// 获取模型信息
	model, err := t.store.Model().GetModelByCode(ctx, modelCode)
	if err != nil {
		return nil, err
	}

	// 获取模型属性信息
	modelAttrs, err := t.store.ModelAttributes().GetModelAttributesByCustomFilter(ctx, bson.M{
		"type_name":  bson.M{"$ne": "relationship"},
		"model_code": modelCode,
	}, nil)
	if err != nil {
		return nil, err
	}

	// 初始化filter
	filter := bson.M{}
	filter["model_code"] = modelCode

	// 根据mode判定是下载模型下的所有数据，还是下载模型下的部分数据
	if mode == "all" {
		if len(excludeId) > 0 {
			filter["_id"] = bson.M{"$nin": excludeId}
		}
	} else {
		// 非全量下载，则根据ID进行过滤
		if len(modelIds) > 0 {
			filter["_id"] = bson.M{"$in": modelIds}
		}
	}

	// 获取要下载的数据
	modelDataList := make([]*v1.ModelData, 0)
	if len(modelIds) > 0 || mode == "all" {
		modelDataList, err = t.store.ModelData().GetModelDataByCustomFilter(ctx, filter, nil)
		if err != nil {
			return nil, err
		}
	}

	file := excelize.NewFile()
	f, err := WriteTemplateExcel(*model, modelAttrs, modelDataList, false, file)
	if err != nil {
		return nil, err
	}

	// 把默认生成的sheet1删除
	if err := f.DeleteSheet("sheet1"); err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 把第一个sheet设置为活跃度sheet
	f.SetActiveSheet(0)

	return f, nil
}

func (t modelTemplate) GetModelTemplateExcel(ctx *gin.Context, modelCode string) (*excelize.File, error) {
	model, err := t.store.Model().GetModelByCode(ctx, modelCode)
	if err != nil {
		return nil, err
	}
	// 查询对应模型的所有字段
	modelAttrs, err := t.store.ModelAttributes().GetModelAttributeList2(ctx, modelCode, nil, bson.M{
		"type_name":  bson.M{"$ne": "relationship"},
		"is_display": true,
	})
	if err != nil {
		return nil, err
	}

	// 获取初始化的模板，渲染模板的样式
	file, err := excel.GetInitTemplateFile(model.Name, len(modelAttrs))
	if err != nil {
		return nil, err
	}
	// 在渲染好的样式区域填充对应的模板内容
	file, col := t.parseAttr(model.Name, model.Code, file, modelAttrs, 'B', false)
	delColCount := len(modelAttrs) - col
	delCol := 'B' + col

	// 遍历删除多余的列
	for i := 0; i < delColCount; i++ {
		// 64 = 'A' + 1
		colTobeDeleted := utils.ConvertToColumnTitle(delCol - 64)
		if colTobeDeleted == "" {
			return nil, errno.InternalServerError.Add("内部错误, 要删除的列名为空")
		}
		if err := file.RemoveCol(model.Name, colTobeDeleted); err != nil {
			return nil, err
		}
		delCol--
	}
	return file, nil
}

// parseAttr attrs属性解析，返回file对象和有效属性数量
func (t modelTemplate) parseAttr(modelName, modelCode string, file *excelize.File, attrs []v1.CommonModelAttribute, startCol rune, showUnique bool) (*excelize.File, int) {
	exampleStyleId, _ := excel.GetSampleColStyle(file)
	count := 0
	col := 0
	prefixRow := int32(0)
	// 提前拼接出模型的唯一标识字段的名称
	modelAttrCodeName := fmt.Sprintf("%s_code", modelCode)
	for _, attr := range attrs {
		// 是否展示唯一标识字段，如果不展示的话，当遇到唯一标识字段的时候就直接略过
		if !showUnique {
			attrCode := attr.Code
			// 这里不可以直接判断是不是包含code，因为这样不严谨，有可能会误伤到其他包含code这个字符串的字段，所以要严格判等
			if attrCode == modelAttrCodeName {
				continue
			}
		}
		var index = int32(col)
		row := startCol + index

		if row > 90 {
			startCol = 64
			prefixRow = startCol + 1
			col = 1
			index = int32(col)
			row = startCol + index
		}

		// 第一行写入名称(从B1开始）
		name := attr.Name
		var cell string
		if prefixRow != int32(0) {
			cell = fmt.Sprintf("%c%c", prefixRow, row)
		} else {
			cell = fmt.Sprintf("%c", row)
		}
		if !attr.IsNull {
			_ = file.SetCellValue(modelName, cell+"1", name)
			_ = file.SetCellRichText(modelName, cell+"1", excel.GetRichTextRedStyle(name+"(必填)"))
		} else if !attr.IsEditAble {
			_ = file.SetCellValue(modelName, cell+"1", name)
			_ = file.SetCellRichText(modelName, cell+"1", excel.GetRichTextRedStyle(name+"(不可编辑)"))
		} else if attr.IsUnique {
			_ = file.SetCellValue(modelName, cell+"1", name)
			_ = file.SetCellRichText(modelName, cell+"1", excel.GetRichTextRedStyle(name+"(唯一)"))
		} else {
			_ = file.SetCellValue(modelName, cell+"1", name)
		}
		// 第二行写入字段类型
		typeName := attr.TypeName
		_ = file.SetCellValue(modelName, cell+"2", enum.GetI18NTypeName(typeName))
		// 第三行写入字段标识
		attrCode := attr.Code
		_ = file.SetCellValue(modelName, cell+"3", attrCode)
		if len(attr.Code) > len(attr.Name) {
			_ = file.SetColWidth(modelName, cell, cell, 1.2*float64(len(attr.Code)))
		} else if len(attr.Name) > len(attr.TypeName) {
			_ = file.SetColWidth(modelName, cell, cell, 1.2*float64(len(attr.Name)))
		} else {
			_ = file.SetColWidth(modelName, cell, cell, 1.2*float64(len(attr.TypeName)))
		}
		// 第四行写入示例值
		var example = ""
		switch typeName {
		case enum.Bool:
			example = "TRUE"
			break
		case enum.Date:
			example = "2025/1/31"
			break
		case enum.Time:
			example = "10:01:01"
			break
		case enum.Select:
			opts := attr.Attrs["opts"].(primitive.A)
			dep := attr.Attrs["depth"]
			var depth = 0
			switch dep.(type) {
			case int64:
				depth = int(dep.(int64))
			case int32:
				depth = int(dep.(int32))
			case int8:
				depth = int(dep.(int8))
			case int16:
				depth = int(dep.(int16))
			}
			if depth == 1 {
				for _, opt := range opts {
					tempOpt := opt.(map[string]interface{})
					if example == "" {
						example = fmt.Sprintf("填写实际值，如：%s\n", tempOpt["name"])
						_ = file.SetColWidth(modelName, cell, cell, float64(len(example))*0.8)
					}
					example += fmt.Sprintf("- %s\n", tempOpt["name"])
				}
			} else {
				chainData := attr.Attrs["chain_data"].(map[string]interface{})
				dataTree := chainData["data_tree"].(primitive.A)
				parentCode := chainData["parent_code"].(string)
				for _, findAttr := range attrs {
					if findAttr.Code == parentCode {
						file.SetCellValue(modelName, cell+"2", enum.GetI18NTypeName(typeName)+"（上级："+findAttr.Name+")")
					}
				}
				for _, primitiveNode := range dataTree {
					node := primitiveNode.(map[string]interface{})
					parentName := node["name"]
					if node["children"] == nil {
						continue
					}
					kids := node["children"].(primitive.A)
					for _, kid := range kids {
						children := kid.(map[string]interface{})
						if example == "" {
							example = fmt.Sprintf("填写实际值，如：%s\n", children["name"].(string))
						}
						example += fmt.Sprintf("%s-%s\n", parentName, children["name"].(string))
					}
				}
			}
			break
		default:
			sample := attr.Attrs["sample"]
			if sample == nil {
				break
			}
			example = sample.(string)
			break
		}
		_ = file.SetCellValue(modelName, cell+"4", example)
		_ = file.SetCellStyle(modelName, cell+"4", cell+"4", exampleStyleId)
		col += 1
		count += 1
	}

	return file, count
}

// WriteTemplateExcel 写入Excel数据，要求模型, 模型属性，模型数据一致
// 即属性是对应模型的，数据也是对应模型的
func WriteTemplateExcel(model v1.Model, modelAttrs []v1.CommonModelAttribute,
	modelDataList []*v1.ModelData, skipCodeField bool, file *excelize.File) (*excelize.File, error) {
	var f *excelize.File

	if file == nil {
		f = excelize.NewFile()
	} else {
		f = file
	}

	// 把当前模型所有的字段都查出来，但是要过滤掉关系字段以及唯一标识字段
	sheetName := model.Name
	idx, err := f.NewSheet(sheetName)
	if err != nil {
		zap.L().Error("创建sheet失败", zap.Error(err))
		return nil, err
	}

	// 将刚创建的sheet设置为活动sheet
	f.SetActiveSheet(idx)

	// 初始化表头
	if err := f.SetCellValue(sheetName, "A1", "字段名(请勿编辑)"); err != nil {
		return nil, err
	}
	if err := f.SetCellValue(sheetName, "A2", "字段类型(请勿编辑)"); err != nil {
		return nil, err
	}
	if err := f.SetCellValue(sheetName, "A3", "字段标识(请勿编辑)"); err != nil {
		return nil, err
	}
	if err := f.SetCellValue(sheetName, "A4", "示例(请勿编辑)"); err != nil {
		return nil, err
	}

	// 设置初始化的列宽，startCol和endCol分别表示起始列和结束列，A列是说明列，宽度为20个字符
	if err := f.SetColWidth(sheetName, "A", "A", 20); err != nil {
		return nil, err
	}

	// 设置A列的样式
	rowAStyleID, err := excel.GetColATableStyle(f)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	if err := f.SetCellStyle(sheetName, "A1", "A4", rowAStyleID); err != nil {
		zap.L().Error("设置表格样式失败", zap.Error(err))
		return nil, err
	}

	// 字段类型和字段标识使用的样式
	tableStyleId, err := excel.GetFieldTableColStyle(f)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 字段类型使用的样式
	fieldNameStyleId, err := excel.GetFieldNameStyle(f)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 列数取决于字段数量
	colNumber := 'B' + len(modelAttrs)

	// 把列数转换为列名, 65是A的ASCII码
	cell := utils.ConvertToColumnTitle(colNumber - 64)

	// 填充B2到cell+"4", 假设说cell是I，那么就是B2到I4，其实是一个矩形区域
	if err := f.SetCellStyle(sheetName, "B2", cell+"4", tableStyleId); err != nil {
		return nil, err
	}
	// 这里填充的是一个横向区域从B1到cell+"1"
	if err := f.SetCellStyle(sheetName, "B1", cell+"1", fieldNameStyleId); err != nil {
		return nil, err
	}

	_ = f.SetCellValue(sheetName, "A5", "实例数据")

	// 针对ID列单独进行处理
	idExample := "67d3e9ab4075805dd6f7dcd2"
	_ = f.SetCellValue(sheetName, "B1", "ID")
	_ = f.SetCellValue(sheetName, "B2", "字符串")
	_ = f.SetCellValue(sheetName, "B3", "id")
	_ = f.SetCellValue(sheetName, "B4", idExample)

	// 计算数据要填充到多少行，我们要把A4之后的所有行都填充上对应的样式
	endRowOfColA := fmt.Sprintf("A%d", 4+len(modelDataList))
	if err := f.SetCellStyle(sheetName, "A5", endRowOfColA, rowAStyleID); err != nil {
		zap.L().Error(err.Error())
	}

	// ID列设置的稍微宽一些
	if err := f.SetColWidth(sheetName, "B", "B", 1.2*float64(len(idExample))); err != nil {
		zap.L().Error(err.Error())
	}

	// 获取示例数据表格样式ID
	exampleColStyleID, err := excel.GetSampleColStyle(f)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	var (
		col      int32 = 0   // 初始化列数
		prefix   int32 = 0   // 初始化一个前缀，用于列大于Z列的时候，保存对应的列前缀
		startCol       = 'C' // 上面A列是说明列，B列是ID列，我们正式从C列开始写数据
	)

	// 填充示例数据
	identity := fmt.Sprintf("%s_code", model.Code)
	for _, attr := range modelAttrs {
		// 我不关心唯一标识字段，因为一堆随机生成的字符串，直接pass即可
		if attr.Code == identity && skipCodeField {
			continue
		}

		// 获取字段名称, 字段名称由两个部分组成，列明和行坐标（x, y）
		// 列名称：比如A，B，AA，AAA等
		// 行坐标：比如1，2，3，4等
		// 所以字段名称就是列名称+行坐标，比如：A1，B1，AA1，AAA1等
		var cellName string
		// 初始化列数，随着字段的遍历逐步自增
		index := col
		// 初始化列，从C列开始，默认首次循环，index为0，即列为C列，随每一次遍历，index增加1，列后移
		columnRune := startCol + index

		// ASCII中，Z是90，如果超过90，那么就重置列数，比如91代表的就应该是AA
		if columnRune > 90 {
			// @是64, A是65
			startCol = 64
			// 65代表 A
			prefix = startCol + 1
			col = 1
			// 1
			index = col
			// 64 + 1 = 65, 也是A
			columnRune = startCol + index
		}

		if prefix > 0 {
			cellName = fmt.Sprintf("%c%c", prefix, columnRune)
		} else {
			cellName = fmt.Sprintf("%c", columnRune)
		}

		// 首先写入第一行，第一行写字段名称
		if !attr.IsNull {
			_ = f.SetCellValue(sheetName, cellName+"1", attr.Name)
			_ = f.SetCellRichText(sheetName, cellName+"1", excel.GetRichTextRedStyle(attr.Name+"(必填)"))
		} else if !attr.IsEditAble {
			_ = f.SetCellValue(sheetName, cellName+"1", attr.Name)
			_ = f.SetCellRichText(sheetName, cellName+"1", excel.GetRichTextRedStyle(attr.Name+"(不可编辑)"))
		} else if attr.IsUnique {
			_ = f.SetCellValue(sheetName, cellName+"1", attr.Name)
			_ = f.SetCellRichText(sheetName, cellName+"1", excel.GetRichTextRedStyle(attr.Name+"(唯一)"))
		} else {
			_ = f.SetCellValue(sheetName, cellName+"1", attr.Name)
		}

		// 第二行写字段类型
		_ = f.SetCellValue(sheetName, cellName+"2", enum.GetI18NTypeName(attr.TypeName))

		// 第三行要写入字段的标识
		_ = f.SetCellValue(sheetName, cellName+"3", attr.Code)
		// 看名称和唯一标识谁更长一点，以谁的长度为准，避免表格出现展示不全的情况，提升用户的使用体验
		codeLength := utils.ToFloat64(len(attr.Code))
		nameLength := utils.ToFloat64(len(attr.Name))
		colWidth := math.Max(codeLength, nameLength)
		if err := f.SetColWidth(sheetName, cellName, cellName, 1.2*colWidth); err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}

		// 第四行写入示例值
		example := ""
		switch attr.TypeName {
		case enum.Bool:
			// 布尔值的示例值
			example = "TRUE"
		case enum.Date:
			// 日期示例值
			example = "2025/1/31"
		case enum.Time:
			// 时间示例值
			example = "10:01:01"
		case enum.Select:
			// 枚举类型的示例值
			opts := attr.Attrs["opts"].(primitive.A)
			depth := utils.ToInt(attr.Attrs["depth"])
			if depth == 1 {
				// 非继承的枚举字段
				for _, opt := range opts {
					tempOpt := opt.(map[string]interface{})
					if example == "" {
						example = fmt.Sprintf("填写实际值，如：%s\n", tempOpt["name"])
						_ = f.SetColWidth(sheetName, cellName, cellName, float64(len(example))*0.8)
					}
					example += fmt.Sprintf("- %s\n", tempOpt["name"])
				}
			} else {
				// 继承的枚举字段
				chainData := attr.Attrs["chain_data"].(map[string]interface{})
				dataTree := chainData["data_tree"].(primitive.A)
				parentCode := chainData["parent_code"].(string)
				for _, findAttr := range modelAttrs {
					if findAttr.Code == parentCode {
						_ = f.SetCellValue(sheetName, cellName+"2", enum.GetI18NTypeName(attr.TypeName)+"（上级："+findAttr.Name+")")
					}
				}
				for _, primitiveNode := range dataTree {
					node := primitiveNode.(map[string]interface{})
					parentName := node["name"]
					if node["children"] == nil {
						continue
					}
					kids := node["children"].(primitive.A)
					for _, kid := range kids {
						children := kid.(map[string]interface{})
						if example == "" {
							example = fmt.Sprintf("填写实际值，如：%s\n", children["name"].(string))
						}
						example += fmt.Sprintf("%s-%s\n", parentName, children["name"].(string))
					}
				}
			}
		default:
			sample := utils.ToString(attr.Attrs["sample"])
			if sample == "" {
				break
			}
			example = sample
		}
		// 写入示例值
		_ = f.SetCellValue(sheetName, cellName+"4", example)
		// 设置示例值的样式
		_ = f.SetCellStyle(sheetName, cellName+"4", cellName+"4", exampleColStyleID)

		// 列数增加
		col++
	}

	// 填充数据，但是只有当数据不为空的时候才填充
	if len(modelDataList) == 0 {
		return f, nil
	}
	for idx1, modelData := range modelDataList {
		idCell := fmt.Sprintf("%c%d", 'B', 5+idx1)
		// 写id
		_ = f.SetCellValue(sheetName, idCell, modelData.ID)
		for idx2, attr := range modelAttrs {
			// 跳过的字段，不要累加idx2，否则会造成有的列为空的。
			if attr.Code == identity && skipCodeField {
				idx2 -= 1
				continue
			}
			dataValue := utils.ToString(modelData.Data[attr.Code])
			if dataValue == "" {
				continue
			}
			// 从C列开始，A代表1，则C代表的是3
			colNameStr := utils.ConvertToColumnTitle(3 + idx2)
			cellName := fmt.Sprintf("%s%d", colNameStr, 5+idx1)

			// 写入数据
			_ = file.SetCellValue(sheetName, cellName, dataValue)
		}
	}

	return f, nil
}
