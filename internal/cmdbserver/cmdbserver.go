package cmdbserver

import (
	"ks-knoc-server/config"
	"ks-knoc-server/internal/cmdbserver/app"
	"ks-knoc-server/internal/cmdbserver/options"
)

// NewAPIServer 创建APIServer
func NewAPIServer() *app.App {
	opts := options.NewOptions()
	application := app.NewApp("CMDB API Server",
		config.ProjectName,
		app.WithOptions(opts),
		app.WithRunFunc(run(opts)),
	)

	return application
}

func run(opts *options.Options) app.RunFunc {
	return func(basename string) error {
		return nil
	}
}
