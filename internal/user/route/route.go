package route

import (
	"net/http"

	"ks-knoc-server/internal/common/http/middleware"
	commonMiddleware "ks-knoc-server/internal/common/middleware"
	"ks-knoc-server/internal/common/sso"
	"ks-knoc-server/internal/user/controller/v1/check"
	"ks-knoc-server/internal/user/controller/v1/user"
	"ks-knoc-server/internal/user/store"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm/module/apmgin"
)

// APIServerRouter APIServer路由
func APIServerRouter(g *gin.Engine, db *store.DataStore, mw ...gin.HandlerFunc) *gin.Engine {
	// 应用中间件
	g.Use(apmgin.Middleware(g))
	g.Use(gin.Recovery())
	g.Use(middleware.NoCache)
	g.Use(middleware.Options)
	g.Use(middleware.Secure)
	g.Use(mw...)
	g.GET("/sso", sso.LoginValidate)

	g.NoRoute(func(c *gin.Context) {
		c.String(http.StatusNotFound, "The incorrect API route, Please contact the Kwai IT Developer.")
	})

	// 定义API相关路由
	api := g.Group("/api")
	api.Use(middleware.GetRequestDuration)
	{
		// 初始化API v1版本的路由
		v1 := api.Group("/v1")
		v1.Use(commonMiddleware.CORSMiddleware)
		{
			// 初始化userController
			userController := user.NewUserController(db)
			// 定义用户相关路由
			userRoute := v1.Group("/user")
			// 模糊搜索用户
			userRoute.GET("/search", userController.FuzzySearchUser)
		}
	}

	// 定义服务的健康检查接口
	c := g.Group("/check")
	{
		c.GET("/health", check.HealthCheck)
	}

	return g
}
