package store

import (
	"ks-knoc-server/internal/common/base/model/user"

	"git.corp.kuaishou.com/infra/infra-framework-go.git/kdb"
)

const userTableName = "user_info"

type UserStore interface {
	GetUserByNumber(number string) (*user.User, error)
	GetUserByUsername(username string) (*user.User, error)
}

type userStore struct {
	db *kdb.Korm
}

var _ UserStore = (*userStore)(nil)

func newUserStore(ds *DataStore) *userStore {
	return &userStore{
		db: ds.Db,
	}
}

func (s *userStore) GetUserByNumber(number string) (*user.User, error) {
	u := &user.User{}
	if result := s.db.Table(userTableName).Where("number = ?", number).First(&u); result.Error != nil {
		return nil, result.Error
	}
	return u, nil
}

func (s *userStore) GetUserByUsername(username string) (*user.User, error) {
	u := &user.User{}
	if result := s.db.Table(userTableName).Where("username = ?", username).First(&u); result.Error != nil {
		return nil, result.Error
	}
	return u, nil
}

func (s *userStore) GetUserListByEmail(email string) (*user.User, error) {
	u := &user.User{}
	if result := s.db.Table(userTableName).Where("email = ?", email).First(u); result.Error != nil {
		return nil, result.Error
	}
	return u, nil
}
