package server

import (
	"time"

	"ks-knoc-server/internal/user/options"
	"ks-knoc-server/internal/user/route"
	"ks-knoc-server/internal/user/store"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server ...
type Server struct {
	Log *zap.Logger
	Web *gin.Engine
}

// NewServer 启动服务相关中间件初始化
func NewServer(opts *options.Options) (*Server, error) {
	var err error

	// 初始化logger
	logger, err := opts.LogsOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化DataStore
	ds := &store.DataStore{}

	// 生成路由的同时注入ds
	g := route.APIServerRouter(opts.WebOptions.Engine, ds)
	g.Use(ginzap.Ginzap(logger, time.RFC3339, true))
	g.Use(ginzap.RecoveryWithZap(logger, true))

	return &Server{Log: logger, Web: g}, nil
}
