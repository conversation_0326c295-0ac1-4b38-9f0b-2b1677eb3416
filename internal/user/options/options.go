package options

import (
	"encoding/json"

	"ks-knoc-server/config/user"
	"ks-knoc-server/internal/common/http"
	"ks-knoc-server/internal/common/logger"
)

// Options 初始化依赖的选项
type Options struct {
	WebOptions  *http.WebOptions   `json:"http"`
	LogsOptions *logger.LogOptions `json:"log"`
}

// NewOptions 配置文件以及依赖项初始化
func NewOptions() *Options {
	// 初始化Kconf配置文件
	if err := user.InitConfig(); err != nil {
		panic(err)
	}
	o := Options{
		WebOptions:  http.NewWebOptions(),
		LogsOptions: logger.NewLogOptions(),
	}
	return &o
}

func (o *Options) String() string {
	data, _ := json.Marshal(o)
	return string(data)
}
