package app

import (
	"context"
	"errors"
	"net/http"
	"time"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/user/options"
	"ks-knoc-server/internal/user/server"
	"ks-knoc-server/pkg/shutdown"

	"go.uber.org/zap"
)

// App ...
type App struct {
	basename    string
	name        string
	description string
	options     *options.Options
	runFunc     RunFunc
}

// RunFunc ...
type RunFunc func(basename string) error

// Option ...
type Option func(*App)

// WithOptions ...
func WithOptions(opt *options.Options) Option {
	return func(a *App) {
		a.options = opt
		a.options.LogsOptions.FileRename(config.APIName)
	}
}

// WithRunFunc ...
func WithRunFunc(run RunFunc) Option {
	return func(a *App) {
		a.runFunc = run
	}
}

// NewApp ...
func NewApp(name, basename string, opts ...Option) *App {
	a := &App{
		name:     name,
		basename: basename,
	}

	for _, o := range opts {
		o(a)
	}
	return a
}

// Run 启动服务
func (a *App) Run() {
	var err error

	srv, err := server.NewServer(a.options)
	if err != nil {
		panic(err)
	}

	// 程序意外关闭之前，将内存中的日志flush到磁盘
	defer func() {
		_ = srv.Log.Sync()
	}()

	srv.Log.Info("User Server Start Succeed.")
	a.options.WebOptions.Server.Handler = srv.Web
	if err = a.options.WebOptions.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
		srv.Log.Fatal("http server startup err", zap.Error(err))
	}

	shutdown.NewHook().Close(
		func() {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			if err = a.options.WebOptions.Shutdown(ctx); err != nil {
				srv.Log.Error("server shutdown err", zap.Error(err))
			}
		},
	)
}
