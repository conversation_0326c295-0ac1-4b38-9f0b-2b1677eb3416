package app

import (
	"context"
	"log"
	"sync"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/common/logger"
	"ks-knoc-server/internal/common/mq"
	"ks-knoc-server/internal/dnsagent/consumer"
)

// App DNS Agent应用
type App struct {
	ctx             context.Context
	cancel          context.CancelFunc
	db              *gorm.DB
	logger          *zap.Logger
	consumerService *mq.ConsumerService
	wg              sync.WaitGroup
}

// NewApp 创建DNS Agent应用
func NewApp() (*App, error) {
	// 初始化上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 初始化日志
	logger, err := logger.NewLogOptions().Init()
	if err != nil {
		log.Fatalf("初始化日志失败: %v", err)
		cancel()
		return nil, err
	}

	// 初始化数据库
	mysqlDB, err := db.NewMySQLOptions().Init()
	if err != nil {
		zap.L().Error("初始化数据库失败", zap.Error(err))
		cancel()
		return nil, err
	}

	// 创建DNS任务处理器
	taskHandler := consumer.NewDNSTaskHandler(mysqlDB, logger)

	// 创建Kafka消费者
	consumerConfig := mq.NewConsumerConfig()

	// 创建Kafka消费者
	kafkaConsumer, err := mq.NewKafkaConsumer(consumerConfig, taskHandler)
	if err != nil {
		logger.Error("创建Kafka消费者失败", zap.Error(err))
		cancel()
		return nil, err
	}

	// 创建消费者服务
	consumerService := mq.NewConsumerService(kafkaConsumer)

	// 创建DNS Agent应用
	app := &App{
		ctx:             ctx,
		cancel:          cancel,
		db:              mysqlDB,
		logger:          logger,
		consumerService: consumerService,
	}

	return app, nil
}

// Run 运行DNS Agent
func (a *App) Run() error {
	a.logger.Info("启动DNS Agent")

	// 启动消费者服务
	if err := a.consumerService.Start(); err != nil {
		a.logger.Error("启动消费者服务失败", zap.Error(err))
		return err
	}

	a.logger.Info("DNS Agent启动成功，等待任务...")

	// 等待停止信号
	<-a.ctx.Done()
	a.logger.Info("DNS Agent正在停止...")

	return nil
}

// Stop 停止DNS Agent
func (a *App) Stop() error {
	a.logger.Info("停止DNS Agent")

	// 停止消费者服务
	if err := a.consumerService.Stop(); err != nil {
		a.logger.Error("停止消费者服务失败", zap.Error(err))
	}

	// 取消上下文
	if a.cancel != nil {
		a.cancel()
	}

	// 等待所有goroutine结束
	a.wg.Wait()

	a.logger.Info("DNS Agent已停止")

	// 同步日志
	_ = a.logger.Sync()

	return nil
}
