package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/IBM/sarama"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
)

const (
	DnsViewTableName       = "view"
	DnsServerTableName     = "dns_server"
	DnsTaskTableName       = "dns_task"
	DnsTaskDetailTableName = "dns_task_details"
)

// DNSTaskHandler DNS任务处理器
type DNSTaskHandler struct {
	nsupdate *BindNsUpdate
	db       *gorm.DB
	logger   *zap.Logger
}

// NewDNSTaskHandler 创建DNS任务处理器
func NewDNSTaskHandler(db *gorm.DB, logger *zap.Logger) *DNSTaskHandler {
	// 初始化nsupdate处理器
	nsupdate := &BindNsUpdate{
		GetTaskDetails: func(taskID int64) ([]*model.TaskDetails, error) {
			var details []*model.TaskDetails
			if err := db.Table(DnsTaskDetailTableName).Where("task_id = ? AND status IN (?)", taskID, []string{
				string(model.TaskDetailStatusInit),
				string(model.TaskDetailStatusPending),
			}).Find(&details).Error; err != nil {
				return nil, fmt.Errorf("查询任务详情失败: %w", err)
			}
			return details, nil
		},
		GetServers: func() (map[string]*model.Server, error) {
			var servers []model.Server
			if err := db.Table(DnsServerTableName).Find(&servers).Error; err != nil {
				return nil, fmt.Errorf("查询服务器失败: %w", err)
			}

			serverMap := make(map[string]*model.Server)
			for i := range servers {
				serverMap[servers[i].IP] = &servers[i]
			}
			return serverMap, nil
		},
		GetViews: func() (map[string]*model.View, error) {
			views := make([]model.View, 0)
			if err := db.Table(DnsViewTableName).Find(&views).Error; err != nil {
				return nil, fmt.Errorf("查询视图失败: %w", err)
			}

			viewMap := make(map[string]*model.View)
			for i := range views {
				viewMap[views[i].Code] = &views[i]
			}
			return viewMap, nil
		},
		UpdateTaskDetailStatus: func(detailID int64, status model.TaskDetailStatus, resMsg string, reqTs, resTs int64) error {
			updates := map[string]any{
				"status":  status,
				"res_msg": resMsg,
			}

			// 只有当reqTs > 0时才更新req_ts
			if reqTs > 0 {
				updates["req_ts"] = reqTs
			}

			// 只有当resTs > 0时才更新res_ts
			if resTs > 0 {
				updates["res_ts"] = resTs
			}

			return db.Table(DnsTaskDetailTableName).Where("id = ?", detailID).Updates(updates).Error
		},
		keyToken: viper.GetString("keyToken"),
	}

	return &DNSTaskHandler{
		nsupdate: nsupdate,
		db:       db,
		logger:   logger,
	}
}

// Handle 实现MessageHandler接口，处理单条消息
func (h *DNSTaskHandler) Handle(ctx context.Context, message *sarama.ConsumerMessage) error {
	h.logger.Info("收到DNS任务消息",
		zap.String("topic", message.Topic),
		zap.Int32("partition", message.Partition),
		zap.Int64("offset", message.Offset),
	)

	// 解析任务消息
	var task model.Task
	if err := json.Unmarshal(message.Value, &task); err != nil {
		h.logger.Error("解析DNS任务消息失败", zap.Error(err))
		return err
	}

	h.logger.Info("开始处理DNS任务", zap.Int64("task_id", task.ID))

	// 更新任务状态为执行中
	if err := h.updateTaskStatus(task.ID, model.TaskStatusRunning); err != nil {
		h.logger.Error("更新任务状态失败", zap.Error(err), zap.Int64("task_id", task.ID))
		return err
	}

	// 使用nsupdate处理任务
	result, err := h.nsupdate.ProcessTask(ctx, &task)
	if err != nil {
		h.logger.Error("ProcessTask执行失败", zap.Error(err), zap.Int64("task_id", task.ID))
		// 更新任务状态为失败
		if err := h.updateTaskStatus(task.ID, model.TaskStatusFailed); err != nil {
			h.logger.Error("更新任务状态失败", zap.Error(err), zap.Int64("task_id", task.ID))
		}
		return err
	}

	if result == nil {
		// 说明没有执行任务，直接返回了
		return nil
	}

	// 更新任务计数（基于数据库中实际状态统计）
	if err := h.updateTaskCountsFromDB(task.ID); err != nil {
		h.logger.Error("更新任务计数失败", zap.Error(err), zap.Int64("task_id", task.ID))
		return err
	}

	// 检查是否所有TaskDetails都已完成（成功或失败）
	// 查询当前Task下所有TaskDetails的状态
	var (
		totalDetails     = make([]*model.TaskDetails, 0)
		completedDetails = make([]*model.TaskDetails, 0)
	)

	// 查询所有的任务详情
	if err := h.db.Table(DnsTaskDetailTableName).Where("task_id = ?", task.ID).Find(&totalDetails).Error; err != nil {
		h.logger.Error("查询任务详情总数失败", zap.Error(err), zap.Int64("task_id", task.ID))
		return err
	}

	zap.L().Debug("所有任务详情数量", zap.Int64("totalDetails", int64(len(totalDetails))))

	if err := h.db.Table(DnsTaskDetailTableName).Where("task_id = ? AND status IN (?)", task.ID, []string{
		string(model.TaskDetailStatusSuccess),
		string(model.TaskDetailStatusFailed),
		string(model.TaskDetailStatusTimeout),
		string(model.TaskDetailStatusSkipped),
	}).Find(&completedDetails).Error; err != nil {
		h.logger.Error("查询已完成任务详情数量失败", zap.Error(err), zap.Int64("task_id", task.ID))
		return err
	}

	zap.L().Debug("已完成任务详情数量（成功、失败、超时、跳过）", zap.Int64("completedDetails", int64(len(completedDetails))))

	// 判定一下是否存在失败的TaskDetails
	hasFailed := false
	for _, detail := range totalDetails {
		if detail.Status == model.TaskDetailStatusFailed || detail.Status == model.TaskDetailStatusTimeout {
			hasFailed = true
			break
		}
	}

	// 根据执行结果和完成情况决定最终状态
	if hasFailed {
		h.logger.Error("存在执行失败的任务")
		// 如果所有TaskDetails都已完成，则任务状态为失败
		if len(completedDetails) == len(totalDetails) {
			if err := h.updateTaskStatus(task.ID, model.TaskStatusFailed); err != nil {
				h.logger.Error("更新最终任务状态失败", zap.Error(err), zap.Int64("task_id", task.ID))
				return err
			}
		} else {
			// 还有未完成的TaskDetails，保持running状态等待重试
			h.logger.Info("任务部分失败，但还有未完成的TaskDetails，保持running状态", zap.Int64("task_id", task.ID))
		}
	} else {
		h.logger.Info("执行成功")
		// 如果所有TaskDetails都已完成且没有失败，则任务状态为成功
		if len(completedDetails) == len(totalDetails) {
			if err := h.updateTaskStatus(task.ID, model.TaskStatusSuccess); err != nil {
				h.logger.Error("更新最终任务状态失败", zap.Error(err), zap.Int64("task_id", task.ID))
				return err
			}
		} else {
			// 还有未完成的TaskDetails，保持running状态
			h.logger.Info("任务部分成功，但还有未完成的TaskDetails，保持running状态", zap.Int64("task_id", task.ID))
		}
	}

	// 注意：任务详情状态的更新已经在executeTask函数中处理了，这里不需要再次更新

	h.logger.Info("DNS任务处理完成", zap.Int64("task_id", task.ID))
	return nil
}

// updateTaskStatus 更新任务状态
func (h *DNSTaskHandler) updateTaskStatus(taskID int64, status model.TaskStatus) error {
	return h.db.Table(DnsTaskTableName).Where("id = ?", taskID).Updates(map[string]any{
		"status":      status,
		"update_time": time.Now().Unix(), // 使用秒级时间戳
	}).Error
}

// updateTaskCountsFromDB 从数据库实际状态统计并更新任务计数
func (h *DNSTaskHandler) updateTaskCountsFromDB(taskID int64) error {
	// 统计成功的TaskDetails数量
	var successCount int64
	if err := h.db.Table(DnsTaskDetailTableName).
		Where("task_id = ? AND status = ?", taskID, string(model.TaskDetailStatusSuccess)).Count(&successCount).Error; err != nil {
		return fmt.Errorf("统计成功任务详情数量失败: %w", err)
	}

	// 统计失败的TaskDetails数量
	var failedCount int64
	if err := h.db.Table(DnsTaskDetailTableName).Where("task_id = ? AND status IN (?)", taskID, []string{
		string(model.TaskDetailStatusFailed),
		string(model.TaskDetailStatusTimeout),
	}).Count(&failedCount).Error; err != nil {
		return fmt.Errorf("统计失败任务详情数量失败: %w", err)
	}

	// 更新任务计数
	return h.db.Table(DnsTaskTableName).Where("id = ?", taskID).Updates(map[string]any{
		"success_count": int(successCount),
		"failed_count":  int(failedCount),
		"update_time":   time.Now().Unix(),
	}).Error
}
