package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	model "ks-knoc-server/internal/common/base/model/dnsserver"
	"ks-knoc-server/internal/common/bind/nsupdate"
	"ks-knoc-server/internal/common/bind/tsig"
)

const (
	DefaultDnsPort = 53
)

type taskResult struct {
	err           error
	taskDetailIDs []int64
}

type BindNsUpdate struct {
	keyToken string
	// 查询任务详情的函数
	GetTaskDetails func(taskID int64) ([]*model.TaskDetails, error)
	// 查询服务器信息的函数
	GetServers func() (map[string]*model.Server, error)
	// 查询视图信息的函数
	GetViews func() (map[string]*model.View, error)
	// 更新任务详情状态的函数
	UpdateTaskDetailStatus func(detailID int64, status model.TaskDetailStatus, resMsg string, reqTs, resTs int64) error
}

// ProcessTask 处理任务, 调用nsupdate
func (b *BindNsUpdate) ProcessTask(ctx context.Context, task *model.Task) (chan taskResult, error) {
	taskTargetInfo := &model.TaskTargetInfo{}
	if err := json.Unmarshal(task.TargetInfo, taskTargetInfo); err != nil {
		zap.L().Error("反序列化taskTargetInfo失败", zap.Error(err))
		return nil, fmt.Errorf("反序列化taskTargetInfo失败")
	}

	// 查询所有的task_details
	taskDetails, err := b.GetTaskDetails(task.ID)
	if err != nil {
		zap.L().Error("查询任务详情失败", zap.Error(err), zap.Int64("task_id", task.ID))
		return nil, fmt.Errorf("查询任务详情失败: %w", err)
	}

	if len(taskDetails) == 0 {
		zap.L().Info("任务无详情，直接返回", zap.Int64("task_id", task.ID))
		return nil, nil
	}

	// 只需要执行状态为init的task_details
	detailsToDo := make([]*model.TaskDetails, 0)
	for _, detail := range taskDetails {
		if detail.Status == model.TaskDetailStatusInit {
			detailsToDo = append(detailsToDo, detail)
		}
	}

	// 获取最新的服务器和视图信息
	serverMap, err := b.GetServers()
	if err != nil {
		zap.L().Error("查询服务器信息失败", zap.Error(err))
		return nil, fmt.Errorf("查询服务器信息失败: %w", err)
	}

	viewMap, err := b.GetViews()
	if err != nil {
		zap.L().Error("查询视图信息失败", zap.Error(err))
		return nil, fmt.Errorf("查询视图信息失败: %w", err)
	}

	// 按照server进行分组合并
	// {
	// 	serverIP: {
	// 		viewName: []*model.TaskDetails
	// 	}
	// }
	executeMapping := make(map[string]map[string][]*model.TaskDetails)
	for _, detail := range detailsToDo {
		// 按照server进行分组合并
		if _, ok := executeMapping[detail.ServerIP]; !ok {
			executeMapping[detail.ServerIP] = make(map[string][]*model.TaskDetails)
		}
		if _, ok := executeMapping[detail.ServerIP][detail.ViewName]; !ok {
			executeMapping[detail.ServerIP][detail.ViewName] = make([]*model.TaskDetails, 0)
		}
		executeMapping[detail.ServerIP][detail.ViewName] = append(executeMapping[detail.ServerIP][detail.ViewName], detail)
	}

	// 预先验证所有服务器和视图信息，避免在并发执行时出错
	type taskUnit struct {
		server   *model.Server
		view     *model.View
		zoneName string
		details  []*model.TaskDetails
	}

	var taskUnits []taskUnit

	// 预处理：验证并构建任务单元
	for serverIP, taskMapByView := range executeMapping {
		if len(taskMapByView) == 0 {
			zap.L().Info("server没有任务", zap.String("serverIP", serverIP))
			continue
		}

		serverObj, ok := serverMap[serverIP]
		if !ok {
			zap.L().Error("server信息不存在", zap.String("serverIP", serverIP))
			return nil, fmt.Errorf("server信息不存在: %s", serverIP)
		}

		for viewName, details := range taskMapByView {
			if len(details) == 0 {
				zap.L().Info("view没有任务", zap.String("viewName", viewName), zap.String("serverIP", serverIP))
				continue
			}

			viewObj, ok := viewMap[viewName]
			if !ok {
				zap.L().Error("view信息不存在", zap.String("viewName", viewName))
				return nil, fmt.Errorf("view信息不存在: %s", viewName)
			}

			taskUnits = append(taskUnits, taskUnit{
				server:   serverObj,
				view:     viewObj,
				zoneName: taskTargetInfo.ZoneName,
				details:  details,
			})
		}
	}

	if len(taskUnits) == 0 {
		zap.L().Info("没有有效的任务单元", zap.Int64("task_id", task.ID))
		return nil, nil
	}

	// 现在可以安全地并发执行，WaitGroup计数与实际goroutine数量完全匹配
	wg := sync.WaitGroup{}
	wg.Add(len(taskUnits))

	// 使用 errChan 收集错误，避免在goroutine中直接返回
	resultChan := make(chan taskResult, len(taskUnits))

	// 并发执行所有任务单元
	for _, unit := range taskUnits {
		go func(u taskUnit) {
			defer wg.Done()
			taskDetailIDs := make([]int64, 0)
			for _, detail := range u.details {
				taskDetailIDs = append(taskDetailIDs, detail.ID)
			}

			// 执行任务
			if err := b.executeTask(u.server, u.view, u.zoneName, u.details); err != nil {
				zap.L().Error("执行任务失败", zap.Error(err))
				resultChan <- taskResult{
					err:           err,
					taskDetailIDs: taskDetailIDs,
				}
			} else {
				// 成功
				resultChan <- taskResult{
					err:           nil,
					taskDetailIDs: taskDetailIDs,
				}
			}
		}(unit)
	}

	wg.Wait()
	close(resultChan)

	return resultChan, nil
}

func (b *BindNsUpdate) executeTask(server *model.Server, viewObj *model.View, zoneName string, tasks []*model.TaskDetails) error {
	zap.L().Debug("执行任务", zap.String("serverIP", server.ServerInfo.IP), zap.String("viewName", viewObj.Name), zap.String("zoneName", zoneName))

	// 记录开始执行时间（毫秒级，用于精确测量执行耗时）
	startTime := time.Now().UnixMilli()

	// 更新所有任务详情的req_ts为开始执行时间
	for _, task := range tasks {
		if b.UpdateTaskDetailStatus != nil {
			// 先更新req_ts，状态保持不变
			if err := b.UpdateTaskDetailStatus(task.ID, task.Status, task.ResMsg, startTime, task.ResTs); err != nil {
				zap.L().Error("更新任务详情req_ts失败", zap.Error(err), zap.Int64("task_id", task.ID))
			}
		}
	}

	builder := nsupdate.NewNsUpdateBuilderWithServer(
		server.ServerInfo.IP,
		server.ServerInfo.Port,
		zoneName,
		viewObj.Code,
		viewObj.ViewKeyAlgo,
	)

	// tsig的密钥是经过aes加密后的，所以这里需要进行解密
	secret, err := tsig.DecryptKey(b.keyToken, viewObj.ViewKey, viewObj.ViewKeyMD5)
	if err != nil {
		zap.L().Error("解密tsig密钥失败", zap.Error(err))
		// 记录结束时间并更新失败状态
		endTime := time.Now().UnixMilli()
		for _, task := range tasks {
			if b.UpdateTaskDetailStatus != nil {
				b.UpdateTaskDetailStatus(task.ID, model.TaskDetailStatusFailed, fmt.Sprintf("解密tsig密钥失败: %v", err), startTime, endTime)
			}
		}
		return fmt.Errorf("解密tsig密钥失败")
	}

	// 设置tsig密钥
	builder.SetTSIGSecret(secret)

	for _, task := range tasks {
		reqMsg := &model.RecordInfo{}
		if err := json.Unmarshal(task.ReqMsg, reqMsg); err != nil {
			zap.L().Error("反序列化reqMsg失败", zap.Error(err))
			// 记录结束时间并更新失败状态
			endTime := time.Now().UnixMilli()
			for _, t := range tasks {
				if b.UpdateTaskDetailStatus != nil {
					b.UpdateTaskDetailStatus(t.ID, model.TaskDetailStatusFailed, fmt.Sprintf("反序列化reqMsg失败: %v", err), startTime, endTime)
				}
			}
			return fmt.Errorf("反序列化reqMsg失败")
		}

		// 配置更新记录
		switch task.OpType {
		case model.RecordChangeCreate.String():
			builder.AddRecord(reqMsg.Name, reqMsg.Type, reqMsg.NewValue, uint32(reqMsg.TTL))
		case model.RecordChangeDelete.String():
			builder.DeleteRecord(reqMsg.Name, reqMsg.Type, reqMsg.OldValue)
		case model.RecordChangeUpdate.String():
			builder.UpdateRecord(reqMsg.Name, reqMsg.Type, reqMsg.OldValue, reqMsg.NewValue, uint32(reqMsg.TTL))
		default:
			zap.L().Error("不支持的操作类型", zap.String("opType", task.OpType))
			// 记录结束时间并更新失败状态
			endTime := time.Now().UnixMilli()
			for _, t := range tasks {
				if b.UpdateTaskDetailStatus != nil {
					b.UpdateTaskDetailStatus(t.ID, model.TaskDetailStatusFailed, fmt.Sprintf("不支持的操作类型: %s", task.OpType), startTime, endTime)
				}
			}
			return fmt.Errorf("不支持的操作类型")
		}
	}

	// 发送nsupdate请求
	if err := builder.Submit(); err != nil {
		zap.L().Error("发送nsupdate请求失败", zap.Error(err))
		// 记录结束时间并更新失败状态
		endTime := time.Now().UnixMilli()
		for _, task := range tasks {
			if b.UpdateTaskDetailStatus != nil {
				b.UpdateTaskDetailStatus(task.ID, model.TaskDetailStatusFailed, fmt.Sprintf("发送nsupdate请求失败: %v", err), startTime, endTime)
			}
		}
		return fmt.Errorf("发送nsupdate请求失败")
	}

	// 记录结束时间并更新成功状态
	endTime := time.Now().UnixMilli()
	for _, task := range tasks {
		if b.UpdateTaskDetailStatus != nil {
			if err := b.UpdateTaskDetailStatus(task.ID, model.TaskDetailStatusSuccess, "执行成功", startTime, endTime); err != nil {
				zap.L().Error("更新任务详情状态失败", zap.Error(err), zap.Int64("task_id", task.ID))
			}
		}
	}

	// 记录执行耗时
	duration := endTime - startTime
	zap.L().Debug("任务执行完成",
		zap.String("serverIP", server.ServerInfo.IP),
		zap.String("viewName", viewObj.Name),
		zap.String("zoneName", zoneName),
		zap.Int("taskCount", len(tasks)),
		zap.Int64("duration_ms", duration))

	return nil
}
