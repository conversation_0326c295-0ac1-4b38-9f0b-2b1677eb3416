package monitor

import (
	"net/http"

	"ks-knoc-server/internal/common/errno"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm"
	"go.uber.org/zap"
)

func (mc *Controller) GetMonitorTarget(c *gin.Context) {
	zap.L().Debug("GetMonitorTarget Controller Called")
	span, ctx := apm.StartSpan(c, "OnRackController", "GET")
	defer span.End()

	collector := c.<PERSON>("collector")
	office := c.Query("office")

	if collector == "" || office == "" {
		zap.L().Error("GetMonitorTarget Parameter Missing")
		err := errno.ErrParameterRequired.Add("collector 或 office 参数缺失")
		e := apm.CaptureError(ctx, err)
		e.Send()
		c.<PERSON>(http.StatusBadRequest, err)
		return
	}

	res, err := mc.svc.Target().GetMonitorTarget(ctx, collector, office)
	if err != nil {
		e := apm.CaptureError(ctx, err)
		e.Send()
		c.<PERSON>(http.StatusInternalServerError, err)
		return
	}

	c.<PERSON>(http.StatusOK, res)
}
