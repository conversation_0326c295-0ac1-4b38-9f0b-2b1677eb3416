package route

import (
	"net/http"

	"ks-knoc-server/internal/common/http/middleware"
	"ks-knoc-server/internal/monitor/controller/v1/check"
	"ks-knoc-server/internal/monitor/controller/v1/monitor"
	"ks-knoc-server/internal/monitor/store"

	"github.com/gin-gonic/gin"
	"go.elastic.co/apm/module/apmgin"
)

// APIServerRouter APIServer路由
func APIServerRouter(g *gin.Engine, db *store.DataStore, mw ...gin.HandlerFunc) *gin.Engine {

	// 应用中间件
	g.Use(apmgin.Middleware(g))
	g.Use(gin.Recovery())
	g.Use(middleware.NoCache)
	g.Use(middleware.Options)
	g.Use(middleware.Secure)
	g.Use(mw...)

	g.NoRoute(func(c *gin.Context) {
		c.String(http.StatusNotFound, "The incorrect API route, Please contact the Kwai IT Developer.")
	})

	// 定义API相关路由
	api := g.Group("/api")
	api.Use(middleware.GetRequestDuration)
	{
		// 初始化API v1版本的路由
		v1 := api.Group("/v1")
		{
			// 初始化cmdb的控制器
			monitorController := monitor.NewMonitorController(db)
			v1.GET("/monitor/targets", monitorController.GetMonitorTarget)
		}
	}

	// 定义服务的健康检查接口
	c := g.Group("/check")
	{
		c.GET("/health", check.HealthCheck)
	}

	return g
}
