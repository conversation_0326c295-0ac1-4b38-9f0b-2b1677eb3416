package v1

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	mon "ks-knoc-server/internal/common/base/model/monitor/v1"
	"ks-knoc-server/internal/common/errno"
	"ks-knoc-server/internal/monitor/store"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type TargetService interface {
	GetMonitorTarget(ctx context.Context, collector, office string) ([]*mon.HTTPSDObject, error)
}

type targetService struct {
	store store.Factory
}

func newTargetService(srv *service) *targetService {
	return &targetService{
		store: srv.store,
	}
}

// CollectorPrefix 定义采集器公共前缀
const CollectorPrefix = "__meta_collector_"

func mapToString(m map[string]string) string {
	// 先把key给排序了
	keys := make([]string, 0)
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 然后按照排序后的key构造字符串
	strSlice := make([]string, 0)
	for _, k := range keys {
		strSlice = append(strSlice, fmt.Sprintf("%s:%s", k, m[k]))
	}
	return strings.Join(strSlice, ",")
}

func stringToMap(s string) map[string]string {
	labelMap := make(map[string]string)
	kvPairs := strings.Split(s, ",")
	if len(kvPairs) == 0 {
		return nil
	}
	for _, kv := range kvPairs {
		ret := strings.Split(kv, ":")
		if len(ret) != 2 {
			return nil
		}
		labelMap[ret[0]] = ret[1]
	}
	return labelMap
}

func (t *targetService) GetMonitorTarget(ctx context.Context, collector, office string) ([]*mon.HTTPSDObject, error) {
	zap.L().Debug("GetMonitorTarget Service Called")
	span, ctx := apm.StartSpan(ctx, "GetMonitorTarget", "service")
	defer span.End()

	// 初始化一个要返回的数组
	sdList := make([]*mon.HTTPSDObject, 0)

	// 获取到对应的collector的端口，目前是写死在CollectorPortMapping这个变量中的
	port, exist := mon.CollectorPortMapping[collector]
	if !exist {
		return nil, errors.New("采集器" + collector + " 端口未配置，请确认您输入的collector是否正确或联系管理员")
	}

	// 对应的collector和office其实都是label标签，我们根据label标签搜索，根据label标签返回
	collectorKey, err := t.store.Labels().GetSpecifiedLabelKeyByName(ctx, CollectorPrefix+collector)
	zap.L().Debug("Search Label Key " + CollectorPrefix + collector)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	zap.L().Debug("Search Label Key " + CollectorPrefix + collector + " Success, LabelID is " + collectorKey.ID)

	officeKey, err := t.store.Labels().GetSpecifiedLabelKeyByName(ctx, "office")
	zap.L().Debug("Search Label Key office")
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}
	zap.L().Debug("Search Label Key office Success, LabelID is " + officeKey.ID)

	// 验证用户传递过来的collector的合法性
	labelCollector, err := t.store.Labels().GetLabelValue(ctx, collectorKey.ID, collector)
	zap.L().Debug("Search Label Value " + CollectorPrefix + collector + ":" + collector)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			errMsg := fmt.Sprintf("label %s%s:%s 不存在", CollectorPrefix, collector, collector)
			zap.L().Debug(errMsg)
			return nil, errno.ErrDataNotExists.Add(errMsg)
		}
		zap.L().Error(err.Error())
		return nil, err
	}
	zap.L().Debug("Search Label Value " + CollectorPrefix + collector + ":" + collector + " Success, LabelValue is " + labelCollector.Value)

	labelOffice, err := t.store.Labels().GetLabelValue(ctx, officeKey.ID, office)
	zap.L().Debug("Search Label Value office:" + office)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			errMsg := fmt.Sprintf("label office:%s 不存在", office)
			return nil, errno.ErrDataNotExists.Add(errMsg)
		}
		zap.L().Error(err.Error())
		return nil, err
	}
	zap.L().Debug("Search Label Value office:" + office + " Success, LabelValue is " + labelOffice.Value)

	// 根据collector和office查询包含对应label的数据对象
	dataList, err := t.store.Targets().GetTargetsByLabels(ctx, []string{labelCollector.ID, labelOffice.ID})
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	dataMap := make(map[string]v1.Dev)
	dataIDList := make([]string, 0)
	for _, data := range dataList {
		d, err := v1.ToDev(*data)
		if err != nil {
			zap.L().Error(err.Error())
			return nil, err
		}
		dataMap[data.ID] = d
		dataIDList = append(dataIDList, data.ID)
	}

	result, err := t.store.Targets().GetLabelMapByTargetIDs(ctx, dataIDList)
	if err != nil {
		zap.L().Error(err.Error())
		return nil, err
	}

	// 先构造map, 该map的key为排序后的labels map转换成的字符串，value为对应合并后的dataID列表
	compareMap := make(map[string][]string)
	for _, data := range result {
		strLabels := mapToString(data.Labels)
		if _, ok := compareMap[strLabels]; !ok {
			compareMap[strLabels] = []string{data.DataID}
		} else {
			compareMap[strLabels] = append(compareMap[strLabels], data.DataID)
		}
	}

	for k, v := range compareMap {
		targets := make([]string, 0)
		for _, dataID := range v {
			device, exist := dataMap[dataID]
			if !exist {
				return nil, errors.New("data and label mismatch")
			}
			ipAddr := device.IP()
			if ipAddr == "" {
				zap.L().Error("device ip is empty, device name is " + device.Name())
				return nil, fmt.Errorf("device ip is empty, device name is %s", device.Name())
			}
			targets = append(targets, fmt.Sprintf("%s:%d", ipAddr, port))
		}

		labels := stringToMap(k)
		labels["discover"] = "http"
		labels["port"] = fmt.Sprintf("%d", port)

		obj := mon.HTTPSDObject{
			Targets: targets,
			Labels:  labels,
		}

		sdList = append(sdList, &obj)
	}

	return sdList, nil
}
