package server

import (
	"time"

	"ks-knoc-server/internal/common/db"
	"ks-knoc-server/internal/monitor/options"
	"ks-knoc-server/internal/monitor/route"
	"ks-knoc-server/internal/monitor/store"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server ...
type Server struct {
	Mgo *db.MongoOptions
	Log *zap.Logger
	Web *gin.Engine
}

// NewServer 启动服务相关中间件初始化
func NewServer(opts *options.Options) (*Server, error) {
	var err error

	// 初始化logger
	logger, err := opts.LogsOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化MongoDB
	mgo, err := opts.MongoOptions.Init()
	if err != nil {
		return nil, err
	}

	// 初始化DataStore
	ds := &store.DataStore{Mgo: mgo}

	// 生成路由的同时注入ds
	g := route.APIServerRouter(opts.WebOptions.Engine, ds)
	g.Use(ginzap.Ginzap(logger, time.RFC3339, true))
	g.Use(ginzap.RecoveryWithZap(logger, true))

	return &Server{Log: logger, Mgo: mgo, Web: g}, nil
}
