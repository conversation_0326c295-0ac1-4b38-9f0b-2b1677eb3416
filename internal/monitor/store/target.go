package store

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	mon "ks-knoc-server/internal/common/base/model/monitor/v1"
	"ks-knoc-server/internal/common/db"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type TargetStore interface {
	GetTargetsByLabels(ctx context.Context, labelIDs []string) ([]*v1.ModelData, error)
	GetLabelMapByTargetIDs(ctx context.Context, dataIDs []string) ([]mon.TargetLabelMap, error)
}

type targetStore struct {
	mgo *db.MongoOptions
}

func newTargetStore(ds *DataStore) *targetStore {
	return &targetStore{mgo: ds.Mgo}
}

var _ TargetStore = (*targetStore)(nil)

func (ts *targetStore) GetTargetsByLabels(ctx context.Context, labelIDs []string) ([]*v1.ModelData, error) {
	zap.L().Debug("GetTargetsByLabels Function Called")
	span, ctx := apm.StartSpan(ctx, "GetTargetsByLabels", "store")
	defer span.End()

	dataList := make([]*v1.ModelData, 0)
	if err := ts.mgo.GetCollection("model_data").Find(ctx, bson.M{"label_id_list": bson.M{"$all": labelIDs}}).All(&dataList); err != nil {
		return nil, err
	}

	return dataList, nil
}

func (ts *targetStore) GetLabelMapByTargetIDs(ctx context.Context, dataIDs []string) ([]mon.TargetLabelMap, error) {
	zap.L().Debug("GetTargetByLabels Function Called")
	span, ctx := apm.StartSpan(ctx, "GetTargetByLabels", "store")
	defer span.End()

	pipe := make([]bson.M, 0)
	pipe = append(pipe, bson.M{"$match": bson.M{"_id": bson.M{"$in": dataIDs}}})
	pipe = append(pipe, bson.M{"$unwind": "$label_id_list"})
	pipe = append(pipe, bson.M{"$lookup": bson.M{
		"from":         v1.LabelValueColName,
		"localField":   "label_id_list",
		"foreignField": "_id",
		"as":           "label_value_info",
	}})
	pipe = append(pipe, bson.M{"$unwind": "$label_value_info"})
	pipe = append(pipe, bson.M{"$lookup": bson.M{
		"from":         v1.LabelKeyColName,
		"localField":   "label_value_info.key_id",
		"foreignField": "_id",
		"as":           "label_key_info",
	}})
	pipe = append(pipe, bson.M{"$unwind": "$label_key_info"})
	pipe = append(pipe, bson.M{"$group": bson.M{
		"_id": "$_id",
		"labels": bson.M{
			"$push": bson.M{
				"k": "$label_key_info.name",
				"v": "$label_value_info.value",
			},
		},
	}})
	pipe = append(pipe, bson.M{"$project": bson.M{"_id": 1, "labels": bson.M{"$arrayToObject": "$labels"}}})
	result := make([]mon.TargetLabelMap, 0)
	if err := ts.mgo.GetCollection("model_data").Aggregate(ctx, pipe).All(&result); err != nil {
		return nil, err
	}
	return result, nil
}
