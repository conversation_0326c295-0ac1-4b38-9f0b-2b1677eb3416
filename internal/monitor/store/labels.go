package store

import (
	"context"

	v1 "ks-knoc-server/internal/common/base/model/cmdbserver/v1"
	"ks-knoc-server/internal/common/db"

	"go.elastic.co/apm"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

type LabelStore interface {
	GetSpecifiedLabelKeyByName(ctx context.Context, key string) (*v1.Label<PERSON>ey, error)
	GetLabelValue(ctx context.Context, keyID, value string) (*v1.LabelValue, error)
}

func newLabelStore(ds *DataStore) *labelStore {
	return &labelStore{mgo: ds.Mgo}
}

type labelStore struct {
	mgo *db.MongoOptions
}

var _ LabelStore = (*labelStore)(nil)

func (s *labelStore) GetSpecifiedLabelKeyByName(ctx context.Context, key string) (*v1.LabelKey, error) {
	span, ctx := apm.StartSpan(ctx, "GetSpecifiedLabelKey", "store")
	defer span.End()

	var labelKey v1.LabelKey
	if err := s.mgo.GetCollection(v1.LabelKeyColName).Find(ctx, bson.M{"name": key}).One(&labelKey); err != nil {
		return nil, err
	}
	return &labelKey, nil
}

func (s *labelStore) GetLabelValue(ctx context.Context, keyID, value string) (*v1.LabelValue, error) {
	zap.L().Debug("GetLabelValue Store Called")
	span, ctx := apm.StartSpan(ctx, "GetLabelValue", "store")
	defer span.End()

	var lv v1.LabelValue
	if err := s.mgo.GetCollection(v1.LabelValueColName).Find(ctx, bson.M{"key_id": keyID, "value": value}).One(&lv); err != nil {
		return nil, err
	}
	return &lv, nil
}
