-- 流程执行日志表
-- 自己实现的原因是因为bpm的接口不支持记录接收任务节点，所以需要自己实现
CREATE TABLE `process_execution_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `business_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务ID',
  `process_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '流程Key',
  `node_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点ID',
  `node_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点名称',
  `node_type` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节点类型',
  `comments` text COLLATE utf8mb4_unicode_ci COMMENT '评论, 审批意见',
  `executor` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行人',
  `start_time` bigint NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` bigint NOT NULL DEFAULT '0' COMMENT '结束时间',
  `duration` bigint NOT NULL DEFAULT '0' COMMENT '执行时长，单位：秒',
  `status` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态, 比如：审批中，审批通过，审批拒绝，审批终止',
  `sequence` int NOT NULL COMMENT '执行顺序',
  `created_at` bigint NOT NULL COMMENT '创建时间',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  `task_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务ID，用于幂等性控制',
  `approval_list` text COLLATE utf8mb4_unicode_ci COMMENT '审批人列表',
  `event_id` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '唯一事件ID，用于幂等性控制',
  PRIMARY KEY (`id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_process_key` (`process_key`),
  KEY `idx_node_id` (`node_id`),
  KEY `idx_process_node` (`process_key`,`node_id`),
  KEY `idx_sequence` (`business_id`,`sequence`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程执行日志表';

-- 操作系统重装相关
CREATE TABLE `os_install` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `business_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单ID',
  `sn` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备序列号',
  `creator` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '发起人',
  `config` json NOT NULL COMMENT '装机信息',
  `install_status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '重装任务状态',
  `created_at` bigint NOT NULL COMMENT '创建时间',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sn` (`sn`),
  KEY `idx_status` (`install_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作系统安装表';

-- 安装任务详情表
CREATE TABLE `install_tasks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `os_install_id` bigint NOT NULL COMMENT 'install id',
  `task_name` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `task_status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '任务状态',
  `message` text COLLATE utf8mb4_general_ci COMMENT '响应消息',
  `start_time` bigint NOT NULL DEFAULT '0' COMMENT '开始时间戳',
  `end_time` bigint NOT NULL DEFAULT '0' COMMENT '结束时间戳',
  `duration` int NOT NULL DEFAULT '0' COMMENT '任务耗时',
  `created_at` bigint NOT NULL COMMENT '创建时间',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_task` (`os_install_id`,`task_name`),
  KEY `idx_install_id` (`os_install_id`),
  KEY `idx_task_name` (`task_name`),
  KEY `idx_status` (`task_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='安装任务详情表';

