-- 集群表
CREATE TABLE `cluster` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '集群ID',
  `name` varchar(128) NOT NULL COMMENT '集群名称',
  `office_id` varchar(128) NOT NULL COMMENT '职场ID',
  `description` varchar(1024) NOT NULL COMMENT '集群描述',
  `status` varchar(128) NOT NULL COMMENT '集群状态',
  `forwarders` json NOT NULL COMMENT '集群转发器',
  `for_vpn` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否VPN使用',
  `has_load_balance` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否负载均衡',
  `load_balance_ips` json NOT NULL COMMENT '负载均衡IP',
  `rndc_key_algo` varchar(128) NOT NULL COMMENT 'RNDC密钥算法',
  `rndc_key` varchar(255) NOT NULL COMMENT 'RNDC密钥',
  `rndc_key_md5` varchar(255) NOT NULL COMMENT 'RNDC密钥MD5',
  `secret_control` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否密钥控制',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `for_test` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否用于测试',
  `for_drill` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否用于演练',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_office_id` (`office_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='集群表';

-- 视图表
CREATE TABLE `view` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '视图ID',
    `name` VARCHAR(255) NOT NULL COMMENT '视图名称',
    `description` TEXT COMMENT '视图描述',
    `acls` JSON COMMENT 'view对应的ACL',
    `secret_control` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否开启密钥控制',
    `view_key_algo` VARCHAR(255) COMMENT 'view对应的key算法',
    `view_key` VARCHAR(255) COMMENT 'view对应的key',
    `view_key_md5` VARCHAR(255) COMMENT 'view对应的key的md5',
    `parent_id` BIGINT(20) UNSIGNED COMMENT '父视图ID',
    `level` INT NOT NULL COMMENT '视图级别',
    `level_type` VARCHAR(255) COMMENT '视图级别类型',
    `create_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='视图表';

-- 区域表
CREATE TABLE `zone` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '区域ID',
    `name` VARCHAR(255) NOT NULL COMMENT '区域名称',
    `zone_type` VARCHAR(50) NOT NULL COMMENT '区域类型',
    `description` VARCHAR(255) COMMENT '区域描述',
    `create_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='区域表';

-- 记录表
CREATE TABLE `records` (
    `id` BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `name` VARCHAR(255) NOT NULL COMMENT '记录名称',
    `r_type` VARCHAR(50) NOT NULL COMMENT '记录类型',
    `description` VARCHAR(1024) COMMENT '记录描述',
    `ttl` INT NOT NULL DEFAULT 3600 COMMENT 'TTL',
    `value` VARCHAR(512) COMMENT '记录值',
    `is_wildcard` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否泛解析',
    `zone_id` BIGINT(20) UNSIGNED NOT NULL COMMENT '区域ID',
    `view_id` BIGINT(20) UNSIGNED NOT NULL COMMENT '视图ID',
    `create_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '更新时间',
    `creator` VARCHAR(255) NOT NULL COMMENT '创建者',
    `owner` VARCHAR(255) NOT NULL COMMENT '负责人',
    `enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
    `mx_priority` INT(32) NOT NULL DEFAULT 0 COMMENT 'MX优先级',
    `srv_priority` INT(32) NOT NULL DEFAULT 0 COMMENT 'SRV优先级',
    `srv_weight` INT(32) NOT NULL DEFAULT 0 COMMENT 'SRV权重',
    `srv_port` INT(32) NOT NULL DEFAULT 0 COMMENT 'SRV端口',
    PRIMARY KEY (id),
    UNIQUE KEY `uk_name_rtype_zoneid_viewid_value` (`name`, `r_type`, `zone_id`, `view_id`, `value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='记录表';

-- 服务器表
CREATE TABLE `dns_server` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '服务器ID',
  `cluster_id` bigint NOT NULL COMMENT '对应的集群ID',
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器的主机名',
  `role` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器角色(master/slave/recursion/unknown)',
  `status` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器状态(running/stopped/unknown)',
  `ip` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器IP',
  `port` int NOT NULL COMMENT '服务器端口',
  `version` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '软件版本',
  `db_path` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '软件数据库路径',
  `checked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否检查',
  `last_alive_time` bigint NOT NULL DEFAULT '0' COMMENT '最后活跃时间',
  `view_id` bigint NOT NULL DEFAULT '0' COMMENT '线路版本ID',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `update_time` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cluster_id` (`cluster_id`),
  KEY `idx_view_id` (`view_id`),
  KEY `idx_ip_port` (`ip`,`port`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='DNS服务器表'

-- 任务表
CREATE TABLE `dns_task` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `status` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务状态(created/running/success/failed/canceled)',
  `target_info` json NOT NULL COMMENT '目标信息(包含要操作的记录和服务器)',
  `operator` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `execute_time` bigint NOT NULL DEFAULT '0' COMMENT '执行时间',
  `total_details` int NOT NULL DEFAULT '0' COMMENT '总子任务数',
  `success_count` int NOT NULL DEFAULT '0' COMMENT '成功子任务数',
  `failed_count` int NOT NULL DEFAULT '0' COMMENT '失败子任务数',
  `scheduled_at` bigint NOT NULL DEFAULT '0' COMMENT '计划执行时间',
  `create_time` bigint NOT NULL COMMENT '任务创建时间',
  `update_time` bigint NOT NULL COMMENT '任务更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_operator` (`operator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='DNS任务表'

-- 任务详情表
CREATE TABLE `dns_task_details` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '任务详情ID',
  `task_id` bigint unsigned NOT NULL COMMENT '所属任务ID',
  `op_type` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型(add/update/delete)',
  `status` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务状态(init/pending/running/success/failed/skipped/timeout)',
  `server_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器名称',
  `server_ip` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器IP',
  `zone_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '区域名称',
  `view_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '视图名称',
  `req_msg` json NOT NULL COMMENT '请求消息',
  `res_msg` text COLLATE utf8mb4_general_ci COMMENT '响应消息',
  `req_ts` bigint unsigned NOT NULL DEFAULT '0' COMMENT '请求时间戳',
  `res_ts` bigint unsigned NOT NULL DEFAULT '0' COMMENT '响应时间戳',
  `operation_timeout` int NOT NULL DEFAULT '60' COMMENT '操作超时时间(秒)',
  `create_at` bigint NOT NULL COMMENT '创建时间',
  `update_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_server_ip` (`server_ip`),
  KEY `idx_zone_view` (`zone_name`,`view_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='DNS任务详情表'

-- 系统配置表
CREATE TABLE `dns_system_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(255) NOT NULL COMMENT '配置键名',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(50) NOT NULL DEFAULT 'string' COMMENT '配置类型(string/json/int/bool)',
  `description` varchar(1024) DEFAULT '' COMMENT '配置描述',
  `category` varchar(100) DEFAULT 'general' COMMENT '配置分类',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加密存储',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `update_time` bigint NOT NULL COMMENT '更新时间',
  `creator` varchar(255) DEFAULT '' COMMENT '创建者',
  `updater` varchar(255) DEFAULT '' COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_config_key` (`config_key`),
  KEY `idx_category` (`category`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置表';

-- 插入示例配置数据
-- 插入系统配置示例数据
INSERT INTO `system_config` (
    `config_key`, 
    `config_value`, 
    `config_type`, 
    `description`, 
    `category`, 
    `create_time`, 
    `update_time`, 
    `creator`
) VALUES
-- nsupdate客户端IP排除配置
(
    'nsupdate_exclude_ips', 
    '["**********", "**********", "************"]', 
    'json', 
    'nsupdate客户端IP排除列表，这些IP不会被包含在view的match-clients中', 
    'dns', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
),
-- DNS相关配置
(
    'dns_cache_ttl', 
    '3600', 
    'int', 
    'DNS缓存TTL时间（秒）', 
    'dns', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
),
(
    'max_zone_records', 
    '10000', 
    'int', 
    '单个zone最大记录数限制', 
    'dns', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
),
-- 系统配置
(
    'enable_debug_log', 
    'false', 
    'bool', 
    '是否启用调试日志', 
    'system', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
),
(
    'backup_retention_days', 
    '30', 
    'int', 
    '配置备份保留天数', 
    'system', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
),
-- 通知配置
(
    'admin_notification_email', 
    '<EMAIL>', 
    'string', 
    '管理员通知邮箱', 
    'notification', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
),
-- view模板配置
(
    'view_template_exclude_patterns', 
    '["!**********/32;", "!**********/32;", "!************/32;"]', 
    'json', 
    'view模板中需要排除的IP模式列表', 
    'dns', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    'system'
);