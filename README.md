主要实现 `Knoc` 服务器端能力，提供对 `Knoc` 客户端的支持。

## 基本功能

主要实现Knoc服务器的全部功能。

功能如下：

后续补充

### 安装部署

后续补充

### 目录结构

# DNS配置继承功能说明

## 功能概述

本功能实现了基于视图层级的DNS配置继承系统，允许在不同视图级别（default、biz、office）间共享和覆盖配置设置。系统遵循以下继承规则：

1. 默认视图（default）提供基础配置
2. 业务视图（biz）继承默认视图的配置，并可覆盖特定设置
3. 办公视图（office）具有最高优先级，继承业务视图和默认视图的配置，并可覆盖特定设置

这种继承机制确保了配置的复用性和一致性，同时提供了灵活性。

## 代码结构

### 模型层

- `model/dnsserver/config.go`: 定义了`DnsConfig`和`ConfigResponse`等模型
- `model/dnsserver/view.go`: 定义了视图层级结构和视图类型

### 存储层

- `store/config.go`: 实现了`ConfigStore`接口，负责数据库交互
- `store/view.go`: 提供视图相关的数据访问

### 服务层

- `service/v1/config.go`: 实现了`ConfigService`接口，特别是`GetConfigByViewId`方法，处理配置继承逻辑和生成BIND配置文件

### 控制器层

- `controller/v1/dns/config.go`: 处理HTTP请求并返回最终结果

## 核心实现

配置继承逻辑主要在服务层的`GetConfigByViewId`方法中实现：

1. 首先获取请求的视图信息
2. 获取视图的父级层次结构（从低优先级到高优先级）
3. 获取所有相关视图的配置
4. 按优先级合并配置设置，高级别视图的设置覆盖低级别视图
5. 根据合并后的设置生成BIND配置文件

## API使用示例

访问接口：`GET /api/v1/config/{view_id}`

返回的JSON包含：
- 当前视图的配置
- 视图信息
- 父级视图配置
- 合并后的配置设置
- 根据配置生成的BIND配置文件内容

## 注意事项

1. 视图层级关系必须正确配置才能确保继承机制正常工作
2. 默认视图必须存在，以提供基础配置
3. 删除视图前需要考虑对依赖该视图的子视图的影响

```

