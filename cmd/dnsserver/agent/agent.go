package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/pflag"

	"ks-knoc-server/config"
	"ks-knoc-server/internal/dnsagent/app"
)

var cfg = pflag.StringP("config", "c", "", "Config file path.")

func main() {
	pflag.Parse()

	if *cfg == "" {
		log.Fatalf("配置文件路径不能为空")
	}

	// 读取配置文件
	if err := config.NewConf(*cfg); err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	// 创建DNS Agent应用
	dnsAgent, err := app.NewApp()
	if err != nil {
		log.Fatalf("创建DNS Agent失败: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)

	// 监听信号, 收到SIGINT或SIGTERM信号时, 将会将信号写入到sigChan中
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)

	// 启动应用, 启用一个goroutine来运行应用
	go func() {
		if err := dnsAgent.Run(); err != nil {
			log.Printf("DNS Agent运行失败: %v", err)
		}
	}()

	// 等待停止信号，在收到信号来之前，这里会一直阻塞
	<-sigChan
	fmt.Println("\n收到停止信号，正在关闭DNS Agent...")

	// 停止应用
	if err := dnsAgent.Stop(); err != nil {
		log.Printf("停止DNS Agent失败: %v", err)
	}

	fmt.Println("DNS Agent已停止")
}
